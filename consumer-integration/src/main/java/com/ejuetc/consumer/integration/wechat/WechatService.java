package com.ejuetc.consumer.integration.wechat;

import com.ejuetc.commons.base.exception.BusinessException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@Component
public class WechatService {
    private final WechatConfig wechatConfig;
    private final RedisTemplate<String, String> redisTemplate;
    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    public String getOpenId(String loginIdent) {
        String wxOpenId = getWxOpenId(loginIdent);
        if (wxOpenId == null) {
            throw new BusinessException("bc.ejuetc.consumer.1002");
        }
        return wxOpenId;
    }

    public String getAccessToken() {
        String key = "consumer:wechat:accessToken";
        String token = redisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(token)) {
            return token;
        }
        String accessTokenUrl = wechatConfig.getAccessTokenUrl();
        String resp = restTemplate.getForObject(accessTokenUrl, String.class);
        log.info("wechat resp get access token, resp={}", resp);
        WechatAccessTokenRO wechatAccessTokenRO = fromJson(resp, WechatAccessTokenRO.class);
        if (wechatAccessTokenRO == null || !wechatAccessTokenRO.isSuccess()) {
            return null;
        }
        token = wechatAccessTokenRO.getAccessToken();
        redisTemplate.opsForValue().set(key, token, Duration.ofSeconds(wechatAccessTokenRO.getExpiresIn()));
        return token;
    }

    private String getWxOpenId(String code) {
        String openIdUrl = wechatConfig.getOpenIdUrl(code);
        log.info("wechat req get open id, code={}", code);
        String resp = restTemplate.getForObject(openIdUrl, String.class);
        log.info("wechat resp get open id, code={}, resp={}", code, resp);
        WechatOpenIdRO wechatOpenIdRO = fromJson(resp, WechatOpenIdRO.class);
        if (wechatOpenIdRO == null || !wechatOpenIdRO.isSuccess()) {
            return null;
        }
        return wechatOpenIdRO.getOpenid();
    }

    public String getPhone(String code) {
        String accessToken = getAccessToken();
        String phoneNumberUrl = wechatConfig.getPhoneNumberUrl(accessToken);
        log.info("wechat req get phone number, code={}, url={}", code, phoneNumberUrl);
        String resp = restTemplate.postForObject(phoneNumberUrl, "{\"code\":\"" + code + "\"}", String.class);
        log.info("wechat resp get phone number, code={}, resp={}", code, resp);
        WechatPhoneRO wechatPhoneRO = fromJson(resp, WechatPhoneRO.class);
        if (wechatPhoneRO == null || !wechatPhoneRO.isSuccess()) {
            String errmsg = wechatPhoneRO == null ? "" : wechatPhoneRO.getErrmsg();
            throw new BusinessException("bc.ejuetc.consumer.1003", errmsg);
        }
        return wechatPhoneRO.getPhoneInfo().getPurePhoneNumber();
    }

    @SneakyThrows
    private <T> T fromJson(String json, Class<T> clazz) {
        return objectMapper.readValue(json, clazz);
    }
}
