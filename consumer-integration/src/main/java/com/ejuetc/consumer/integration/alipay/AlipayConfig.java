package com.ejuetc.consumer.integration.alipay;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

@Data
@Configuration
@ConfigurationProperties(prefix = "alipay")
public class AlipayConfig {
    private String appId;
    private String privateKey;
    private String alipayPublicKey;
    private String aesKey;
    private String serverUrl;
    private String format = "JSON";
    private String charset = "UTF-8";
    private String signType = "RSA2";
}
