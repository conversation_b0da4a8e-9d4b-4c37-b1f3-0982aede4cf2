package com.ejuetc.consumer.integration.wechat;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class WechatPhoneRO extends WechatRO {

    @JsonProperty("phone_info")
    private PhoneNumber phoneInfo;

    @Data
    public static class PhoneNumber {
        /**
         * 用户绑定的手机号（国外手机号会有区号）
         */
        private String phoneNumber;
        /**
         * 没有区号的手机号
         */
        private String purePhoneNumber;
        /**
         * 区号
         */
        private String countryCode;
        /**
         * 数据水印,timestamp,appid
         */
        private JsonNode watermark;
    }
}
