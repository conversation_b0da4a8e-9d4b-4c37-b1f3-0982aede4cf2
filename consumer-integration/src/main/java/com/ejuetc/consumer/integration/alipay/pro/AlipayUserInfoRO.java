package com.ejuetc.consumer.integration.alipay.pro;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;

@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class AlipayUserInfoRO extends AlipayBaseResponse {
    private String openId;
    private String nickName;
    private String avatar;
    /**
     * F：女性； M：男性。
     */
    private String gender;
    private String province;
    private String city;
}
