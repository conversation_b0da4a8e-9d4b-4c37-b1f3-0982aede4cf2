package com.ejuetc.consumer.integration.alipay.pro;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class AlipayOauthTokenRO extends AlipayBaseResponse {
    @JsonProperty("user_id")
    private String userId;
//    @JsonProperty("open_id")
//    private String openId;
    @JsonProperty("access_token")
    private String accessToken;
    @JsonProperty("refresh_token")
    private String refreshToken;
    @JsonProperty("expires_in")
    private Integer expiresIn;
    @JsonProperty("re_expires_in")
    private Integer reExpiresIn;
    @JsonProperty("auth_start")
    private String authStart;
//    @JsonProperty("alipay_user_id")
//    private String alipayUserId;

    public boolean isSuccess() {
        return super.isSuccess() || (getCode() == null && userId != null);
    }
}
