package com.ejuetc.consumer.integration.alipay.pro;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@Data
public class AlipayBaseResponse {
    private String code;
    private String msg;
    @JsonProperty("sub_code")
    private String subCode;
    @JsonProperty("sub_msg")
    private String subMsg;

    public boolean isSuccess() {
        return "10000".equals(code);
    }
}
