package com.ejuetc.consumer.integration.wechat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class WechatAccessTokenRO extends WechatRO {
    @JsonProperty("access_token")
    private String accessToken;
    @JsonProperty("expires_in")
    private Integer expiresIn;

    public Integer getExpiresIn() {
        return expiresIn == null ? 60 : expiresIn - 5;
    }
}
