package com.ejuetc.consumer.integration.alipay;

import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class AlipayUtil {
    private static final Logger logger = LoggerFactory.getLogger(AlipayUtil.class);
    
    /**
     * 签名算法
     */
    private static final String SIGN_ALGORITHMS = "SHA256WithRSA";
    
    /**
     * 生成签名
     * @param content 待签名内容
     * @param privateKey 私钥字符串
     * @param charset 字符集
     * @param signType 签名类型，如 RSA2
     * @return 签名字符串
     */
    public static String sign(String content, String privateKey, Charset charset, String signType) {
        try {
            PrivateKey priKey = getPrivateKeyFromPKCS8(privateKey);
            String signAlgorithm = "RSA2".equals(signType) ? "SHA256WithRSA" : "SHA1WithRSA";
            Signature signature = Signature.getInstance(signAlgorithm);
            signature.initSign(priKey);
            signature.update(content.getBytes(charset));
            byte[] signed = signature.sign();
            return Base64.getEncoder().encodeToString(signed);
        } catch (Exception e) {
            logger.error("签名失败", e);
            throw new RuntimeException("签名失败", e);
        }
    }

    /**
     * 验证签名
     * @param content 待验证内容
     * @param publicKey 公钥字符串
     * @param sign 签名字符串
     * @param charset 字符集
     * @param signType 签名类型，如 RSA2
     * @return 验证结果
     */
    public static boolean verify(String content, String publicKey, String sign, Charset charset, String signType) {
        try {
            PublicKey pubKey = getPublicKeyFromX509(publicKey);
            String signAlgorithm = "RSA2".equals(signType) ? "SHA256WithRSA" : "SHA1WithRSA";
            Signature signature = Signature.getInstance(signAlgorithm);
            signature.initVerify(pubKey);
            signature.update(content.getBytes(charset));
            return signature.verify(Base64.getDecoder().decode(sign));
        } catch (Exception e) {
            logger.error("验签失败", e);
            return false;
        }
    }

    /**
     * 为了保持兼容性，保留原来的方法
     */
    public static String sign(Map<String, String> params, String privateKey) {
        String content = getSignContent(params);
        return sign(content, privateKey, StandardCharsets.UTF_8, "RSA2");
    }

    /**
     * 为了保持兼容性，保留原来的方法
     */
    public static boolean verify(Map<String, String> params, String publicKey, String sign) {
        String content = getSignContent(params);
        return verify(content, publicKey, sign, StandardCharsets.UTF_8, "RSA2");
    }

    /**
     * 获取待签名内容
     * @param params 参数Map
     * @return 待签名字符串
     */
    private static String getSignContent(Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return "";
        }

        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);

        StringBuilder content = new StringBuilder();
        int index = 0;
        for (String key : keys) {
            String value = params.get(key);
            if (value != null && value.length() > 0) {
                if (index > 0) {
                    content.append("&");
                }
                content.append(key).append("=").append(value);
                index++;
            }
        }
        return content.toString();
    }

    /**
     * 获取PKCS8格式的私钥
     * @param privateKey 私钥字符串
     * @return PrivateKey对象
     */
    private static PrivateKey getPrivateKeyFromPKCS8(String privateKey) throws Exception {
        privateKey = privateKey.replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s+", "");
        
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        byte[] encodedKey = Base64.getDecoder().decode(privateKey);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(encodedKey);
        return keyFactory.generatePrivate(keySpec);
    }

    /**
     * 获取X509格式的公钥
     * @param publicKey 公钥字符串
     * @return PublicKey对象
     */
    private static PublicKey getPublicKeyFromX509(String publicKey) throws Exception {
        publicKey = publicKey.replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replaceAll("\\s+", "");
        
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        byte[] encodedKey = Base64.getDecoder().decode(publicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(encodedKey);
        return keyFactory.generatePublic(keySpec);
    }


    /**
     * 构建支付宝请求参数
     * @param appId 应用ID
     * @param method 接口名称
     * @param privateKey 私钥
     * @param charset 字符集
     * @param signType 签名方式 RSA2
     * @param map 请求参数
     * @return 完整的请求参数Map
     */
    public static Map<String, String> buildParams(String appId,
                                                  String method,
                                                  String privateKey,
                                                  Charset charset,
                                                  String signType,
                                                  Map<String, String> map) {
        if (!signType.equalsIgnoreCase("RSA2")) {
            throw new IllegalArgumentException("signType must be RSA2");
        }
        Map<String, String> params = new HashMap<>(map);
        params.put("app_id", appId);
        params.put("method", method);
        params.put("format", "JSON");
        params.put("charset", charset.name());
        params.put("sign_type", signType);
        params.put("timestamp", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        params.put("version", "1.0");

        // 生成签名
        String sign = sign(getSignContent(params), privateKey, charset, signType);
        params.put("sign", sign);

        return params;
    }

    /**
     * 将参数Map转换为请求字符串
     * @param params 参数Map
     * @return 请求字符串
     */
    public static String buildRequestString(Map<String, String> params) {
        return params.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + urlEncode(entry.getValue()))
                .collect(Collectors.joining("&"));
    }

    /**
     * URL编码
     */
    private static String urlEncode(String value) {
        return URLEncoder.encode(value, StandardCharsets.UTF_8)
                .replace("+", "%20")
                .replace("*", "%2A")
                .replace("%7E", "~");
    }

    private static final String FULL_ALG = "AES/CBC/PKCS5Padding";

    /**
     * AES 加密
     *
     * @param encryptKey AES 密钥
     * @param content 待加密文案
     * @return 加密后内容
     * @throws Exception
     */
    @SneakyThrows
    public static String encrypt(String encryptKey, String content) {
        Cipher cipher = Cipher.getInstance(FULL_ALG);
        IvParameterSpec iv = new IvParameterSpec(initIv(FULL_ALG));
        cipher.init(Cipher.ENCRYPT_MODE,
                new SecretKeySpec(java.util.Base64.getDecoder().decode(encryptKey.getBytes()), "AES"),
                iv);

        byte[] encryptBytes = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
        return new String(java.util.Base64.getEncoder().encode(encryptBytes));
    }

    /**
     * 初始向量的方法, 全部为0. 这里的写法适合于其它算法,针对AES算法的话,IV值一定是128位的(16字节).
     *
     * @param fullAlg 算法/模式/补码方式
     * @return
     * @throws GeneralSecurityException
     */
    private static byte[] initIv(String fullAlg) throws GeneralSecurityException {
        Cipher cipher = Cipher.getInstance(fullAlg);
        int blockSize = cipher.getBlockSize();
        return new byte[blockSize];
    }

    /**
     * AES 解密
     *
     * @param content 密文
     * @param key aes密钥
     * @return 原文
     */
    @SneakyThrows
    public static String decrypt(String content, String key) {
        //反序列化AES密钥
        SecretKeySpec keySpec = new SecretKeySpec(Base64.getDecoder().decode(key.getBytes()), "AES");
        //128bit全零的IV向量
        byte[] iv = new byte[16];
        IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);

        //初始化加密器并加密
        Cipher deCipher = Cipher.getInstance(FULL_ALG);
        deCipher.init(Cipher.DECRYPT_MODE, keySpec, ivParameterSpec);
        byte[] encryptedBytes = Base64.getDecoder().decode(content.getBytes(StandardCharsets.UTF_8));
        byte[] bytes = deCipher.doFinal(encryptedBytes);
        return new String(bytes);
    }

    public static void main(String[] args) {
        String encrypt = encrypt("a638f850954a1f8c40cc9d225a6e3ced", "abddd");
        System.out.println(encrypt);
        System.out.println(decrypt(encrypt,  "a638f850954a1f8c40cc9d225a6e3ced"));
    }
}
