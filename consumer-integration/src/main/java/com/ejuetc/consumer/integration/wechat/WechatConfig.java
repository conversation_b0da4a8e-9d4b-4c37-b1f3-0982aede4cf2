package com.ejuetc.consumer.integration.wechat;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "wechat")
public class WechatConfig {
    private String appId;
    private String appSecret;

    public String getAccessTokenUrl() {
        return "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s".formatted(appId, appSecret);
    }

    public String getOpenIdUrl(String jsCode) {
        return "https://api.weixin.qq.com/sns/jscode2session?grant_type=authorization_code&appid=%s&secret=%s&js_code=%s".formatted(appId, appSecret, jsCode);
    }

    public String getPhoneNumberUrl(String accessToken) {
        return "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=%s".formatted(accessToken);
    }
}
