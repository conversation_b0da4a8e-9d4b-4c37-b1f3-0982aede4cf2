package com.ejuetc.consumer.integration.alipay;

import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.consumer.integration.alipay.pro.AlipayGenericResponse;
import com.ejuetc.consumer.integration.alipay.pro.AlipayGetMobileRO;
import com.ejuetc.consumer.integration.alipay.pro.AlipayOauthTokenRO;
import com.ejuetc.consumer.integration.alipay.pro.AlipayUserInfoRO;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.Charset;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@Component
public class AliPayService {
    private final AlipayConfig alipayConfig;
    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    public String getOpenId(String loginIdent) {
        AlipayGenericResponse<AlipayOauthTokenRO> oauthToken = getOauthToken(loginIdent);
        if (oauthToken.getResponse() != null && oauthToken.getResponse().isSuccess()) {
            return oauthToken.getResponse().getUserId();
        } else {
            throw new BusinessException("bc.ejuetc.consumer.1002");
        }
    }

    /**
     * 获取手机号
     * API说明： https://opendocs.alipay.com/mini/api/getphonenumber
     *
     * @param encryptData
     * @return
     */
    @SneakyThrows
    public String getPhone(String encryptData) {
        String response;
        try {
            log.info("getPhone encryptData={}", encryptData);
            response = AlipayUtil.decrypt(encryptData, alipayConfig.getAesKey());
            log.info("getPhone decryptData={}", response);
        } catch (Exception e) {
            log.error("getPhone decrypt error", e);
            throw new BusinessException("bc.ejuetc.consumer.1003", "错误的加密数据");
        }

        AlipayGetMobileRO alipayGetMobileRO = objectMapper.readValue(response, AlipayGetMobileRO.class);
        if (!alipayGetMobileRO.isSuccess()) {
            throw new BusinessException("bc.ejuetc.consumer.1003", alipayGetMobileRO.getSubMsg());
        }

        return alipayGetMobileRO.getMobile();
    }

    public AlipayGenericResponse<AlipayOauthTokenRO> getOauthToken(String code) {
        return execute("alipay.system.oauth.token",
                Map.of("grant_type", "authorization_code", "code", code), AlipayOauthTokenRO.class);
    }

    public AlipayGenericResponse<AlipayUserInfoRO> getUserInfo(String accessToken) {
        return execute("alipay.user.info.share", Map.of("auth_token", accessToken), AlipayUserInfoRO.class);
    }

    @SneakyThrows
    private <T> AlipayGenericResponse<T> execute(String method, Map<String, String> param, Class<T> clazz) {
        Map<String, String> paramsMap = AlipayUtil.buildParams(
                alipayConfig.getAppId(),
                method,
                alipayConfig.getPrivateKey(),
                Charset.forName(alipayConfig.getCharset()),
                alipayConfig.getSignType(),
                param);
        log.info("支付宝请求参数: {}", paramsMap);
        
        LinkedMultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        paramsMap.forEach(formData::add);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(formData, headers);

        String response = restTemplate.postForObject(alipayConfig.getServerUrl(), requestEntity, String.class);
        log.info("支付宝响应: {}", response);
        return objectMapper.readValue(response, objectMapper.getTypeFactory().constructParametricType(AlipayGenericResponse.class, clazz));
    }
}
