package com.ejuetc.agent.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.ejuetc.commons.base.valueobj.Memo;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.agent.domain.statement.StatementInvoice")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "结算发票")
public class StatementInvoiceDTO extends BaseDTO<StatementInvoiceDTO> {

    @QueryField
    @Schema(description = "发票批次号")
    private String batchNO;

    @QueryField
    @Schema(description = "结算单税前金额")
    private BigDecimal statementPreTax;

    @QueryField
    @Schema(description = "结算单税后金额")
    private BigDecimal statementAfterTax;

    @QueryField
    @Schema(description = "实际税前金额")
    private BigDecimal factPreTax;

    @QueryField
    @Schema(description = "实际税后金额")
    private BigDecimal factAfterTax;

    @QueryField
    @Schema(description = "税前差异金额")
    private BigDecimal diffPreTax;

    @QueryField
    @Schema(description = "税后差异金额")
    private BigDecimal diffAfterTax;


    @QueryField
    @Schema(description = "关联电子签")
    protected List<StatementDTO> statements;

    @QueryField
    @Schema(description = "调发票差结算单")
    private StatementDTO diffStatement;

    @QueryField
    @Schema(description = "调发票差结算单")
    private ClearingDTO diffClearing;

}
