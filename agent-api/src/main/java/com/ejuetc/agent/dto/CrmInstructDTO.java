package com.ejuetc.agent.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static jakarta.persistence.criteria.JoinType.LEFT;

/**
 * 订单包装
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ToString(callSuper = true)
@QueryDomain("com.ejuetc.agent.domain.crm.CrmInstruct")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "crm交互指令")
public class CrmInstructDTO extends BaseDTO<CrmInstructDTO> {
    @Getter
    public enum Type implements TitleEnum {
        CONTRACT("代理合同"),
        CASH_OUT("请款"),
        CHARGE_OFF("请款核销"),
        ;

        final String title;

        Type(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @Getter
    public enum Status implements TitleEnum {
        WAIT("待发起"),
        SUCC("成功"),
        FAIL("失败"),
        UNKNOW("未知"),
        ;

        final String title;

        Status(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    public CrmInstructDTO(Long id) {
        setId(id);
    }

    @QueryField
    @Schema(description = "类型")
    private Type type;

    @QueryField
    @Schema(description = "订单状态")
    private Status status;

    @QueryField
    @Schema(description = "合同")
    private ContractDTO contract;

    @QueryField
    @Schema(description = "异常备注")
    private String memo;


    @JsonIgnore
    public String getStatusName() {
        return status.getTitle();
    }

    @JsonIgnore
    public String getTypeName() {
        return type.getTitle();
    }
}
