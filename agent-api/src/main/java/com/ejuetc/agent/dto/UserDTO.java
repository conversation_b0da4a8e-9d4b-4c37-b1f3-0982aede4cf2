package com.ejuetc.agent.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.ejuetc.agent.dto.UserDTO.Status.INIT;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.agent.domain.user.User")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "用户")
public class UserDTO extends BaseDTO<UserDTO> {

    public UserDTO(Long userId) {
        super(userId);
    }

    @Getter
    public enum Type implements TitleEnum {
        COMPANY("公司"),
        PERSONAL("个人"),
        ;

        private final String title;

        Type(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @Getter
    public enum Status implements TitleEnum {
        INIT("初始"),
        AUTH_ING("认证中"),
        AUTH_SUCC("认证通过"),
        AUTH_FAIL("认证失败"),
        ;

        private final String title;

        Status(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }


    @QueryField
    @Schema(description = "用户类型")
    private UserDTO.Type type;

    @QueryField
    @Schema(description = "联系人姓名")
    private String contactName;

    @QueryField
    @Schema(description = "联系人手机号")
    private String contactMobile;

    @QueryField
    @Schema(description = "联系地址")
    private String contactAddress;

    @QueryField
    @Schema(description = "身份证姓名")
    private String personalName;

    @QueryField
    @Schema(description = "身份证号")
    private String personalNum;

    @QueryField
    @Schema(description = "营业执照名")
    private String companyName;

    @QueryField
    @Schema(description = "营业执照号")
    private String companyNum;

    @QueryField
    @Schema(description = "是否为易居员工(用于确认工作业绩)")
    private Boolean ejuStaff;

    @QueryField(loadOnly = true)
    @Schema(description = "主体证件名")
    private String certName;

    @QueryField(loadOnly = true)
    @Schema(description = "主体证件号")
    private String certNum;

    @QueryField
    @Schema(description = "认证URL")
    private String authUrl;

    @QueryField
    @Schema(description = "认证短链接")
    private String authShortUrl;

    @QueryField
    @Schema(description = "认证流程ID")
    private String authFlowId;

    @QueryField
    @Schema(description = "认证成功时间")
    private LocalDateTime authSuccTime;

    @QueryField
    @Schema(description = "状态")
    private UserDTO.Status status;

    @QueryField
    @Schema(description = "公司法人(姓名)")
    private String companyLegal;

    @QueryField
    @Schema(description = "企业认证时门店名称")
    private String storeName;

    @QueryField
    @Schema(description = "是否绑定闲鱼租房账号")
    private Boolean bindXYRent;

    @QueryField
    @Schema(description = "是否绑定闲鱼二手房账号")
    private Boolean bindXYSale;


    @QueryField
    @Schema(description = "作为业务主体的邀请码列表")
    List<InvitationDTO> userInvitations;

    @QueryField
    @Schema(description = "作为操作员的邀请码列表")
    List<InvitationDTO> operatorInvitations;

}
