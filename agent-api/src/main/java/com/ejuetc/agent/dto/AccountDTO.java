package com.ejuetc.agent.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

import static com.ejuetc.agent.dto.AccountDTO.Status.INIT;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.agent.domain.account.Account")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "(佣金)账户")
public class AccountDTO extends BaseDTO<AccountDTO> {

    public AccountDTO(Long userId) {
        super(userId);
    }

    @Getter
    public enum Type implements TitleEnum {
        BANK_ACCOUNT("银行账户"),
        YZH_ACCOUNT("云账户账户"),
        ;

        private final String title;

        Type(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @Getter
    public enum Status implements TitleEnum {
        INIT("初始"),
        BIND_ING("绑定中"),
        BIND_SUCC("绑定通过"),
        BIND_FAIL("绑定失败"),
        INVALID("失效"),
        ;

        private final String title;

        Status(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }


    @QueryField
    @Schema(description = "所属用户")
    protected UserDTO user;

    @QueryField
    @Schema(description = "账号(银行卡号或悠客账号)")
    protected String accountNO;

    @QueryField
    @Schema(description = "外部编号")
    protected String externalNO;

    @QueryField
    @Schema(description = "账户名")
    protected String accountName;

    @QueryField
    @Schema(description = "类型")
    private AccountDTO.Type type;

    @QueryField
    @Schema(description = "状态")
    protected AccountDTO.Status status = INIT;

    @QueryField
    @Schema(description = "开户行所属省份")
    private String bankProvince;

    @QueryField
    @Schema(description = "开户行所属城市")
    private String bankCity;

    @QueryField
    @Schema(description = "开户行")
    private String bankAddress;

    @QueryField
    @Schema(description = "身份证号")
    private String cardId;

    @QueryField
    @Schema(description = "手机号")
    private String mobile;

    @QueryField
    @Schema(description = "通知内容")
    private String notifyBody;

    @QueryField
    @Schema(description = "签约地址")
    private String signUrl;

    @QueryField
    @Schema(description = "是否是自己的")
    private Boolean self;

    @QueryField
    @Schema(description = "银行名称")
    private String bankName;

    @QueryField
    @Schema(description = "排序")
    private Integer sort;

}
