package com.ejuetc.agent.dto;

import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.agent.domain.deal.Deal")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "(用于清分的)交易")
public class DealDTO extends BaseDTO<DealDTO> {
    @Getter
    public enum Type implements TitleEnum {
        CHANNEL_OPEN("开通渠道"),
        CLUE_BUY("线索购买"),
        CLUE_CONSUME("线索消耗"),
        ;

        private final String title;

        Type(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @Getter
    public enum Category implements TitleEnum {
        FORWARD_ENJOY("结转畅享包"),
        COMPANY_ENJOY("公司畅享包"),
        WITHOUT_PAY("无支付订单"),
        EXPIRE("过期未分配"),
        NORMAL("正常"),
        ;

        private final String title;

        Category(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @Getter
    public enum Status implements TitleEnum {
        INIT("初始"),
        CONFIGED("已配置"),
        WAIT_NOTIFY("待通知"),//待通知上架或支付审核时间
        WAIT_CLEAR("待清分"),
        CLEARED("已清分"),
        FINAL("已完结"),
        ;

        private final String title;

        Status(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }


    @QueryField
    @Schema(description = "交易类型")
    private DealDTO.Type type;

    @QueryField
    @Schema(description = "交易类别")
    private DealDTO.Category category;

    @QueryField
    @Schema(description = "交易有效开始时间")
    private LocalDateTime indateBegin;

    @QueryField
    @Schema(description = "交易有效结束时间")
    private LocalDateTime indateEnd;

    @QueryField
    @Schema(description = "状态")
    private DealDTO.Status status;

    @QueryField
    @Schema(description = "来源交易ID")
    private Long srcId;

    @QueryField
    @Schema(description = "渠道码")
    private ChannelDTO.Code channelCode;

    @QueryField
    @Schema(description = "业务码")
    private BusinessOpenDTO.Code businessCode;

    @QueryField
    @Schema(description = "城市码")
    private String cityCode;

    @QueryField
    @Schema(description = "城市名")
    protected String cityName;

    @QueryField
    @Schema(description = "商品外键")
    private Long goodsId;

    @QueryField
    @Schema(description = "商品名称")
    private String goodsName;

    @QueryField
    @Schema(description = "父订单外键")
    private Long parentOrderId;

    @QueryField("buyUser.id")
    @Schema(description = "购买人ID")
    private Long buyUserId;

    @QueryField
    @Schema(description = "购买人")
    private UserDTO buyUser;

    @QueryField
    @Schema(description = "购买人名称")
    private String buyUserName;

    @QueryField
    @Schema(description = "支付金额")
    private BigDecimal payAmount;

    @QueryField
    @Schema(description = "支付时间")
    private LocalDateTime payTime;

    @QueryField
    @Schema(description = "经纪操作员")
    private UserDTO brokerOperator;

    @QueryField
    @Schema(description = "经纪类型")
    private UserDTO.Type brokerUserType;

    @QueryField
    @Schema(description = "代理邀请码")
    private String agentCode;

    @QueryField
    @Schema(description = "所属经纪")
    private UserDTO brokerUser;
    @QueryField
    @Schema(description = "经纪机构名称")
    private String brokerUserName;
    @QueryField
    @Schema(description = "经纪机构操作人名称")
    private String brokerOperatorName;

    @QueryField
    @Schema(description = "经纪合同")
    protected ContractDTO brokerContract;

    @QueryField
    @Schema(description = "代理邀请信息")
    private InvitationDTO agentInvitation;

    @QueryField
    @Schema(description = "合伙邀请信息")
    private InvitationDTO partnerInvitation;

    @QueryField
    @Schema(description = "代理商")
    private UserDTO agentUser;

    @QueryField
    @Schema(description = "代理商名称")
    private String agentUserName;

    @QueryField
    @Schema(description = "是否参与代理分佣")
    private Boolean agentJoinComm;

    @QueryField
    @Schema(description = "代理商类型")
    private UserDTO.Type agentUserType;

    @QueryField
    @Schema(description = "代理商合同子类型")
    private ContractDTO.SubType agentSubType;

    @QueryField
    @Schema(description = "代理合同")
    protected ContractDTO agentContract;

    @QueryField
    @Schema(description = "代理价配置")
    private PriceDTO agentPrice;

    @QueryField
    @Schema(description = "合伙协议")
    protected ContractDTO partnerContract;

    @QueryField
    @Schema(description = "合伙商")
    private UserDTO partnerUser;

    @QueryField
    @Schema(description = "合伙商名称")
    private String partnerUserName;

    @QueryField
    @Schema(description = "合伙商类型")
    private UserDTO.Type partnerType;

    @QueryField
    @Schema(description = "是否参与合伙分佣")
    private Boolean partnerJoinComm;

    @QueryField
    @Schema(description = "首次上委托时间")
    private LocalDateTime upDelegateTime;

    @QueryField
    @Schema(description = "是否上架委托")
    private Boolean upDelegateFlag;

//    @QueryField
//    @Schema(description = "备注信息")
//    protected String memo;

    @QueryField
    @Schema(description = "")
    List<ClearingDTO> clearings;

    @QueryField
    @Schema(description = "合同子类型列表")
    private List<ContractDTO.SubType> brokerContractSubTypes;

    @QueryField
    @Schema(description = "原始清分记录(不考虑调账部分)")
    private ClearingDTO clearing;

    @QueryField
    @Schema(description = "MALL订单号")
    private String orderNum;

    @QueryField
    @Schema(description = "MALL父订单号")
    private String parentOrderNum;

    @QueryField
    @Schema(description = "CRM订单号")
    private String crmOrderNO;

    @QueryField
    @Schema(description = "代理折扣价(代理售价*代理折扣)")
    private BigDecimal agentDiscountPrice;

    @QueryField
    @Schema(description = "是否天猫结转")
    private Boolean tmForward;

    @QueryField
    @Schema(description = "天猫服务费转")
    private BigDecimal tmServiceFee;

    @QueryField
    @Schema(description = "天猫保证金转")
    private BigDecimal tmBail;

    @QueryField
    @Schema(description = "新交")
    private BigDecimal tmNewTrade;

    @QueryField
    @Schema(description = "天猫结转收入合同编号")
    private String tmIncomeContractNO;

    @QueryField
    @Schema(description = "子订单外键 （mall模块子订单id）")
    private Long orderId;

    @QueryField
    @Schema(description = "父交易单")
    private DealDTO parent;
}

