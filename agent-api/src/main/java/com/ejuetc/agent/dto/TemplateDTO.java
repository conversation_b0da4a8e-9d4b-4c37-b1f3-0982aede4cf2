package com.ejuetc.agent.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.agent.domain.template.Template")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "(合同)模板")
public class TemplateDTO extends BaseDTO<TemplateDTO> {
    @Getter
    public enum Type implements TitleEnum {
        PARTNER("合伙协议"),
        AGENT("代理协议"),
        SUBRO("权利转让协议"),
        BROKER("经纪协议"),
        STATEMENT("结算单"),
        ;

        private final String title;

        Type(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

}
