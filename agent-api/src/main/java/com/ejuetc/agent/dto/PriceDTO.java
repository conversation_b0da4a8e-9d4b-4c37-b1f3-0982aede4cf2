package com.ejuetc.agent.dto;

import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.ejuetc.commons.base.valueobj.time.TimeInterval;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.agent.domain.price.Price")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "代理价")
public class PriceDTO extends BaseDTO<PriceDTO> {

    @QueryField
    @Schema(description = "渠道码")
    private ChannelDTO.Code channelCode;

    @QueryField
    @Schema(description = "业务码")
    private BusinessOpenDTO.Code businessCode;

    @QueryField
    @Schema(description = "城市名称")
    private String cityName;

    @QueryField
    @Schema(description = "城市编码")
    private String cityCode;

    @QueryField
    @Schema(description = "交易类型")
    private DealDTO.Type dealType;

    @QueryField
    @Schema(description = "代理价")
    private BigDecimal agentPrice;

    @QueryField
    @Schema(description = "价目关联合同列表")
    private List<ContractDTO> contracts;

    @QueryField
    @Schema(description = "标准售价")
    private BigDecimal standardPrice;

    @QueryField
    @Schema(description = "代理折扣")
    private BigDecimal agentDiscount;

    @QueryField
    @Schema(description = "代理折扣价(代理售价*代理折扣)")
    private BigDecimal agentDiscountPrice;

    @QueryField
    @Schema(description = "正常售价返佣比例")
    private BigDecimal normalCommRate;

    @QueryField
    @Schema(description = "超额售价返佣比例")
    private BigDecimal excessCommRatio;

    @QueryField("indate.beginTime")
    @Schema(description = "开始时间")
    private LocalDateTime beginTime;

    @QueryField("indate.endTime")
    @Schema(description = "结束时间")
    private LocalDateTime endTime;

}
