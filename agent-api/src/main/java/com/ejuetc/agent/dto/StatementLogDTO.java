package com.ejuetc.agent.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.agent.domain.statement.StatementLog")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "结算日志")
public class StatementLogDTO extends BaseDTO<StatementLogDTO> {

    @QueryField
    @Schema(description = "所属结算单")
    protected StatementDTO statement;

    @QueryField("flow.fromStatus")
    @Schema(description = "操作前状态")
    private StatementDTO.Status fromStatus;

    @QueryField("flow.role")
    @Schema(description = "角色")
    private StatementDTO.Role role;

    @QueryField("flow.action")
    @Schema(description = "操作")
    private StatementDTO.Action action;

    @QueryField
    @Schema(description = "操作后状态")
    protected StatementDTO.Status toStatus;

    @QueryField
    @Schema(description = "操作参数")
    private String param;

    @QueryField
    @Schema(description = "操作描述")
    private String remark;

    @QueryField
    @Schema(description = "操作人姓名")
    private String operatorName;
}
