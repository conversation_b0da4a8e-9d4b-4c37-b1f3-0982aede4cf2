package com.ejuetc.agent.dto;

import com.ejuetc.agent.api.statement.StatementPartnerAmountRO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.agent.domain.crm.OaFinance")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "oa财务")
public class OaFinanceDTO {
    @QueryField
    @Schema(name = "申请编号")
    private Long id;

    @QueryField
    @Schema(name = "类型")
    private Type type;

    @QueryField
    @Schema(name = "申请时间")
    private LocalDateTime applyTime;

    @QueryField
    @Schema(name = "申请人")
    private String applyName;

    @QueryField
    @Schema(name = "OA状态")
    private ContractDTO.OAStatus oaStatus;

    @QueryField
    @Schema(name = "OA审核时间")
    private LocalDateTime oaAuditTime;

    @QueryField
    @Schema(name = "oa备注信息")
    private String oaMemo;

    @QueryField
    @Schema(name = "'公司名称'")
    private String companyName;

    @QueryField
    @Schema(name = "'公司营业执照号'")
    private String companyNo;

    @QueryField
    @Schema(name = "'合同单据号'")
    private String contractNo;

    @QueryField
    @Schema(name = "'银行账号'")
    private String bankNo;

    @QueryField
    @Schema(name = "'发票URL'")
    private String invoiceUrl;

    @QueryField
    @Schema(name = "'发票确认URL'")
    private String invoiceConfirmUrl;

    @QueryField
    @Schema(name = "'结算单URL'")
    private String statementUrl;

    @QueryField
    @Schema(name = "'预付款单号'")
    private String prePayNo;

    @QueryField
    @Schema(name = "'预付款合同号'")
    private String prePayContractNo;

    @QueryField
    @Schema(name = "'费用类型'")
    private String feeType;

    @QueryField
    @Schema(name = "'已付金额'")
    private BigDecimal payAmount;

    @QueryField
    @Schema(name = "'未核销总金额'")
    private BigDecimal unCancelAmount;

    @QueryField
    @Schema(name = "'本次核销金额'")
    private BigDecimal cancelAmount;

    @QueryField
    @Schema(name = "'备注'")
    private String remark;

    @QueryField
    @Schema(name = "'考核信息'")
    private List<StatementPartnerAmountRO> khInfo;

    @QueryField
    @Schema(name = "'结算单'")
    private List<StatementDTO> statements;

    @QueryField
    @Schema(name = "入账日期")
    private LocalDate chargeOffTime;

    @QueryField
    @Schema(name = "'发票确认税前金额'")
    protected BigDecimal invoicePreTax;

    @QueryField
    @Schema(name = "'发票确认税后金额'")
    protected BigDecimal invoiceAfterTax;

    @Getter
    public enum Type implements TitleEnum {
        CASH_OUT("请款"),
        CHARGE_OFF("核销"),
        ;

        private final String title;

        Type(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
}
