package com.ejuetc.agent.dto;

import java.util.List;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.agent.domain.esign.ESign")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "电子签")
public class ESignDTO extends BaseDTO<ESignDTO> {
    @Getter
    public enum Status implements TitleEnum {
        INIT("初始"),
        SIGN_ING("签约中"),//等待委托上架
        CANCEL("已取消"),
        REJECT("已拒绝"),
        COMPLE("已签约"),
        INVALID_ING("作废中"),
        INVALID("已作废"),
        ;

        private final String title;

        Status(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @QueryField
    @Schema(description = "签约用户")
    protected UserDTO user;

    @QueryField
    @Schema(description = "签约状态")
    protected ESignDTO.Status status;

    @QueryField
    @Schema(description = "合同模板")
    protected TemplateDTO template;

    @QueryField
    @Schema(description = "文件ID")
    private String docId;

    @QueryField
    @Schema(description = "文件名")
    private String docName;

    @QueryField
    @Schema(description = "文件下载地址")
    private String docUrl;

    @QueryField
    @Schema(description = "签约流程ID")
    private String flowId;

    @QueryField
    @Schema(description = "签约地址")
    private String flowUrl;

    @QueryField
    @Schema(description = "签约短地址")
    private String flowShortUrl;

    @QueryField
    @Schema(description = "完成签约文件路径")
    private List<String> completeFiles;

    @QueryField
    @Schema(description = "签署结果")
    private String callbackResult;

    @QueryField
    @Schema(description = "回调描述")
    private String callbackDesc;

    @QueryField
    @Schema(description = "个人发起方账号ID|经办人账号ID")
    private String psnId;

}
