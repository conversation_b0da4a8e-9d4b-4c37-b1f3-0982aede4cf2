package com.ejuetc.agent.dto;

import com.ejuetc.commons.base.entity.TitleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PersonalPayDTO {

    @Schema(description = "批次号")
    private String withdrawNO;

    @Schema(description = "结算账号ID")
    private Long statementAccountId;

    @Schema(description = "结算账号状态")
    private String statementAccountStatus;

    @Schema(description = "结算账号状态描述")
    private String statementAccountStatusDesc;

    @Schema(description = "状态")
    private Status status;

    @Schema(description = "备注（失败原因）")
    private String memo;

    @Getter
    public enum Status implements TitleEnum {
        SUCC("成功"),
        FAIL("失败"),
        UNKNOW("未知"),
        ;

        final String title;

        Status(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
}
