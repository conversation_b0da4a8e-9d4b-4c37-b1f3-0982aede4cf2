package com.ejuetc.agent.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.agent.domain.withdraw.Withdraw")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "出款单")
public class WithdrawDTO extends BaseDTO<WithdrawDTO> {


    @Getter
    public enum Status implements TitleEnum {
        INIT("初始"),
        PAYING("付款中"),
        SUCCESS("付款成功"),
        FAIL("付款失败"),
        ;

        private final String title;

        Status(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @Getter
    public enum Type implements TitleEnum {
        PERSONAL("个人"),
        COMPANY("公司"),
        ;

        private final String title;

        Type(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @QueryField
    @Schema(description = "出款单包含的结算账户")
    private List<StatementAccountDTO> statementAccounts;

    @QueryField
    @Schema(description = "总金额")
    private BigDecimal totalAmount;

    @QueryField
    @Schema(description = "结算账户数量")
    private Integer statementAccountCount;

    @QueryField
    @Schema(description = "出款单号")
    private String withdrawNO;

}
