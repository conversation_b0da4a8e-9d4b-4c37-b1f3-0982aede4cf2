package com.ejuetc.agent.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.ejuetc.commons.base.valueobj.Memo;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.agent.domain.contract.Contract")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "(合作)合同")
public class ContractDTO extends BaseDTO<ContractDTO> {
    @Getter
    public enum Type implements TitleEnum {
        PARTNER("合伙", null, SubType.PARTNER, TemplateDTO.Type.PARTNER),
        AGENT("代理", PARTNER, SubType.AGENT_NORMAL, TemplateDTO.Type.AGENT),
        BROKER("经纪", AGENT, SubType.BROKER, TemplateDTO.Type.BROKER),
        ;

        private final String title;
        private final Type invitedType;
        private final SubType defSubType;
        private final TemplateDTO.Type templateType;

        Type(String title, Type invitedType, SubType defSubType, TemplateDTO.Type templateType) {
            this.title = title;
            this.invitedType = invitedType;
            this.defSubType = defSubType;
            this.templateType = templateType;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @Getter
    public enum SubType implements TitleEnum {
        PARTNER("合伙"),
        AGENT_NORMAL("普通代理"),
        AGENT_XIANYU_EXCLUSIVE("闲鱼独家代理"),
        AGENT_LEJU_EXCLUSIVE("乐居独家代理"),
        BROKER("经纪"),
        ;

        private final String title;

        SubType(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @Getter
    public enum Status implements TitleEnum {
        INIT("初始"),
        SIGN_ING("签约中"),
        SIGN_FAIL("签约拒绝"),
        EFFECTIVE("生效"),
        FREEZE("冻结"),//(代理合同)冻结状态下不可以发展新订单,但是历史订单可以分佣
        INVALID("失效"),
        ;

        private final String title;

        Status(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @Getter
    public enum OAStatus implements TitleEnum {

        IN_HAND("审核中"),
        SUCCESS("审核通过"),
        FAIL("失败"),
        INIT("初始"),
        ;

        private final String title;

        OAStatus(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @QueryField
    @Schema(description = "合同编号")
    private String contractNo;

    @QueryField(typeField = true)
    @Schema(description = "合同类别")
    private ContractDTO.Type type;

    @QueryField
    @Schema(description = "子类别")
    private ContractDTO.SubType subType;

    @QueryField
    @Schema(description = "状态")
    protected ContractDTO.Status status;

    @QueryField
    @Schema(description = "下载完成的文件")
    protected List<String> completeFiles;

    @QueryField
    @Schema(description = "备注信息")
    protected Memo memo;

    @QueryField
    @Column(name = "user_type", nullable = false)
    private UserDTO.Type userType;

    @QueryField
    @Schema(description = "所属用户")
    private UserDTO user;

    @QueryField("user.id")
    @Schema(description = "所属用户")
    private Long userId;

    @QueryField
    @Schema(description = "用户证件名")
    private String userCertName;

    @QueryField
    @Schema(description = "用户证件号")
    private String userCertNum;

    @QueryField
    @Schema(description = "操作人")
    private UserDTO operator;

    @QueryField("operator.id")
    @Schema(description = "操作人")
    private Long operatorId;

    @QueryField
    @Schema(description = "操作人姓名")
    private String operatorName;

    @QueryField
    @Schema(description = "操作人手机号")
    private String operatorMobile;

    @QueryField
    @Schema(description = "邀请信息")
    private InvitationDTO invited;

    @QueryField
    @Schema(description = "邀请用户")
    private UserDTO invitedUser;

    @QueryField
    @Schema(description = "邀请合同")
    protected ContractDTO invitedContract;

    @QueryField
    @Schema(description = "使用合同模板")
    protected TemplateDTO template;

    @QueryField
    @Schema(description = "推荐码")
    private String invitedCode;

    @QueryField
    @Schema(description = "合同关联邀请信息列表")
    private List<InvitationDTO> inviteds;

    @QueryField("contractIndate.beginTime")
    @Schema(description = "合同开始时间")
    private LocalDateTime contractBeginTime;

    @QueryField("contractIndate.endTime")
    @Schema(description = "合同结束时间")
    private LocalDateTime contractEndTime;

    @QueryField
    @Schema(description = "关联电子签")
    protected ESignDTO eSign;

    @QueryField
    @Schema(description = "关联代理价")
    private List<PriceDTO> prices;

    @QueryField
    @Schema(description = "城市代码")
    private String cityCode;

    @QueryField
    @Schema(description = "城市名称")
    private String cityName;

}
