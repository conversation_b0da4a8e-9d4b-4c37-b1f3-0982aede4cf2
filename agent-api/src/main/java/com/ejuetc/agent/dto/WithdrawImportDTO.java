package com.ejuetc.agent.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.agent.domain.withdraw.WithdrawImport")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "出款单导入")
public class WithdrawImportDTO extends BaseDTO<WithdrawImportDTO> {

    @Getter
    public enum ProcessStatus implements TitleEnum {
        UNDO("未处理"),
        DOING("处理中"),
        FINISH("已处理"),
        ;

        private final String title;

        ProcessStatus(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @QueryField
    @Schema(description = "处理状态")
    private WithdrawImportDTO.ProcessStatus processStatus;

    @QueryField
    @Schema(description = "导入时间")
    private LocalDateTime importTime;

    @QueryField
    @Schema(description = "导入文件名")
    private String fileName;

    @QueryField
    @Schema(description = "导入总条数")
    private Integer importTotalCount;

    @QueryField
    @Schema(description = "成功条数")
    private Integer importSuccessCount;

    @QueryField
    @Schema(description = "失败条数")
    private Integer importFailCount;

    @QueryField
    @Schema(description = "导出文件URL")
    private String exportUrl;

}
