package com.ejuetc.agent.dto;

import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Set;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.agent.domain.invitation.Invitation")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "邀请")
public class InvitationDTO {
    @QueryField
    @Schema(description = "推荐码")
    private String code;


    @QueryField
    @Schema(description = "所属操作员")
    private UserDTO operator;

    @QueryField
    @Schema(description = "所属主体")
    private UserDTO user;

    @QueryField
    @Schema(description = "邀请码邀请创建的合同")
    private List<ContractDTO> contracts;

}
