package com.ejuetc.agent.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.ejuetc.commons.base.valueobj.Memo;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.agent.domain.statement.StatementAccount")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "结算账号(汇总)")
public class StatementAccountDTO extends BaseDTO<StatementAccountDTO> {


    @Getter
    public enum Status implements TitleEnum {
        INIT("初始"),
        PAYING("付款中"),
        SUCCESS("付款成功"),
        FAIL("付款失败"),
        ;

        private final String title;

        Status(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }


    @QueryField
    @Schema(description = "(佣金)账户")
    private AccountDTO account;

    @QueryField
    @Schema(description = "结算单")
    private StatementDTO statement;

    @QueryField
    @Schema(description = "税前返佣金额")
    private BigDecimal commPreTax;

}
