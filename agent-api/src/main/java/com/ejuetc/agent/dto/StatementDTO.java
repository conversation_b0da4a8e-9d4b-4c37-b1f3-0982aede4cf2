package com.ejuetc.agent.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.ejuetc.commons.base.valueobj.Memo;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.agent.domain.statement.Statement")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "结算单")
public class StatementDTO extends BaseDTO<StatementDTO> {

    @Getter
    public enum Status implements TitleEnum {
        CHECK_WAIT("待审核"),
        CHECK_REJECT("已作废"),
        LAUNCH_WAIT("待发起"),
        SIGN_WAIT("待签约"),
        SIGN_ING("签约中"),
        INVOICE_WAIT_UP("发票待上传"),
        INVOICE_REJECT("发票审核被拒"),
        INVOICE_WAIT_CHECK("发票待审核"),
        PAY_ING("发票已审核"),
        PAY_FAIL("支付失败或部分失败"),
        PAY_DONE("已付款"),
        STOP_WAIT("终止待确认"),
        STOP_DONE("已终止"),
        ;

        private final String title;

        Status(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @Getter
    public enum Role implements TitleEnum {
        USER("用户"),
        OPERATOR("操作员"),
        SYSTEM("系统"),
        ;
        private final String title;

        Role(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }


    @Getter
    public enum Action implements TitleEnum {
        AGREE("同意"),
        REJECT("拒绝"),
        STOP("终止"),
        ;
        private final String title;

        Action(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }


    public StatementDTO(Long userId) {
        super(userId);
    }


    @QueryField
    @Schema(description = "结算单号")
    private String statementNO;

    @QueryField
    @Schema(description = "结算状态")
    private StatementDTO.Status status;

    @QueryField
    @Schema(description = "所属主体")
    private UserDTO user;

    @QueryField
    @Schema(description = "结算月份")
    private String statementMonth;

    @QueryField
    @Schema(description = "清分数量")
    private Integer clearingItemCount;

    @QueryField
    @Schema(description = "打款时间")
    private LocalDateTime payTime;

    @QueryField
    @Schema(description = "签约URL")
    private String signUrl;

    @QueryField
    @Schema(description = "签名文档")
    private String signDoc;

    @QueryField
    @Schema(description = "发票URL")
    private String invoiceUrl;

    @QueryField
    @Schema(description = "关联电子签")
    private ESignDTO eSign;

    @QueryField
    @Schema(description = "结算日志")
    private List<StatementLogDTO> statementLogs;

    @QueryField
    @Schema(description = "备注信息")
    private Memo memo;

    @QueryField
    @Schema(description = "结算批次")
    private String statementBatch;


    @QueryField
    @Schema(description = "超售佣金")
    private BigDecimal excessComm;

    @QueryField
    @Schema(description = "普通佣金")
    private BigDecimal normalComm;

    @QueryField
    @Schema(description = "返佣总额(未计税)")
    private BigDecimal comm;

    @QueryField
    @Schema(description = "税前返佣金额")
    private BigDecimal commPreTax;

    @QueryField
    @Schema(description = "报表税前金额")
    private BigDecimal reportPreTax;

    @QueryField
    @Schema(description = "出款服务费")
    private BigDecimal disburseFee;

    @QueryField
    @Schema(description = "报表税后金额")
    private BigDecimal reportAfterTax;

    @QueryField
    @Schema(description = "发票上传时间")
    private LocalDateTime invoiceTime;

    @QueryField
    @Schema(description = "签约时间")
    private LocalDateTime signTime;

    @QueryField
    @Schema(description = "清分列表")
    private List<ClearingDTO> clearings;

    @QueryField
    @Schema(description = "税率")
    private BigDecimal taxRate;

    @QueryField
    @Schema(description = "操作描述")
    private String remark;

    @QueryField
    @Schema(description = "结算金额")
    protected BigDecimal statementAmount;

    @QueryField
    @Schema(description = "发票确认URL")
    private String invoiceConfirmUrl;

    @QueryField
    @Schema(description = "发票确认税前金额")
    private BigDecimal invoicePreTax;

    @QueryField
    @Schema(description = "发票确认税后金额")
    private BigDecimal invoiceAfterTax;

    @QueryField
    @Schema(description = "发票审核时间")
    private LocalDateTime invoiceCheckTime;

    @QueryField
    @Schema(description = "请款核销")
    private OaFinanceDTO oaFinance;

    @QueryField
    @Schema(description = "发票批次号")
    private String invoiceBatchNO;
}
