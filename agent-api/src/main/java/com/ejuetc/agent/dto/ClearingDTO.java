package com.ejuetc.agent.dto;

import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.agent.domain.clearing.Clearing")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "佣金清分记录")
public class ClearingDTO {
	
	@Getter
    public enum Type implements TitleEnum {
        AGENT("代理"),
        PARTNER("合伙")
        ;

        private final String title;

        Type(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @QueryField
    @Schema(description = "关联交易")
    private DealDTO deal;

    @QueryField
    @Schema(description = "支付金额")
    private BigDecimal payAmount;

    @QueryField
    @Schema(description = "超售金额")
    private BigDecimal excessAmount;

    @QueryField
    @Schema(description = "超售佣金")
    private BigDecimal excessComm;

    @QueryField
    @Schema(description = "关联代理价")
    private PriceDTO price;

    @QueryField
    @Schema(description = "标准售价")
    private BigDecimal standardPrice;

    @QueryField
    @Schema(description = "代理价(结算价)")
    private BigDecimal agentPrice;

    @QueryField
    @Schema(description = "代理折扣")
    private BigDecimal agentDiscount;

    @QueryField
    @Schema(description = "代理折扣价(代理售价*代理折扣)")
    private BigDecimal agentDiscountPrice;

    @QueryField
    @Schema(description = "代理正常售价返佣比例")
    private BigDecimal agentNormalCommRate;

    @QueryField
    @Schema(description = "代理超额售价返佣比例")
    private BigDecimal agentExcessCommRatio;

    @QueryField
    @Schema(description = "代理佣金总额")
    private BigDecimal agentComm;

    @QueryField
    @Schema(description = "代理税前返佣")
    private BigDecimal agentCommPreTax;

    @QueryField
    @Schema(description = "代理税后返佣")
    private BigDecimal agentCommAfterTax;

    @QueryField
    @Schema(description = "合伙返佣比例")
    private BigDecimal partnerCommRate;

    @QueryField
    @Schema(description = "合伙佣金总额")
    private BigDecimal partnerComm;

    @QueryField
    @Schema(description = "合伙税前返佣")
    private BigDecimal partnerCommPreTax;

    @QueryField
    @Schema(description = "合伙税后返佣")
    private BigDecimal partnerCommAfterTax;

    @QueryField
    @Schema(description = "结算金额")
    protected BigDecimal statementAmount;

}
