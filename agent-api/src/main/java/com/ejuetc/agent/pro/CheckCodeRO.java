package com.ejuetc.agent.pro;

import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.agent.domain.invitation.Invitation")
@ToString(callSuper = true)
@Schema(name = "邀请")
public class CheckCodeRO {
    @QueryField("operator.certName")
    @Schema(description = "所属操作员名称")
    private String operatorName;
}
