package com.ejuetc.agent.api.user;

import com.ejuetc.agent.dto.UserDTO;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import static com.ejuetc.commons.base.utils.StringUtils.notBlank;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "编辑用户信息")
public class EditUserPO {
    @Schema(description = "用户编号")
    private Long id;

    @Schema(description = "用户类型")
    private UserDTO.Type type;

    @Schema(description = "联系人姓名")
    private String contactName;

    @Schema(description = "联系人手机号")
    private String contactMobile;

    @Schema(description = "联系地址")
    private String contactAddress;

    @Schema(description = "身份证姓名")
    private String personalName;

    @Schema(description = "身份证号")
    private String personalNum;

    @Schema(description = "营业执照名")
    private String companyName;

    @Schema(description = "营业执照号")
    private String companyNum;

    @Schema(description = "公司法人")
    private String companyLegal;

    @Schema(description = "公司地址")
    private String companyAddress;

    @Schema(description = "证件照URL")
    private String certUrl;

    @Schema(description = "是否发起实名认证")
    private boolean launchAuth = false;

    @Schema(description = "开户行所属省份")
    private String bankProvince;

    @Schema(description = "开户行所属城市")
    private String bankCity;

    @Schema(description = "开户行")
    private String bankAddress;

    @Schema(description = "银行名")
    private String bankName;

    @Schema(description = "银行账号")
    private String bankAccount;

    @Schema(description = "所属主体用户ID")
    private Long parentId;

    @Schema(description = "SaaS许可类型")
    private String licenseType;

    @Schema(description = "SaaS认证状态")
    private Boolean authed;

    @Schema(description = "企业认证时门店名称")
    private String storeName;
}
