package com.ejuetc.agent.api.user;

import com.ejuetc.agent.api.account.CreateAccountPO;
import com.ejuetc.agent.dto.AccountDTO;
import com.ejuetc.agent.dto.UserDTO;
import com.ejuetc.commons.base.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Tag(name = "用户接口")
@FeignClient(value = "ejuetc.agent", contextId = "UserApi")
public interface UserApi {


    @Operation(summary = "编辑用户信息")
    @PostMapping("/user/editUser")
    ApiResponse<UserDTO> editUser(@RequestBody EditUserPO po);

    @Operation(summary = "添加账号")
    @PostMapping("/user/addAccount")
    ApiResponse<AccountDTO> addAccount(@RequestBody CreateAccountPO po);

    @Operation(summary = "绑定变更通知")
    @PostMapping("/user/notifyBind")
    ApiResponse<UserDTO> notifyBind(@RequestBody UserBindNotifyPo po);
}
