package com.ejuetc.agent.api.statement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PersonalPayPO {

    @Schema(description = "批次号")
    private String withdrawNO;

    @Schema(description = "结算账号ID")
    private Long statementAccountId;

    @Schema(description = "结算账号状态")
    private String statementAccountStatus;

    @Schema(description = "结算账号状态描述")
    private String statementAccountStatusDesc;
}
