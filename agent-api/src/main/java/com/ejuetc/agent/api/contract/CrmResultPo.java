package com.ejuetc.agent.api.contract;

import com.ejuetc.commons.base.entity.TitleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "oa/支付结果通知")
public class CrmResultPo {
    @Schema(description = "合同编号（typ =AGENT_CONTRACT才有）")
    private String contractNo;

    @Schema(description = "请款单号（type = CASH_OUT,CASH_OUT_PAY_RESULT,CHARGE_OFF 才有）")
    private String qkNo;

    @Schema(description = "类型")
    private Type resultType;

    @Schema(description = "是否成功（typ =AGENT_CONTRACT,CASH_OUT oa审核状态， CASH_OUT_PAY_RESULT 支付状态）")
    private Boolean success;

    @Schema(description = "驳回原因（typ =AGENT_CONTRACT,CASH_OUT,CHARGE_OFF 才有）")
    private String rejectReason;

    @Schema(description = "OA编号（typ =AGENT_CONTRACT,CASH_OUT,CHARGE_OFF 才有）")
    private String oaNum;

    @Schema(description = "通过时间（typ =AGENT_CONTRACT,CASH_OUT,CHARGE_OFF 才有）")
    private LocalDateTime auditTime;

    @Schema(description = "实际支付金额（typ =CASH_OUT_PAY_RESULT 才有）")
    private BigDecimal realAmount;

    @Getter
    public enum Type implements TitleEnum {
        AGENT_CONTRACT("代理合同"),
        CASH_OUT("公司请款"),
        CASH_OUT_PAY_RESULT("公司请款单支付结果"),
        CHARGE_OFF("个人请款核销"),
        ;

        private final String title;

        Type(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
}
