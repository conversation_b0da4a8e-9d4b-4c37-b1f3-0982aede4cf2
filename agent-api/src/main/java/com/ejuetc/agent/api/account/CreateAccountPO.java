package com.ejuetc.agent.api.account;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "添加账户参数")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public abstract class CreateAccountPO {

    @Schema(description = "所属用户ID")
    protected Long userId;

    public CreateAccountPO(Long userId) {
        this.userId = userId;
    }


}
