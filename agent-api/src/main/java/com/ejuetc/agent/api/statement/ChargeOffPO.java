package com.ejuetc.agent.api.statement;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "个人请款核销")
public class ChargeOffPO {

    @Schema(name = "核销单id")
    private Long oaFinanceId;

    @Schema(name = "结算单主键id")
    private List<Long> statementIds;

    @Schema(name = "供应商")
    private String companyName;

    @Schema(name = "供应商营业执照")
    private String companyNo;

    @Schema(name = "合同单据号")
    private String contractNo;

    @Schema(name = "结算账户")
    private String bankNo;

    @Schema(name = "结算书图片")
    private String statementUrl;

    @Schema(name = "发票图片")
    private String invoiceUrl;

    @Schema(name = "发票确认图片")
    private String invoiceConfirmUrl;

    @Schema(name = "发票确认税前金额")
    private BigDecimal invoicePreTax;

    @Schema(name = "发票确认税后金额")
    private BigDecimal invoiceAfterTax;

    @Schema(name = "预付款单号")
    private String prePayNo;

    @Schema(name = "预付款合同号")
    private String prePayContractNo;

    @Schema(name = "费用类型")
    private String feeType;

    @Schema(name = "已付金额")
    private BigDecimal payAmount;

    @Column(name = "未核销总金额")
    private BigDecimal unCancelAmount;

    @Schema(name = "本次核销金额")
    private BigDecimal cancelAmount;

    @Schema(name = "备注")
    private String remark;

    @Schema(name = "申请人")
    private String applyName;

    @Schema(name = "入账日期")
    private LocalDate chargeOffTime;
}
