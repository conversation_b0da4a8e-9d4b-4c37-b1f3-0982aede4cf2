package com.ejuetc.agent.api.price;

import com.ejuetc.agent.dto.DealDTO;
import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "创建定价参数")
public class CreatePricePO {

    @Schema(description = "渠道代码")
    private ChannelDTO.Code channelCode;

    @Schema(description = "业务代码")
    private BusinessOpenDTO.Code businessCode;

    @Schema(description = "城市名称")
    private String cityName;

    @Schema(description = "城市代码")
    private String cityCode;

    @Schema(description = "交易类型")
    private DealDTO.Type dealType;

    @Schema(description = "代理价")
    private BigDecimal agentPrice;

    @Schema(description = "标准售价")
    private BigDecimal standardPrice;

    @Schema(description = "代理折扣")
    private BigDecimal agentDiscount;

    @Schema(description = "代理折扣价(代理售价*代理折扣)")
    private BigDecimal agentDiscountPrice;

    @Schema(description = "正常售价返佣比例")
    private BigDecimal normalCommRate;

    @Schema(description = "超额售价返佣比例")
    private BigDecimal excessCommRatio;

}
