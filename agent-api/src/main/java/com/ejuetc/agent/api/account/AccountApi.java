package com.ejuetc.agent.api.account;

import com.ejuetc.agent.dto.AccountDTO;
import com.ejuetc.agent.pro.QuerySignContractRO;
import com.ejuetc.commons.base.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "(收款)账户接口")
@FeignClient(value = "ejuetc.agent", contextId = "AccountApi")
public interface AccountApi {

    @Operation(summary = "新建账号")
    @PostMapping("/account/add")
    ApiResponse<AccountDTO> add(@RequestBody CreateAccountPO po);

    @Operation(summary = "账号排序")
    @PostMapping("/account/sort")
    ApiResponse<List<AccountDTO>> sort(
            @Parameter(description = "变更用户ID") @RequestParam Long userId,
            @RequestBody List<Long> accountIds
    );

    @Operation(summary = "获取账号签约协议")
    @GetMapping("/account/querySignContract")
    ApiResponse<QuerySignContractRO> querySignContract();

    @Operation(summary = "用户解约")
    @GetMapping("/account/{id}/signRelease")
    ApiResponse<?> signRelease(@PathVariable("id") Long id);
}
