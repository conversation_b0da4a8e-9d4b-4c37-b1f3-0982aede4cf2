package com.ejuetc.agent.api.invitation;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "新建或换绑邀请码")
public class CreateOrRebindPO {
    @Schema(description = "发起交易用户ID")
    private Long userId;

    @Schema(description = "发起交易用户ID")
    private Long operatorId;

    @Schema(description = "邀请码")
    private String invitedCode;

}
