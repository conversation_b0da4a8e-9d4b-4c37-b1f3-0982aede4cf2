package com.ejuetc.agent.api.deal;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "上架房源参数")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public class MakeUpDelegationPo {

    private Long orderId;

    private LocalDateTime upTime;
}
