package com.ejuetc.agent.api.statement;

import com.ejuetc.agent.dto.StatementDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "结算单行为")
public class FlowActionPO {
    @Schema(description = "结算单主键")
    private Long statementId;

    @Schema(description = "操作人主键")
    private Long operatorId;

    @Schema(description = "操作人姓名")
    private String operatorName;

    @Schema(description = "操作描述")
    private String remark;

    @Schema(description = "操作角色")
    private StatementDTO.Role role;

    @Schema(description = "操作行为")
    private StatementDTO.Action action;

    @Schema(description = "操作参数")
    private String param;
}
