package com.ejuetc.agent.api.deal;

import com.ejuetc.agent.dto.DealDTO;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "创建线索购买交易参数")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public class CreateClueBuyDealPO extends RootDealPO {

    @Override
    public DealDTO.Type getType() {
        return DealDTO.Type.CLUE_BUY;
    }
}
