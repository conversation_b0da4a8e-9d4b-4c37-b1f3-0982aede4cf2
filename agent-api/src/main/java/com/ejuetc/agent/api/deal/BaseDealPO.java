package com.ejuetc.agent.api.deal;

import com.ejuetc.agent.dto.DealDTO;
import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.ejuetc.agent.dto.DealDTO.Category.*;
import static java.math.BigDecimal.ZERO;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "创建交易参数")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public abstract class BaseDealPO {

    @Schema(description = "来源端交易编号")
    private Long srcId;

    @Schema(description = "crm订单号")
    private String crmOrderNO;

    @Schema(description = "支付金额")
    private BigDecimal payAmount;

    @Schema(description = "支付时间")
    private LocalDateTime payTime;

    @Schema(description = "实际支付时间")
    private LocalDateTime factPayTime;

    @Schema(description = "支付审核时间")
    private LocalDateTime payAuditTime;

    @Schema(description = "天猫服务费转")
    private BigDecimal tmServiceFee = ZERO;

    @Schema(description = "天猫保证金转")
    private BigDecimal tmBail = ZERO;

    @Schema(description = "新交")
    private BigDecimal tmNewTrade = ZERO;

    @Schema(description = "天猫结转收入合同编号")
    private String tmIncomeContractNO;

    @Schema(description = "渠道代码")
    private ChannelDTO.Code channelCode;

    @Schema(description = "业务代码")
    private BusinessOpenDTO.Code businessCode;

    @Schema(description = "购买人发票批次号")
    private String invoiceBatchNO;

    @Schema(description = "交易类别")
    private DealDTO.Category category;

    @Schema(description = "交易有效开始时间")
    private LocalDateTime indateBegin;

    @Schema(description = "交易有效结束时间")
    private LocalDateTime indateEnd;

    @Schema(description = "是否是畅享包")
    private boolean enjoy = false;

    @Schema(description = "是否天猫结转")
    private Boolean tmForward = false;

    public abstract DealDTO.Type getType();

    public DealDTO.Category getCategory() {
        if (category == COMPANY_ENJOY) {
            return COMPANY_ENJOY;
        } else if (payAmount.compareTo(ZERO) == 0) {
            return WITHOUT_PAY;
        } else if (indateEnd != null && indateEnd.isBefore(LocalDateTime.now())) {
            return EXPIRE;
        } else if (tmForward == Boolean.TRUE || enjoy) {
            return FORWARD_ENJOY;
        } else {
            return NORMAL;
        }
    }
}
