package com.ejuetc.agent.api.contract;

import com.ejuetc.agent.dto.ContractDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "检查合同邀请码")
public class CheckInvitedCodePO {

    @Schema(description = "合同主体ID")
    private Long userId;

    @Schema(description = "合同类型")
    private ContractDTO.Type contractType;

    @Schema(description = "新的邀请码")
    private String invitedCode;

    @Schema(description = "城市代码(可选:适用于不同城市不同协议)", nullable = true)
    private String cityCode;

    @Schema(description = "是否更新合同")
    private boolean update = false;

    @Schema(description = "是否建立关联")
    private boolean relation = false;
}
