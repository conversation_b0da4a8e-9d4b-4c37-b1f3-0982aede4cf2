package com.ejuetc.agent.api.account;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "添加云账户账号参数")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public class CreateYzhAccountPO extends CreateAccountPO {
    @Schema(description = "银行账户名")
    private String accountName;
    @Schema(description = "身份证号")
    private String cardId;
    @Schema(description = "银行名")
    private String bankName;
    @Schema(description = "银行卡号")
    private String accountNO;
}
