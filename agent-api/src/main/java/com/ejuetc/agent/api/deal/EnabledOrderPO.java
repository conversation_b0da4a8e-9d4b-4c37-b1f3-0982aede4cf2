package com.ejuetc.agent.api.deal;

import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "已激活订单参数")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public class EnabledOrderPO {

    @Schema(description = "渠道码")
    private ChannelDTO.Code channelCode;

    @Schema(description = "业务码")
    private BusinessOpenDTO.Code businessCode;

    @Schema(description = "是否绑定")
    private Boolean rentBind;

    @Schema(description = "是否绑定")
    private Boolean saleBind;

    @Schema(description = "是否上架房源")
    private Boolean upDelegate;

    @Schema(description = "支持模糊搜索公司名称（企业经纪）、姓名（个人经纪）、订单编号")
    private String keyWord;

    @Schema(description = "合伙用户id")
    private Long partnerUserId;

    @Schema(description = "合伙邀请用户id")
    private Long partnerInvitationUserId;

    @Schema(description = "合同邀请操作用户id")
    private Long partnerInvitationOperatorUserId;

    @Schema(description = "代理用户id")
    private Long agentUserId;

    @Schema(description = "代理邀请用户id")
    private Long agentInvitationUserId;

    @Schema(description = "代理邀请操作用户id")
    private Long agentInvitationOperatorUserId;

    @Schema(description = "页数")
    private Integer page;

    @Schema(description = "分页大小")
    private Integer pageSize;
}
