package com.ejuetc.agent.api.contract;

import com.ejuetc.agent.dto.ContractDTO;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "创建经纪合同参数")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public class CreateBrokerContractPO extends CreateContractPO{

    @Override
    public ContractDTO.Type getType() {
        return ContractDTO.Type.BROKER;
    }
}
