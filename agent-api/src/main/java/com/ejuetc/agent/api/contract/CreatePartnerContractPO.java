package com.ejuetc.agent.api.contract;

import com.ejuetc.agent.dto.ContractDTO;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "创建合伙合同参数")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public class CreatePartnerContractPO extends CreateContractPO {
    @Schema(description = "佣金比例")
    private BigDecimal commissionRate;

    @Override
    public ContractDTO.Type getType() {
        return ContractDTO.Type.PARTNER;
    }
}
