package com.ejuetc.agent.api.deal;

import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "创建根交易参数")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public abstract class RootDealPO extends BaseDealPO {

    @Schema(description = "城市代码")
    private String cityCode;

    @Schema(description = "城市名称")
    private String cityName;

    @Schema(description = "邀请码")
    private String invitedCode;

    @Schema(description = "发起交易用户ID")
    private Long userId;

    @Schema(description = "发起交易用户ID")
    private Long operatorId;

    @Schema(description = "子交易")
    protected Integer childQuantity;

}
