package com.ejuetc.agent.api.contract;

import com.ejuetc.agent.dto.ContractDTO;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "创建合同参数")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public abstract class CreateContractPO {
    @Schema(description = "用户编号")
    private Long userId;

    @Schema(description = "操作者ID")
    private Long operatorId;

    @Schema(description = "邀请码")
    private String invitationCode;

    @Schema(description = "城市代码")
    private String cityCode;

    @Schema(description = "城市名称")
    private String cityName;

    @Schema(description = "二级分类")
    private ContractDTO.SubType subType;

    @Schema(description = "是否未补录")
    private Boolean suppFlag = false;

    @Schema(description = "开始时间")
    private LocalDateTime beginTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;


    public abstract ContractDTO.Type getType();

}
