package com.ejuetc.agent.api.contract;

import com.ejuetc.agent.dto.StatementDTO;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.agent.dto.ContractDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "签约接口")
@FeignClient(value = "ejuetc.agent", contextId = "ContractApi")
public interface ContractApi {

    @Operation(summary = "新建签约")
    @PostMapping("/contract/create")
    ApiResponse<ContractDTO> create(@RequestBody CreateContractPO po);

    @Operation(summary = "变更经纪机构邀请码")
    @PostMapping("/contract/checkBrokerInvitedCode")
    ApiResponse<String> checkInvitedCode(@RequestBody CheckInvitedCodePO po);

    @Operation(summary = "经纪机构签约结果")
    @PostMapping("/contract/agentSignResult")
    ApiResponse<?> agentSignResult(
            @Parameter(description = "合同ID") @RequestParam Long contractId,
            @Parameter(description = "签约结果") @RequestParam Boolean signResult,
            @Parameter(description = "描述信息") @RequestParam String description,
            @Parameter(description = "合同文件URL") @RequestParam String contractFile
    );

    @Operation(summary = "查询邀请列表")
    @PostMapping("/contract/findInvited")
    ApiResponse<List<ContractDTO>> findInvitedBrokerContracts(@RequestBody FindInvitedBrokerContractsPO po);


    @Operation(summary = "oa审核结果")
    @PostMapping("/contract/contractCrmResult")
    ApiResponse<?> contractCrmResult(@RequestBody CrmResultPo po);

    @Operation(summary = "补发crm合同")
    @PostMapping("/contract/sendCrmContractByIds")
    ApiResponse<?> sendCrmContractByIds(@RequestBody SendCrmContractByIdsPO po);

    @Operation(summary = "查询独家代理邀请码")
    @GetMapping("/contract/findInvitationCodeByExclusive")
    ApiResponse<String> findInvitationCodeByExclusive(@Parameter(description = "cityCode") @RequestParam String cityCode);

    @Operation(summary = "发起补充协议")
    @PostMapping("/contract/launchSupplESign")
    ApiResponse<?> launchSupplESign(
            @Parameter(description = "合同主键") @RequestParam List<Long> contractIds,
            @Parameter(description = "合同模板") @RequestParam Long templateId
    );

    @Operation(summary = "撤销电子合同")
    @PostMapping("/contract/esignCancel")
    ApiResponse<?> esignCancel(
            @Parameter(description = "电子签主键") @RequestParam Long signId,
            @Parameter(description = "撤销角色") @RequestParam StatementDTO.Role role,
            @Parameter(description = "撤销原因") @RequestParam String reason
    );
}
