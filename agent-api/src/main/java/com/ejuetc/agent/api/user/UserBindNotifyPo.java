package com.ejuetc.agent.api.user;

import com.ejuetc.channel.dto.BusinessOpenDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "账号绑定通知信息")
public class UserBindNotifyPo {

    @Schema(description = "用户编号")
    private Long userId;

    @Schema(description = "绑定状态")
    private Boolean bind;

    @Schema(description = "业务")
    private BusinessOpenDTO.Code businessCode;
}
