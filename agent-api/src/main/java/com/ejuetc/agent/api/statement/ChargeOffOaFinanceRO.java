package com.ejuetc.agent.api.statement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "个人请款")
public class ChargeOffOaFinanceRO {
    @Schema(description = "统计结果")
    private List<StatementPartnerAmountRO> statementPartnerAmountROList;

    @Schema(description = "oaId")
    private Long oaFinanceId;
}
