package com.ejuetc.agent.api.contract;

import com.ejuetc.agent.dto.ContractDTO;
import com.ejuetc.agent.api.price.CreatePricePO;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "创建代理参数")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public class CreateAgentContractPO extends CreateContractPO {
    @Schema(description = "新增定价列表")
    private List<CreatePricePO> pricePOS;


    @Override
    public ContractDTO.Type getType() {
        return ContractDTO.Type.AGENT;
    }
}
