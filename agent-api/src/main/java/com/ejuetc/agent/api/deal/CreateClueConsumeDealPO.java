package com.ejuetc.agent.api.deal;

import com.ejuetc.agent.dto.DealDTO;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "创建线索消耗交易参数")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public class CreateClueConsumeDealPO extends BaseDealPO {

    @Schema(description = "购买线索订单号")
    private Long parentSrcId;

    @Schema(description = "线索名称")
    private String goodsName;

    @Schema(description = "订单号")
    private String orderNum;

    @Override
    public DealDTO.Type getType() {
        return DealDTO.Type.CLUE_CONSUME;
    }
}
