package com.ejuetc.agent.api.account;

import com.ejuetc.agent.api.user.EditUserPO;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

@Setter
@Getter
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "添加银行账号参数")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public class CreateBankAccountPO extends CreateAccountPO {

    @Schema(description = "账号")
    protected String accountNO;

    @Schema(description = "开户行所属省份")
    private String bankProvince;

    @Schema(description = "开户行所属城市")
    private String bankCity;

    @Schema(description = "开户行")
    private String bankAddress;

    @Schema(description = "银行名")
    private String bankName;

    public CreateBankAccountPO(EditUserPO po) {
        super(po.getId());
        this.accountNO = po.getBankAccount();
        this.bankProvince = po.getBankProvince();
        this.bankCity = po.getBankCity();
        this.bankAddress = po.getBankAddress();
        this.bankName = po.getBankName();
    }

}
