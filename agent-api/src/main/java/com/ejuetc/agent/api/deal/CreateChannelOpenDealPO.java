package com.ejuetc.agent.api.deal;

import com.ejuetc.agent.dto.DealDTO;
import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

import static java.math.BigDecimal.ZERO;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "创建渠道开通交易参数")
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public class CreateChannelOpenDealPO extends RootDealPO {

    @QueryField
    @Schema(description = "商品外键")
    private Long goodsId;

    @QueryField
    @Schema(description = "商品名称")
    private String goodsName;

    @QueryField
    @Schema(description = "父订单外键 (mall模块 父订单id)")
    private Long parentOrderId;

    @Schema(description = "mall订单号 (mall模块 子订单号)")
    private String orderNum;

    @Schema(description = "mall父订单号  (mall模块 父订单号)")
    private String parentOrderNum;

    @Schema(description = "子订单id (mall模块 子订单id)")
    private Long orderId;

    @Schema(description = "天猫服务费转")
    private BigDecimal tmServiceFee = ZERO;

    @Schema(description = "天猫保证金转")
    private BigDecimal tmBail = ZERO;

    @Schema(description = "新交")
    private BigDecimal tmNewTrade = ZERO;

    @Schema(description = "天猫结转收入合同编号")
    private String tmIncomeContractNO;

    @Override
    public DealDTO.Type getType() {
        return DealDTO.Type.CHANNEL_OPEN;
    }
}
