package com.ejuetc.agent.api.statement;

import com.ejuetc.agent.dto.PersonalPayDTO;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class WithdrawImportDetailPO {
    private Long id;

    private Long batchId;

    private Integer rowNo;

    private String batchNo;

    private String platformOrderNo;

    private String platformEnterpriseOrderNo;

    private Date createOrderTime;

    private Date orderCompleteTime;

    private String collectionAccountNo;

    private String certificateNo;

    private String collectionAccountName;

    private String bankReservedMobileNo;

    private String integratedServiceSubject;

    private String platformEnterpriseName;

    private String paymentBasicServiceFeeAmount;

    private String userReceivedAmount;

    private String bonusServiceFeeReceivedAmount;

    private String userBonusServiceFee;

    private String orderStatus;

    private String statusDescription;

    private String remark;

    private String paymentPath;

    private BigDecimal userBonusServiceRate;

    private BigDecimal platformEnterpriseBonusServiceRate;

    private String deductedBonusServiceFeeAmount;

    private Date refundTime;

    private String refundStatus;

    private String refundedUserReceivedAmount;

    private String refundedBonusServiceFeeAmount;

    private String refundedUserBonusServiceFee;

    private String collectionBankName;

    private PersonalPayDTO.Status status;

    private String error;
}
