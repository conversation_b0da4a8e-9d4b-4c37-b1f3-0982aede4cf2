package com.ejuetc.agent.api.statement;

import com.ejuetc.agent.dto.*;
import com.ejuetc.commons.base.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Tag(name = "邀请接口")
@FeignClient(value = "ejuetc.agent", contextId = "StatementApi")
public interface StatementApi {

    @Operation(summary = "插入结算单")
    @PostMapping("/statement/insertStatement")
    ApiResponse<String> insertStatement();

    @Operation(summary = "结算单推进流行为")
    @PostMapping("/statement/flowAction")
    ApiResponse<StatementDTO> flowAction(@RequestBody FlowActionPO po);

    @Operation(summary = "生成个人打款EXCEL")
    @PostMapping("/statement/withdraw")
    ApiResponse<WithdrawDTO> withdraw(@RequestBody WithdrawPO po);

    @Operation(summary = "获取结算单考核主体")
    @PostMapping("/statement/selKhInfo")
    ApiResponse<ChargeOffOaFinanceRO> selKhInfo(@RequestBody ChargeOffPO po);

    @Operation(summary = "获取支付完成未发起oa的个人结算单")
    @PostMapping("/statement/selPersonalPayDone")
    ApiResponse<List<StatementDTO>> selPersonalPayDone(@RequestBody SelPersonalPayDonePO po);

    @Operation(summary = "个人请款核销")
    @PostMapping("/statement/chargeOff")
    ApiResponse<OaFinanceDTO> chargeOff(@RequestBody ChargeOffPO po);

    @Operation(summary = "导入个人付款记录")
    @PostMapping("/statement/importPensonalPay")
    ApiResponse<Long> importPensonalPay(@RequestBody WithdrawImportPO po);

    @Operation(summary = "推送crm")
    @GetMapping("/statement/sendCrm")
    ApiResponse<OaFinanceDTO> sendCrm(@Parameter(description = "变更用户ID") @RequestParam Long oaFinanceId);

    @Operation(summary = "订单待签约提醒")
    @GetMapping("/statement/sendWxMessage")
    void sendWxMessage();

    @Operation(summary = "执行发票")
    @GetMapping("/statement/execInvoice")
    ApiResponse<StatementInvoiceDTO> execInvoice(@RequestParam Long invoiceId);
}
