package com.ejuetc.agent.api.contract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class FindInvitedBrokerContractsPO {
    @Schema(description = "代理主体ID")
    private Long agentUserId;
    @Schema(description = "代理操作员ID")
    private Long agentOperatorId;
    @Schema(description = "经纪主体名称")
    private String brokerName;
    @Schema(description = "页码")
    private int pageNum;
    @Schema(description = "页行数")
    private int pageSize;
}
