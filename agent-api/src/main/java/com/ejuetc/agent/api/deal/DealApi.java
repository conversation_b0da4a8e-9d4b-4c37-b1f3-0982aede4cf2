package com.ejuetc.agent.api.deal;

import com.ejuetc.agent.dto.DealDTO;
import com.ejuetc.commons.base.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Tag(name = "交易接口")
@FeignClient(value = "ejuetc.agent", contextId = "DealApi")
public interface DealApi {

    @Operation(summary = "新建交易")
    @PostMapping("/deal/create")
    ApiResponse<DealDTO> edit(@RequestBody BaseDealPO po);

    @Operation(summary = "标记(分配)订单首次上架房源")
    @PostMapping("/deal/makeFirstUpDelegate")
    ApiResponse<?> makeUpDelegate(@RequestBody MakeUpDelegationPo po);

    @Operation(summary = "触发配置任务")
    @PostMapping("/deal/config")
    ApiResponse<Integer> config();

    @Operation(summary = "触发清分任务")
    @PostMapping("/deal/clarity")
    int clearing();

    @Operation(summary = "查询已激活的订单")
    @PostMapping("/deal/selOrderByEnabled")
    ApiResponse<List<DealDTO>> selOrderByEnabled(@RequestBody EnabledOrderPO po);
}
