package com.ejuetc.agent.api.statement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "统计结果")
public class StatementPartnerAmountRO {

    @Schema(description = "考核主体编码")
    private String khCode;

    @Schema(description = "考核主体名称")
    private String khName;

    @Schema(description = "成本中心编码")
    private String costCode;

    @Schema(description = "成本中心名称")
    private String costName;

    @Schema(description = "税前金额")
    private BigDecimal reportPreTax;

    @Schema(description = "税后金额")
    private BigDecimal reportAfterTax;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "未调差税前金额")
    private BigDecimal reportPreTaxNoFact;

    @Schema(description = "未调差税后金额")
    private BigDecimal reportAfterTaxNoFact;

    public StatementPartnerAmountRO(String khName, String khCode, String costCode, String costName, BigDecimal reportPreTax, BigDecimal reportAfterTax, Long userId, BigDecimal reportPreTaxNoFact, BigDecimal reportAfterTaxNoFact) {
        this.khCode = khCode;
        this.khName = khName;
        this.costCode = costCode;
        this.costName = costName;
        this.reportPreTax = reportPreTax;
        this.reportAfterTax = reportAfterTax;
        this.userId = userId;
        this.reportPreTaxNoFact = reportPreTaxNoFact;
        this.reportAfterTaxNoFact = reportAfterTaxNoFact;
    }
}
