package com.ejuetc.agent.api.statement;

import com.ejuetc.agent.dto.StatementDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "个人打款EXCEL")
public class PersonalPayExcelPO {
    @Schema(description = "结算单主键")
    private List<String> statementBatch;

    @Schema(description = "操作人主键")
    private List<String> accountNO;

}
