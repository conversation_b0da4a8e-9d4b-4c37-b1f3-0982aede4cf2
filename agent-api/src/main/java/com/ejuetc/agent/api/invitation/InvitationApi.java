package com.ejuetc.agent.api.invitation;

import com.ejuetc.agent.dto.InvitationDTO;
import com.ejuetc.agent.pro.CheckCodeRO;
import com.ejuetc.commons.base.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@Tag(name = "邀请接口")
@FeignClient(value = "ejuetc.agent", contextId = "InvitationApi")
public interface InvitationApi {


    @Operation(summary = "新建或换绑邀请码")
    @PostMapping("/invitation/createOrRebind")
    ApiResponse<InvitationDTO> createOrRebind(@RequestBody CreateOrRebindPO po);

    @Operation(summary = "校验邀请码")
    @GetMapping("/invitation/checkInviteCode")
    ApiResponse<Boolean> checkCode4Api(@Parameter(description = "inviteCode") @RequestParam String inviteCode, @Parameter(description = "cityCode") @RequestParam String cityCode);

    @Operation(summary = "查询邀请码")
    @GetMapping("/rpc/invitation/checkInviteCode")
    ApiResponse<CheckCodeRO> checkCode4Rpc(@Parameter(description = "inviteCode") @RequestParam String inviteCode, @Parameter(description = "cityCode") @RequestParam String cityCode);
}
