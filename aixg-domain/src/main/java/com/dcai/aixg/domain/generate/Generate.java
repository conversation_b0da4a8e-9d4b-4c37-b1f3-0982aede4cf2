package com.dcai.aixg.domain.generate;

import com.dcai.aixg.pro.generate.CreateGenerateByCodePO;
import com.dcai.aixg.pro.generate.CreateGeneratePO;
import com.ejuetc.commons.base.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 生成任务实体
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@NoArgsConstructor
@Comment("生成任务")
@Table(name = "tb_generate")
@Where(clause = "logic_delete = 0")
public class Generate extends BaseEntity<Generate> {

    @Id
    @Comment("任务ID")
    @GeneratedValue(generator = "generate_id")
    @SequenceGenerator(name = "generate_id", sequenceName = "seq_generate_id")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "config_id", columnDefinition = "bigint(20) COMMENT '配置ID'", nullable = false)
    private GenerateConfig config;

    @Column(name = "request", columnDefinition = "text COMMENT '请求内容'", nullable = false)
    private String request;

    @Column(name = "system_prompt", columnDefinition = "text COMMENT '系统提示'", nullable = false)
    private String systemPrompt;

    @Column(name = "user_prompt", columnDefinition = "text COMMENT '用户提示'", nullable = false)
    private String userPrompt;

    @Column(name = "response", columnDefinition = "longtext COMMENT '响应内容'")
    private String response;

    @Column(name = "duration", columnDefinition = "bigint COMMENT '执行时长(毫秒)'")
    private Long duration;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", columnDefinition = "varchar(50) COMMENT '状态'", nullable = false)
    private GenerateStatus status;

    @Column(name = "memo", columnDefinition = "text COMMENT '备忘录'")
    private String memo;

    @Column(name = "error_message", columnDefinition = "text COMMENT '错误信息'")
    private String errorMessage;

    @Column(name = "started_at", columnDefinition = "datetime COMMENT '开始时间'")
    private LocalDateTime startedAt;

    @Column(name = "completed_at", columnDefinition = "datetime COMMENT '完成时间'")
    private LocalDateTime completedAt;

    @OneToMany(mappedBy = "generate", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<GenerateStep> steps = new ArrayList<>();

    /**
     * 生成状态枚举
     */
    public enum GenerateStatus {
        PENDING("待处理"),
        PROCESSING("处理中"),
        SUCCESS("成功"),
        FAILED("失败"),
        CANCELLED("已取消");

        private final String description;

        GenerateStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 构造函数
     */
    public Generate(GenerateConfig config, String request, String userPrompt) {
        this.config = config;
        this.request = request;
        this.userPrompt = userPrompt;
        this.systemPrompt = buildSystemPrompt();
        this.status = GenerateStatus.PENDING;
    }

    /**
     * 构建系统提示
     */
    public String buildSystemPrompt() {
        if (config == null || config.getSystemTemplate() == null) {
            return "";
        }
        // 这里可以根据需要进行模板变量替换
        return config.getSystemTemplate();
    }

    /**
     * 构建用户提示
     */
    public String buildUserPrompt() {
        if (config == null || config.getUserTemplate() == null) {
            return userPrompt;
        }
        // 这里可以根据需要进行模板变量替换，比如将{request}替换为实际请求内容
        return config.getUserTemplate().replace("{request}", request);
    }

    /**
     * 开始处理
     */
    public void startProcessing() {
        this.status = GenerateStatus.PROCESSING;
        this.startedAt = LocalDateTime.now();
    }

    /**
     * 处理成功
     */
    public void completeSuccess(String response) {
        this.status = GenerateStatus.SUCCESS;
        this.response = response;
        this.completedAt = LocalDateTime.now();
        if (startedAt != null) {
            this.duration = java.time.Duration.between(startedAt, completedAt).toMillis();
        }
    }

    /**
     * 处理失败
     */
    public void completeFailed(String errorMessage) {
        this.status = GenerateStatus.FAILED;
        this.errorMessage = errorMessage;
        this.completedAt = LocalDateTime.now();
        if (startedAt != null) {
            this.duration = java.time.Duration.between(startedAt, completedAt).toMillis();
        }
    }

    /**
     * 取消处理
     */
    public void cancel() {
        this.status = GenerateStatus.CANCELLED;
        this.completedAt = LocalDateTime.now();
        if (startedAt != null) {
            this.duration = java.time.Duration.between(startedAt, completedAt).toMillis();
        }
    }

    /**
     * 添加步骤
     */
    public void addStep(GenerateStep step) {
        step.setGenerate(this);
        this.steps.add(step);
    }

    /**
     * 设置备忘录
     */
    public void setMemo(String memo) {
        this.memo = memo;
    }

    /**
     * 是否完成
     */
    public boolean isCompleted() {
        return status == GenerateStatus.SUCCESS || 
               status == GenerateStatus.FAILED || 
               status == GenerateStatus.CANCELLED;
    }

    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return status == GenerateStatus.SUCCESS;
    }
}
