package com.dcai.aixg.domain.generate;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Comment;

/**
 * 函数调用实体，继承自GenerateStep
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@NoArgsConstructor
@DiscriminatorValue("FUNCTION_CALLING")
@Comment("函数调用步骤")
public class FunctionCalling extends GenerateStep {

    @Column(name = "function_name", columnDefinition = "varchar(100) COMMENT '函数名称'")
    private String functionName;

    @Column(name = "function_args", columnDefinition = "text COMMENT '函数参数(JSON格式)'")
    private String functionArgs;

    @Column(name = "function_result", columnDefinition = "longtext COMMENT '函数执行结果'")
    private String functionResult;

    @Enumerated(EnumType.STRING)
    @Column(name = "function_status", columnDefinition = "varchar(50) COMMENT '函数执行状态'")
    private FunctionStatus functionStatus;

    @Column(name = "retry_count", columnDefinition = "int DEFAULT 0 COMMENT '重试次数'")
    private Integer retryCount = 0;

    @Column(name = "max_retry", columnDefinition = "int DEFAULT 3 COMMENT '最大重试次数'")
    private Integer maxRetry = 3;

    /**
     * 函数执行状态枚举
     */
    public enum FunctionStatus {
        PENDING("待执行"),
        EXECUTING("执行中"),
        SUCCESS("执行成功"),
        FAILED("执行失败"),
        TIMEOUT("执行超时"),
        RETRY("重试中");

        private final String description;

        FunctionStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 构造函数
     */
    public FunctionCalling(String title, String functionName, String functionArgs) {
        super(title, functionArgs);
        this.functionName = functionName;
        this.functionArgs = functionArgs;
        this.functionStatus = FunctionStatus.PENDING;
        this.retryCount = 0;
        this.maxRetry = 3;
    }

    /**
     * 构造函数
     */
    public FunctionCalling(String title, String functionName, String functionArgs, Integer stepOrder) {
        super(title, functionArgs, stepOrder);
        this.functionName = functionName;
        this.functionArgs = functionArgs;
        this.functionStatus = FunctionStatus.PENDING;
        this.retryCount = 0;
        this.maxRetry = 3;
    }

    /**
     * 开始执行函数
     */
    public void startFunctionExecution() {
        this.functionStatus = FunctionStatus.EXECUTING;
        startProcessing();
    }

    /**
     * 函数执行成功
     */
    public void completeFunctionSuccess(String functionResult, String response) {
        this.functionStatus = FunctionStatus.SUCCESS;
        this.functionResult = functionResult;
        completeSuccess(response);
    }

    /**
     * 函数执行失败
     */
    public void completeFunctionFailed(String errorMessage) {
        this.functionStatus = FunctionStatus.FAILED;
        completeFailed(errorMessage);
    }

    /**
     * 函数执行超时
     */
    public void functionTimeout(String timeoutMessage) {
        this.functionStatus = FunctionStatus.TIMEOUT;
        completeFailed(timeoutMessage);
    }

    /**
     * 重试函数执行
     */
    public boolean retryFunction() {
        if (retryCount < maxRetry) {
            this.retryCount++;
            this.functionStatus = FunctionStatus.RETRY;
            return true;
        }
        return false;
    }



    /**
     * 设置最大重试次数
     */
    public void setMaxRetry(Integer maxRetry) {
        this.maxRetry = maxRetry != null ? maxRetry : 3;
    }

    /**
     * 获取函数调用信息
     */
    public String getFunctionCallInfo() {
        return String.format("Function: %s, Args: %s, Status: %s, Retry: %d/%d", 
                           functionName, functionArgs, functionStatus, retryCount, maxRetry);
    }

    /**
     * 是否函数执行成功
     */
    public boolean isFunctionSuccess() {
        return functionStatus == FunctionStatus.SUCCESS;
    }

    /**
     * 是否函数执行失败
     */
    public boolean isFunctionFailed() {
        return functionStatus == FunctionStatus.FAILED || functionStatus == FunctionStatus.TIMEOUT;
    }

    /**
     * 是否可以重试
     */
    public boolean canRetry() {
        return isFunctionFailed() && retryCount < maxRetry;
    }
}
