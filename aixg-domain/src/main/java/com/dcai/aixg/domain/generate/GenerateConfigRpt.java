package com.dcai.aixg.domain.generate;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 生成配置Repository
 * 
 * <AUTHOR>
 */
@Repository
public interface GenerateConfigRpt extends JpaRepository<GenerateConfig, Long> {

    /**
     * 根据代码查找配置
     */
    Optional<GenerateConfig> findByCode(String code);

    /**
     * 根据代码和启用状态查找配置
     */
    Optional<GenerateConfig> findByCodeAndEnabled(String code, Boolean enabled);

    /**
     * 查找所有启用的配置
     */
    List<GenerateConfig> findByEnabledOrderBySortOrderAsc(Boolean enabled);

    /**
     * 根据任务类型查找配置
     */
    List<GenerateConfig> findByTaskTypeAndEnabledOrderBySortOrderAsc(GenerateConfig.TaskType taskType, Boolean enabled);

    /**
     * 根据标题模糊查询
     */
    @Query("SELECT gc FROM GenerateConfig gc WHERE gc.title LIKE %:title% AND gc.enabled = :enabled ORDER BY gc.sortOrder ASC")
    List<GenerateConfig> findByTitleContainingAndEnabled(@Param("title") String title, @Param("enabled") Boolean enabled);

    /**
     * 检查代码是否存在
     */
    boolean existsByCode(String code);

    /**
     * 检查代码是否存在（排除指定ID）
     */
    @Query("SELECT COUNT(gc) > 0 FROM GenerateConfig gc WHERE gc.code = :code AND gc.id != :id")
    boolean existsByCodeAndIdNot(@Param("code") String code, @Param("id") Long id);

    /**
     * 获取最大排序值
     */
    @Query("SELECT COALESCE(MAX(gc.sortOrder), 0) FROM GenerateConfig gc")
    Integer findMaxSortOrder();

    /**
     * 根据任务类型统计数量
     */
    @Query("SELECT COUNT(gc) FROM GenerateConfig gc WHERE gc.taskType = :taskType AND gc.enabled = :enabled")
    Long countByTaskTypeAndEnabled(@Param("taskType") GenerateConfig.TaskType taskType, @Param("enabled") Boolean enabled);
}
