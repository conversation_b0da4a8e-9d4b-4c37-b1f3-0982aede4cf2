package com.dcai.aixg.domain.generate;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 生成步骤Repository
 * 
 * <AUTHOR>
 */
@Repository
public interface GenerateStepRpt extends JpaRepository<GenerateStep, Long> {

    /**
     * 根据生成任务ID查找步骤
     */
    List<GenerateStep> findByGenerateIdOrderByStepOrderAsc(Long generateId);

    /**
     * 根据生成任务ID和状态查找步骤
     */
    List<GenerateStep> findByGenerateIdAndStatusOrderByStepOrderAsc(Long generateId, GenerateStep.StepStatus status);

    /**
     * 根据状态查找步骤
     */
    List<GenerateStep> findByStatusOrderByCreateTimeDesc(GenerateStep.StepStatus status);

    /**
     * 查找指定任务的函数调用步骤
     */
    @Query("SELECT fs FROM FunctionCalling fs WHERE fs.generate.id = :generateId ORDER BY fs.stepOrder ASC")
    List<FunctionCalling> findFunctionCallingByGenerateId(@Param("generateId") Long generateId);

    /**
     * 查找所有函数调用步骤
     */
    @Query("SELECT fs FROM FunctionCalling fs ORDER BY fs.createTime DESC")
    List<FunctionCalling> findAllFunctionCalling();

    /**
     * 根据函数名查找函数调用步骤
     */
    @Query("SELECT fs FROM FunctionCalling fs WHERE fs.functionName = :functionName ORDER BY fs.createTime DESC")
    List<FunctionCalling> findByFunctionName(@Param("functionName") String functionName);

    /**
     * 根据函数状态查找函数调用步骤
     */
    @Query("SELECT fs FROM FunctionCalling fs WHERE fs.functionStatus = :functionStatus ORDER BY fs.createTime DESC")
    List<FunctionCalling> findByFunctionStatus(@Param("functionStatus") FunctionCalling.FunctionStatus functionStatus);

    /**
     * 统计各状态的步骤数量
     */
    @Query("SELECT gs.status, COUNT(gs) FROM GenerateStep gs GROUP BY gs.status")
    List<Object[]> countByStatus();

    /**
     * 统计指定任务的各状态步骤数量
     */
    @Query("SELECT gs.status, COUNT(gs) FROM GenerateStep gs WHERE gs.generate.id = :generateId GROUP BY gs.status")
    List<Object[]> countByStatusAndGenerateId(@Param("generateId") Long generateId);

    /**
     * 查找处理中的步骤
     */
    @Query("SELECT gs FROM GenerateStep gs WHERE gs.status = 'PROCESSING' AND gs.startedAt < :timeoutTime ORDER BY gs.startedAt ASC")
    List<GenerateStep> findTimeoutProcessingSteps(@Param("timeoutTime") LocalDateTime timeoutTime);

    /**
     * 统计函数调用成功率
     */
    @Query("SELECT " +
           "SUM(CASE WHEN fs.functionStatus = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(fs) " +
           "FROM FunctionCalling fs")
    Double calculateFunctionSuccessRate();

    /**
     * 根据函数名统计成功率
     */
    @Query("SELECT " +
           "SUM(CASE WHEN fs.functionStatus = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(fs) " +
           "FROM FunctionCalling fs WHERE fs.functionName = :functionName")
    Double calculateFunctionSuccessRateByName(@Param("functionName") String functionName);

    /**
     * 统计平均步骤执行时长
     */
    @Query("SELECT AVG(gs.duration) FROM GenerateStep gs WHERE gs.status = 'SUCCESS' AND gs.duration IS NOT NULL")
    Double findAverageStepDuration();

    /**
     * 统计函数调用平均执行时长
     */
    @Query("SELECT AVG(fs.duration) FROM FunctionCalling fs WHERE fs.functionStatus = 'SUCCESS' AND fs.duration IS NOT NULL")
    Double findAverageFunctionDuration();

    /**
     * 查找需要重试的函数调用
     */
    @Query("SELECT fs FROM FunctionCalling fs WHERE fs.functionStatus IN ('FAILED', 'TIMEOUT') AND fs.retryCount < fs.maxRetry ORDER BY fs.createTime ASC")
    List<FunctionCalling> findRetryableFunctionCalls();

    /**
     * 根据标题模糊查询步骤
     */
    @Query("SELECT gs FROM GenerateStep gs WHERE gs.title LIKE %:keyword% ORDER BY gs.createTime DESC")
    List<GenerateStep> findByTitleContaining(@Param("keyword") String keyword);
}
