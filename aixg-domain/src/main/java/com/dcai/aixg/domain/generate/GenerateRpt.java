package com.dcai.aixg.domain.generate;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 生成任务Repository
 * 
 * <AUTHOR>
 */
@Repository
public interface GenerateRpt extends JpaRepository<Generate, Long> {

    /**
     * 根据配置ID查找任务
     */
    List<Generate> findByConfigIdOrderByCreateTimeDesc(Long configId);

    /**
     * 根据状态查找任务
     */
    List<Generate> findByStatusOrderByCreateTimeDesc(Generate.GenerateStatus status);

    /**
     * 根据配置ID和状态查找任务
     */
    List<Generate> findByConfigIdAndStatusOrderByCreateTimeDesc(Long configId, Generate.GenerateStatus status);

    /**
     * 分页查询任务
     */
    Page<Generate> findByOrderByCreateTimeDesc(Pageable pageable);

    /**
     * 根据配置ID分页查询任务
     */
    Page<Generate> findByConfigIdOrderByCreateTimeDesc(Long configId, Pageable pageable);

    /**
     * 根据状态分页查询任务
     */
    Page<Generate> findByStatusOrderByCreateTimeDesc(Generate.GenerateStatus status, Pageable pageable);

    /**
     * 根据时间范围查询任务
     */
    @Query("SELECT g FROM Generate g WHERE g.createTime >= :startTime AND g.createTime <= :endTime ORDER BY g.createTime DESC")
    List<Generate> findByCreateTimeBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 根据配置代码查询任务
     */
    @Query("SELECT g FROM Generate g WHERE g.config.code = :configCode ORDER BY g.createTime DESC")
    List<Generate> findByConfigCode(@Param("configCode") String configCode);

    /**
     * 根据配置代码和状态查询任务
     */
    @Query("SELECT g FROM Generate g WHERE g.config.code = :configCode AND g.status = :status ORDER BY g.createTime DESC")
    List<Generate> findByConfigCodeAndStatus(@Param("configCode") String configCode, @Param("status") Generate.GenerateStatus status);

    /**
     * 统计各状态的任务数量
     */
    @Query("SELECT g.status, COUNT(g) FROM Generate g GROUP BY g.status")
    List<Object[]> countByStatus();

    /**
     * 统计指定配置的各状态任务数量
     */
    @Query("SELECT g.status, COUNT(g) FROM Generate g WHERE g.config.id = :configId GROUP BY g.status")
    List<Object[]> countByStatusAndConfigId(@Param("configId") Long configId);

    /**
     * 查找处理中的任务
     */
    @Query("SELECT g FROM Generate g WHERE g.status = 'PROCESSING' AND g.startedAt < :timeoutTime ORDER BY g.startedAt ASC")
    List<Generate> findTimeoutProcessingTasks(@Param("timeoutTime") LocalDateTime timeoutTime);

    /**
     * 统计平均处理时长
     */
    @Query("SELECT AVG(g.duration) FROM Generate g WHERE g.status = 'SUCCESS' AND g.duration IS NOT NULL")
    Double findAverageSuccessDuration();

    /**
     * 统计指定配置的平均处理时长
     */
    @Query("SELECT AVG(g.duration) FROM Generate g WHERE g.config.id = :configId AND g.status = 'SUCCESS' AND g.duration IS NOT NULL")
    Double findAverageSuccessDurationByConfigId(@Param("configId") Long configId);

    /**
     * 查找最近的成功任务
     */
    @Query("SELECT g FROM Generate g WHERE g.status = 'SUCCESS' ORDER BY g.completedAt DESC")
    List<Generate> findRecentSuccessTasks(Pageable pageable);

    /**
     * 根据请求内容模糊查询
     */
    @Query("SELECT g FROM Generate g WHERE g.request LIKE %:keyword% ORDER BY g.createTime DESC")
    List<Generate> findByRequestContaining(@Param("keyword") String keyword);
}
