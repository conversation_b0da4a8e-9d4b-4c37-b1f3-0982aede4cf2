package com.dcai.aixg.domain.generate;

import com.ejuetc.commons.base.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

/**
 * 生成配置实体
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@NoArgsConstructor
@Comment("生成配置")
@Table(name = "tb_generate_config")
@Where(clause = "logic_delete = 0")
public class GenerateConfig extends BaseEntity<GenerateConfig> {

    @Id
    @Comment("配置ID")
    @GeneratedValue(generator = "generate_config_id")
    @SequenceGenerator(name = "generate_config_id", sequenceName = "seq_generate_config_id")
    private Long id;

    @Column(name = "code", columnDefinition = "varchar(100) COMMENT '配置代码'", nullable = false, unique = true)
    private String code;

    @Column(name = "title", columnDefinition = "varchar(200) COMMENT '配置标题'", nullable = false)
    private String title;

    @Column(name = "description", columnDefinition = "text COMMENT '配置描述'")
    private String description;

    @Column(name = "demo", columnDefinition = "text COMMENT '演示内容'")
    private String demo;

    @Column(name = "system_template", columnDefinition = "text COMMENT '系统模板'", nullable = false)
    private String systemTemplate;

    @Column(name = "user_template", columnDefinition = "text COMMENT '用户模板'", nullable = false)
    private String userTemplate;

    @Enumerated(EnumType.STRING)
    @Column(name = "task_type", columnDefinition = "varchar(50) COMMENT '任务类型'", nullable = false)
    private GenerateType generateType;

    @Column(name = "enabled", columnDefinition = "tinyint(1) DEFAULT 1 COMMENT '是否启用'", nullable = false)
    private Boolean enabled = true;

    @Column(name = "sort_order", columnDefinition = "int DEFAULT 0 COMMENT '排序'", nullable = false)
    private Integer sortOrder = 0;

    /**
     * 任务类型枚举
     */
    public enum GenerateType {
        DEFAULT("默认任务"),
        CUSTOM("自定义任务"),
        FUNCTION_CALLING("函数调用任务");

        private final String description;

        GenerateType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 构造函数
     */
    public GenerateConfig(String code, String title, String description, String systemTemplate, 
                         String userTemplate, GenerateType generateType) {
        this.code = code;
        this.title = title;
        this.description = description;
        this.systemTemplate = systemTemplate;
        this.userTemplate = userTemplate;
        this.generateType = generateType;
        this.enabled = true;
        this.sortOrder = 0;
    }

    /**
     * 创建生成任务
     */
    public Generate makeGenerate(String request, String userPrompt) {
        return new Generate(this, request, userPrompt);
    }

    /**
     * 更新配置
     */
    public void updateConfig(String title, String description, String demo, 
                           String systemTemplate, String userTemplate, GenerateType generateType) {
        this.title = title;
        this.description = description;
        this.demo = demo;
        this.systemTemplate = systemTemplate;
        this.userTemplate = userTemplate;
        this.generateType = generateType;
    }

    /**
     * 启用配置
     */
    public void enable() {
        this.enabled = true;
    }

    /**
     * 禁用配置
     */
    public void disable() {
        this.enabled = false;
    }

    /**
     * 设置排序
     */
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder != null ? sortOrder : 0;
    }

    /**
     * 获取系统模板
     */
    public String getSystemTemplate() {
        return systemTemplate;
    }

    /**
     * 获取用户模板
     */
    public String getUserTemplate() {
        return userTemplate;
    }
}
