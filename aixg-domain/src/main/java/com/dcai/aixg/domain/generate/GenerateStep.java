package com.dcai.aixg.domain.generate;

import com.ejuetc.commons.base.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.time.LocalDateTime;

/**
 * 生成步骤实体
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@NoArgsConstructor
@Comment("生成步骤")
@Table(name = "tb_generate_step")
@Where(clause = "logic_delete = 0")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "step_type", discriminatorType = DiscriminatorType.STRING)
public class GenerateStep extends BaseEntity<GenerateStep> {

    @Id
    @Comment("步骤ID")
    @GeneratedValue(generator = "generate_step_id")
    @SequenceGenerator(name = "generate_step_id", sequenceName = "seq_generate_step_id")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "generate_id", columnDefinition = "bigint(20) COMMENT '生成任务ID'", nullable = false)
    private Generate generate;

    @Column(name = "title", columnDefinition = "varchar(200) COMMENT '步骤标题'", nullable = false)
    private String title;

    @Column(name = "request", columnDefinition = "text COMMENT '请求内容'")
    private String request;

    @Column(name = "response", columnDefinition = "longtext COMMENT '响应内容'")
    private String response;

    @Column(name = "duration", columnDefinition = "bigint COMMENT '执行时长(毫秒)'")
    private Long duration;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", columnDefinition = "varchar(50) COMMENT '状态'", nullable = false)
    private StepStatus status;

    @Column(name = "memo", columnDefinition = "text COMMENT '备忘录'")
    private String memo;

    @Column(name = "error_message", columnDefinition = "text COMMENT '错误信息'")
    private String errorMessage;

    @Column(name = "started_at", columnDefinition = "datetime COMMENT '开始时间'")
    private LocalDateTime startedAt;

    @Column(name = "completed_at", columnDefinition = "datetime COMMENT '完成时间'")
    private LocalDateTime completedAt;

    @Override
    public Long getId() {
        return id;
    }

    @Column(name = "step_order", columnDefinition = "int DEFAULT 0 COMMENT '步骤顺序'", nullable = false)
    private Integer stepOrder = 0;

    /**
     * 步骤状态枚举
     */
    public enum StepStatus {
        PENDING("待处理"),
        PROCESSING("处理中"),
        SUCCESS("成功"),
        FAILED("失败"),
        SKIPPED("已跳过");

        private final String description;

        StepStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 构造函数
     */
    public GenerateStep(String title, String request) {
        this.title = title;
        this.request = request;
        this.status = StepStatus.PENDING;
        this.stepOrder = 0;
    }

    /**
     * 构造函数
     */
    public GenerateStep(String title, String request, Integer stepOrder) {
        this.title = title;
        this.request = request;
        this.status = StepStatus.PENDING;
        this.stepOrder = stepOrder != null ? stepOrder : 0;
    }

    /**
     * 开始处理
     */
    public void startProcessing() {
        this.status = StepStatus.PROCESSING;
        this.startedAt = LocalDateTime.now();
    }

    /**
     * 处理成功
     */
    public void completeSuccess(String response) {
        this.status = StepStatus.SUCCESS;
        this.response = response;
        this.completedAt = LocalDateTime.now();
        if (startedAt != null) {
            this.duration = java.time.Duration.between(startedAt, completedAt).toMillis();
        }
    }

    /**
     * 处理失败
     */
    public void completeFailed(String errorMessage) {
        this.status = StepStatus.FAILED;
        this.errorMessage = errorMessage;
        this.completedAt = LocalDateTime.now();
        if (startedAt != null) {
            this.duration = java.time.Duration.between(startedAt, completedAt).toMillis();
        }
    }

    /**
     * 跳过步骤
     */
    public void skip(String reason) {
        this.status = StepStatus.SKIPPED;
        this.memo = reason;
        this.completedAt = LocalDateTime.now();
        if (startedAt != null) {
            this.duration = java.time.Duration.between(startedAt, completedAt).toMillis();
        }
    }

    /**
     * 设置备忘录
     */
    public void setMemo(String memo) {
        this.memo = memo;
    }

    /**
     * 是否完成
     */
    public boolean isCompleted() {
        return status == StepStatus.SUCCESS || 
               status == StepStatus.FAILED || 
               status == StepStatus.SKIPPED;
    }

    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return status == StepStatus.SUCCESS;
    }

    /**
     * 设置生成任务
     */
    public void setGenerate(Generate generate) {
        this.generate = generate;
    }

}
