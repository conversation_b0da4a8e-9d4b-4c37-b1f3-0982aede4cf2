package com.dcai.aixg.domain.generate.service;

import com.dcai.aixg.domain.generate.Generate;
import com.dcai.aixg.domain.generate.GenerateConfig;
import com.dcai.aixg.domain.generate.GenerateConfigRpt;
import com.dcai.aixg.domain.generate.GenerateRpt;
import com.dcai.aixg.pro.generate.CreateGenerateByCodePO;
import com.dcai.aixg.pro.generate.CreateGeneratePO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * 生成任务领域服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GenerateDomainService {

    private final GenerateConfigRpt generateConfigRpt;
    private final GenerateRpt generateRpt;

    /**
     * 根据配置ID创建生成任务
     */
    @Transactional
    public Generate createGenerate(CreateGeneratePO po) {
        log.info("创建生成任务，配置ID: {}, 请求: {}", po.getConfigId(), po.getRequest());

        GenerateConfig config = generateConfigRpt.findById(po.getConfigId())
                .orElseThrow(() -> new RuntimeException("配置不存在: " + po.getConfigId()));

        if (!config.getEnabled()) {
            throw new RuntimeException("配置已禁用: " + po.getConfigId());
        }

        Generate generate = new Generate(config, po);
        return generateRpt.save(generate);
    }

    /**
     * 根据配置代码创建生成任务
     */
    @Transactional
    public Generate createGenerateByCode(CreateGenerateByCodePO po) {
        log.info("根据配置代码创建生成任务，代码: {}, 请求: {}", po.getConfigCode(), po.getRequest());

        GenerateConfig config = generateConfigRpt.findByCodeAndEnabled(po.getConfigCode(), true)
                .orElseThrow(() -> new RuntimeException("配置不存在或已禁用: " + po.getConfigCode()));

        Generate generate = new Generate(config, po);
        return generateRpt.save(generate);
    }

    /**
     * 执行生成任务
     */
    @Transactional
    public Generate executeGenerate(Long generateId, String response) {
        log.info("执行生成任务: {}", generateId);

        Generate generate = generateRpt.findById(generateId)
                .orElseThrow(() -> new RuntimeException("生成任务不存在: " + generateId));

        if (generate.isCompleted()) {
            log.warn("生成任务已完成，无需重复执行: {}", generateId);
            return generate;
        }

        try {
            generate.startProcessing();
            generateRpt.save(generate);

            generate.completeSuccess(response);
            return generateRpt.save(generate);

        } catch (Exception e) {
            log.error("生成任务执行失败: {}", generateId, e);
            generate.completeFailed(e.getMessage());
            return generateRpt.save(generate);
        }
    }

    /**
     * 取消生成任务
     */
    @Transactional
    public Generate cancelGenerate(Long generateId) {
        log.info("取消生成任务: {}", generateId);

        Generate generate = generateRpt.findById(generateId)
                .orElseThrow(() -> new RuntimeException("生成任务不存在: " + generateId));

        if (generate.isCompleted()) {
            log.warn("生成任务已完成，无法取消: {}", generateId);
            return generate;
        }

        generate.cancel();
        return generateRpt.save(generate);
    }

    /**
     * 重新执行生成任务
     */
    @Transactional
    public Generate retryGenerate(Long generateId) {
        log.info("重新执行生成任务: {}", generateId);

        Generate generate = generateRpt.findById(generateId)
                .orElseThrow(() -> new RuntimeException("生成任务不存在: " + generateId));

        // 重置状态
        generate.reset();
        return generateRpt.save(generate);
    }

    /**
     * 获取生成任务
     */
    public Optional<Generate> getGenerate(Long generateId) {
        return generateRpt.findById(generateId);
    }
}
