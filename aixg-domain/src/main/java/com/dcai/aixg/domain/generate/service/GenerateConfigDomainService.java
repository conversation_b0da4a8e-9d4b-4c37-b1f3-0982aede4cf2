package com.dcai.aixg.domain.generate.service;

import com.dcai.aixg.domain.generate.GenerateConfig;
import com.dcai.aixg.domain.generate.GenerateConfigRpt;
import com.dcai.aixg.pro.generate.CreateGenerateConfigPO;
import com.dcai.aixg.pro.generate.UpdateGenerateConfigPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 生成配置领域服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GenerateConfigDomainService {

    private final GenerateConfigRpt generateConfigRpt;

    /**
     * 创建生成配置
     */
    @Transactional
    public GenerateConfig createConfig(CreateGenerateConfigPO po) {
        log.info("创建生成配置，代码: {}, 标题: {}", po.getCode(), po.getTitle());
        
        // 检查代码是否已存在
        if (generateConfigRpt.existsByCode(po.getCode())) {
            throw new RuntimeException("配置代码已存在: " + po.getCode());
        }
        
        GenerateConfig config = new GenerateConfig(po);
        return generateConfigRpt.save(config);
    }

    /**
     * 更新生成配置
     */
    @Transactional
    public GenerateConfig updateConfig(UpdateGenerateConfigPO po) {
        log.info("更新生成配置，ID: {}, 标题: {}", po.getId(), po.getTitle());
        
        GenerateConfig config = generateConfigRpt.findById(po.getId())
                .orElseThrow(() -> new RuntimeException("配置不存在: " + po.getId()));
        
        config.updateConfig(po);
        return generateConfigRpt.save(config);
    }

    /**
     * 删除生成配置
     */
    @Transactional
    public void deleteConfig(Long id) {
        log.info("删除生成配置，ID: {}", id);
        
        GenerateConfig config = generateConfigRpt.findById(id)
                .orElseThrow(() -> new RuntimeException("配置不存在: " + id));
        
        // 这里可以添加删除前的检查，比如是否有关联的生成任务
        
        generateConfigRpt.delete(config);
    }

    /**
     * 获取生成配置
     */
    public Optional<GenerateConfig> getConfig(Long id) {
        return generateConfigRpt.findById(id);
    }

    /**
     * 根据代码获取生成配置
     */
    public Optional<GenerateConfig> getConfigByCode(String code) {
        return generateConfigRpt.findByCode(code);
    }

    /**
     * 获取所有启用的配置
     */
    public List<GenerateConfig> getEnabledConfigs() {
        return generateConfigRpt.findByEnabledOrderBySortOrderAsc(true);
    }

    /**
     * 根据任务类型获取配置
     */
    public List<GenerateConfig> getConfigsByTaskType(GenerateConfig.GenerateType generateType) {
        return generateConfigRpt.findByTaskTypeAndEnabledOrderBySortOrderAsc(generateType, true);
    }

    /**
     * 启用/禁用配置
     */
    @Transactional
    public GenerateConfig toggleConfig(Long id) {
        log.info("切换配置状态，ID: {}", id);
        
        GenerateConfig config = generateConfigRpt.findById(id)
                .orElseThrow(() -> new RuntimeException("配置不存在: " + id));
        
        config.toggle();
        return generateConfigRpt.save(config);
    }

    /**
     * 根据标题搜索配置
     */
    public List<GenerateConfig> searchConfigsByTitle(String title) {
        return generateConfigRpt.findByTitleContainingAndEnabled(title, true);
    }
}
