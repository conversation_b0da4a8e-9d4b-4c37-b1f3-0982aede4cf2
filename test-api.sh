#!/bin/bash

# AI Agent 房地产内容生成系统 API 测试脚本

BASE_URL="http://localhost:8080/api"

echo "=== AI Agent 房地产内容生成系统 API 测试 ==="
echo

# 检查服务是否启动
echo "1. 检查服务状态..."
curl -s "$BASE_URL/actuator/health" | jq '.' || echo "服务未启动或健康检查失败"
echo

# 获取所有房源
echo "2. 获取所有房源列表..."
curl -s "$BASE_URL/properties" | jq '.[] | {id, communityName, listPrice, area}' || echo "获取房源列表失败"
echo

# 获取特定房源详情
echo "3. 获取房源详情 (PROP_001)..."
curl -s "$BASE_URL/properties/PROP_001" | jq '{id, communityName, address, listPrice, features}' || echo "获取房源详情失败"
echo

# 搜索房源
echo "4. 搜索房源 (价格区间 500-900万)..."
curl -s "$BASE_URL/properties/search?minPrice=500&maxPrice=900" | jq '.[] | {id, communityName, listPrice}' || echo "搜索房源失败"
echo

# 获取支持的内容类型
echo "5. 获取支持的内容类型..."
curl -s "$BASE_URL/content/types" | jq '.' || echo "获取内容类型失败"
echo

# 获取支持的平台
echo "6. 获取支持的平台..."
curl -s "$BASE_URL/content/platforms" | jq '.' || echo "获取平台列表失败"
echo

# 生成推广内容
echo "7. 生成推广内容..."
curl -s -X POST "$BASE_URL/content/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "propertyId": "PROP_001",
    "contentType": "PROPERTY_RECOMMENDATION",
    "targetPlatforms": ["XIAOHONGSHU"],
    "targetAudience": "UPGRADING_BUYER",
    "contentStyle": "PROFESSIONAL",
    "wordCount": 600,
    "needCharts": true,
    "chartTypes": ["PRICE_COMPARISON"],
    "specialRequirements": "突出满五唯一的税费优势"
  }' | jq '{taskId, propertyId, status, processingTime, content: {title, wordCount, tags}}' || echo "生成内容失败"
echo

echo "=== API 测试完成 ==="
echo "注意：如果某些测试失败，请确保："
echo "1. 应用已启动 (mvn spring-boot:run)"
echo "2. 已配置正确的DeepSeek API密钥"
echo "3. 网络连接正常"
