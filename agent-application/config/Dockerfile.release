FROM registry.ejuops.com/library/skywalking-openjdk17:8.15.2
MAINTAINER <EMAIL>
ENV TimeZone=Asia/Shanghai
ARG PUB_BU
ARG PUB_MODULE
ARG PUB_ENV
ENV pub_env=${PUB_ENV}
ENV java_applicaton_jar="agent-application.jar"

ADD ./agent-application/target/$java_applicaton_jar /$java_applicaton_jar
COPY ./agent-application/config/startup.release.sh /
VOLUME /tmp
EXPOSE 8096
RUN ln -snf /usr/share/zoneinfo/$TimeZone /etc/localtime
RUN echo $TimeZone >/etc/timezone
RUN ["chmod", "+x", "/startup.release.sh"]
CMD [ "sh", "-c", "/startup.release.sh $pub_env $java_applicaton_jar" ]