#!/bin/bash
release_env=$1
java_applicaton=$2
echo "run with $release_env"
JVM_OPTS="-Xss256k -Xmx1024m -Duser.timezone=Asia/Shanghai -Djava.security.egd=file:/dev/./urandom" \
JAVA_OPTS="-javaagent:/skywalking-agent/skywalking-agent.jar"
APP_OPTS=""
if [ $release_env == "tst" ]; then
  APP_OPTS="--spring.profiles.active=test"
fi

if [ $release_env == "lpt" ]; then
 APP_OPTS="--spring.profiles.active=uat"
fi

if [ $release_env == "uat" ]; then
 APP_OPTS="--spring.profiles.active=pre"
fi

if [ $release_env == "release" ]; then
  APP_OPTS="--spring.profiles.active=prod"
fi
java $JVM_OPTS $JAVA_OPTS -jar $java_applicaton $APP_OPTS