#datasource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=******************************************************
spring.datasource.username=prod_agent_app
spring.datasource.password=O02YxZULBR8AAyjUo3e2
spring.datasource.initial-size=10
spring.datasource.max-active=20
spring.datasource.min-idle=8
spring.datasource.max-idle=8
spring.datasource.max-wait=10000

#jpa
spring.jpa.database-platform=org.hibernate.dialect.TiDBDialect
#spring.jpa.hibernate.ddl-auto=none
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.open-in-view=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.hibernate.naming-strategy=org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy

#jackson
spring.jackson.serialization.indent_output=true

#eureka-client
#eureka.client.service-url.defaultZone=http://127.0.0.1:8081/eureka/
eureka.client.service-url.defaultZone=http://ejuetc-eureka.uat.ejucloud.cn/eureka/
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
eureka.instance.prefer-ip-address=true
eureka.instance.lease-renewal-interval-in-seconds=1
eureka.instance.lease-expiration-duration-in-seconds=3
spring.mvc.pathmatch.matching-strategy=ant_path_matcher

#E签宝配置(生产环境)
ejuetc.agent.esign.appId=5111652006
ejuetc.agent.esign.appSecret=b1123c52cd7fedd49dbcb6a5b9a662ed
ejuetc.agent.esign.host=https://openapi.esign.cn

#E签宝配置
esign.notifyUrl=https://agent-etc.ebaas.com/gateway/esign/callback
esign.redirectUrl=wechat://back

saas.api.url=https://om-api.fangyou.com
wx.sendMessage.url=https://pre-api.fangyou.com

#rocketmq
rocketmq.name-server=************:9876;*************:9876

crm.api.baseUrl=https://crm.fangyou.com
crm.api.appId=c6f24cac0a584684827e74c0a2e44342
crm.api.appSecret=cf029840271ee60a4ba3a8db76fdeee420ce63f6f8aca1c980cfeae011250c95

#云账户配置
yzh.baseUrl=https://api-service.yunzhanghu.com
yzh.dealerId=09332794
yzh.brokerId=yiyun73
yzh.appKey=2T3eE7T0Tt1aYu6agak30vPpOqoz1P1G
yzh.desKey=14AGy55F4dsbbp0ROUM1qGRD
yzh.publicKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDFruYiCMl2BXmcTXJn695fbyMYHUOp5GR8G4+P/WmB/tkjJjq6KkQUqoJ8MoY/ZynW+KWVFPRhQa++XT9XpQs+LQKFM2Dr1rlpaSR/HYxrQMmrI/lvppbz+mf7nff7L7G+D1j4ShMxffLN40T5RWH6N1lW7xWOmbpLas64wdOdNQIDAQAB
yzh.ejuPublicKey=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt7WkcDbZu0N3Zch3zWi+lbSWp058ATB1EpEaydld7YCCQZIsQkf0y6GEgjOSWkQENGHH2ZqPTM5sb4EidcB/+nTNdECZ0eCHygssoM6lpe1KBvdTWwRqakKsyLihQc6gJsQrLp54NyisPyH8S+3i1LgrBGw60lqT2YXfs4/JJh3h2RXeepGW4lAYR3hBev1dSIOPSK8y5ZH/a1ocvwftrhgqlmbUFv6Hx9cpNIC8Abj16dLtw9lRMv1DhITrQs4RTpLNr5SVPRK8cTPayZV1QlWEHOgmGcFGYlWtp+E+ryThr1qbkWoKGE75jFao+c58Zwgc7x0S4Y/tiyoPWJSwqQIDAQAB
yzh.ejuPrivateKey=MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC3taRwNtm7Q3dlyHfNaL6VtJanTnwBMHUSkRrJ2V3tgIJBkixCR/TLoYSCM5JaRAQ0YcfZmo9MzmxvgSJ1wH/6dM10QJnR4IfKCyygzqWl7UoG91NbBGpqQqzIuKFBzqAmxCsunng3KKw/IfxL7eLUuCsEbDrSWpPZhd+zj8kmHeHZFd56kZbiUBhHeEF6/V1Ig49IrzLlkf9rWhy/B+2uGCqWZtQW/ofH1yk0gLwBuPXp0u3D2VEy/UOEhOtCzhFOks2vlJU9ErxxM9rJlXVCVYQc6CYZwUZiVa2n4T6vJOGvWpuRagoYTvmMVqj5znxnCBzvHRLhj+2LKg9YlLCpAgMBAAECggEAAkzVlXYYkq/F5XyuhDWMFVVSgZEYHCezuHBY4vZSUYW5KgIfExMnWAshjpkcQoxmzC/EyOuzC2WysheZC2l/DSgRc85kl4iufFc8JJbY4v4uJPzZw4z5KJAatJvGvYtpwF0sMT0f0GyKuOSGmLcAVfKalmPsEtdImRASYC3zQHU8TXLfxAWoY3vIi7LBiDtvZoVeNq7rXP8HCuDOdgJyaHv7FYz1lL4/APePuDbB9b89DFUu4kECjiKC/RPuFmXohLgIB/FMyODf7D0jsgsOMBIUZWbFM0kg+pzpE9U/DYQ716uK1QVO9lSTE7UWHHM+JP/kOVTRek0MbzbmsMy1lQKBgQDy0Z9q9sS0puC4bkmxxNo7tII+3n74/0jFwFJzensnGEAn120bYwzXTd2REshHQK/JJgC62He++8BmocpcqJ41no1o0aTG4ApY59iT69jvk4gYsJg/Z8+WDfNcs4xC0Y4A2cvdkDL8zxhNww86xpDTIx6Wl1XFktPVXcUHu0EYFwKBgQDBrpiiGiX7odDphoz8Y7biJ7cKcfJM2lfpPYk8lNzKYqBenYuD92vSw0nEkqX+qq820Bbw63vNe2/0e1lOIJV8phvUtDNz+JxmhFhrjc7A0QClt8uAapIJA3nhhIGQVwAT/rBQfwEwIgNyrLLp2wwLJw9PHx96yQqfoobJy+U1PwKBgQCmKEd8fGxSajcwPVf0a44iePz/ZGViod38ZASHvPJ06CVOnh2tNfRdiIzrbvzZtSwU6wpo0SPv7nnUQTYmfcP1UQ/XAE5PQJQUDmKnpiYGmt/wdTdZfAIS3mz/rGV4e8k+T8svTDN3x4cL/0R2pdYIhOmzhs9MqZKHWOkwxzRd9QKBgHL5KjJQQ2v0ywdVkCvjJZVq7gWNTsDAfJ9AoE8OL2MU2Q5Rh5h3L9KVixzd0Rw0Rn7LWFL79yortHH1JzZxsUulb6/8qO6XDoDs33A5Zhqf9zpouK0qljYzkg6FkkQU4T79WbzvJ81i45kXW73nAmc8cKIPX6iL33qUCF+SYiYRAoGBAO02bvQfh3nnT29UAqh30EdVjxppdp+6Ly/nt6tryr9cJhWhOVfOCbC0AzHfuZOSDOZA/PH5PwvB9THJ4yCPWXxv3wRzlyMwRYEzSXfuSikX6bc+Wexh/ZXVVdtAS1ZIT5POkoRG25uI0GfQvoHNVJxEbJ4znGozE11FnMSp/rMu

ejuetc.agent.deal.config.cron=0 0 0 31 2 ?
ejuetc.agent.deal.clarity.cron=0 0 0 31 2 ?
ejuetc.agent.StatementAPIImpl.clearingAndStatement.cron=0 0 0 31 2 ?
ejuetc.agent.statement.sendMessage.cron=0 0 0 31 2 ?

esign.mock=true
