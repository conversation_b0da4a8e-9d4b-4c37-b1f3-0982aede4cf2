#datasource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=***********************************************************
spring.datasource.username=test_agent_app
spring.datasource.password=h3JMRuaxZjfdMSwjUODl
spring.datasource.initial-size=10
spring.datasource.max-active=20
spring.datasource.min-idle=8
spring.datasource.max-idle=8
spring.datasource.max-wait=10000

#jpa
spring.jpa.database-platform=org.hibernate.dialect.TiDBDialect
spring.jpa.hibernate.ddl-auto=none
#spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.open-in-view=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.hibernate.naming-strategy=org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy

#jackson
spring.jackson.serialization.indent_output=true

#eureka-client
#eureka.client.service-url.defaultZone=http://127.0.0.1:8081/eureka/
eureka.client.service-url.defaultZone=http://*************:8081/eureka/
#eureka.client.service-url.defaultZone=http://ejuetc-eureka.tst.ejucloud.cn/eureka/
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
eureka.instance.prefer-ip-address=true
eureka.instance.lease-renewal-interval-in-seconds=1
eureka.instance.lease-expiration-duration-in-seconds=3
spring.mvc.pathmatch.matching-strategy=ant_path_matcher

com.ejuetc.exception.alarm=false

crm.api.baseUrl=https://test-crm.fangyou.com
crm.api.appId=ed3cd5a70d6348a898c3914c07f45dcc
crm.api.appSecret=120c6d7acab099c033ca2b27b7240dc8e000d00fd2f7d1e3121bc3d52c5b17e8


yzh.baseUrl=https://api-service.yunzhanghu.com/sandbox