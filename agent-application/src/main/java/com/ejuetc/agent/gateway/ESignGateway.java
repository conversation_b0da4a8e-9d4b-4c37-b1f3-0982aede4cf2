package com.ejuetc.agent.gateway;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@Tag(name = "E签宝网关")
@FeignClient(value = "ejuetc.agent", contextId = "ESignGateway")
public interface ESignGateway {

    @PostMapping("/gateway/esign/callback")
    String callback(@RequestHeader Map<String, String> headers, @RequestParam Map<String, String> query, @RequestBody String body);
}
