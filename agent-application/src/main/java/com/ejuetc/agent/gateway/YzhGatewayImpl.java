package com.ejuetc.agent.gateway;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RequiredArgsConstructor
@Slf4j
@RestController
public class YzhGatewayImpl implements YzhGateway {

    @Override
    public String callback(String action, Map<String, String> headers, Map<String, String> query) {
        log.info("收到云账户回调,action:{},headers:{},query:{}", action, headers, query);
        //TODO: 云账户解约
        return "success";
    }
}
