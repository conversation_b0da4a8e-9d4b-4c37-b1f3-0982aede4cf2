package com.ejuetc.agent.gateway;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@Tag(name = "云账户网关")
@FeignClient(value = "ejuetc.agent", contextId = "YzhGateway")
public interface YzhGateway {

    @PostMapping("/gateway/yzh/callback/{action}")
    String callback(@PathVariable("action") String action,
                    @RequestHeader Map<String, String> headers,
                    @RequestParam Map<String, String> query);
}
