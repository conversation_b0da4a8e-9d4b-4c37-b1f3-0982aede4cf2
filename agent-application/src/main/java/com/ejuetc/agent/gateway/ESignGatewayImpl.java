package com.ejuetc.agent.gateway;

import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ejuetc.agent.domain.esign.ESignRpt;
import com.ejuetc.agent.domain.user.UserRpt;
import com.ejuetc.agent.integration.esign.ESignComponent;

import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class ESignGatewayImpl implements ESignGateway {

    private final ESignRpt eSignRpt;
    private final EntityManager entityManager;
    private final ESignComponent eSign;
    private final UserRpt userRpt;

    @Value("${esign.mock:false}")
    private boolean mock;

    @Override
    public String callback(@RequestHeader Map<String, String> headers, @RequestParam Map<String, String> query, @RequestBody String body) {
        log.info("收到E签宝回调 body:\n{}", body);
        if (!mock && !eSign.checkSign(headers, query, body)) {
            return "{\"code\":\"400\",\"msg\":\"sign check failed\"}";
        }

        JSONObject bodyObj = JSON.parseObject(body);
        try {
            switch (bodyObj.getString("action")) {
                case "SIGN_FLOW_COMPLETE":
                    eSignRpt.findByFlowIdOrRescissionFlowId(bodyObj.getString("signFlowId"))
                            .orElseThrow(() -> new RuntimeException("signFlowId not found: " + bodyObj.getString("signFlowId")))
                            .callback(bodyObj);
                    break;
                case "AUTHORIZE_FINISH":
                    userRpt.findByAuthFlowId(bodyObj.getString("authFlowId"))
                            .orElseThrow(() -> new RuntimeException("authFlowId not found"))
                            .authPass();
                    break;
                case "SIGN_FILE_RESCINDED":
                case "SIGN_MISSON_COMPLETE":
                    eSignRpt.findByFlowId(bodyObj.getString("signFlowId"))
                            .orElseThrow(() -> new RuntimeException("signFlowId not found: " + bodyObj.getString("signFlowId")))
                            .callback4Rescission(bodyObj);
                    break;
                default:
                    return "{\"code\":\"400\",\"msg\":\"action not support\"}";
            }
            return "{\"code\":\"200\",\"msg\":\"success\"}";
        } catch (Exception e) {
            log.error("callback error", e);
            return "{\"code\":\"500\",\"msg\":\"callback error\"}";
        }
    }

    private void authorizeFinish(JSONObject bodyObj) {

    }

}
