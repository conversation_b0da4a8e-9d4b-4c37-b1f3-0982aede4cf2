package com.ejuetc.agent.apiImpl;

import com.ejuetc.agent.api.contract.*;
import com.ejuetc.agent.domain.contract.*;
import com.ejuetc.agent.domain.crm.OaFinance;
import com.ejuetc.agent.domain.crm.OaFinance4CashOut;
import com.ejuetc.agent.domain.crm.OaFinance4ChargeOff;
import com.ejuetc.agent.domain.esign.ESignRpt;
import com.ejuetc.agent.domain.esign.Template;
import com.ejuetc.agent.domain.esign.TemplateRpt;
import com.ejuetc.agent.dto.StatementDTO;
import com.ejuetc.agent.dto.UserDTO;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.agent.dto.ContractDTO;
import com.ejuetc.commons.base.response.ResponseStatus;
import org.springframework.data.domain.Page;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ejuetc.agent.apiImpl.InvitationAPIImpl.INVALID_INVITE_CODES;
import static com.ejuetc.agent.dto.ContractDTO.Status.EFFECTIVE;
import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.apiResponse;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class ContractAPIImpl implements ContractApi {

    private final ContractRpt contractRpt;

    private final EntityManager entityManager;

    private final ESignRpt eSignRpt;

    private final TemplateRpt templateRpt;

    @Override
    public ApiResponse<ContractDTO> create(CreateContractPO po) {
        List<Contract> exist = contractRpt.findContract(po.getUserId(), EFFECTIVE, po.getType());
        if (exist.stream().anyMatch(c -> c.isApplyCity(po.getCityCode()))) {
            throw new CodingException("已存在重叠合同");
        }

        Contract newContract = Contract.newContract(po);
        entityManager.persist(newContract);
        newContract.exec();
        return succ(convert2DTO(newContract, new ContractDTO()));
    }

    @Override
    public ApiResponse<String> checkInvitedCode(CheckInvitedCodePO po) {
        //(250205:临时代码,将来删除)杭州离职员工原邀请码
        if (INVALID_INVITE_CODES.contains(po.getInvitedCode())) throw new CodingException("邀请码已失效");

        List<Contract> contracts = contractRpt.findContract(po.getUserId(), EFFECTIVE, po.getContractType())
                .stream().filter(c -> po.getCityCode() == null || c.isApplyCity(po.getCityCode())).toList();
        if (contracts.size() != 1) throw new CodingException("[" + po + "]合同不存在或存在多个");
        Contract contract = contracts.get(0);
        contract.checkInvitedCode(po.getInvitedCode(), po.isUpdate(), po.isRelation());
        return succ(contract.getInvitedContract().getInvitedCode());
    }

    @Override
    public ApiResponse<?> agentSignResult(Long contractId, Boolean signResult, String description, String contractFile) {

        Contract contract = entityManager.find(Contract.class, contractId);
        contract.setSuppFlag(true);
        contract.signResult(signResult, description, List.of(contractFile));
        return succ();
    }

    @Override
    public ApiResponse<List<ContractDTO>> findInvitedBrokerContracts(FindInvitedBrokerContractsPO po) {
        Page<BrokerContract> page = contractRpt.findInvitedBrokers(po.getAgentUserId(), po.getAgentOperatorId(), po.getBrokerName(), PageRequest.of(po.getPageNum() - 1, po.getPageSize()));
        ApiResponse<List<ContractDTO>> response = succ(convert2DTO(page.get().toList(), new ContractDTO().setUser(new UserDTO())));
        response.setPage(new com.ejuetc.commons.base.response.Page(po.getPageNum(), po.getPageSize()).setTotalRow(page.getTotalElements()));
        return response;
    }

    @Override
    public ApiResponse<?> contractCrmResult(CrmResultPo po) {
        log.info("contractCrmResult po = {}", po);
        if (po.getResultType() == CrmResultPo.Type.AGENT_CONTRACT) {
            Contract contract = contractRpt.findByContractNo(po.getContractNo());
            contract.contractCrmResult(po);
        }
        if (po.getResultType() == CrmResultPo.Type.CASH_OUT)
            entityManager.find(OaFinance.class, po.getQkNo()).contractCrmResult(po);
        if (po.getResultType() == CrmResultPo.Type.CASH_OUT_PAY_RESULT)
            entityManager.find(OaFinance4CashOut.class, po.getQkNo()).crmPayResult(po);
        if (po.getResultType() == CrmResultPo.Type.CHARGE_OFF)
            entityManager.find(OaFinance4ChargeOff.class, po.getQkNo()).contractCrmResult(po);
        return succ();
    }

    @Override
    public ApiResponse<?> sendCrmContractByIds(SendCrmContractByIdsPO po) {
        List<Contract> contracts = contractRpt.findAllById(po.getIds());
        for (Contract contract : contracts) {
            contract.sendCrmContract();
        }
        return succ();
    }

    @Override
    public ApiResponse<String> findInvitationCodeByExclusive(String cityCode) {
        List<String> cityCodes = contractRpt.findInvitationCodeByExclusive(cityCode);
        if (cityCodes != null && cityCodes.size() == 1) {
            return succ(cityCodes.get(0));
        } else {
            return succ();
        }
    }

    @Override
    public ApiResponse<?> launchSupplESign(List<Long> contractIds, Long templateId) {
        Map<Long, String> errors = new HashMap<>();
        Template template = templateRpt.getReferenceById(templateId);
        contractRpt.findAllById(contractIds).forEach(contract -> {
            try {
                contract.launchSupplESign(template);
            } catch (Exception e) {
                log.error("合同[{}]e签宝签约异常", contract.getId(), e);
                errors.put(contract.getId(), e.getMessage());
            }
        });

        return errors.isEmpty() ? succ() : apiResponse(ResponseStatus.FAIL_SYS).setError(errors);
    }

    @Override
    public ApiResponse<?> esignCancel(Long signId, StatementDTO.Role role, String reason) {
        eSignRpt.getReferenceById(signId).cancel(role, reason);
        return succ();
    }
}
