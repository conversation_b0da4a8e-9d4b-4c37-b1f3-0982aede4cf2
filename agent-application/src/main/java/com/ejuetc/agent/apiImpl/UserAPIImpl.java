package com.ejuetc.agent.apiImpl;

import com.ejuetc.agent.api.account.CreateAccountPO;
import com.ejuetc.agent.api.user.EditUserPO;
import com.ejuetc.agent.api.user.UserApi;
import com.ejuetc.agent.api.user.UserBindNotifyPo;
import com.ejuetc.agent.domain.account.Account;
import com.ejuetc.agent.domain.user.User;
import com.ejuetc.agent.domain.user.UserRpt;
import com.ejuetc.agent.dto.AccountDTO;
import com.ejuetc.agent.dto.UserDTO;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.response.ApiResponse;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class UserAPIImpl implements UserApi {

    private final UserRpt userRpt;

    private final EntityManager entityManager;

    @Override
    public ApiResponse<UserDTO> editUser(EditUserPO po) {
        Optional<User> optional = userRpt.findById(po.getId());
        User user;
        if (optional.isEmpty()) {
            user = User.of(po);
            entityManager.persist(user);
        } else {
            user = optional.get();
            user.edit(po);
        }
        if (po.isLaunchAuth()) {
            user.launchAuth();
        }
        return succ(convert2DTO(user, new UserDTO()));
    }

    @Override
    public ApiResponse<AccountDTO> addAccount(CreateAccountPO po) {
        User user = userRpt.findById(po.getUserId()).orElseThrow(() -> new CodingException("用户不存在[" + po.getUserId() + "]"));
        Account account = user.addAccount(po);
        entityManager.persist(account);
        return succ(convert2DTO(account, new AccountDTO()));
    }

    @Override
    public ApiResponse<UserDTO> notifyBind(UserBindNotifyPo po) {
        User user = userRpt.findById(po.getUserId()).orElseThrow(() -> new CodingException("用户不存在[" + po.getUserId() + "]"));
        user.bindNotify(po);
        return succ(convert2DTO(user, new UserDTO()));
    }

}
