package com.ejuetc.agent.apiImpl;

import com.ejuetc.agent.api.account.AccountApi;
import com.ejuetc.agent.api.account.CreateAccountPO;
import com.ejuetc.agent.domain.account.Account;
import com.ejuetc.agent.domain.account.AccountRpt;
import com.ejuetc.agent.domain.user.User;
import com.ejuetc.agent.domain.user.UserRpt;
import com.ejuetc.agent.dto.AccountDTO;
import com.ejuetc.agent.dto.UserDTO;
import com.ejuetc.agent.integration.yzh.YzhApiResponse;
import com.ejuetc.agent.integration.yzh.YzhComponentImpl;
import com.ejuetc.agent.integration.yzh.ro.YzhSignContractRO;
import com.ejuetc.agent.integration.yzh.ro.YzhSignReleaseRO;
import com.ejuetc.agent.pro.QuerySignContractRO;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.response.ApiResponse;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class AccountAPIImpl implements AccountApi {

    private final UserRpt userRpt;
    private final AccountRpt accountRpt;
    private final EntityManager entityManager;
    private final YzhComponentImpl yzhComponent;

    @Override
    public ApiResponse<AccountDTO> add(CreateAccountPO po) {
        User user = userRpt.findById(po.getUserId()).orElseThrow(() -> new CodingException("用户不存在[" + po.getUserId() + "]"));
        Account account = user.addAccount(po);
        return succ(convert2DTO(account, new AccountDTO().setUser(new UserDTO())));
    }

    @Override
    public ApiResponse<List<AccountDTO>> sort(Long userId, List<Long> accountIds) {
        User user = userRpt.findById(userId).orElseThrow(() -> new CodingException("用户不存在[" + userId + "]"));
        user.sortAccount(accountIds);
        return succ(convert2DTO(user.getAccounts(), new AccountDTO()));
    }

    @Override
    public ApiResponse<QuerySignContractRO> querySignContract() {
        YzhApiResponse<YzhSignContractRO> signContract = yzhComponent.getSignContract();
        if (!signContract.isSucc()) {
            throw new CodingException("获取签约协议失败", signContract.getMessage());
        }
        return succ(new QuerySignContractRO(signContract.getData().getTitle(), signContract.getData().getUrl()));
    }

    @Override
    public ApiResponse<?> signRelease(Long id) {
        Account account = accountRpt.findById(id).orElseThrow(() -> new CodingException("账户不存在"));
        //仅测试用
        YzhApiResponse<YzhSignReleaseRO> resp = yzhComponent.userSignRelease(account.getAccountName(), account.getAccountNO());
        if (!resp.isSucc()) {
            throw new CodingException("用户解约失败", resp.getMessage());
        }
        return ApiResponse.succ();
    }
}
