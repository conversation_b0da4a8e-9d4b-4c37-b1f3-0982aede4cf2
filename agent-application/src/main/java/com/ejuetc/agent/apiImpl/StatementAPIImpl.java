package com.ejuetc.agent.apiImpl;

import com.ejuetc.agent.api.statement.*;
import com.ejuetc.agent.domain.clearing.ClearingRpt;
import com.ejuetc.agent.domain.crm.OaFinance;
import com.ejuetc.agent.domain.crm.OaFinance4ChargeOff;
import com.ejuetc.agent.domain.crm.OaFinanceRpt;
import com.ejuetc.agent.domain.statement.*;
import com.ejuetc.agent.domain.withdraw.Withdraw;
import com.ejuetc.agent.domain.withdraw.WithdrawImport;
import com.ejuetc.agent.dto.*;
import com.ejuetc.agent.integration.wxSendMessage.SendMessagePO;
import com.ejuetc.agent.integration.wxSendMessage.WxSendMessage;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.response.ApiResponse;
import jakarta.persistence.EntityManager;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.ejuetc.agent.dto.ContractDTO.OAStatus.IN_HAND;
import static com.ejuetc.agent.dto.ContractDTO.OAStatus.SUCCESS;
import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.apiResponse;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.response.ResponseStatus.SUCC_PART;
import static com.ejuetc.commons.base.spring.SpringUtil.*;
import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class StatementAPIImpl implements StatementApi {

    @Accessors(fluent = true)
    @Getter(lazy = true)
    private static final String personalpayOperatorId = getProperty("personalpay.operatorId");
    @Accessors(fluent = true)
    @Getter(lazy = true)
    private static final String personalpayOperatorName = getProperty("personalpay.operatorId");
    private final StatementRpt statementRpt;
    private final StatementAccountRpt statementAccountRpt;
    private final ClearingRpt clearingRpt;
    private final DealAPIImpl dealAPI;
    private final EntityManager entityManager;
    private final StatementInvoiceRpt invoiceRpt;

    @Override
    @Transactional(propagation = REQUIRES_NEW)
//    @Scheduled(cron = "${ejuetc.agent.StatementAPIImpl.clearingAndStatement.cron:21 15 0 1 * ?}")
    public ApiResponse<String> insertStatement() {
        String statementBatch = UUID.randomUUID().toString().replaceAll("-", "");
        int updateBatchCount = clearingRpt.updateStatementBatch(statementBatch);
        if (updateBatchCount == 0) return apiResponse(SUCC_PART, "没有需要结算的数据", null);
        int insertCount = statementRpt.insertStatement(statementBatch);
        if (insertCount == 0) throw new CodingException("批次[%s]更新[%s]条结算项,但插入结算单数为[%s]", statementBatch, updateBatchCount, insertCount);
        statementRpt.findByStatementBatch(statementBatch).forEach(Statement::calcTaxValue);
        int updateStatementIdCount = clearingRpt.updateStatementId(statementBatch);
        if (updateStatementIdCount != updateBatchCount) throw new CodingException("批次[%s]更新[%s]条结算项,但更新结算单数为[%s]", statementBatch, updateBatchCount, updateStatementIdCount);
        return succ(statementBatch);
    }

    @Override
    public ApiResponse<StatementDTO> flowAction(FlowActionPO po) {
        Statement statement = statementRpt.findAndLockById(po.getStatementId());
        statement.flowAction(po);
        return succ(convert2DTO(statement, new StatementDTO().setESign(new ESignDTO()).setStatementLogs(List.of(new StatementLogDTO()))));
    }

    @Override
    public ApiResponse<WithdrawDTO> withdraw(WithdrawPO po) {
        List<Statement> statements = statementRpt.findAllById(po.getStatementIds());
        List<StatementAccount> statementAccounts = statements.stream().flatMap(statement -> statement.getStatementAccounts().stream().filter(e -> e.getStatement().getAccountBatchNo().equals(e.getAccountBatchNO()))).toList()
                .stream().filter(e -> Arrays.asList(WithdrawDTO.Status.INIT, WithdrawDTO.Status.FAIL).contains(e.getStatus())).collect(Collectors.toList());
        if (!statementAccounts.isEmpty()) {
            Withdraw withdraw = new Withdraw(statementAccounts, WithdrawDTO.Type.PERSONAL);
            entityManager.persist(withdraw);
            return succ(convert2DTO(withdraw, new WithdrawDTO().setStatementAccounts(List.of(new StatementAccountDTO().setAccount(new AccountDTO())))));
        }
        return succ();
    }

    @Override
    public ApiResponse<ChargeOffOaFinanceRO> selKhInfo(ChargeOffPO po) {

        List<Statement> statements = statementRpt.findAllById(po.getStatementIds());
        if (statements.stream().anyMatch(t -> t.getStatus() != StatementDTO.Status.PAY_DONE)) {
            throw new BusinessException("bc.ejuetc.agent.1009");
        }
        if (statements.stream().map(Statement::getInvoiceBatchNO).collect(Collectors.toSet()).size() > 1)
            throw new BusinessException("bc.ejuetc.agent.1010");
        Set<OaFinance> oaFinance4ChargeOffs = statements.stream().filter(t -> !t.notOaFinance()).map(Statement::getOaFinance).collect(Collectors.toSet());
        if (oaFinance4ChargeOffs.size() > 1) {
            throw new BusinessException("存在已发生指令的结算单");
        }
        OaFinance oaFinance = oaFinance4ChargeOffs.stream().filter(Objects::nonNull).findFirst().orElse(null);
        if (oaFinance == null) {
            oaFinance = new OaFinance4ChargeOff(statements, po);
            getBean(OaFinanceRpt.class).save(oaFinance);
        }
        if (statements.stream().map(Statement::getOaFinance).anyMatch(OaFinance::isSucc)) {
            throw new BusinessException("bc.ejuetc.agent.1008");
        }
        Statement statement = statements.stream().filter(Statement::hasChannelOpen).findFirst().orElse(statements.get(0));
        statement.refreshInvoice();

        List<Object[]> resultList = getBean(ClearingRpt.class).selPartnerAmount(po.getStatementIds());
        List<StatementPartnerAmountRO> partnerAmountROList = resultList.stream().map(t -> {
            return new StatementPartnerAmountRO((String) t[0], (String) t[1], (String) t[2], (String) t[3], (BigDecimal) t[4], (BigDecimal) t[5], (Long) t[6], (BigDecimal) t[7], (BigDecimal) t[8]);
        }).collect(Collectors.toList());
        ChargeOffOaFinanceRO chargeOffOaFinanceRO = new ChargeOffOaFinanceRO();
        chargeOffOaFinanceRO.setOaFinanceId(oaFinance.getId());
        chargeOffOaFinanceRO.setStatementPartnerAmountROList(partnerAmountROList);
        return succ(chargeOffOaFinanceRO);
    }

    @Override
    public ApiResponse<List<StatementDTO>> selPersonalPayDone(SelPersonalPayDonePO po) {
        List<Statement> statements = statementRpt.selPersonalPayDone(po);
        return succ(convert2DTO(statements, new StatementDTO().setUser(new UserDTO())));
    }

    @Override
    public ApiResponse<OaFinanceDTO> chargeOff(ChargeOffPO po) {
        OaFinance4ChargeOff oaFinance4ChargeOff = null;
        if (po.getOaFinanceId() != null) {
            oaFinance4ChargeOff = entityManager.find(OaFinance4ChargeOff.class, po.getOaFinanceId());
        }
        List<Statement> statements = statementRpt.findAllById(oaFinance4ChargeOff != null ? oaFinance4ChargeOff.getStatements().stream().map(Statement::getId).collect(Collectors.toList()) : po.getStatementIds());
        if (statements.stream().anyMatch(t -> t.getStatus() != StatementDTO.Status.PAY_DONE)) {
            throw new BusinessException("bc.ejuetc.agent.1009");
        }
        if (statements.stream().map(Statement::getInvoiceBatchNO).collect(Collectors.toSet()).size() > 1)
            throw new BusinessException("bc.ejuetc.agent.1010");
        if (oaFinance4ChargeOff == null) {
            oaFinance4ChargeOff = new OaFinance4ChargeOff(statements, po);
        }
        if (statements.stream().map(Statement::getOaFinance).anyMatch(OaFinance::isSucc)) {
            throw new BusinessException("bc.ejuetc.agent.1008");
        }
        oaFinance4ChargeOff.sendCrm(po.getApplyName());
        return succ(convert2DTO(oaFinance4ChargeOff, new OaFinanceDTO().setKhInfo(new ArrayList<>())));
    }

    public ApiResponse<Long> importPensonalPay(WithdrawImportPO po) {

        WithdrawImport withdrawImport = new WithdrawImport(po);
        entityManager.persist(withdrawImport);

        pensonalPay(withdrawImport);

        return succ(withdrawImport.getId());
    }

    @Override
    public ApiResponse<OaFinanceDTO> sendCrm(Long oaFinanceId) {
        OaFinance oaFinance = getBean(OaFinanceRpt.class).findById(oaFinanceId).orElse(null);
        if (oaFinance != null && oaFinance.getOaStatus() != IN_HAND && oaFinance.getOaStatus() != SUCCESS) {
            oaFinance.sendCrm(null);
        }
        return succ(convert2DTO(oaFinance, new OaFinanceDTO()));
    }

    public void pensonalPay(WithdrawImport withdrawImport) {

        if (withdrawImport.getImportTotalCount() == 0) {
            return;
        }
        Integer failCnt = 0;
        Set<Statement> statementList = new HashSet<>();
        for (WithdrawImportDetailPO po : withdrawImport.getDetailList()) {
            Optional<StatementAccount> statementAccountOptional = statementAccountRpt.findById(Long.valueOf(po.getPlatformEnterpriseOrderNo()));// 对应StatementAccountId
            if (statementAccountOptional.isEmpty()) {
                po.setStatus(PersonalPayDTO.Status.FAIL).setError("结算账号不存在");
                failCnt++;
                continue;
            }
            StatementAccount statementAccount = statementAccountOptional.get();
            if (statementAccount.getStatus() == WithdrawDTO.Status.SUCCESS) {
                po.setStatus(PersonalPayDTO.Status.FAIL).setError("结算账号已经付款成功，无需处理");
                failCnt++;
                continue;
            }
            if (!Arrays.asList("已支付", "已付款", "支付失败", "失败").contains(po.getOrderStatus())) { // 对应StatementAccountStatus
                po.setStatus(PersonalPayDTO.Status.FAIL).setError("支付状态不合法");
                failCnt++;
                continue;
            }

            statementAccount.updateStatusAndPayTime(po.getOrderStatus(), LocalDateTime.now());
            statementAccountRpt.save(statementAccount);
            statementList.add(statementAccount.getStatement());
        }
        withdrawImport.updateImportCntAndStatus(failCnt);

        for (Statement statement : statementList) {
            if (statement.getStatementAccounts().stream().anyMatch(e -> e.getStatus() == WithdrawDTO.Status.PAYING)) {
                continue;
            }
            statement.refreshPayResult(LocalDateTime.now());
        }
    }

    @Override
    @Scheduled(cron = "${ejuetc.agent.statement.sendMessage.cron:0 15 10 * * ?}")
    public void sendWxMessage() {
        List<Statement> statements = statementRpt.findByStatus(StatementDTO.Status.SIGN_WAIT);
        statements.forEach(t -> {
            getAPI(WxSendMessage.class, getProperty("wx.sendMessage.url")).sendMessage(new SendMessagePO()
                    .setUserId(t.getUser().getId())
                    .setTemplateType(3)
                    .setOrderId(t.getStatementNO())
                    .setStatus("待签约")
                    .setOrderDetail("签署结算书")
                    .setRemake("您当前有未签署的结算书，请及时签署")
            );
        });
    }

    @Override
    public ApiResponse<StatementInvoiceDTO> execInvoice(Long invoiceId){
        StatementInvoice invoice = invoiceRpt.getReferenceById(invoiceId);
        invoice.exec();
        return succ(convert2DTO(invoice, new StatementInvoiceDTO()));
    }

}
