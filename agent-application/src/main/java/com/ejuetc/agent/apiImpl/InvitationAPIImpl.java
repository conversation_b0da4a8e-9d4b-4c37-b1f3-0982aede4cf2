package com.ejuetc.agent.apiImpl;

import com.ejuetc.agent.api.invitation.CreateOrRebindPO;
import com.ejuetc.agent.api.invitation.InvitationApi;
import com.ejuetc.agent.domain.contract.Contract;
import com.ejuetc.agent.domain.invitation.Invitation;
import com.ejuetc.agent.domain.invitation.InvitationRpt;
import com.ejuetc.agent.domain.user.User;
import com.ejuetc.agent.domain.user.UserRpt;
import com.ejuetc.agent.dto.ContractDTO;
import com.ejuetc.agent.dto.InvitationDTO;
import com.ejuetc.agent.pro.CheckCodeRO;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.response.ApiResponse;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static org.jsoup.internal.StringUtil.isBlank;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class InvitationAPIImpl implements InvitationApi {

    private final InvitationRpt invitationRpt;
    private final EntityManager entityManager;
    private final UserRpt userRpt;
    public static final List<String> INVALID_INVITE_CODES = Arrays.asList(
            "207713",
            "100611",
            "803997",
            "224327",
            "224349",
            "224752",
            "100622"
    );

    @Override
    public ApiResponse<InvitationDTO> createOrRebind(CreateOrRebindPO po) {
        Invitation invitation;
        User operator = userRpt.findById(po.getOperatorId()).orElseThrow(() -> new CodingException("操作人[%s]不存在", po.getOperatorId()));
        if (isBlank(po.getInvitedCode())) {
            User user = userRpt.findById(po.getUserId()).orElseThrow(() -> new CodingException("用户不存在[%s]", po.getUserId()));
            invitation = user.newInvitation(operator);
        } else {
            invitation = invitationRpt.findByCode(po.getInvitedCode()).orElseThrow(() -> new CodingException("邀请码不存在"));
            invitation.changeOwner(operator);
        }
        return succ(convert2DTO(invitation, new InvitationDTO()));
    }

    @Override
    public ApiResponse<Boolean> checkCode4Api(String inviteCode, String cityCode) {
        checkCode4Rpc(inviteCode, cityCode);
        return succ(true);
    }

    @Override
    public ApiResponse<CheckCodeRO> checkCode4Rpc(String inviteCode, String cityCode) {
        Invitation invitation = checkCode(inviteCode, cityCode);
        return succ(convert2DTO(invitation, new CheckCodeRO()));
    }

    private Invitation checkCode(String inviteCode, String cityCode) {
        if (INVALID_INVITE_CODES.contains(inviteCode)) throw new BusinessException("bc.ejuetc.agent.1013");//(250205:临时代码,将来删除)杭州离职员工原邀请码
        Invitation invitation = invitationRpt.findByCode(inviteCode).orElseThrow(() -> new BusinessException("bc.ejuetc.agent.1012"));
        Contract contract = invitation.getUser().findContract(ContractDTO.Type.AGENT, LocalDateTime.now(), cityCode, List.of(ContractDTO.Status.EFFECTIVE));
        if (contract == null) throw new BusinessException("bc.ejuetc.agent.1014");
        return invitation;
    }
}
