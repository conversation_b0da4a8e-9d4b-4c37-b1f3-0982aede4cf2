package com.ejuetc.agent.apiImpl;

import com.ejuetc.agent.api.deal.*;
import com.ejuetc.agent.domain.deal.ChannelOpenDeal;
import com.ejuetc.agent.domain.deal.Deal;
import com.ejuetc.agent.domain.deal.DealRpt;
import com.ejuetc.agent.dto.ClearingDTO;
import com.ejuetc.agent.dto.DealDTO;
import com.ejuetc.agent.dto.UserDTO;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.response.Page;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.ThreadUtils.asyncExec;
import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class DealAPIImpl implements DealApi {

    private final DealRpt dealRpt;
    private final EntityManager entityManager;
    private static final int BATCH_MAX_SIZE = 100;

    @Override
    public ApiResponse<DealDTO> edit(BaseDealPO po) {
        ApiResponse<DealDTO> response = getBean(DealAPIImpl.class).doEdit(po);
        DealDTO data = response.getData();
        asyncExec(() -> getBean(DealAPIImpl.class).config(data.getId(), true));
        return response;
    }

    @Transactional(propagation = REQUIRES_NEW)
    public ApiResponse<DealDTO> doEdit(BaseDealPO po) {
        Deal deal = dealRpt.findExist(po);
        if (deal == null) {
            deal = Deal.of(po);
            entityManager.persist(deal);
        } else {
            deal.update(po);
        }
        return succ(convert2DTO(deal, new DealDTO()));
    }

    @Override
    public ApiResponse<?> makeUpDelegate(MakeUpDelegationPo po) {
        ChannelOpenDeal deal = dealRpt.findAndLockByTypeAndOrderId(po.getOrderId()).orElseThrow(() -> new CodingException("找不到渠道开通交易[" + po.getOrderId() + "]"));
        deal.makeUpDelegate(po.getUpTime());
        return succ();
    }


    @Override
    @Scheduled(cron = "${ejuetc.agent.deal.config.cron:21 3/5 * * * ?}")
    public ApiResponse<Integer> config() {
        int sum = 0;
        int thisTime = 0;
        do {
            ApiResponse<Integer> response = getBean(DealAPIImpl.class).config(null, true);
            thisTime = response.getData();
            sum += thisTime;
        } while (thisTime == BATCH_MAX_SIZE);
        log.info("交易配置结果:{}", sum);
        return succ(sum);
    }

    @Transactional(propagation = REQUIRES_NEW)
    public ApiResponse<Integer> config(Long dealId, boolean clearing) {
        List<Deal> deals = dealId != null
                ? List.of(dealRpt.findAndLockById(dealId))
                : dealRpt.findWaitConfig(BATCH_MAX_SIZE);
        log.info("待结算交易数量:{}", deals.size());
        deals.forEach(deal -> {
            try {
                log.info("clarity dealId:{} clearing:{}", deal.getId(), clearing);
                deal.config();
                if (clearing) deal.clearing();
            } catch (Exception e) {
                log.error("交易配置失败[" + deal.getId() + "]", e);
                deal.addMemo("结算失败:" + e.getMessage());
            }
        });
        return succ(deals.size());
    }

    @Override
    @Transactional(propagation = REQUIRES_NEW)
    @Scheduled(cron = "${ejuetc.agent.deal.clarity.cron:11 5/7 * * * ?}")
    public int clearing() {
        int successCount = 0;
        List<Deal> deals = dealRpt.findWaitClearing();
        for (Deal deal : deals) {
            try {
                log.info("clearing dealId:{}", deal.getId());
                deal.clearing();
                successCount++;
            } catch (Exception e) {
                log.error("交易清分失败[" + deal.getId() + "]", e);
                deal.addMemo("清分失败:" + e.getMessage());
            }
        }
        return successCount;
    }

    @Override
    public ApiResponse<List<DealDTO>> selOrderByEnabled(EnabledOrderPO po) {
        Long total = getBean(DealRpt.class).amountOrderByEnabled(po);
        List<Deal> deals = getBean(DealRpt.class).selOrderByEnabled(po, PageRequest.of(po.getPage() - 1, po.getPageSize()));
        return ApiResponse.succ(convert2DTO(deals, new DealDTO().setParent(new DealDTO()).setClearing(new ClearingDTO()).setBrokerUser(new UserDTO()).setBrokerOperator(new UserDTO())))
                .setPage(new Page(po.getPage(), po.getPageSize()).setTotalRow(total));
    }

}
