package com.ejuetc.agent;

import com.ejuetc.agent.dto.*;
import com.ejuetc.channel.api.DelegateAPI;
import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.channel.dto.ChannelDelegateDTO;
import com.ejuetc.channel.pro.DelegateStatisticsPO;
import com.ejuetc.channel.pro.DelegateStatisticsRO;
import com.ejuetc.commons.base.querydomain.api.QueryDomainAPI;
import com.ejuetc.commons.base.querydomain.api.QueryResponse;
import com.ejuetc.commons.base.querydomain.api.QueryTerm;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.mall.api.dto.WalletDTO;
import org.junit.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ejuetc.agent.dto.ContractDTO.Status.EFFECTIVE;
import static com.ejuetc.agent.dto.ContractDTO.Type.AGENT;
import static com.ejuetc.agent.dto.ContractDTO.Type.BROKER;
import static com.ejuetc.agent.dto.DealDTO.Type.CHANNEL_OPEN;
import static com.ejuetc.agent.dto.DealDTO.Type.CLUE_CONSUME;
import static com.ejuetc.channel.dto.BusinessOpenDTO.Code.RENT;
import static com.ejuetc.channel.dto.BusinessOpenDTO.Code.SALE;
import static com.ejuetc.commons.base.spring.SpringUtil.getAPI;
import static com.ejuetc.mall.api.dto.WalletDTO.Type.getClueWalletType;
import static java.lang.Boolean.TRUE;

//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = {AgentApplication.class})
public class QueryDomainTest {

//        @Autowired
//    private QueryDomainApiImpl queryDomain;
//    private QueryDomainAPI queryDomain = getAPI(QueryDomainAPI.class, "http://ejuetc-agent.release.ejucloud.cn");
    private QueryDomainAPI queryDomain = getAPI(QueryDomainAPI.class, "http://ejuetc-agent.tst.ejucloud.cn");
//    private QueryDomainAPI queryDomain = getAPI(QueryDomainAPI.class, "http://localhost:8096");

    //    @Autowired
    private DelegateAPI delegateAPI;

    @Test //查询下游经纪人或代理机构
    public void queryDownstreamBrokers() {
        QueryResponse<ContractDTO> response = queryDomain.query(new QueryTerm<>(new ContractDTO().setUser(new UserDTO()))
                        .beginFieldsAnd("=", new ContractDTO()
                                        .setStatus(EFFECTIVE)
                                        .setType(BROKER)   //查询下游经纪机构
//                      .setType(ContractDTO.Type.AGENT)    //查询下游代理
                                        .setInvitedUser(new UserDTO().setId(1727601531412L)) //邀请码所属机构或人的userId
                        )
                        .setPage(1, 4)
        );
        System.out.println(response.getPage());
        response.getData().forEach(c -> {
            UserDTO user = c.getUser();
            System.out.println(user.getType().getTitle() + "\t" + //用户类型:公司/个人
                               user.getCompanyName() + "\t" +              //公司名称
                               user.getPersonalName() + "\t" +             //个人姓名
                               user.getContactName() + "\t" +              //联系人姓名
                               user.getContactMobile());                   //联系人手机号
        });
    }

    @Test //查询下游经纪人或代理机构
    public void queryDownstreamBrokers2() {
        InvitationDTO inv = new InvitationDTO().setOperator(new UserDTO(1727601531412L));
        QueryResponse<ContractDTO> response = queryDomain.query(new QueryTerm<>(new ContractDTO().setUser(new UserDTO()))
                        .beginFieldsAnd("=", new ContractDTO()
                                        .setStatus(EFFECTIVE)
                                        .setType(BROKER)   //查询下游经纪机构
//                      .setType(ContractDTO.Type.AGENT)    //查询下游代理
//                                        .setInvitedUser(new UserDTO().setId(1727601531412L)) //邀请码所属机构或人的userId
                        )
                        .andFieldsOr("=", new ContractDTO().setInviteds(List.of(inv))
                                , new ContractDTO().setInvited(inv)
                        )
//                        .setPage(1, 4)
        );
        System.out.println(response.getPage());
        response.getData().forEach(c -> {
            UserDTO user = c.getUser();
            System.out.println(user.getType().getTitle() + "\t" + //用户类型:公司/个人
                               user.getCompanyName() + "\t" +              //公司名称
                               user.getPersonalName() + "\t" +             //个人姓名
                               user.getContactName() + "\t" +              //联系人姓名
                               user.getContactMobile());                   //联系人手机号
        });
    }

    @Test
    public void queryUser() {
        List<ContractDTO> query = queryDomain.query(new ContractDTO().setUserId(9095301597710284800L).setType(BROKER).setStatus(EFFECTIVE));
        System.out.println(query);
    }

    @Test
    public void queryAgentByName() {
        QueryResponse<ContractDTO> response = queryDomain.query(new QueryTerm<>(new ContractDTO().setUser(new UserDTO().setUserInvitations(List.of(new InvitationDTO()))))
                .beginFieldsAnd("=", new ContractDTO().setType(AGENT).setStatus(EFFECTIVE).setInvitedUser(new UserDTO(1727143506002L)))
                .andFieldsOr("like", new ContractDTO().setUser(new UserDTO().setCompanyName("%家道%").setPersonalName("%家道%")))

        );
        response.getData().forEach(c ->
                c.getUser().getUserInvitations().forEach(i -> System.out.println(i.getCode()))
        );
    }

    @Test
    public void queryCode() {
        UserDTO user = new UserDTO(1727143506002L);
        QueryResponse<InvitationDTO> response = queryDomain.query(new QueryTerm<>(new InvitationDTO())
                .beginFieldsOr("=", new InvitationDTO().setUser(user).setOperator(user))
        );
        response.getData().forEach(i -> System.out.println(i.getCode()));
    }

    @Test
    public void queryDeal() {
        QueryResponse<DealDTO> query = queryDomain.query(new QueryTerm<>(new DealDTO().setClearing(new ClearingDTO()).setBrokerUser(new UserDTO()).setParent(new DealDTO()))
                        .beginFieldsAnd("=", new DealDTO()
                                        .setType(CHANNEL_OPEN)
//                        .setUpDelegateFlag(true)
//                                        .setBrokerUser(new UserDTO().setBindXYRent(TRUE))
                        )
                .andFieldsOr("=", new DealDTO().setBusinessCode(null))
                        .addOrderBy("payTime", false)
        );
        System.out.println(query.getPage() + "\n");
        Set<Long> brokerIds = query.getData().stream().map(d -> d.getBrokerUser().getId()).collect(Collectors.toSet());

        QueryResponse<WalletDTO> queryWallets = getAPI(QueryDomainAPI.class, "http://ejuetc-mall.tst.ejucloud.cn").query(new QueryTerm<>(new WalletDTO())
                .beginFieldsAnd("in", brokerIds.stream().map(brokerId -> new WalletDTO().setUserId(brokerId)).toList())
        );
        Map<String, BigDecimal> walletsValue = queryWallets.getData().stream().collect(Collectors.toMap(w -> w.getUserId() + "_" + w.getType(), WalletDTO::getValue));

        ApiResponse<List<DelegateStatisticsRO>> response = delegateAPI.count(new DelegateStatisticsPO()
                .setBusinessCodes(List.of(BusinessOpenDTO.Code.RENT, BusinessOpenDTO.Code.SALE))
                .setChannelCodes(List.of(ChannelDTO.Code.XIANYU))
                .setStatuses(List.of(ChannelDelegateDTO.Status.UP_SUCC))
                .setUserIds(new ArrayList<>(brokerIds))
        );
        Map<String, Long> delegateCounts = response.getData().stream().collect(Collectors.toMap(d -> d.getUserId() + "_" + d.getBusinessCode(), d -> d.getCount()));


        query.getData().forEach(d -> System.out.printf(
                """
                        商品名称:%s
                        子订单金额:%s
                        留存价:%s
                        父订单编号:%s
                        业务:%s
                        购买人:%s
                        推荐码:%s
                        使用人:%s
                        闲鱼账号绑定:%s
                        现金余额:%s
                        线索余额:%s
                        在线房源数:%s
                                                
                        %n""",
//                d.getGoodsName(),
                d.getPayAmount(),
                d.getAgentPrice(),
                d.getChannelCode().getTitle() + d.getBusinessCode().getTitle(),
//                d.getParentOrderId(),
//                d.getBuyUserName(),
                d.getAgentCode(),
                d.getBrokerUserName(),
                d.getBrokerUser().getBindXYRent() ? "已绑定" : "未绑定",
//                walletsValue.get(d.getBuyUserId() + "_" + WalletDTO.Type.CASH),
//                walletsValue.get(d.getBuyUserId() + "_" + getClueWalletType(d.getBusinessCode())),
                delegateCounts.get(1726993499091L + "_" + RENT)
        ));
    }

    @Test
    public void testQueryClue() {
        QueryResponse<DealDTO> query = queryDomain.query(new QueryTerm<>(new DealDTO().setClearing(new ClearingDTO()).setBrokerUser(new UserDTO()))
                        .beginFieldsAnd("=", new DealDTO()
                                        .setType(CLUE_CONSUME)
                                        .setBusinessCode(SALE)
                                        .setAgentUser(new UserDTO().setId(1L))
                                        .setPartnerUser(new UserDTO().setId(1L))
//                        .setUpDelegateFlag(true)
                                        .setBrokerUser(new UserDTO().setBindXYRent(TRUE))
//                        .setSrcId(1729065122354L)
                        )
                        .andFieldsAnd(">=", new DealDTO().setPayTime(LocalDateTime.now()))
                        .andFieldsAnd("<=", new DealDTO().setPayTime(LocalDateTime.now()))
                        .andFieldsOr("like", new DealDTO().setBrokerUser(new UserDTO().setPersonalName("%冬%").setCompanyName("%冬%").setContactMobile("186%")))
                        .addOrderBy("payTime", false)
                        .setPage(1, 2)
        );
        System.out.println(query.getPage() + "\n");

    }

    @Test
    public void t() {
        List<StatementLogDTO> query = queryDomain.query(new StatementLogDTO().setStatement(new StatementDTO(50751L)));
        query.forEach(System.out::println);
    }

}
