package com.ejuetc.agent;

import com.ejuetc.agent.api.user.EditUserPO;
import com.ejuetc.agent.domain.esign.ESign;
import com.ejuetc.agent.domain.esign.ESignRpt;
import com.ejuetc.agent.dto.UserDTO;
import com.ejuetc.commons.base.response.ApiResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {AgentApplication.class})
public class TemplateTest {

    @Autowired
    private ESignRpt eSignRpt;

    @Test
    @Transactional
    public void editUser() {
        Optional<ESign> eSign = eSignRpt.findById(50652L);
        System.out.println(eSign);
    }

}
