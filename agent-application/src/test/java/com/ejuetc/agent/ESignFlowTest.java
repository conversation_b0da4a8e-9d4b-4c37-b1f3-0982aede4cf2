package com.ejuetc.agent;

import static com.alibaba.fastjson.JSON.toJSONString;
import static com.ejuetc.agent.integration.esign.EsignHeaderConstant.CONTENTTYPE_STREAM;
import static com.ejuetc.agent.integration.esign.EsignHttpHelper.doUploadHttp;
import static com.ejuetc.agent.integration.esign.EsignRequestType.PUT;

import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.ejuetc.agent.integration.esign.ESignComponent;
import com.ejuetc.agent.integration.esign.EsignDemoException;
import com.ejuetc.agent.integration.esign.EsignFileBean;

public class ESignFlowTest {

    //测试环境
//    public static final String APP_ID = "7439035509";
//    public static final String SECRET = "f621269df2ed50d22153a3aecbe0699e";
//    public static final String HOST = "https://smlopenapi.esign.cn";
//    public static final String orgId = "9b2155d9bda04ffd9681504f77bf6788";
//    public static final String sealId = "e65fb735-014e-4019-b8a2-8725240eb2d4";

    //生产环境(SaaS电子签)
    public static final String APP_ID = "5111652006";
    public static final String SECRET = "b1123c52cd7fedd49dbcb6a5b9a662ed";
    public static final String HOST = "https://openapi.esign.cn";
    public static final String orgId = "b37a8257ff7f4ac09ce450e0e6294dac";//壹家易
    public static final String sealId = "b66014da-03ac-4b5b-997d-28f68ef6be9d";

    static ESignComponent eSign = new ESignComponent(APP_ID, SECRET, HOST);
    
    /**
     * 合同解约
     */
    public static void main22222(String[] args) {
    	String signFlowId = "c11fc5a936f5468bb19aa1616d7c0445";
    	String docParam = """
        		{"rescindReason": "4", "rescindFileList": ["be616d6cddc148b4bc62507e5ad1ff9c"], "rescissionInitiator":{"orgInitiator":{"orgId":"9b2155d9bda04ffd9681504f77bf6788", "transactor":{"psnId":"fb5abb8dfc4444379dd6d068fd22ad9e"}}},"signFlowConfig": {"signFlowConfig":{"noticeTypes":"1"},"notifyUrl": "https://agent-test.ebaas.com/gateway/esign/callback"},"autoSignOrg":[{"orgId": "9b2155d9bda04ffd9681504f77bf6788","sealId": "e65fb735-014e-4019-b8a2-8725240eb2d4"}]}
        		""";
    	System.out.println("查询结果为：" + eSign.call("/v3/sign-flow/" + signFlowId + "/initiate-rescission", docParam));
    }
    
    /**
     * 获取签署页面链接
     */
    public static void main12(String[] args) {
    	String flowId = "519ad26539144fd48e0144e4db40c9a9";
    	System.out.println("查询结果为：" + eSign.call(
                "/v3/sign-flow/" + flowId + "/sign-url",
                Map.of(
                        "clientType", "ALL",
                        "needLogin", false,
                        "urlType", 2,
                        "operator", Map.of(
                                "psnAccount", "***********"
                        )
                )
        ));
    }
    
    /**
     * 获取签署页面链接
     */
    public static void main3333(String[] args) {
    	String flowId = "c11fc5a936f5468bb19aa1616d7c0445";
    	System.out.println("查询结果为：" + eSign.call(
                "/v3/sign-flow/" + flowId + "/sign-url",
                Map.of(
                        "clientType", "ALL",
                        "needLogin", false,
                        "urlType", 2,
                        "operator", Map.of(
                                "psnAccount", "***********"
                        )
                )
        ));
    }
    
    /**
     * 查询合同模板列表
     */
    public static void main123(String[] args) {
    	System.out.println("查询结果为：" + eSign.call("/v3/doc-templates"));
    }
    
    /**
     * 查询合同模板中控件详情
     */
    public static void main1(String[] args) {
    	String docTemplateId = "bfc828d53c2e492a8eb67560e4195f68";
    	System.out.println("查询结果为：" + eSign.call("/v3/doc-templates/" + docTemplateId));
    }
    
    /**
     * 查询文件上传状态
     */
    public static void main3434(String[] args) {
    	System.out.println("查询结果为：" + eSign.call("/v3/files/e9b31a7690654e1c95c077d3a434c4ca"));
    }
    
    /**
     * 获取预览合同模板页面
     */
    public static void main44443(String[] args) {
    	System.out.println("查询结果为：" + eSign.call("/v3/doc-templates/doc-template-preview-url", Map.of(
                "docTemplateId", "6af0722c37054ed1a0e9ac6a48adc7ad"
        )));
    }
    
    /**
     * 获取编辑合同模板页面
     */
    public static void main(String[] args) {
    	String docTemplateId = "6ccaf8afc7f1489db74bb562cdf8fc1d";
    	System.out.println("查询结果为：" + eSign.call("/v3/doc-templates/" + docTemplateId + "/doc-template-edit-url", Map.of(
                "docTemplateId", docTemplateId
        )));
    }

    /**
     * 填写模板生成文件
     */
    public static void maina(String[] args) throws InterruptedException {
        String docParam = """
                {
                  "components": [
                    {
                      "componentKey": "userCertName",
                      "componentValue": "林冬成"
                    },
                    {
                      "componentKey": "userCertName2",
                      "componentValue": "林冬成"
                    }
                  ],
                  "docTemplateId": "4bc0db2a64864d0e91aa0a579efa6ee0",
                  "fileName": "权利义务转让三方协议"
                }""";
        JSONObject data = eSign.call("/v3/files/create-by-doc-template", docParam);
        System.out.println("查询结果为：" + data);
        String fileId = data.getString("fileId");
        while (true) {
            JSONObject fileStatus = eSign.call("/v3/files/" + fileId);
            if (fileStatus.getInteger("fileStatus").equals(2) || fileStatus.getInteger("fileStatus").equals(5)) {
                System.out.println("文件生成完毕:"+fileStatus);
                break;
            } else {
                System.out.println("文件生成中...");
                Thread.sleep(2000);
            }
        }
    }

    //上传文件并生成合同模板
    public static void main_1(String[] args) throws EsignDemoException, InterruptedException {
        EsignFileBean esignFileBean = new EsignFileBean("/Users/<USER>/IdeaProjects/ejuetc/agent/docs/v250526/权利义务转让三方协议_20250522.docx");
        JSONObject data = eSign.call("/v3/files/file-upload-url", toJSONString(Map.of(
                "contentMd5", esignFileBean.getFileContentMD5(),
                "contentType", CONTENTTYPE_STREAM.VALUE(),
                "fileName", esignFileBean.getFileName(),
                "fileSize", esignFileBean.getFileSize(),
                "convertToHTML", true
        )));
        doUploadHttp(data.getString("fileUploadUrl"), PUT, esignFileBean.getFileBytes(), esignFileBean.getFileContentMD5(), CONTENTTYPE_STREAM.VALUE(), true);
        String fileId = data.getString("fileId");
        while (true) {
            JSONObject fileStatus = eSign.call("/v3/files/" + fileId);
            if (fileStatus.getInteger("fileStatus").equals(2) || fileStatus.getInteger("fileStatus").equals(5)) {
                System.out.println(fileStatus);
                break;
            } else {
                System.out.println("文件生成中...");
                Thread.sleep(2000);
            }
        }
        System.out.println("文件生成完毕:"+fileId);
        System.out.println(eSign.call("/v3/doc-templates/doc-template-create-url", Map.of(
                "docTemplateName", "权利义务转让三方协议",
                "docTemplateType", 1,
                "fileId", fileId
        )));
    }

}
