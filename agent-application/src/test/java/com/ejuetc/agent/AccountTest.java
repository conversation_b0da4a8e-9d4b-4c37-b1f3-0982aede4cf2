package com.ejuetc.agent;

import com.ejuetc.agent.api.account.CreateAccountPO;
import com.ejuetc.agent.api.account.CreateYzhAccountPO;
import com.ejuetc.agent.apiImpl.AccountAPIImpl;
import com.ejuetc.agent.dto.AccountDTO;
import com.ejuetc.agent.pro.QuerySignContractRO;
import com.ejuetc.commons.base.response.ApiResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {AgentApplication.class})
public class AccountTest {

    @Autowired
    private AccountAPIImpl accountAPI;
//    private AccountApi accountAPI = getAPI(AccountApi.class, "http://localhost:8096");
//    private AccountApi accountAPI = getAPI(AccountApi.class, "http://ejuetc-agent.tst.ejucloud.cn");

    @Test
    public void testSort() {
        accountAPI.sort(1728988845677L, List.of(100004L, 100002L));
    }

    @Test
    public void testQuerySignContract() {
        ApiResponse<QuerySignContractRO> response = accountAPI.querySignContract();
        System.out.println(response);
    }

    @Test
    public void testAddYzhAccount() {
        CreateAccountPO po = new CreateYzhAccountPO()
                .setAccountName("谢俊")
                .setCardId("500002202003033337")
                .setBankName("建设银行")
                .setAccountNO("****************")
                .setUserId(245847471515772416L);
        ApiResponse<AccountDTO> response = accountAPI.add(po);
        System.out.println(response);
    }
}
