package com.ejuetc.agent;

import com.ejuetc.agent.api.account.CreateBankAccountPO;
import com.ejuetc.agent.api.user.EditUserPO;
import com.ejuetc.agent.api.user.UserApi;
import com.ejuetc.agent.apiImpl.UserAPIImpl;
import com.ejuetc.agent.dto.AccountDTO;
import com.ejuetc.agent.dto.UserDTO;
import com.ejuetc.commons.base.response.ApiResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static com.ejuetc.commons.base.spring.SpringUtil.getAPI;
import static java.time.LocalDateTime.now;

//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = {AgentApplication.class})
public class UserTest {

    //        @Autowired
//    private UserAPIImpl userAPI;
//    private UserApi userAPI = getAPI(UserApi.class, "http://ejuetc-agent.tst.ejucloud.cn");
    private UserApi userAPI = getAPI(UserApi.class, "ejuetc-agent.release.ejucloud.cn");
//    private UserApi userAPI = getAPI(UserApi.class, "http://ejuetc-agent.uat.ejucloud.cn");
//    private UserApi userAPI = getAPI(UserApi.class, "http://localhost:8096");

    @Test
    public void editCompany() throws JsonProcessingException {
        EditUserPO po = new EditUserPO()
                .setId(1727168719435L)
                .setType(UserDTO.Type.COMPANY)
                .setLaunchAuth(true)
                .setContactName("林冬成")
                .setContactMobile("***********")
                .setContactAddress("上海市静安区广延路383号")
                .setCompanyName("壹家易（上海）网络科技有限公司")
                .setCompanyNum("91310120MA1HL5H27C")
//                .setCompanyName("上海添玑好房网络服务有限公司")
//                .setCompanyNum("91310000676211967N")
                .setBankAddress("中国银行股份有限公司上海市羽山路支行")
                .setBankAccount("************")
                .setBankProvince("河南")
                .setBankCity("焦作");
        System.out.println(new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(po));
        ApiResponse<UserDTO> response = userAPI.editUser(po);
        System.out.println(response);
    }

    @Test
    public void editPersonal() {
        ApiResponse<UserDTO> response = userAPI.editUser(new EditUserPO()
                .setId(System.currentTimeMillis())
                .setLaunchAuth(true)
                .setType(UserDTO.Type.PERSONAL)
                .setContactName("林冬成")
                .setContactMobile("***********")
                .setContactAddress("上海市静安区广延路383号")
                .setPersonalName("林冬成")
                .setPersonalNum("410811198209079094")
                .setCompanyName("esigntest上海添玑好房网络服务有限公司PABQ")
                .setCompanyNum("910000581244590042")
                .setBankAccount("6222001001113548573")
                .setBankAddress("中国工商银行")
                .setBankProvince("上海")
                .setBankCity("上海")
                .setParentId(1727168719435L)
                .setAuthed(true)
                .setLicenseType("付费版")
        );
        System.out.println(response);

    }

    @Test
    public void batchEditPersonal() {
        String[][] users = {
                {"9095213144469406721", "周慧丽", "***********", "420302198405200021"},
                {"5679959548989851392", "韦大龙", "***********", "440301198210233111"},
                {"9095213138362522941", "王晓东", "***********", "110224198001011611"},
                {"9095213127558001409", "王巍", "***********", "210102198212185013"},
                {"5547497161440129024", "王慧莉", "***********", "411082198711301829"},
                {"9071063233167669760", "唐尧", "***********", "513021199104211351"},
                {"9093283230635849990", "李梅", "***********", "532901199002052464"},
                {"4688815624104731136", "陈博洋", "***********", "120102198602152311"},
                {"9091903573527109120", "杨宇", "15114076255", "510781199504243397"},
                {"9091944147477130496", "肖祖檀", "15221268482", "362426199201268418"},
                {"9089230690153722369", "包国超", "13818392786", "100000000000000000"},
        };


        for (String[] user : users) {
            ApiResponse<UserDTO> response = userAPI.editUser(new EditUserPO()
                    .setId(Long.parseLong(user[0]))
                    .setType(UserDTO.Type.PERSONAL)
                    .setContactName(user[1])
                    .setContactMobile(user[2])
                    .setPersonalName(user[1])
                    .setPersonalNum(user[3])
            );
            System.out.println(response);
        }

    }

    @Test
    public void addBankAccount() {
        ApiResponse<AccountDTO> response = userAPI.addAccount(new CreateBankAccountPO()
                .setAccountNO("*****************")
                .setBankProvince("浙江省")
                .setBankCity("宁波市")
                .setBankAddress("宁波银行鄞州支行")
                .setUserId(9096019060244653056L)
        );
        System.out.println(response);
    }

}
