package com.ejuetc.agent;

import com.alibaba.fastjson.JSON;
import com.ejuetc.agent.api.invitation.CreateOrRebindPO;
import com.ejuetc.agent.api.invitation.InvitationApi;
import com.ejuetc.agent.apiImpl.InvitationAPIImpl;
import com.ejuetc.agent.dto.InvitationDTO;
import com.ejuetc.agent.dto.UserDTO;
import com.ejuetc.agent.pro.CheckCodeRO;
import com.ejuetc.commons.base.querydomain.api.QueryDomainAPI;
import com.ejuetc.commons.base.querydomain.api.QueryTerm;
import com.ejuetc.commons.base.response.ApiResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static com.ejuetc.commons.base.spring.SpringUtil.getAPI;

//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = {AgentApplication.class})
public class InvitationTest {

//    @Autowired
//    private InvitationAPIImpl invitationAPI;
    private InvitationApi invitationAPI = getAPI(InvitationApi.class, "http://ejuetc-agent.release.ejucloud.cn");
//    private InvitationApi invitationAPI = getAPI(InvitationApi.class, "http://ejuetc-agent.tst.ejucloud.cn");

    //        @Autowired
//    private QueryDomainApiImpl queryDomainApi;
    private QueryDomainAPI queryDomainApi = getAPI(QueryDomainAPI.class, "http://ejuetc-agent.tst.ejucloud.cn");

    @Test
    public void testCreateInvited() {
        List.of(
                9096385614496985835L,
                9096385615034846977L,
                9096385615505694517L,
                9096385615573822465L,
                9096385616979990017L,
                9096385617718208945L,
                9096386007219082240L,
                9096386008226729474L,
                9096386009703127808L,
                9096386011111360258L,
                9096386011849559808L,
                9096386013126742017L,
                9096386013728626176L
        ).forEach(operatorId -> {
            ApiResponse<InvitationDTO> response = invitationAPI.createOrRebind(new CreateOrRebindPO()
                    .setUserId(9095277100755078915L)
                    .setOperatorId(operatorId));
            System.out.println(response);
        });
    }

    @Test
    public void testRebindInvited() {
        ApiResponse<InvitationDTO> response = invitationAPI.createOrRebind(new CreateOrRebindPO()
                .setOperatorId(17267275187701L)
                .setInvitedCode("RTJO2K")
        );
        System.out.println(response);
    }

    @Test
    public void testQueryInvited() {
        System.out.println(queryDomainApi.query(new QueryTerm<>(new InvitationDTO().setUser(new UserDTO()))
                .beginFieldsAnd("=", new InvitationDTO().setOperator(new UserDTO(9094662523348608768L)))
        ));
    }

    @Test
    public void testInvitedCode() {
        System.out.println(queryDomainApi.query(new QueryTerm<>(new InvitationDTO().setUser(new UserDTO()).setOperator(new UserDTO()))
                .beginFieldsAnd("=", new InvitationDTO().setCode("TZWW01"))
        ));
    }

    @Test
    public void testCheckInviteCode() {
        ApiResponse<Boolean> response = invitationAPI.checkCode4Api("KJ8GR3", "310100");
        System.out.println(response);
    }

    @Test
    public void queryInviteCode() {
        ApiResponse<CheckCodeRO> response = invitationAPI.checkCode4Rpc("123456", "310100");
        System.out.println(JSON.toJSONString(response, true));
    }
}
