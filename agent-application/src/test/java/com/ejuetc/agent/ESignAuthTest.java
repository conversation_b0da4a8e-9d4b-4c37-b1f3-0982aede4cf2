package com.ejuetc.agent;

import com.alibaba.fastjson.JSONObject;
import com.ejuetc.agent.integration.esign.ESignComponent;

import java.util.List;
import java.util.Map;

import static com.alibaba.fastjson.JSON.toJSONString;

public class ESignAuthTest {

    //测试环境
//    public static final String APP_ID = "**********";
//    public static final String SECRET = "f621269df2ed50d22153a3aecbe0699e";
//    public static final String HOST = "https://smlopenapi.esign.cn";
//    public static final String orgId = "9b2155d9bda04ffd9681504f77bf6788";
//    public static final String sealId = "e65fb735-014e-4019-b8a2-8725240eb2d4";

    //生产环境(SaaS电子签)
    public static final String APP_ID = "5111652006";
    public static final String SECRET = "b1123c52cd7fedd49dbcb6a5b9a662ed";
    public static final String HOST = "https://openapi.esign.cn";
    public static final String orgId = "b37a8257ff7f4ac09ce450e0e6294dac";//壹家易
    public static final String sealId = "6903c36b-6c16-4e82-9ce1-77dff7b83ced";


    static ESignComponent eSign = new ESignComponent(APP_ID, SECRET, HOST);

    //获取机构认证&授权页面链接
    public static void main1111(String[] args) {
        JSONObject data = eSign.call("/v3/org-auth-url", toJSONString(Map.of(
                "orgAuthConfig", Map.of(
                        "orgName", "壹家易（上海）网络科技有限公司",
                        "orgInfo", Map.of(
                                "orgIDCardType", "CRED_ORG_USCC",
                                "orgIDCardNum", "91310120MA1HL5H27C",
                                "legalRepName", "聂万海"
                        ),
                        "transactorInfo", Map.of(
                                "psnAccount", "***********",
                                "psnInfo", Map.of(
                                        "psnName", "林冬成"
                                )
                        )
                ),
                "authorizeConfig", Map.of(
                        "authorizedScopes", List.of(
                                "manage_org_seal",
                                "manage_org_member",
                                "manage_org_template",
                                "use_org_template",
                                "manage_org_resource",
                                "manage_psn_resource",
                                "org_initiate_sign",
                                "psn_initiate_sign",
                                "get_org_identity_info",
                                "get_psn_identity_info",
                                "psn_sign_file_storage",
                                "org_sign_file_storage",
                                "org_approval_info",
                                "use_org_order")
                )
        )));
        System.out.println(data);
    }


    //查询机构认证信息
    // 测试环境: {"orgName":"壹家易（上海）网络科技有限公司","orgInfo":{"adminName":"林*成","legalRepName":"聂万海","orgIDCardType":"CRED_ORG_USCC","legalRepIDCardType":"CRED_PSN_CH_IDCARD","legalRepIDCardNum":"340521198705292036","adminAccount":"1******3894","orgIDCardNum":"91310120MA1HL5H27C"},"authorizeUserInfo":true,"realnameStatus":1,"orgId":"9b2155d9bda04ffd9681504f77bf6788"}
    // 生产环境: {"orgName":"壹家易（上海）网络科技有限公司","orgInfo":{"adminName":"林*成","legalRepName":"聂万海","orgIDCardType":"CRED_ORG_USCC","adminAccount":"1******3894","orgIDCardNum":"91310120MA1HL5H27C"},"authorizeUserInfo":false,"realnameStatus":1,"orgId":"b37a8257ff7f4ac09ce450e0e6294dac"}
    // 生产环境: {"orgName":"上海添玑好房网络服务有限公司","orgInfo":{"adminName":"林*成","legalRepName":"付晶","orgIDCardType":"CRED_ORG_USCC","legalRepIDCardType":"CRED_PSN_CH_IDCARD","legalRepIDCardNum":"36240219841213051X","adminAccount":"1******3894","orgIDCardNum":"91310000676211967N"},"authorizeUserInfo":true,"realnameStatus":1,"orgId":"4a7829ff172948a787d4d074c57f2d97"}
    public static void main1234(String[] args) {
        System.out.println(eSign.call("/v3/organizations/identity-info?orgName=壹家易（上海）网络科技有限公司"));
    }

    //查询认证授权流程详情
    public static void main012(String[] args) {
        System.out.println(eSign.call("/v3/auth-flow/{authFlowId}"));
    }


    //生成电子章:
    // 测试环境:{"sealId":"e65fb735-014e-4019-b8a2-8725240eb2d4"}
    // 生产环境:{"sealId":"b66014da-03ac-4b5b-997d-28f68ef6be9d"}
    public static void main08(String[] args) {
        JSONObject data = eSign.call("/v3/seals/org-seals/create-by-template", toJSONString(Map.of(
                "orgId", orgId,
                "sealName", "合同章",
                "sealTemplateStyle", "PUBLIC_ROUND_STAR",
                "sealSize", "42_42"
        )));
        System.out.println(data);
    }

    //查询企业内部印章
    public static void main12345(String[] args) {
        System.out.println(eSign.call("/v3/seals/org-own-seal-list?pageNum=1&pageSize=20&orgId=b37a8257ff7f4ac09ce450e0e6294dac"));
    }

    //查询机构授权信息
    public static void main34(String[] args) {
        System.out.println(eSign.call("/v3/organizations/" + orgId + "/authorized-info"));
    }

    //查询被外部企业授权印章
    public static void main88(String[] args) {
        System.out.println(eSign.call("/v3/seals/org-authorized-seal-list?pageNum=1&pageSize=20&orgId=4a7829ff172948a787d4d074c57f2d97"));
    }

    //查询指定印章详情（机构） {"sealId":"e65fb735-014e-4019-b8a2-8725240eb2d4","defaultSealFlag":false,"sealCreateTime":1727180072000,"sealHeight":42,"sealBizTypeDescription":"公章","sealStatus":1,"sealStyle":1,"sealName":"合同章","statusDescription":"已启用","sealImageDownloadUrl":"https://esignoss.esign.cn/seal-service/ce7ea99e-e757-47e4-8592-6ac3eba67450/a02b54c4-a244-43bd-aa0c-27c43524181a-openseal.png?Expires=**********&OSSAccessKeyId=LTAI4G23YViiKnxTC28ygQzF&Signature=372NmL8FSsSpihTPNtv08IoisTg%3D","sealBizType":"PUBLIC","sealWidth":42}
    public static void main11(String[] args) {
        System.out.println(eSign.call("/v3/seals/org-seal-info?orgId=" + orgId + "&sealId=" + sealId));
    }

    //查询企业成员列表
    // 测试环境: {"total":1,"members":[{"role":"1,99","psnId":"37907d7c5b844c64a0e6e8a098565026","memberName":"林冬成","departments":[],"psnName":"林冬成","psnAccount":{"accountMobile":"***********"}}]}
    // 生产环境(壹家易): {"total":1,"members":[{"role":"1,99","psnId":"5de7a5c0ecea44d5b9976b2ebcb8cbf9","memberName":"林冬成","departments":[],"psnName":"林冬成","psnAccount":{"accountMobile":"***********"}}]}
    public static void main67(String[] args) {
        System.out.println(eSign.call("/v3/organizations/" + orgId + "/member-list?pageNum=1&pageSize=100"));
    }


    //跨企业授权 {"authorizationSignUrl":"https://smlh5.esign.cn/mesign/guide?context=be3Qo36&flowId=b9e6fbdaafe34ae89097b999ec262bf8&organ=true&appId=**********&linkSource=1&bizType=1&tsign_source_type=SIGN_LINK_XUANYUAN&tsign_source_detail=16R2mv%2F27h2Y5CkM9bwhboM3nf5nJosU4lmjrQGR0qzRAGxhQ01EK7aoGwKk2wjsxmZuCBwNdJ0Jz%2Bm5jcOiYOdnPfw%2FL6QbxclYHk66trjlKorryv2ktvcuEwHF1zs%2FeuQtu6uopFmxnQxEGFHrdYYUUoAnzaYmt4y3Srz2zyGtG1nNE%2FEiJxniR9Aj83eg8UNlw6kgk%2BP4SEmLrBrXuN4%2BETQD0NuxEF9fVzDr6UbUZ6LAA4b0DqEfFkzKYC3Ye","sealAuthBizId":"81617e17-e4ec-4a21-b036-d9f3960e2c71","authorizationSignShortUrl":"https://smlt.esign.cn/po1Q82M"}
    public static void main1(String[] args) {
        JSONObject data = eSign.call("/v3/seals/org-seals/external-auth", toJSONString(Map.of(
                "orgId", orgId,
                "sealId", sealId,
                "transactorPsnId", "5de7a5c0ecea44d5b9976b2ebcb8cbf9",
                "authorizedOrgInfo", Map.of(
                        "orgName", "上海添玑好房网络服务有限公司",
                        "orgIDCardNum", "91310000676211967N"
                ),
                "effectiveTime", System.currentTimeMillis(),
                "expireTime", System.currentTimeMillis() + 1000L * 60 * 60 * 24 * 30 * 12 * 3
        )));
        System.out.println(data);
    }

    //查询对外部企业授权详情 {"sealAuthorizedInfos":[{"expireReasonDescription":"未失效","expireReason":"NOT_EXPIRE","sealId":"e65fb735-014e-4019-b8a2-8725240eb2d4","statusDescription":"正常","expireTime":1820505599000,"sealAuthBizId":"81617e17-e4ec-4a21-b036-d9f3960e2c71","authorizedOrgId":"8186ce8e2216462a83218dab2d23b145","effectiveTime":1727107200000,"authorizerPsnId":"37907d7c5b844c64a0e6e8a098565026","signFlowId":"b9e6fbdaafe34ae89097b999ec262bf8","authorizeStatus":1}],"total":1}
    public static void main(String[] args) {
        System.out.println(eSign.call("/v3/seals/org-seals/external-auth?orgId=" + orgId + "&pageNum=1&pageSize=20&sealId=" + sealId));
    }
}
