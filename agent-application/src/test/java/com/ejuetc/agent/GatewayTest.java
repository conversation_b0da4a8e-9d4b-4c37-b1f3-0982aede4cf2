package com.ejuetc.agent;

import com.alibaba.fastjson.JSON;
import com.ejuetc.agent.gateway.ESignGateway;
import com.ejuetc.agent.gateway.ESignGatewayImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

import static com.ejuetc.commons.base.spring.SpringUtil.getAPI;
import static java.time.LocalDateTime.now;

//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = {AgentApplication.class})
public class GatewayTest {

    //    @Autowired
//    private ESignGatewayImpl gateway;
    private ESignGateway gateway = getAPI(ESignGateway.class, "http://localhost:8096");
//    private ESignGateway gateway = getAPI(ESignGateway.class, "http://ejuetc-agent.tst.ejucloud.cn");
//    private ESignGateway gateway = getAPI(ESignGateway.class, "http://ejuetc-agent.uat.ejucloud.cn");

    @Test
    public void testSignComplete() {
        String response = gateway.callback(
                Map.of(
                        "X-Tsign-Open-TIMESTAMP", "1750736813139",
                        "X-Tsign-Open-SIGNATURE-ALGORITHM", "hmac-sha256",
                        "X-Tsign-Open-App-Id", "5111652006",
                        "X-Tsign-Open-SIGNATURE", "284ee6701ef232c5e2bd953b9ba6af5fb6d1858cc09ab2de8c92768760158e58"
                ),
                Map.of(),
                """
                        {
                        	"signFlowStatus":"2",
                        	"statusDescription":"完成",
                        	"signFlowStartTime":1750727897000,
                        	"signFlowCreateTime":1750727897000,
                        	"action":"SIGN_FLOW_COMPLETE",
                        	"signFlowId":"8c2eda6024114506a5717f6c9e7ce4c0",
                        	"signFlowFinishTime":1750736793000,
                        	"signFlowTitle":"闲鱼结算书企业版",
                        	"timestamp":1750736792733
                        }
                        """
        );
        System.out.println(response);
    }

    @Test
    public void testAuthFinish() {
        String response = gateway.callback(
                Map.of(
                        "X-Tsign-Open-TIMESTAMP", "1726993132994",
                        "X-Tsign-Open-SIGNATURE-ALGORITHM", "hmac-sha256",
                        "X-Tsign-Open-App-Id", "7439035509",
                        "X-Tsign-Open-SIGNATURE", "0b01a2849eaa4cb8494ab4910a3dd0b626b4c1a8b06772c80dd39fac83bced60"
                ),
                Map.of(),
                """
                        {
                          "timestamp": 1728062629309,
                          "psnId": "37907d7c5b844c64a0e6e8a098565026",
                          "authFlowId": "OF-34a8ce9a81080012",
                          "authorizedInfo": [
                              {
                                  "authorizedScope": "psn_initiate_sign",
                                  "effectiveTime": 1728062629232,
                                  "expireTime": 1759598629232
                              }
                          ],
                          "action": "AUTHORIZE_FINISH"
                          }"""
        );
        System.out.println(response);
    }

}
