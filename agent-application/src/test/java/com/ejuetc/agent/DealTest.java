package com.ejuetc.agent;

import com.ejuetc.agent.api.deal.*;
import com.ejuetc.agent.apiImpl.DealAPIImpl;
import com.ejuetc.agent.dto.DealDTO;
import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.response.ApiResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

import static com.ejuetc.commons.base.spring.SpringUtil.getAPI;
import static java.lang.Thread.sleep;
import static java.time.LocalDateTime.now;
import static java.time.format.DateTimeFormatter.ofPattern;

//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = {AgentApplication.class})
public class DealTest {

    //    @Autowired
//    private DealAPIImpl dealAPI;
//    private DealApi dealAPI = getAPI(DealApi.class, "http://ejuetc-agent.tst.ejucloud.cn");
    private DealApi dealAPI = getAPI(DealApi.class, "http://ejuetc-agent.release.ejucloud.cn");
//    private DealApi dealAPI = getAPI(DealApi.class, "http://localhost:8096");


    //    @Autowired
//    @Qualifier("dealMQ")
//    private DealMQ dealMQ;

    long orderId = System.currentTimeMillis();
    long buySrcId = orderId + 1;
    long cusSrcId = orderId + 2;
    long userId = 9095202519587552256L;
    String cityCode = "310100";
    String cityName = "上海";
    String invitedCode = "KJ8GR3";

    @Test
    @Transactional
    public void testEdit() throws InterruptedException {
        LocalDateTime payAuditTime = LocalDateTime.parse("2025-04-21 11:38:04", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        ApiResponse<DealDTO> response = dealAPI.edit(new CreateClueConsumeDealPO().setSrcId(1513037L).setPayAuditTime(payAuditTime));
        System.out.println(response);
        sleep(Long.MAX_VALUE);
    }

    @Test
    public void testDealAll() throws InterruptedException {
        testCreateChannelOpenDeal();
        testMakeUpDelegate();

        testCreateClueBuyDeal();
        testCreateClueConsumeDeal();

        testClarity();
    }

    @Test
    public void testCreateChannelOpenDeal() throws InterruptedException {
//        long srcId = System.currentTimeMillis();
//        for (int i = 0; i < 1; i++) {
        ApiResponse<DealDTO> response = dealAPI.edit(new CreateChannelOpenDealPO()
                        .setOrderNum(UUID.randomUUID().toString().replaceAll("-", ""))
                        .setGoodsId(1L)
                        .setGoodsName("闲鱼二手房端口推广")
                        .setOrderId(orderId)
                        .setParentOrderId(orderId * 10 + 1)
                        .setUserId(userId)
                        .setOperatorId(userId)
                        .setInvitedCode(invitedCode)
                        .setCityCode(cityCode)
                        .setCityName(cityName)
                        .setSrcId(orderId)
                        .setCrmOrderNO(UUID.randomUUID().toString())
                        .setPayAmount(new BigDecimal(900))
                        .setPayTime(now())
//                .setChannelCode(ChannelDTO.Code.XIANYU)
//                .setBusinessCode(BusinessOpenDTO.Code.RENT)
                        .setCategory(DealDTO.Category.WITHOUT_PAY)
                        .setIndateBegin(now())
                        .setIndateEnd(now().plusYears(1))
        );
        System.out.println(response);
//        }
        sleep(15000);
    }

    @SneakyThrows
    @Test
    public void testCreateClueBuyDeal() {
//        long srcId = System.currentTimeMillis();
//        for (int i = 0; i < 1; i++) {
        ApiResponse<DealDTO> response = dealAPI.edit(new CreateClueBuyDealPO()
                .setChildQuantity(3)
                .setInvitedCode(invitedCode)
                .setCityCode(cityCode)
                .setCityName(cityName)
                .setUserId(userId)
                .setOperatorId(userId)
                .setChannelCode(ChannelDTO.Code.XIANYU)
                .setBusinessCode(BusinessOpenDTO.Code.SALE)
                .setSrcId(buySrcId)
                .setPayAmount(new BigDecimal(36))
                .setPayTime(now())
                .setCrmOrderNO(UUID.randomUUID().toString())
        );
        System.out.println(response);
//        }

        sleep(3000);
    }

    @SneakyThrows
    @Test
    public void testCreateClueConsumeDeal() {
//        List.of(1730287384006L).forEach(parentId -> {
//            long srcId = System.currentTimeMillis();
//            for (int i = 0; i < 3; i++) {
        ApiResponse<DealDTO> response = dealAPI.edit(new CreateClueConsumeDealPO()
                        .setGoodsName("线索_")
                        .setOrderNum(UUID.randomUUID().toString().replaceAll("-", ""))
                        .setParentSrcId(1743148971868L)
//                .setParentSrcId(buySrcId)
                        .setSrcId(cusSrcId)
                        .setPayAmount(new BigDecimal(12))
                        .setPayTime(now())
                        .setCrmOrderNO(UUID.randomUUID().toString())
        );
        System.out.println(response);
//            }
//        });
        sleep(5000);
    }

    @SneakyThrows
    @Test
    public void testMakeUpDelegate() {
        List.of(
                62203L,
                62204L,
                62205L,
                101908L,
                106552L,
                106553L,
                106554L,
                106602L,
                106603L,
                106604L,
                106652L,
                1730337570720L,
                1730337582224L,
                1730771944568L,
                1730772096482L
        ).forEach(id -> {
            ApiResponse<?> response = dealAPI.makeUpDelegate(new MakeUpDelegationPo().setOrderId(id).setUpTime(LocalDateTime.now()));
            System.out.println(response);
        });

        sleep(5000);
    }

    @Test
    public void testConfig() {
        ApiResponse<Integer> response = dealAPI.config();
        System.out.println(response);
    }

    @Test
    public void testClarity() {
        int clearing = dealAPI.clearing();
        System.out.println(clearing);
    }

    public static void main(String[] args) throws JsonProcessingException {
        String s = new ObjectMapper().writeValueAsString(new CreateChannelOpenDealPO());
        System.out.println(s);
    }

    @Test
    public void testSelOrderByEnabled() {
        ApiResponse<List<DealDTO>> response = dealAPI.selOrderByEnabled(new EnabledOrderPO().setAgentInvitationUserId(9095074090737248256L).setPage(1).setPageSize(20));
        System.out.println(response);
    }
}
