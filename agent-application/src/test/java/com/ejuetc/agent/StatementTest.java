package com.ejuetc.agent;

import com.ejuetc.agent.api.account.AccountApi;
import com.ejuetc.agent.api.query.QueryDomainAPI4Agent;
import com.ejuetc.agent.api.statement.*;
import com.ejuetc.agent.apiImpl.StatementAPIImpl;
import com.ejuetc.agent.dto.*;
import com.ejuetc.commons.base.querydomain.api.QueryDomainAPI;
import com.ejuetc.commons.base.querydomain.api.QueryResponse;
import com.ejuetc.commons.base.querydomain.api.QueryTerm;
import com.ejuetc.commons.base.querydomain.impl.QueryDomainApiImpl;
import com.ejuetc.commons.base.response.ApiResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.List;

import static com.ejuetc.agent.dto.StatementDTO.Action.AGREE;
import static com.ejuetc.agent.dto.StatementDTO.Action.REJECT;
import static com.ejuetc.agent.dto.StatementDTO.Role.*;
import static com.ejuetc.commons.base.spring.SpringUtil.getAPI;

//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = {AgentApplication.class})
public class StatementTest {

    //    @Autowired
//    private StatementAPIImpl statementAPI;
//    private StatementApi statementAPI = getAPI(StatementApi.class, "http://localhost:8096");
    private StatementApi statementAPI = getAPI(StatementApi.class, "http://ejuetc-agent.release.ejucloud.cn");
//    private StatementApi statementAPI = getAPI(StatementApi.class, "http://ejuetc-agent.tst.ejucloud.cn");

    //    @Autowired
    private QueryDomainApiImpl queryDomainAPI;

    @Test
    public void testInsertStatement() {
        ApiResponse<String> response = statementAPI.insertStatement();
        System.out.println(response);
    }

    @Test
    public void testFlowAction() {
        List.of(
                322251L,
                325401L,
                323451L,
                325301L,
                318701L,
                319051L,
                366051L,
                304901L,
                58051L).forEach(statementId -> {
            ApiResponse<StatementDTO> response = statementAPI.flowAction(new FlowActionPO()
                            .setStatementId(statementId)

//                            .setRole(OPERATOR)
//                            .setAction(AGREE)
//                            .setRemark("审核通过")
//                            .setOperatorName("手工触发")

//                            .setRole(OPERATOR)
//                            .setAction(AGREE)
//                            .setRemark("发起结算")
//                            .setOperatorName("手工触发")

//                            .setRole(USER)
//                            .setAction(AGREE)
//                            .setRemark("发起签约(调整税率到1%)")
//                            .setParam("0.01")
//                            .setParam("104302,104352,104306,104303,302,104304")
//                            .setOperatorId(System.currentTimeMillis())
//                            .setOperatorName("吴浩专用测试公司")

                            .setRole(OPERATOR)
                            .setAction(REJECT)
                            .setRemark("撤销签约")
                            .setOperatorName("手工发起")

//                            .setRole(USER)
//                            .setAction(AGREE)
//                            .setRemark("用户同意撤销合同")
//                            .setOperatorName("手工发起")


//                        .setRole(SYSTEM)
//                        .setAction(AGREE)
//                        .setRemark("签约成功")
//                        .setOperatorId(System.currentTimeMillis())
//                        .setOperatorName("E签宝通知")

//                        .setRole(USER)
//                        .setAction(AGREE)
//                        .setRemark("上传发票")
//                        .setParam("https://etc-agent.oss-cn-shanghai.aliyuncs.com/20240827200305_e404fc57-7e3e-41f7-98aa-7b4cee379a27.xlsx")
//                        .setOperatorId(System.currentTimeMillis())
//                        .setOperatorName("张三")

//                        .setRole(USER)
//                        .setAction(REJECT)
//                        .setRemark("发票税率不对")
//                        .setOperatorId(System.currentTimeMillis())
//                        .setOperatorName("张三")

//                        .setRole(OPERATOR)
//                        .setAction(REJECT)
//                        .setRemark("发票税率不对")
//                        .setOperatorId(System.currentTimeMillis())
//                        .setOperatorName("张三")

//                        .setRole(OPERATOR)
//                        .setAction(AGREE)
//                        .setRemark("发票审核通过")
//                        .setOperatorId(System.currentTimeMillis())
//                        .setOperatorName("张三")

//                        .setRole(OPERATOR)
//                        .setAction(AGREE)
//                        .setRemark("支付成功")
//                        .setOperatorId(System.currentTimeMillis())
//                        .setOperatorName("张三")

            );
            System.out.println(response);
        });
    }

    @Test
    public void testPersonalPayExcel() {
        WithdrawPO po = new WithdrawPO();
        po.setStatementIds(List.of(50751L));
        ApiResponse<WithdrawDTO> response = statementAPI.withdraw(po);
        System.out.println(response);
    }

    @Test
    public void testImportPensonalPay() {
        WithdrawImportPO po = new WithdrawImportPO();
        po.setFileName("银行卡交易订单-8c890A3p7p.xlsx");
        po.setFileUrl("https://fyoss-test.fangyou.com/file_2411271313ffd5bdf7bf949d1103bade90f7a19e5cd5f09e");
        ApiResponse<Long> response = statementAPI.importPensonalPay(po);
        System.out.println(response);
    }

    @Test
    public void testChargeOff() {
        ApiResponse<OaFinanceDTO> response = statementAPI.chargeOff(
                new ChargeOffPO()
//                .setOaFinanceId(50252L)
                        .setStatementIds(List.of(50751L))
                        .setCompanyName("爱美家（上海）房地产经纪有限公司")
                        .setCompanyNo("91310112MA1GCUKX8J")
                        .setContractNo("1233321")
                        .setBankNo("*****************")
                        .setStatementUrl("https://fyoss-test.fangyou.com/file_24110717bec6c45a7aa68956beb951fce112a351395a581a")
                        .setInvoiceUrl("https://fyoss-test.fangyou.com/file_24110717bec6c45a7aa68956beb951fce112a351395a581a")
                        .setInvoiceConfirmUrl("https://fyoss-test.fangyou.com/file_24110717bec6c45a7aa68956beb951fce112a351395a581a")
                        .setInvoicePreTax(new BigDecimal("580.94"))
                        .setInvoiceAfterTax(new BigDecimal("600"))
                        .setPrePayNo("123")
                        .setPrePayContractNo("1233333")
                        .setFeeType("合同请款")
                        .setPayAmount(new BigDecimal("100"))
                        .setUnCancelAmount(new BigDecimal("500"))
                        .setCancelAmount(new BigDecimal("400"))
                        .setRemark("1233")
                        .setApplyName("测试")
        );
        System.out.println(response);
    }

    @Test
    public void testQueryUnSendChargeOffStatement() {
//        ApiResponse<List<StatementDTO>> response = statementAPI.selPersonalPayDone(new SelPersonalPayDonePO().setStatementNo(null));
//        System.out.println(response.getData());
        OaFinanceDTO dto = queryDomainAPI.queryDTO(new OaFinanceDTO().setId(50352L).setStatements(List.of(new StatementDTO())));
        System.out.println(dto);
    }

    @Test
    public void testExecInvoice() {
        ApiResponse<StatementInvoiceDTO> response = statementAPI.execInvoice(149952L);
//        ApiResponse<StatementInvoiceDTO> response = statementAPI.execInvoice(52L);
        System.out.println(response);
    }
}
