package com.ejuetc.agent;

import com.ejuetc.agent.api.contract.*;
import com.ejuetc.agent.apiImpl.ContractAPIImpl;
import com.ejuetc.agent.domain.contract.ContractRpt;
import com.ejuetc.agent.dto.ContractDTO;
import com.ejuetc.agent.dto.DealDTO;
import com.ejuetc.agent.api.price.CreatePricePO;
import com.ejuetc.agent.dto.PriceDTO;
import com.ejuetc.agent.dto.StatementDTO;
import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.querydomain.api.QueryDomainAPI;
import com.ejuetc.commons.base.querydomain.api.QueryResponse;
import com.ejuetc.commons.base.querydomain.api.QueryTerm;
import com.ejuetc.commons.base.response.ApiResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static com.ejuetc.agent.dto.ContractDTO.Status.EFFECTIVE;
import static com.ejuetc.agent.dto.ContractDTO.SubType.*;
import static com.ejuetc.agent.dto.ContractDTO.Type.AGENT;
import static com.ejuetc.agent.dto.ContractDTO.Type.BROKER;
import static com.ejuetc.commons.base.spring.SpringUtil.getAPI;
import static java.time.LocalDateTime.now;
import static java.time.format.DateTimeFormatter.ofPattern;

//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = {AgentApplication.class})
public class ContractTest {

    //    @Autowired
//    private ContractAPIImpl contractAPI;
    private ContractApi contractAPI = getAPI(ContractApi.class, "http://ejuetc-agent.release.ejucloud.cn");
//    private ContractApi contractAPI = getAPI(ContractApi.class, "http://ejuetc-agent.uat.ejucloud.cn");
//    private ContractApi contractAPI = getAPI(ContractApi.class, "http://ejuetc-agent.tst.ejucloud.cn");
//    private ContractApi contractAPI = getAPI(ContractApi.class, "http://localhost:8096");

    //            @Autowired
//    private QueryDomainApiImpl queryDomainApi;
    private QueryDomainAPI queryDomainApi = getAPI(QueryDomainAPI.class, "http://ejuetc-agent.tst.ejucloud.cn");
//    private QueryDomainAPI queryDomainApi = getAPI(QueryDomainAPI.class, "http://ejuetc-agent.uat.ejucloud.cn");

    public static final Long userId = 1727412859073L;
    public static final Long operatorId = 1727412859073L;

    //    @Autowired
    private ContractRpt contractRpt;

    @Test
    public void testCreatePartner() {
        ApiResponse<ContractDTO> response = contractAPI.create(new CreatePartnerContractPO()
                        .setCommissionRate(new BigDecimal("0.2"))
                        .setUserId(9095354041470344960L)
                        .setOperatorId(9095354041470344960L)
//                .setInvitationCode("M8BJGF")
        );
        System.out.println(response);
//        }
    }


    @Test
    public void testCreateAgent() {
        List.of(
                9095691220224625933L
        ).forEach(userId -> {
            ApiResponse<ContractDTO> response = contractAPI.create(new CreateAgentContractPO()
//                            .setPricePOS(getPricePOS())
                            .setSubType(AGENT_NORMAL)
                            .setUserId(userId)
                            .setOperatorId(userId)
                            .setInvitationCode("505620")
                            .setCityCode("510100")
                            .setCityName("成都")
                            .setBeginTime(LocalDateTime.parse("2025-05-09 00:00:00", ofPattern("yyyy-MM-dd HH:mm:ss")))
                            .setSuppFlag(true)
            );
            System.out.println(response);
        });
    }


    @Test
    public void testCreateBroker() {
        long merchantId = System.currentTimeMillis();//1726625073047L;
        List.of(
                9091363472532366593L
        ).forEach(userId -> {
            ApiResponse<ContractDTO> response = contractAPI.create(new CreateBrokerContractPO()
                            .setUserId(userId)
                            .setOperatorId(userId)
                            .setCityCode("330100")
                            .setCityName("杭州")
                            .setInvitationCode("I57OAR")
//                            .setBeginTime(LocalDateTime.parse("2025-02-05 00:00:00", ofPattern("yyyy-MM-dd HH:mm:ss")))
//                        .setCityCode("410100")
//                        .setCityName("郑州")
//                        .setInvitationCode("JHJDQX")
            );
            System.out.println(response);
        });
    }

    private static List<CreatePricePO> getPricePOS() {
        String cityName = "杭州";
        String cityCode = "330100";
        return List.of(
                new CreatePricePO()
                        .setChannelCode(ChannelDTO.Code.XIANYU)
                        .setBusinessCode(BusinessOpenDTO.Code.SALE)
                        .setDealType(DealDTO.Type.CHANNEL_OPEN)
                        .setCityName(cityName)
                        .setCityCode(cityCode)
                        .setStandardPrice(new BigDecimal("800"))
                        .setAgentPrice(new BigDecimal("300"))
                        .setAgentDiscount(new BigDecimal("1"))
                        .setAgentDiscountPrice(new BigDecimal("300"))
                        .setNormalCommRate(new BigDecimal("1"))
                        .setExcessCommRatio(new BigDecimal("0.9"))
                ,
                new CreatePricePO()
                        .setChannelCode(ChannelDTO.Code.XIANYU)
                        .setBusinessCode(BusinessOpenDTO.Code.SALE)
                        .setDealType(DealDTO.Type.CLUE_CONSUME)
                        .setCityName(cityName)
                        .setCityCode(cityCode)
                        .setStandardPrice(new BigDecimal("9"))
                        .setAgentPrice(new BigDecimal("6"))
                        .setAgentDiscount(new BigDecimal("1"))
                        .setAgentDiscountPrice(new BigDecimal("6"))
                        .setNormalCommRate(new BigDecimal("1"))
                        .setExcessCommRatio(new BigDecimal("0.9"))
                ,
                new CreatePricePO()
                        .setChannelCode(ChannelDTO.Code.ALIPAY)
                        .setBusinessCode(BusinessOpenDTO.Code.SALE)
                        .setDealType(DealDTO.Type.CLUE_CONSUME)
                        .setCityName(cityName)
                        .setCityCode(cityCode)
                        .setStandardPrice(new BigDecimal("9"))
                        .setAgentPrice(new BigDecimal("6"))
                        .setAgentDiscount(new BigDecimal("1"))
                        .setAgentDiscountPrice(new BigDecimal("6"))
                        .setNormalCommRate(new BigDecimal("1"))
                        .setExcessCommRatio(new BigDecimal("0.9"))
                ,
                new CreatePricePO()
                        .setChannelCode(ChannelDTO.Code.XIANYU)
                        .setBusinessCode(BusinessOpenDTO.Code.RENT)
                        .setDealType(DealDTO.Type.CHANNEL_OPEN)
                        .setCityName(cityName)
                        .setCityCode(cityCode)
                        .setStandardPrice(new BigDecimal("600"))
                        .setAgentPrice(new BigDecimal("300"))
                        .setAgentDiscount(new BigDecimal("0.5"))
                        .setAgentDiscountPrice(new BigDecimal("150"))
                        .setNormalCommRate(new BigDecimal("1"))
                        .setExcessCommRatio(new BigDecimal("0.9"))
                ,
                new CreatePricePO()
                        .setChannelCode(ChannelDTO.Code.XIANYU)
                        .setBusinessCode(BusinessOpenDTO.Code.RENT)
                        .setDealType(DealDTO.Type.CLUE_CONSUME)
                        .setCityName(cityName)
                        .setCityCode(cityCode)
                        .setStandardPrice(new BigDecimal("4.5"))
                        .setAgentPrice(new BigDecimal("3.5"))
                        .setAgentDiscount(new BigDecimal("1"))
                        .setAgentDiscountPrice(new BigDecimal("3.5"))
                        .setNormalCommRate(new BigDecimal("1"))
                        .setExcessCommRatio(new BigDecimal("0.9"))
                ,
                new CreatePricePO()
                        .setChannelCode(ChannelDTO.Code.ALIPAY)
                        .setBusinessCode(BusinessOpenDTO.Code.RENT)
                        .setDealType(DealDTO.Type.CLUE_CONSUME)
                        .setCityName(cityName)
                        .setCityCode(cityCode)
                        .setStandardPrice(new BigDecimal("4.5"))
                        .setAgentPrice(new BigDecimal("3.5"))
                        .setAgentDiscount(new BigDecimal("1"))
                        .setAgentDiscountPrice(new BigDecimal("3.5"))
                        .setNormalCommRate(new BigDecimal("1"))
                        .setExcessCommRatio(new BigDecimal("0.9"))

        );
    }

    @Test
    public void agentSignResult() {
        ApiResponse<?> response = contractAPI.agentSignResult(
                99952L,
                true,
                "签约成功",
                "https://fyoss.fangyou.com/闲鱼房产端口火热售卖中.jpg"
        );
        System.out.println(response);
    }

    @Test
    public void queryContract() {
        QueryResponse<ContractDTO> response = queryDomainApi.query(new QueryTerm<>(new ContractDTO().setPrices(List.of(new PriceDTO())))
                .beginFieldsAnd("=", new ContractDTO().setUserId(userId).setType(AGENT).setStatus(EFFECTIVE))
        );
        System.out.println(response);
    }

    @Test
    public void testCheckInvitedCode() {
        ApiResponse<?> response = contractAPI.checkInvitedCode(new CheckInvitedCodePO()
                .setContractType(AGENT)
                .setUserId(9094431886960246020L)
                .setInvitedCode("68YQ3S")
                .setUpdate(true)
                .setCityCode("310100")
        );
        System.out.println(response);
    }

    @Test
    public void testQueryInvited() {
        ApiResponse<List<ContractDTO>> response = contractAPI.findInvitedBrokerContracts(new FindInvitedBrokerContractsPO()
                .setAgentOperatorId(9091903573527109120L)
                .setAgentUserId(null)
//                .setBrokerName("马哥")
                .setPageNum(1)
                .setPageSize(10));
        System.out.println(response);
        response.getData().forEach(System.out::println);
    }

    @Test
    public void testContractCrmResult() {
        ApiResponse<?> response = contractAPI.contractCrmResult(new CrmResultPo().setResultType(CrmResultPo.Type.CASH_OUT_PAY_RESULT).setQkNo("153").setRealAmount(new BigDecimal("1")).setAuditTime(LocalDateTime.now()).setSuccess(true));
        System.out.println(response);
    }

    @Test
    public void testFindInvitationCodeByExclusive() {
        ApiResponse<?> response = contractAPI.findInvitationCodeByExclusive("350500");
        System.out.println(response);
    }

    @Test
    public void esignCancel() {
        List.of(
                450812L
        ).forEach(signId -> {
            try {
                ApiResponse<?> response = contractAPI.esignCancel(signId, StatementDTO.Role.OPERATOR, "企业用户误用个人协议发起");
                System.out.println(response);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    @Test
    public void launchSupplESign() {
        List<Long> contractIds = List.of(
                199956L
        );
        ApiResponse<?> response = contractAPI.launchSupplESign(contractIds, 7L);
        System.out.println(response);
    }
}
