package com.ejuetc.agent;

import com.ejuetc.agent.integration.yzh.YzhApiResponse;
import com.ejuetc.agent.integration.yzh.YzhComponent;
import com.ejuetc.agent.integration.yzh.YzhComponentImpl;
import com.ejuetc.agent.integration.yzh.ro.YzhSignContractRO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@ActiveProfiles("test")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {AgentApplication.class})
public class YzhComponentTest {
    @Autowired
    private YzhComponent yzhComponent;

    @Test
    public void testBankCardVerify() {
        YzhApiResponse<Void> resp = yzhComponent.bankCardVerify("621901199901199901", "李四", "621901199901199901");
        System.out.println(resp);
    }

    @Test
    public void testPreSign() {
        YzhApiResponse<YzhSignContractRO> resp = yzhComponent.getSignContract();
        System.out.println(resp);
    }
}
