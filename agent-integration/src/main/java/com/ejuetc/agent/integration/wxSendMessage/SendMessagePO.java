package com.ejuetc.agent.integration.wxSendMessage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SendMessagePO {
    @Schema(description = "用户id")
    private Long userId;
    @Schema(description = "模板类型 1分配订单成功 2产品增加买了端口未发房push提示 3个人或公司当前有未签约的结算书时 4账户额度不足提醒")
    private Integer templateType;
    @Schema(description = "1：子订单id 3：结算书单号")
    private String orderId;
    @Schema(description = "1：订单状态 3：结算书状态")
    private String status;
    @Schema(description = "1：产品名称")
    private String orderName;
    @Schema(description = "温馨提示")
    private String remake;
    @Schema(description = "2：房源标题")
    private String title;
    @Schema(description = "3：订单详情")
    private String orderDetail;
    @Schema(description = "4：当前额度")
    private String amount;
}
