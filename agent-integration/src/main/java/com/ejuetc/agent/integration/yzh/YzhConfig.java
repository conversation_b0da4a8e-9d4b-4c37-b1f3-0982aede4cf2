package com.ejuetc.agent.integration.yzh;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "yzh")
@Data
public class YzhConfig {
    private String baseUrl;
    private String dealerId;
    private String brokerId;
    private String appKey;
    private String desKey;
    private String publicKey;
    private String ejuPublicKey;
    private String ejuPrivateKey;

    public com.yunzhanghu.sdk.base.YzhConfig toSDKConfig() {
        com.yunzhanghu.sdk.base.YzhConfig config = new com.yunzhanghu.sdk.base.YzhConfig();
        config.setDealerId(dealerId);
        config.setBrokerId(brokerId);
        config.setYzhAppKey(appKey);
        config.setYzh3DesKey(desKey);
        config.setYzhRsaPrivateKey(ejuPrivateKey);
        config.setYzhRsaPublicKey(ejuPublicKey);
        config.setSignType(com.yunzhanghu.sdk.base.YzhConfig.SignType.RSA);
        config.setYzhUrl(baseUrl);
        return config;
    }
}
