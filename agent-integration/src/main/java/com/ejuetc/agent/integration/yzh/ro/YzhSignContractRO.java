package com.ejuetc.agent.integration.yzh.ro;

import com.yunzhanghu.sdk.apiusersign.domain.ApiUserSignContractResponse;
import lombok.Data;

@Data
public class YzhSignContractRO {
    private String title;
    private String url;

    public static YzhSignContractRO of(ApiUserSignContractResponse response) {
        if (response == null) {
            return null;
        }
        YzhSignContractRO ro = new YzhSignContractRO();
        ro.setTitle(response.getTitle());
        ro.setUrl(response.getUrl());
        return ro;
    }
}
