package com.ejuetc.agent.integration.yzh;

public enum YzhCardType {
    身份证(0, "idcard"),
    台湾身份证(1, "twidcard"),
    港澳居民来往内地通行证(2, "mtphkm"),
    护照(3, "passport"),
    台湾居民来往大陆通行证(5, "mtpt"),
    港澳居民居住证(9, "rphkm"),
    台湾居民居住证(10, "rpt"),
    外国人永久居留身份证(11, "fpr"),
    外国人工作许可证(12, "ffwp");

    public final int code;
    public final String codeStr;

    YzhCardType(int code, String codeStr) {
        this.code = code;
        this.codeStr = codeStr;
    }
}
