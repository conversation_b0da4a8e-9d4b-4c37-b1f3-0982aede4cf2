package com.ejuetc.agent.integration.yzh;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yunzhanghu.sdk.base.YzhResponse;
import lombok.Data;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@Data
public class YzhApiResponse<T> {
    private String code;
    private String message;
    @JsonProperty("request_id")
    private String requestId;
    // 业务数据
    private T data;

    public boolean isSucc() {
        return YzhResponseCode.SUCCESS.eq(code);
    }

    public static <T> YzhApiResponse<T> of(YzhResponse<?> response, T data) {
        YzhApiResponse<T> apiResponse = new YzhApiResponse<>();
        apiResponse.setCode(response.getCode());
        apiResponse.setMessage(response.getMessage());
        apiResponse.setRequestId(response.getRequestId());
        apiResponse.setData(data);
        return apiResponse;
    }

    public static YzhApiResponse<Void> ofException(Exception e) {
        YzhApiResponse<Void> apiResponse = new YzhApiResponse<>();
        apiResponse.setCode("9999");
        apiResponse.setMessage(e.getMessage());
        return apiResponse;
    }
}
