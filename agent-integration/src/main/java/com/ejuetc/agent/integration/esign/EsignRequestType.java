package com.ejuetc.agent.integration.esign;

import org.apache.http.client.methods.*;

/**
 * @description 请求类型
 * <AUTHOR>
 * @since JDK1.7
 */
public enum EsignRequestType {

	POST{
		@Override
		public HttpRequestBase getHttpType(String url) {
			return new HttpPost(url);
		}
	},
	GET{
		@Override
		public HttpRequestBase getHttpType(String url) {
			return new HttpGet(url);
		}
	},
	DELETE{
		@Override
		public HttpRequestBase getHttpType(String url) {
			return new HttpDelete(url);
		}
	},
	PUT{
		@Override
		public HttpRequestBase getHttpType(String url) {
			return new HttpPut(url);
		}
	},
	;

   public abstract HttpRequestBase getHttpType(String url);
}
