package com.ejuetc.agent.integration.yzh;

import lombok.SneakyThrows;

import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

public class YzhUtil {
    private static final String ALGORITHM = "DESede";

    @SneakyThrows
    public static String sign(String content, String privateKeyPem) {
        byte[] encodedKey = privateKeyPem.getBytes();
        encodedKey = org.bouncycastle.util.encoders.Base64.decode(encodedKey);
        PrivateKey privateKey = KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(encodedKey));
        Signature signature = Signature.getInstance("SHA256WithRSA");
        signature.initSign(privateKey);
        signature.update(content.getBytes(StandardCharsets.UTF_8));
        byte[] signed = signature.sign();
        return new String(org.bouncycastle.util.encoders.Base64.encode(signed));
    }

    @SneakyThrows
    public static String signHmac(String content, String privateKey) {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKey = new SecretKeySpec(privateKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        mac.init(secretKey);
        return byteArrayToHexString(mac.doFinal(content.getBytes()));
    }

    private static String byteArrayToHexString(byte[] b) {
        StringBuilder hs = new StringBuilder();
        for(int n = 0; b != null && n < b.length; ++n) {
            String stmp = Integer.toHexString(b[n] & 255);
            if (stmp.length() == 1) {
                hs.append('0');
            }
            hs.append(stmp);
        }
        return hs.toString().toLowerCase();
    }

    @SneakyThrows
    public static boolean verify(String content, String sign, String publicKeyPem) {
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        byte[] encodedKey = publicKeyPem.getBytes();
        encodedKey = org.bouncycastle.util.encoders.Base64.decode(encodedKey);
        PublicKey publicKey = keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
        Signature signature = Signature.getInstance("SHA256WithRSA");
        signature.initVerify(publicKey);
        signature.update(content.getBytes(StandardCharsets.UTF_8));
        return signature.verify(org.bouncycastle.util.encoders.Base64.decode(sign.getBytes()));
    }

    @SneakyThrows
    public static boolean verifyHmac(String content, String sign, String publicKey) {
        return signHmac(content, publicKey).equals(sign);
    }

    /**
     * 3DES加密
     *
     * @param data 数据
     * @param yzh3DesKey 密钥
     * @return String 加密后数据
     * @throws Exception
     */
    @SneakyThrows
    public static String encrypt(String data, String yzh3DesKey) {
        byte[] content = data.getBytes(StandardCharsets.UTF_8);
        byte[] key = yzh3DesKey.getBytes(StandardCharsets.UTF_8);
        byte[] enc = tripleDesEncrypt(content, key);
        byte[] enc64 = Base64.getEncoder().encode(enc);
        return new String(enc64);
    }

    /**
     * 3DES解密
     *
     * 解密报错 javax.crypto.IllegalBlockSizeException: Input length must be multiple of 8 when decrypting with padded cipher
     * 不需要进行urldecode，使用如下解密代码
     * byte[] dec64 = Base64.decodeBase64(notifyResponse.getData());
     *
     * @param data 加密数据
     * @param yzh3DesKey 密钥
     * @return String 解密后数据
     * @throws Exception
     */
    @SneakyThrows
    public static String decrypt(String data, String yzh3DesKey) {
        byte[] dec64 = Base64.getDecoder().decode(data);
        byte[] dec = tripleDesDecrypt(dec64, yzh3DesKey.getBytes(StandardCharsets.UTF_8));
        return new String(dec);
    }

    public static byte[] tripleDesEncrypt(byte[] content, byte[] key) throws Exception {
        byte[] icv = new byte[8];
        System.arraycopy(key, 0, icv, 0, 8);
        return tripleDesEncrypt(content, key, icv);
    }

    private static byte[] tripleDesEncrypt(byte[] content, byte[] key, byte[] icv) throws Exception {
        final SecretKey secretKey = new SecretKeySpec(key, "DESede");
        final Cipher cipher = Cipher.getInstance("DESede/CBC/PKCS5Padding");
        final IvParameterSpec iv = new IvParameterSpec(icv);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);
        return cipher.doFinal(content);
    }

    private static byte[] tripleDesDecrypt(byte[] content, byte[] key) throws Exception {
        byte[] icv = new byte[8];
        System.arraycopy(key, 0, icv, 0, 8);
        return tripleDesDecrypt(content, key, icv);
    }

    private static byte[] tripleDesDecrypt(byte[] content, byte[] key, byte[] icv) throws Exception {
        final SecretKey secretKey = new SecretKeySpec(key, "DESede");
        final Cipher cipher = Cipher.getInstance("DESede/CBC/PKCS5Padding");
        final IvParameterSpec iv = new IvParameterSpec(icv);
        cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);
        return cipher.doFinal(content);
    }
}
