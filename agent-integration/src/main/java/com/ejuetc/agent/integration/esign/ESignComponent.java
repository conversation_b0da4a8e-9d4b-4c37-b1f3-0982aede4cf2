package com.ejuetc.agent.integration.esign;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.exception.CodingException;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.TreeMap;

import static com.ejuetc.agent.integration.esign.EsignRequestType.GET;
import static com.ejuetc.agent.integration.esign.EsignRequestType.POST;
import static com.ejuetc.commons.base.spring.SpringUtil.getProperty;

@NoArgsConstructor
@Component
public class ESignComponent {
    @Value("${ejuetc.agent.esign.appId:7439035509}")
    private String eSignAppId;

    @Value("${ejuetc.agent.esign.appSecret:f621269df2ed50d22153a3aecbe0699e}")
    private String eSignAppSecret;

    @Value("${ejuetc.agent.esign.host:https://smlopenapi.esign.cn}")
    private String eSignHost = getProperty("ejuetc.agent.esign.host");


    public ESignComponent(String eSignAppId, String eSignAppSecret, String eSignHost) {
        this.eSignAppId = eSignAppId;
        this.eSignAppSecret = eSignAppSecret;
        this.eSignHost = eSignHost;
    }

    public JSONObject call(String api) {
        return call(api, null);
    }

    public JSONObject call(String api, Object body) {
        String bodyStr = body == null
                ? null
                : body instanceof String str
                ? str
                : JSON.toJSONString(body);
        EsignRequestType method = bodyStr != null ? POST : GET;
        EsignHttpResponse response;
        try {
            Map<String, String> header = EsignHttpHelper.signAndBuildSignAndJsonHeader(eSignAppId, eSignAppSecret, bodyStr, method.name(), api, true);
            response = EsignHttpHelper.doCommHttp(eSignHost, api, method, bodyStr, header, true);
        } catch (Exception t) {
            throw new CodingException("请求E签宝异常[" + api + "]\nbodyStr:" + bodyStr + "\n", t);
        }
        if (response.getStatus() != 200)
            throw new CodingException("请求E签宝HTTP应答码异常[" + api + "][" + response.getStatus() + "]\nbodyStr:" + bodyStr + "\n");

        JSONObject responseBody = JSON.parseObject(response.getBody());
        if (responseBody.getInteger("code") != 0) {
            throw new BusinessException("bc.ejuetc.agent.1004", responseBody.getString("message"));
        }

        return responseBody.getJSONObject("data");
    }


    public boolean checkSign(Map<String, String> headers, Map<String, String> params, String body) {
        Map<String, String> sortedHeaders = new TreeMap<>(params);
        StringBuilder query = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedHeaders.entrySet()) {
            query.append(entry.getValue());
        }
        return EsignEncryption.callBackCheck(
                headers.get("x-tsign-open-timestamp"),
                query.toString(),
                body,
                eSignAppSecret,
                headers.get("x-tsign-open-signature")
        );
    }
}
