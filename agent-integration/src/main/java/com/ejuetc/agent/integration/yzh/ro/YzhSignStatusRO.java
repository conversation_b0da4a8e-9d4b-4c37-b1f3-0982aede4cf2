package com.ejuetc.agent.integration.yzh.ro;

import com.ejuetc.commons.base.utils.DateTimeUtils;
import com.yunzhanghu.sdk.apiusersign.domain.GetApiUserSignStatusResponse;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@NoArgsConstructor
@Data
public class YzhSignStatusRO {
    public enum Status {
        NOT_SIGNED("0", "未签约"),
        SIGNED("1", "已签约"),
        RELEASED("2", "已解约");

        public final String code;
        public final String title;

        Status(String code, String title) {
            this.code = code;
            this.title = title;
        }

        public static Status of(String code) {
            for (Status status : Status.values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    public LocalDateTime signedAt;
    private Status status;

    public static YzhSignStatusRO of(GetApiUserSignStatusResponse response) {
        if (response == null) {
            return null;
        }

        YzhSignStatusRO ro = new YzhSignStatusRO();
        ro.setSignedAt(DateTimeUtils.localDateTime(response.getSignedAt()));
        ro.setStatus(Status.of(response.getStatus()));
        return ro;
    }
}
