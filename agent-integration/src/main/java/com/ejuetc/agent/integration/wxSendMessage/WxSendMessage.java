package com.ejuetc.agent.integration.wxSendMessage;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Tag(name = "微信发短信")
public interface WxSendMessage {

    @Operation(summary = "推送微信模板消息")
    @PostMapping("/v2/wechat-online/message/fy/x/anony/x/fy/sendMessage")
    void sendMessage(@RequestBody SendMessagePO sendMessagePO);
}
