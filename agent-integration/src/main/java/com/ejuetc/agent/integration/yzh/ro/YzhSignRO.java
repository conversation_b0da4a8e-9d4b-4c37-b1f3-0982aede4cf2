package com.ejuetc.agent.integration.yzh.ro;

import com.yunzhanghu.sdk.apiusersign.domain.ApiUserSignResponse;
import lombok.Data;

import java.util.Objects;

@Data
public class YzhSignRO {
    private String status;

    public static YzhSignRO of(ApiUserSignResponse response) {
        if (response == null) {
            return null;
        }
        YzhSignRO ro = new YzhSignRO();
        ro.setStatus(response.getStatus());
        return ro;
    }

    public boolean isSucc() {
        return Objects.equals("ok", status);
    }
}
