package com.ejuetc.agent.integration.yzh.ro;

import com.yunzhanghu.sdk.apiusersign.domain.ApiUserSignReleaseResponse;
import com.yunzhanghu.sdk.apiusersign.domain.ApiUserSignResponse;
import lombok.Data;

import java.util.Objects;

@Data
public class YzhSignReleaseRO {
    private String status;

    public static YzhSignReleaseRO of(ApiUserSignReleaseResponse response) {
        if (response == null) {
            return null;
        }
        YzhSignReleaseRO ro = new YzhSignReleaseRO();
        ro.setStatus(response.getStatus());
        return ro;
    }

    public boolean isSucc() {
        return Objects.equals("ok", status);
    }
}
