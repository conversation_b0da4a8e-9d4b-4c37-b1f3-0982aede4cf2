package com.ejuetc.agent.integration.yzh;

import com.alibaba.fastjson.JSON;
import com.ejuetc.agent.integration.yzh.ro.YzhSignContractRO;
import com.ejuetc.agent.integration.yzh.ro.YzhSignRO;
import com.ejuetc.agent.integration.yzh.ro.YzhSignReleaseRO;
import com.ejuetc.agent.integration.yzh.ro.YzhSignStatusRO;
import com.yunzhanghu.sdk.apiusersign.ApiUserSignServiceClient;
import com.yunzhanghu.sdk.apiusersign.domain.*;
import com.yunzhanghu.sdk.authentication.AuthenticationClient;
import com.yunzhanghu.sdk.authentication.domain.BankCardThreeVerifyRequest;
import com.yunzhanghu.sdk.authentication.domain.BankCardThreeVerifyResponse;
import com.yunzhanghu.sdk.base.YzhRequest;
import com.yunzhanghu.sdk.base.YzhResponse;
import io.netty.handler.codec.CodecException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Slf4j
@Component
public class YzhComponentImpl implements YzhComponent {
    private final YzhConfig config;

    public YzhComponentImpl(YzhConfig config) {
        this.config = config;
    }

    public YzhApiResponse<YzhSignContractRO> getSignContract() {
        // 配置基础信息
        com.yunzhanghu.sdk.base.YzhConfig config = this.config.toSDKConfig();
        ApiUserSignServiceClient client = new ApiUserSignServiceClient(config);
        ApiUserSignContractRequest request = new ApiUserSignContractRequest();

        request.setDealerId(config.getDealerId());
        request.setBrokerId(config.getBrokerId());
        try {
            YzhRequest<ApiUserSignContractRequest> req = YzhRequest.build(UUID.randomUUID().toString(), request);
            log.info("yzh get sign contract, req={}", JSON.toJSONString(req));
            YzhResponse<ApiUserSignContractResponse> response = client.apiUserSignContract(req);
            log.info("yzh get sign contract response, response={}", JSON.toJSONString(response));
            return YzhApiResponse.of(response, YzhSignContractRO.of(response.getData()));
        } catch (Exception e) {
            log.error("yzh get sign contract error", e);
            throw new CodecException("云账户获取协议失败");
        }
    }

    public YzhApiResponse<YzhSignRO> userSign(String realName, String idCard) {
        com.yunzhanghu.sdk.base.YzhConfig config = this.config.toSDKConfig();
        ApiUserSignServiceClient client = new ApiUserSignServiceClient(config);
        ApiUserSignRequest request = new ApiUserSignRequest();
        request.setDealerId(config.getDealerId());
        request.setBrokerId(config.getBrokerId());
        request.setRealName(realName);
        request.setIdCard(idCard);
        request.setCardType(YzhCardType.身份证.codeStr);
        try {
            YzhRequest<ApiUserSignRequest> req = YzhRequest.build(UUID.randomUUID().toString(), request);
            log.info("yzh sign, req={}", JSON.toJSONString(req));
            YzhResponse<ApiUserSignResponse> response = client.apiUserSign(req);
            log.info("yzh sign response, response={}", JSON.toJSONString(response));
            return YzhApiResponse.of(response, YzhSignRO.of(response.getData()));
        } catch (Exception e) {
            log.error("yzh sign error", e);
            throw new CodecException("云账户签约失败");
        }
    }

    public YzhApiResponse<YzhSignStatusRO> getUserSignStatus(String realName, String idCard) {
        com.yunzhanghu.sdk.base.YzhConfig config = this.config.toSDKConfig();
        ApiUserSignServiceClient client = new ApiUserSignServiceClient(config);
        GetApiUserSignStatusRequest request = new GetApiUserSignStatusRequest();
        request.setDealerId(config.getDealerId());
        request.setBrokerId(config.getBrokerId());
        request.setRealName(realName);
        request.setIdCard(idCard);
        try {
            YzhRequest<GetApiUserSignStatusRequest> req = YzhRequest.build(UUID.randomUUID().toString(), request);
            log.info("yzh get sign status, req={}", JSON.toJSONString(req));
            YzhResponse<GetApiUserSignStatusResponse> response = client.getApiUserSignStatus(req);
            log.info("yzh get sign status response, response={}", JSON.toJSONString(response));
            return YzhApiResponse.of(response, YzhSignStatusRO.of(response.getData()));
        } catch (Exception e) {
            log.error("yzh get sign status error", e);
            throw new CodecException("云账户获取签约状态失败");
        }
    }

    public YzhApiResponse<YzhSignReleaseRO> userSignRelease(String realName, String idCard) {
        com.yunzhanghu.sdk.base.YzhConfig config = this.config.toSDKConfig();
        ApiUserSignServiceClient client = new ApiUserSignServiceClient(config);
        ApiUserSignReleaseRequest request = new ApiUserSignReleaseRequest();
        request.setDealerId(config.getDealerId());
        request.setBrokerId(config.getBrokerId());
        request.setRealName(realName);
        request.setIdCard(idCard);
        request.setCardType(YzhCardType.身份证.codeStr);
        try {
            YzhRequest<ApiUserSignReleaseRequest> req = YzhRequest.build(UUID.randomUUID().toString(), request);
            log.info("yzh release sign, req={}", JSON.toJSONString(req));
            YzhResponse<ApiUserSignReleaseResponse> response = client.apiUserSignRelease(req);
            log.info("yzh release sign response, response={}", JSON.toJSONString(response));
            return YzhApiResponse.of(response, YzhSignReleaseRO.of(response.getData()));
        } catch (Exception e) {
            log.error("yzh release sign error", e);
            throw new CodecException("云账户解约失败");
        }
    }

    public YzhApiResponse<Void> bankCardVerify(String cardId, String realName, String cardNo) {
        com.yunzhanghu.sdk.base.YzhConfig config = this.config.toSDKConfig();
        AuthenticationClient client = new AuthenticationClient(config);
        BankCardThreeVerifyRequest request = new BankCardThreeVerifyRequest();
        request.setCardNo(cardNo);
        request.setIdCard(cardId);
        request.setRealName(realName);
        try {
            YzhRequest<BankCardThreeVerifyRequest> req = YzhRequest.build(UUID.randomUUID().toString(), request);
            log.info("yzh bankCardVerify, req={}", JSON.toJSONString(req));
            YzhResponse<BankCardThreeVerifyResponse> response = client.bankCardThreeVerify(req);
            log.info("yzh bankCardVerify response, response={}", JSON.toJSONString(response));
            return YzhApiResponse.of(response, null);
        } catch (Exception e) {
            log.error("yzh bankCardVerify error", e);
            throw new CodecException("云账户验证失败");
        }
    }
}
