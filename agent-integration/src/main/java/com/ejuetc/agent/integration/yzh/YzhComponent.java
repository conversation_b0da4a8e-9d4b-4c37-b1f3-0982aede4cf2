package com.ejuetc.agent.integration.yzh;

import com.ejuetc.agent.integration.yzh.ro.YzhSignContractRO;
import com.ejuetc.agent.integration.yzh.ro.YzhSignRO;
import com.ejuetc.agent.integration.yzh.ro.YzhSignReleaseRO;
import com.ejuetc.agent.integration.yzh.ro.YzhSignStatusRO;

public interface YzhComponent {
    YzhApiResponse<Void> bankCardVerify(String cardId, String accountName, String accountNO);

    YzhApiResponse<YzhSignContractRO> getSignContract();

    YzhApiResponse<YzhSignRO> userSign(String realName, String idCard);

    YzhApiResponse<YzhSignStatusRO> getUserSignStatus(String realName, String idCard);

    YzhApiResponse<YzhSignReleaseRO> userSignRelease(String realName, String idCard);
}
