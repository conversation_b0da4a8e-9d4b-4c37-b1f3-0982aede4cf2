package com.ejuetc.saasapi.sdk;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import feign.Feign;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.support.SpringMvcContract;
import org.springframework.context.annotation.Bean;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Stream;

import static com.fasterxml.jackson.databind.DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.time.Duration.between;
import static java.time.LocalDateTime.now;
import static java.time.LocalDateTime.parse;
import static java.time.format.DateTimeFormatter.ofPattern;
import static java.util.stream.Collectors.toMap;

@Getter
@Slf4j
@Setter
public class SaaSApiSDK {
    //请求业务头信息
    public static final String HEADER_REQUEST_ID = "etc-request-id";
    public static final String HEADER_NOTIFY_ID = "etc-notify-id";
    public static final String HEADER_KEY_CODE = "etc-key-code";
    public static final String HEADER_API_CODE = "etc-api-code";
    public static final String HEADER_TIME = "etc-time";
    public static final String HEADER_SIGN = "etc-sign";
    public static final String HEADER_FEIGN_URL = "etc-feign-url";
    public static final String HEADER_FEIGN_METHOD = "etc-feign-method";

    //对内转发头信息
    public static final String HEADER_TRACE_NO = "etc-trace-no";
    public static final String HEADER_USER_ID = "etc-user-id";//所属用户ID

    //废弃字段
    public static final String HEADER_REQUEST_TIME = "etc-request-time";//废弃字段(替换为HEADER_TIME)
    public static final String HEADER_MERCHANT_ID = "etc-merchant-id";//废弃字段(替换为HEADER_USER_ID)

    //特殊接口代码
    public static final String API_CODE_FEIGN_CLIENT_CALL = "FeignClientCall";

    //非参数常量
    public static final String ALGORITHM = "SHA-256";
    public static final String NOTIFY_SUCCESS = "RECEIVE_NOTIFY_SUCCESS";
    public static final DateTimeFormatter DATE_TIME_FORMATTER = ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final RestTemplate REST_TEMPLATE = new RestTemplate(List.of(new StringHttpMessageConverter(StandardCharsets.UTF_8)));

    private final String keyCode;
    private final String keySecret;
    private final String host;//域名+上下文根
    private final String invokeUrl;

    public SaaSApiSDK(String keyCode, String keySecret, String host) {
        this.keyCode = keyCode;
        this.keySecret = keySecret;
        this.host = host;
        this.invokeUrl = host + (host.endsWith("/") ? "" : "/") + "gateway/invoke";
    }

    @SneakyThrows
    public ResponseEntity<String> uploadFile(String requestId, String filePath) {
        File file = new File(filePath);
        HttpHeaders headers = makeHeaders(
                Map.of(
                        HEADER_API_CODE, "uploadFile",
                        HEADER_REQUEST_ID, requestId
                ),
                Collections.emptyMap(),
                file.getName()
        );
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", new FileSystemResource(file));
        HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(body, headers);

        log.info("uploadFile request:\n{}", entity);
        ResponseEntity<String> responseEntity = REST_TEMPLATE.exchange(invokeUrl, HttpMethod.POST, entity, String.class);
        log.info("uploadFile response:\n{}", responseEntity.getBody());
        return responseEntity;
    }

    @SneakyThrows
    public ResponseEntity<String> businessRequest(String apiCode, String requestId, String httpBody) {
        HttpHeaders headers = makeHeaders(
                Map.of(
                        HEADER_API_CODE, apiCode,
                        HEADER_REQUEST_ID, requestId),
                Collections.emptyMap(),
                httpBody
        );
        HttpEntity<String> entity = new HttpEntity<>(httpBody, headers);
        log.info("businessRequest request:\n{}", entity);
        ResponseEntity<String> responseEntity = REST_TEMPLATE.exchange(invokeUrl, HttpMethod.POST, entity, String.class);
        log.info("businessRequest response:\n{}", responseEntity.getBody());
        return responseEntity;
    }

    public HttpHeaders makeHeaders(Map<String, String> headersMap, Map<String, String[]> params, String httpBody) {
        Map<String, String> headers = new HashMap<>(headersMap);
        headers.compute(HEADER_KEY_CODE, (k, v) -> v == null ? keyCode : v);
        headers.compute(HEADER_REQUEST_ID, (k, v) -> v == null ? UUID.randomUUID().toString() : v);
        headers.compute(HEADER_TIME, (k, v) -> v == null ? now().format(DATE_TIME_FORMATTER) : v);
        headers.put(HEADER_SIGN, calcSign(headers, params, httpBody, keySecret));
        log.info("httpHeader:\n{}", headers);

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setAll(headers);
        return httpHeaders;
    }

    @SneakyThrows
    public static String calcSign(Map<String, String> headers, Map<String, String[]> params, String body, String key) {
        log.trace("1,原始数据 headers:{} body:{} key:{}", headers, body, key);
        // 1.1 对除sign外所有http header参数，根据参数名称的ASCII码表的顺序排序
        Map<String, String> sortedHeaders = new TreeMap<>();
        headers.forEach((k, v) -> {
            if (k.startsWith("etc-") && !k.equals(HEADER_SIGN))
                sortedHeaders.put(k, v);
        });
        log.trace("2,排序headers:{}", sortedHeaders);

        // 1.2 将排序好的参数名和参数值拼装在一起，再拼装http请求体和key密码
        StringBuilder signatureString = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedHeaders.entrySet()) {
            signatureString.append(entry.getKey()).append(entry.getValue());
        }

        // 2 将url参数根据根据相同的方式追加字符串
        Map<String, String[]> sortedParams = new TreeMap<>(params);
        for (Map.Entry<String, String[]> entry : sortedParams.entrySet()) {
            signatureString.append(entry.getKey());
            Stream.of(entry.getValue()).forEach(signatureString::append);
        }

        // 3. 追加body和秘钥
        signatureString.append(body).append(key);
        log.debug("3,报文拼接:{}", signatureString);

        // 4. 取得utf-8字节码 -> SHA256摘要 -> base64编码
        byte[] utf8Byte = signatureString.toString().getBytes(UTF_8);
        byte[] sha256Digest = MessageDigest.getInstance(ALGORITHM).digest(utf8Byte);
        String base64Code = Base64.getEncoder().encodeToString(sha256Digest);
        log.debug("4,签名结果:{}", base64Code);
        return base64Code;
    }

    public static String checkSign(Map<String, String> headers, Map<String, String[]> params, String body, String secret) {
        LocalDateTime time = parse(getTime(headers), DATE_TIME_FORMATTER);
        Duration duration = between(now(), time);
        if (Math.abs(duration.toSeconds()) > 300)
            return "请求已过期";

        String receiveSign = headers.get(HEADER_SIGN);
        String calcSign = calcSign(headers, params, body, secret);
        if (!calcSign.equals(receiveSign))
            return "签名验证失败";

        return null;
    }

    // 向后兼容方法
    public static String checkSign(Map<String, String> headers, String body, String secret) {
        return checkSign(headers, Collections.emptyMap(), body, secret);
    }

    // 向后兼容方法
    public static String calcSign(Map<String, String> headers, String body, String key) {
        return calcSign(headers, Collections.emptyMap(), body, key);
    }

    public static String getTime(Map<String, String> headers) {
        String time = headers.get(HEADER_TIME);
        if (time == null) time = headers.get(HEADER_REQUEST_TIME);
        return time;
    }

    public <T> T feignClient(Class<T> apiType, String apiServerUrl) {
        ObjectMapper objectMapper = new Jackson2ObjectMapperBuilder()
                .featuresToEnable(ACCEPT_EMPTY_STRING_AS_NULL_OBJECT)
                .serializerByType(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .serializerByType(LocalDate.class, new LocalDateSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                .serializerByType(LocalTime.class, new LocalTimeSerializer(DateTimeFormatter.ofPattern("HH:mm:ss")))
                .deserializerByType(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .deserializerByType(LocalDate.class, new LocalDateDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                .deserializerByType(LocalTime.class, new LocalTimeDeserializer(DateTimeFormatter.ofPattern("HH:mm:ss")))
                .build();


        return Feign.builder()
                .encoder(new JacksonEncoder(objectMapper))
                .decoder(new JacksonDecoder(objectMapper))
                .contract(new SpringMvcContract())
                .requestInterceptor(template -> {
                    Map<String, String[]> params = template.queries().entrySet().stream().collect(toMap(
                            Map.Entry::getKey,
                            e -> e.getValue().stream().map(v -> URLDecoder.decode(v, UTF_8)).toArray(String[]::new)));
                    Map<String, String> headers = Map.of(
                            HEADER_API_CODE, API_CODE_FEIGN_CLIENT_CALL,
                            HEADER_FEIGN_URL, apiServerUrl + template.path(),
                            HEADER_FEIGN_METHOD, template.method()
                    );
                    String body = template.body() != null ? new String(template.body(), UTF_8) : "";
                    HttpHeaders header = makeHeaders(headers, params, body);
                    header.forEach(template::header);
                    template.header("Content-Type", "application/json; charset=UTF-8");
                })
                .target(apiType, this.host);
    }

    @Bean
    public SaaSApiSDK saaSApiSDK() {
        return new SaaSApiSDK(keyCode, keySecret, invokeUrl);
    }
}