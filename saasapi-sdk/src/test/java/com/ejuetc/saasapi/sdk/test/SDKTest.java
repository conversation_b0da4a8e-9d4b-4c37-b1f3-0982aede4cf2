package com.ejuetc.saasapi.sdk.test;

import com.ejuetc.saasapi.sdk.SaaSApiSDK;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;

import java.security.NoSuchAlgorithmException;
import java.util.Map;

import static java.util.UUID.randomUUID;

@Slf4j
@SpringBootTest
public class SDKTest {
    //        SaaSApiSDK sdk = new SaaSApiSDK(
//                "c90982b1474641c5b9ad10724f4f8cd4",
//                "gcuTerMoadFj8nQkFP7U3i84BZu5shoDBO0xkxB3ehI=",
//                "http://saasapi-test.ebaas.com/gateway/invoke"
//        );
    SaaSApiSDK sdk = new SaaSApiSDK(
            "cd0badf8c664484a9500e3f1a5351623",
            "RDX6D3bPTwIReEmi06D0PJz9VJ2WthhTmj2WYs7fXdI=",
            "http://localhost:8095"
//            "http://saasapi-test.ebaas.com"
    );

    @Test
    public void queryBeikeCommunity() throws NoSuchAlgorithmException {
        ResponseEntity<String> resp = sdk.businessRequest(
                "consumer-community-queryBeikeCommunity",
                randomUUID().toString(),
                """
                        {
                        	"pageSize":5,
                        	"cityId":310100,
                        	"keyword":"慧芝湖",
                        	"pageNum":1
                        }""");
        System.out.println(resp);
    }

    @Test
    public void queryLayoutGroup() throws NoSuchAlgorithmException {
        ResponseEntity<String> resp = sdk.businessRequest(
                "consumer-community-queryLayoutGroup",
                randomUUID().toString(),
                """
                        {
                          "beikeId": 5020069179760703
                        }
                        """);
        System.out.println(resp);
    }

    @Test
    public void queryDetail() throws NoSuchAlgorithmException {
        ResponseEntity<String> resp = sdk.businessRequest(
                "consumer-community-queryDetail",
                randomUUID().toString(),
                """
                        {
                            "beikeId": 5020069179760703
                        }
                        """);
        System.out.println(resp);
    }

}