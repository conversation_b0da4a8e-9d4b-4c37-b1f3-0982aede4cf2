package com.ejuetc.saasapi.dto.gateway;

import com.alibaba.fastjson.JSONObject;
import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.saasapi.domain.gateway.notify.Notify")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "通知商户对象")
public class NotifyDTO extends BaseDTO<NotifyDTO> {
    @Getter
    public enum Status implements TitleEnum {
        INIT("初始"),
        SUCC("成功"),
        FAIL("失败"),
        RETRY("带重试"),
        ;

        private final String title;

        Status(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }


    @QueryField
    @Schema(description = "所属用户号")
    private Long userId;

    @QueryField
    @Schema(description = "API代码")
    private String apiCode;

    @QueryField
    @Schema(description = "通知号")
    private String notifyId;

    @QueryField
    @Schema(description = "通知报文")
    private JSONObject notifyBody;

    @QueryField
    @Schema(description = "应答码")
    private Integer responseCode;

    @QueryField
    @Schema(description = "应答报文")
    private String responseBody;

    @QueryField
    @Schema(description = "通知状态")
    private NotifyDTO.Status status;

    @QueryField
    @Schema(description = "秘钥")
    private KeyDTO key;

    @QueryField
    @Schema(description = "接口")
    private ApiDTO api;

    @QueryField
    @Schema(description = "调用耗时")
    private Long duration;

}

