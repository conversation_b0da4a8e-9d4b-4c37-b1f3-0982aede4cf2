package com.ejuetc.saasapi.dto.gateway;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.saasapi.domain.gateway.key.Key")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
@Schema(name = "商户接入网关的秘钥")
public class KeyDTO extends BaseDTO<KeyDTO> {
    @Getter
    public enum Status implements TitleEnum {
        ENABLE("启用"),
        DISABLE("禁用");

        private final String title;

        Status(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @QueryField
    @Schema(description = "商户ID")
    private Long userId;

    @QueryField
    @Schema(description = "代码")
    private String code;

    @QueryField(loadOnly = true)
    @Schema(description = "秘钥明文")
    private String secretText;

    @QueryField
    @Schema(description = "状态")
    private KeyDTO.Status status;

    @QueryField
    @Schema(description = "回调地址")
    private String callbackUrl;

}
