package com.ejuetc.saasapi.dto.gateway;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.saasapi.domain.gateway.invoke.Invoke")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "商户接入网关的秘钥")
public class InvokeDTO extends BaseDTO<InvokeDTO> {
    @Getter
    public enum Status implements TitleEnum {
        INIT("初始"),
        SUCC("成功"),
        FAIL("失败"),
        UNKNOW("未知"),
        ;

        private final String title;

        Status(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }

        public boolean notSucc() {
            return !isSucc();
        }

        private boolean isSucc() {
            return this == SUCC;
        }

        public boolean isFinal() {
            return switch (this) {
                case SUCC, FAIL -> true;
                default -> false;
            };
        }
    }

    @Data
    @AllArgsConstructor
    public static class Result {
        Status status;
        String message;

        public boolean isSucc() {
            return status.isSucc();
        }
    }

    @QueryField
    @Schema(description = "商户ID")
    private Long merchantId;

}

