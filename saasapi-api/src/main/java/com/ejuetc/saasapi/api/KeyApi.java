package com.ejuetc.saasapi.api;

import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.saasapi.dto.gateway.KeyDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Tag(name = "秘钥接口")
@FeignClient(value = "ejuetc.saasapi", contextId = "KeyApi")
public interface KeyApi {

    @Operation(summary = "新建密钥")
    @PostMapping("/api/key/new")
    ApiResponse<KeyDTO> newKey(@Parameter(description = "商户号") @RequestParam(required = false) Long userId,
                               @Parameter(description = "备注内容") @RequestParam String remark,
                               @Parameter(description = "回调地址") @RequestParam(required = false) String callbackUrl);

}
