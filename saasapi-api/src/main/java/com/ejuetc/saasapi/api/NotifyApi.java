package com.ejuetc.saasapi.api;

import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.saasapi.dto.gateway.NotifyDTO;
import com.ejuetc.saasapi.pro.NotifyPO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Tag(name = "通知(商户)接口")
@FeignClient(value = "ejuetc.saasapi", contextId = "NotifyApi")
public interface NotifyApi {

    @Operation(summary = "通知(商户)接口")
    @PostMapping("/api/notify/notify")
    ApiResponse<NotifyDTO> notify(@RequestBody NotifyPO po);
}
