package com.ejuetc.saasapi.pro;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ejuetc.commons.base.response.ResponseStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import static com.alibaba.fastjson.JSON.parseObject;
import static com.alibaba.fastjson.JSON.toJSONString;

@Data
@Getter
@NoArgsConstructor
public class GetawayResponse {
    @Schema(description = "处理状态")
    private ResponseStatus status;

    @Schema(description = "应答信息")
    private String message;

    @Schema(description = "应答体")
    private Object data;

    @Schema(description = "仅当发生错误时有值")
    private JSONObject error;

    public GetawayResponse(ResponseStatus status, String message, Object data) {
        this.status = status;
        this.message = message;
        setData(data);
    }

    public GetawayResponse setData(Object body) {
        this.data = body == null ? null
                : body instanceof JSONObject ? (JSONObject) body
                : body instanceof String ? parseObject((String) body)
                : JSON.parse(toJSONString(body));
        return this;
    }

    public GetawayResponse(Throwable t) {
        this.status = ResponseStatus.FAIL_SYS;
        this.message = t.getMessage();
    }
}
