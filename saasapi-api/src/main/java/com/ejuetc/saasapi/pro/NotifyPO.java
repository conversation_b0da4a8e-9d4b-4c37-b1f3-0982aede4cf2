package com.ejuetc.saasapi.pro;

import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import static com.alibaba.fastjson.JSON.parseObject;
import static com.alibaba.fastjson.JSON.toJSONString;


@Data
@NoArgsConstructor
@Accessors(chain = true)
public class NotifyPO {
    @Schema(description = "用户ID")
    private Long userId;
    @Schema(description = "API代码")
    private String apiCode;
    @Schema(description = "通知号")
    private String notifyId;
    @Schema(description = "通知内容")
    private JSONObject body;

    public NotifyPO(Long userId, String apiCode, String notifyId, Object body) {
        this.userId = userId;
        this.apiCode = apiCode;
        this.notifyId = notifyId;
        setBody(body);
    }

    public NotifyPO setBody(Object body) {
        this.body = body == null ? null
                : body instanceof JSONObject ? (JSONObject) body
                : body instanceof String ? parseObject((String) body)
                : parseObject(toJSONString(body));
        return this;
    }
}
