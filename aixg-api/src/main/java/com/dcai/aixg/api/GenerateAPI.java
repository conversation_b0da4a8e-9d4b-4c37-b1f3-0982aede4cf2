package com.dcai.aixg.api;

import com.dcai.aixg.dto.generate.GenerateConfigDTO;
import com.dcai.aixg.dto.generate.GenerateDTO;
import com.dcai.aixg.pro.generate.*;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.ejuetc.commons.base.filter.LoginTokenFilter.LOGIN_INFO_ATT;

/**
 * 生成相关API接口
 * 
 * <AUTHOR>
 */
@Tag(name = "AI生成接口")
@FeignClient(name = "aixg", path = "/aixg", contextId = "generateAPI")
public interface GenerateAPI {

    // ==================== 配置管理 ====================

    @Operation(summary = "WEB_创建生成配置")
    @PostMapping("/web/generate/config/create")
    ApiResponse<GenerateConfigDTO> createConfig(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @RequestBody @Valid CreateGenerateConfigPO po);

    @Operation(summary = "WEB_更新生成配置")
    @PostMapping("/web/generate/config/update")
    ApiResponse<GenerateConfigDTO> updateConfig(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @RequestBody @Valid UpdateGenerateConfigPO po);

    @Operation(summary = "WEB_删除生成配置")
    @DeleteMapping("/web/generate/config/{id}")
    ApiResponse<Void> deleteConfig(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @PathVariable("id") Long id);

    @Operation(summary = "WEB_获取生成配置详情")
    @GetMapping("/web/generate/config/{id}")
    ApiResponse<GenerateConfigDTO> getConfig(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @PathVariable("id") Long id);

    @Operation(summary = "WEB_根据代码获取生成配置")
    @GetMapping("/web/generate/config/code/{code}")
    ApiResponse<GenerateConfigDTO> getConfigByCode(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @PathVariable("code") String code);

    @Operation(summary = "WEB_获取所有启用的生成配置")
    @GetMapping("/web/generate/config/enabled")
    ApiResponse<List<GenerateConfigDTO>> getEnabledConfigs(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken);

    @Operation(summary = "WEB_根据任务类型获取配置")
    @GetMapping("/web/generate/config/type/{taskType}")
    ApiResponse<List<GenerateConfigDTO>> getConfigsByTaskType(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @PathVariable("taskType") String taskType);

    @Operation(summary = "WEB_启用/禁用配置")
    @PostMapping("/web/generate/config/{id}/toggle")
    ApiResponse<Void> toggleConfig(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @PathVariable("id") Long id);

    // ==================== 任务管理 ====================

    @Operation(summary = "WEB_创建生成任务")
    @PostMapping("/web/generate/create")
    ApiResponse<GenerateDTO> createGenerate(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @RequestBody @Valid CreateGeneratePO po);

    @Operation(summary = "WEB_根据配置代码创建生成任务")
    @PostMapping("/web/generate/create-by-code")
    ApiResponse<GenerateDTO> createGenerateByCode(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @RequestBody @Valid CreateGenerateByCodePO po);

    @Operation(summary = "WEB_获取生成任务详情")
    @GetMapping("/web/generate/{id}")
    ApiResponse<GenerateDTO> getGenerate(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @PathVariable("id") Long id);

    @Operation(summary = "WEB_查询生成任务列表")
    @PostMapping("/web/generate/query")
    ApiResponse<Page<GenerateDTO>> queryGenerates(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @RequestBody @Valid QueryGeneratePO po);

    @Operation(summary = "WEB_取消生成任务")
    @PostMapping("/web/generate/{id}/cancel")
    ApiResponse<Void> cancelGenerate(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @PathVariable("id") Long id);

    @Operation(summary = "WEB_重新执行生成任务")
    @PostMapping("/web/generate/{id}/retry")
    ApiResponse<GenerateDTO> retryGenerate(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @PathVariable("id") Long id);

    @Operation(summary = "WEB_获取任务执行进度")
    @GetMapping("/web/generate/{id}/progress")
    ApiResponse<GenerateDTO.ProgressInfo> getGenerateProgress(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @PathVariable("id") Long id);

    // ==================== 统计分析 ====================

    @Operation(summary = "WEB_获取生成统计信息")
    @GetMapping("/web/generate/stats")
    ApiResponse<Object> getGenerateStats(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken);

    @Operation(summary = "WEB_获取配置使用统计")
    @GetMapping("/web/generate/config/{id}/stats")
    ApiResponse<GenerateConfigDTO.GenerateConfigStatsDTO> getConfigStats(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @PathVariable("id") Long id);
}
