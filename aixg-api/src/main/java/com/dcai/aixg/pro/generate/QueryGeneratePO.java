package com.dcai.aixg.pro.generate;

import com.dcai.aixg.dto.generate.GenerateDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 查询生成任务请求参数
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "查询生成任务请求参数")
public class QueryGeneratePO {

    @Schema(description = "配置ID", example = "1")
    private Long configId;

    @Schema(description = "配置代码", example = "content_generation")
    private String configCode;

    @Schema(description = "状态", example = "SUCCESS")
    private GenerateDTO.GenerateStatus status;

    @Schema(description = "关键词（搜索请求内容）", example = "人工智能")
    private String keyword;

    @Schema(description = "开始时间", example = "2024-01-01T00:00:00")
    private LocalDateTime startTime;

    @Schema(description = "结束时间", example = "2024-12-31T23:59:59")
    private LocalDateTime endTime;

    @Schema(description = "页码", example = "1")
    private Integer page = 1;

    @Schema(description = "每页大小", example = "10")
    private Integer size = 10;

    @Schema(description = "排序字段", example = "createTime")
    private String sortBy = "createTime";

    @Schema(description = "排序方向", example = "desc")
    private String sortDirection = "desc";
}
