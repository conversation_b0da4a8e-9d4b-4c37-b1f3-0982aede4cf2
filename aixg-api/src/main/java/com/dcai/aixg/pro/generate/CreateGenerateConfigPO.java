package com.dcai.aixg.pro.generate;

import com.dcai.aixg.dto.generate.GenerateConfigDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 创建生成配置请求参数
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "创建生成配置请求参数")
public class CreateGenerateConfigPO {

    @NotBlank(message = "配置代码不能为空")
    @Size(max = 100, message = "配置代码长度不能超过100个字符")
    @Schema(description = "配置代码", example = "content_generation")
    private String code;

    @NotBlank(message = "配置标题不能为空")
    @Size(max = 200, message = "配置标题长度不能超过200个字符")
    @Schema(description = "配置标题", example = "内容生成配置")
    private String title;

    @Size(max = 1000, message = "配置描述长度不能超过1000个字符")
    @Schema(description = "配置描述", example = "用于生成各种类型的内容")
    private String description;

    @Size(max = 2000, message = "演示内容长度不能超过2000个字符")
    @Schema(description = "演示内容", example = "这是一个演示内容")
    private String demo;

    @NotBlank(message = "系统模板不能为空")
    @Schema(description = "系统模板", example = "你是一个专业的内容生成助手")
    private String systemTemplate;

    @NotBlank(message = "用户模板不能为空")
    @Schema(description = "用户模板", example = "请根据以下要求生成内容：{request}")
    private String userTemplate;

    @NotNull(message = "任务类型不能为空")
    @Schema(description = "任务类型", example = "DEFAULT")
    private GenerateConfigDTO.TaskType taskType;

    @Schema(description = "是否启用", example = "true")
    private Boolean enabled = true;

    @Schema(description = "排序", example = "0")
    private Integer sortOrder = 0;
}
