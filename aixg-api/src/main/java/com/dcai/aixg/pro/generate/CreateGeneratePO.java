package com.dcai.aixg.pro.generate;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 创建生成任务请求参数
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "创建生成任务请求参数")
public class CreateGeneratePO {

    @NotNull(message = "配置ID不能为空")
    @Schema(description = "配置ID", example = "1")
    private Long configId;

    @NotBlank(message = "请求内容不能为空")
    @Size(max = 5000, message = "请求内容长度不能超过5000个字符")
    @Schema(description = "请求内容", example = "请生成一篇关于人工智能的文章")
    private String request;

    @Size(max = 1000, message = "备忘录长度不能超过1000个字符")
    @Schema(description = "备忘录", example = "这是一个测试任务")
    private String memo;
}
