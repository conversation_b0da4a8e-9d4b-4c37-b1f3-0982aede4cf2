package com.dcai.aixg.dto.generate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 生成任务DTO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GenerateDTO {

    /**
     * 任务ID
     */
    private Long id;

    /**
     * 配置信息
     */
    private GenerateConfigDTO config;

    /**
     * 请求内容
     */
    private String request;

    /**
     * 系统提示
     */
    private String systemPrompt;

    /**
     * 用户提示
     */
    private String userPrompt;

    /**
     * 响应内容
     */
    private String response;

    /**
     * 执行时长(毫秒)
     */
    private Long duration;

    /**
     * 状态
     */
    private GenerateStatus status;

    /**
     * 状态描述
     */
    private String statusDescription;

    /**
     * 备忘录
     */
    private String memo;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 开始时间
     */
    private LocalDateTime startedAt;

    /**
     * 完成时间
     */
    private LocalDateTime completedAt;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 生成步骤列表
     */
    private List<GenerateStepDTO> steps;

    /**
     * 进度信息
     */
    private ProgressInfo progressInfo;

    /**
     * 进度信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProgressInfo {
        /**
         * 总步骤数
         */
        private Integer totalSteps;

        /**
         * 已完成步骤数
         */
        private Integer completedSteps;

        /**
         * 当前步骤
         */
        private String currentStep;

        /**
         * 进度百分比
         */
        private Double progressPercentage;

        /**
         * 预计剩余时间(毫秒)
         */
        private Long estimatedRemainingTime;
    }

    /**
     * 生成状态枚举
     */
    public enum GenerateStatus {
        PENDING("待处理"),
        PROCESSING("处理中"),
        SUCCESS("成功"),
        FAILED("失败"),
        CANCELLED("已取消");

        private final String description;

        GenerateStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
