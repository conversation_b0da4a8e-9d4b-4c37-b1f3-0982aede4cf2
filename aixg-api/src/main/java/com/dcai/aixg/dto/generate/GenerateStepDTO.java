package com.dcai.aixg.dto.generate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 生成步骤DTO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GenerateStepDTO {

    /**
     * 步骤ID
     */
    private Long id;

    /**
     * 生成任务ID
     */
    private Long generateId;

    /**
     * 步骤标题
     */
    private String title;

    /**
     * 请求内容
     */
    private String request;

    /**
     * 响应内容
     */
    private String response;

    /**
     * 执行时长(毫秒)
     */
    private Long duration;

    /**
     * 状态
     */
    private StepStatus status;

    /**
     * 状态描述
     */
    private String statusDescription;

    /**
     * 备忘录
     */
    private String memo;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 开始时间
     */
    private LocalDateTime startedAt;

    /**
     * 完成时间
     */
    private LocalDateTime completedAt;

    /**
     * 步骤顺序
     */
    private Integer stepOrder;

    /**
     * 步骤类型
     */
    private String stepType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 函数调用信息（仅当stepType为FUNCTION_CALLING时有值）
     */
    private FunctionCallingInfo functionCallingInfo;

    /**
     * 函数调用信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FunctionCallingInfo {
        /**
         * 函数名称
         */
        private String functionName;

        /**
         * 函数参数
         */
        private String functionArgs;

        /**
         * 函数执行结果
         */
        private String functionResult;

        /**
         * 函数执行状态
         */
        private FunctionStatus functionStatus;

        /**
         * 函数状态描述
         */
        private String functionStatusDescription;

        /**
         * 重试次数
         */
        private Integer retryCount;

        /**
         * 最大重试次数
         */
        private Integer maxRetry;

        /**
         * 是否可以重试
         */
        private Boolean canRetry;
    }

    /**
     * 步骤状态枚举
     */
    public enum StepStatus {
        PENDING("待处理"),
        PROCESSING("处理中"),
        SUCCESS("成功"),
        FAILED("失败"),
        SKIPPED("已跳过");

        private final String description;

        StepStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 函数状态枚举
     */
    public enum FunctionStatus {
        PENDING("待执行"),
        EXECUTING("执行中"),
        SUCCESS("成功"),
        FAILED("失败"),
        TIMEOUT("超时"),
        RETRY("重试中");

        private final String description;

        FunctionStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
