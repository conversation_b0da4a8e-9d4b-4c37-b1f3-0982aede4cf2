package com.dcai.aixg.dto.generate;

import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 生成配置DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@QueryDomain("com.dcai.aixg.domain.generate.GenerateConfig")
public class GenerateConfigDTO {

    /**
     * 配置ID
     */
    @QueryField
    private Long id;

    /**
     * 配置代码
     */
    @QueryField
    private String code;

    /**
     * 配置标题
     */
    @QueryField
    private String title;

    /**
     * 配置描述
     */
    @QueryField
    private String description;

    /**
     * 演示内容
     */
    @QueryField
    private String demo;

    /**
     * 系统模板
     */
    @QueryField
    private String systemTemplate;

    /**
     * 用户模板
     */
    @QueryField
    private String userTemplate;

    /**
     * 任务类型
     */
    @QueryField("generateType")
    private TaskType taskType;

    /**
     * 任务类型描述
     */
    private String taskTypeDescription;

    /**
     * 是否启用
     */
    @QueryField
    private Boolean enabled;

    /**
     * 排序
     */
    @QueryField
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @QueryField
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @QueryField
    private LocalDateTime updateTime;

    /**
     * 使用统计
     */
    private GenerateConfigStatsDTO stats;

    /**
     * 生成配置统计DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GenerateConfigStatsDTO {
        /**
         * 总任务数
         */
        private Long totalTasks;

        /**
         * 成功任务数
         */
        private Long successTasks;

        /**
         * 失败任务数
         */
        private Long failedTasks;

        /**
         * 处理中任务数
         */
        private Long processingTasks;

        /**
         * 成功率
         */
        private Double successRate;

        /**
         * 平均处理时长(毫秒)
         */
        private Double averageDuration;

        /**
         * 最后使用时间
         */
        private LocalDateTime lastUsedTime;
    }

    /**
     * 任务类型枚举
     */
    public enum TaskType {
        DEFAULT("默认任务"),
        CUSTOM("自定义任务"),
        FUNCTION_CALLING("函数调用任务");

        private final String description;

        TaskType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
