package com.dcai.aixg.dto.generate;

import com.dcai.aixg.domain.generate.GenerateConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 生成配置DTO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GenerateConfigDTO {

    /**
     * 配置ID
     */
    private Long id;

    /**
     * 配置代码
     */
    private String code;

    /**
     * 配置标题
     */
    private String title;

    /**
     * 配置描述
     */
    private String description;

    /**
     * 演示内容
     */
    private String demo;

    /**
     * 系统模板
     */
    private String systemTemplate;

    /**
     * 用户模板
     */
    private String userTemplate;

    /**
     * 任务类型
     */
    private GenerateConfig.TaskType taskType;

    /**
     * 任务类型描述
     */
    private String taskTypeDescription;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 使用统计
     */
    private GenerateConfigStatsDTO stats;

    /**
     * 生成配置统计DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GenerateConfigStatsDTO {
        /**
         * 总任务数
         */
        private Long totalTasks;

        /**
         * 成功任务数
         */
        private Long successTasks;

        /**
         * 失败任务数
         */
        private Long failedTasks;

        /**
         * 处理中任务数
         */
        private Long processingTasks;

        /**
         * 成功率
         */
        private Double successRate;

        /**
         * 平均处理时长(毫秒)
         */
        private Double averageDuration;

        /**
         * 最后使用时间
         */
        private LocalDateTime lastUsedTime;
    }
}
