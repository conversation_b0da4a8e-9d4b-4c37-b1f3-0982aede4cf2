package com.ejuetc.consumer.web.consumer;

import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.filter.LoginInfo;
import com.ejuetc.commons.base.utils.StringUtils;
import com.ejuetc.consumer.api.dto.LoginAccountDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.UUID;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "登录消费者")
public class LoginToken implements LoginInfo {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "登录令牌")
    protected String token = "consumer_" + UUID.randomUUID().toString().replaceAll("-", "");

    @Schema(description = "登录方式")
    protected LoginAccountDTO.Type type;

    @Schema(description = "渠道编码")
    protected ChannelDTO.Code channelCode;

    @Schema(description = "登录账号主键")
    protected Long loginAccountId;

    @Schema(description = "登录历史主键")
    protected Long loginHistoryId;

    @Schema(description = "登录标示")
    protected String accountIdent;

    @Schema(description = "消费者ID")
    protected Long consumerId;

    @Schema(description = "昵称")
    protected String nickName;

    @Schema(description = "手机号")
    protected String phone;

    @Schema(description = "头像URL")
    protected String iconUrl;

    @Schema(description = "城市ID")
    protected Long cityId;

    @Schema(description = "城市名称")
    protected String cityName;

    @Schema(description = "经纬度")
    private String location;

    public LoginToken(Long loginHistoryId) {
        this.loginHistoryId = loginHistoryId;
    }

    public BigDecimal getLongitude() {
        return StringUtils.isBlank(location) ? null : new BigDecimal(location.split(",")[0]);
    }

    public BigDecimal getLatitude() {
        return StringUtils.isBlank(location) ? null : new BigDecimal(location.split(",")[1]);
    }

    @Override
    public Long getUserId() {
        return consumerId;
    }
}
