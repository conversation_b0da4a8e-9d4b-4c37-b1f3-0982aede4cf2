package com.ejuetc.consumer.web.community;

import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.community.*;
import com.ejuetc.consumer.web.consumer.LoginToken;
import com.ejuetc.consumer.web.vo.BeikeCommunityVO;
import com.ejuetc.consumer.web.vo.CommunityLayoutVO;
import com.ejuetc.consumer.web.vo.CommunityVO;
import com.ejuetc.consumer.web.vo.CommunityTipsVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.ejuetc.commons.base.filter.LoginTokenFilter.LOGIN_INFO_ATT;

@Tag(name = "WEB_小区")
@FeignClient(value = "ejuetc.consumer", path = "/consumer", contextId = "CommunityWeb")
public interface CommunityWeb {

    @Operation(summary = "搜索关键字搜索小区列表")
    @GetMapping("/community/list")
    ApiResponse<List<CommunityVO>> queryByKeyword(
            @RequestAttribute(value = LOGIN_INFO_ATT, required = false) LoginToken loginToken,
            @RequestParam("cityId") Long cityId,
            @RequestParam("keyword") String keyword,
            @RequestParam(value = "pageSize", defaultValue = "5") int pageSize,
            @RequestParam(value = "pageNum", defaultValue = "1") int pageNum);

    @Operation(summary = "基于提示词搜索小区列表")
    @GetMapping("/community/search")
    ApiResponse<List<CommunityTipsVO>> queryByTips(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken loginToken,
            @RequestParam("cityName") String cityName,
            @RequestParam("keyword") String keyword,
            @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @RequestParam(value = "pageSize", defaultValue = "15") int pageSize);

    @Operation(summary = "查询小区图片")
    @GetMapping("/community/queryCommunityPictures")
    ApiResponse<Map<String, List<String>>> queryCommunityPictures(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken loginToken,
            @Parameter(description = "小区地址") @RequestParam String communityAddress,
            @Parameter(description = "小区名称") @RequestParam String communityName
    );

    @Operation(summary = "绑定小区")
    @PostMapping("/community/bind")
    ApiResponse<BindCommunityRO> bind(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken loginToken,
            @Parameter(description = "小区地址") @RequestParam("address") String address,
            @Parameter(description = "小区名称") @RequestParam("name") String name
    );

    @Operation(summary = "查询小区户型")
    @GetMapping("/community/queryCommunityLayouts")
    ApiResponse<List<CommunityLayoutVO>> queryCommunityLayouts(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken loginToken,
            @Parameter(description = "小区地址") @RequestParam String communityAddress,
            @Parameter(description = "小区名称") @RequestParam String communityName
    );


    @Operation(summary = "查询贝壳小区")
    @PostMapping("/community/queryBeikeCommunity")
    ApiResponse<List<BeikeCommunityVO>> queryBeikeCommunity(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken loginToken,
            @RequestBody QueryCommunityPO queryCommunityPO
    );

    @Operation(summary = "查询分组小区户型")
    @PostMapping("/community/queryLayoutGroup")
    ApiResponse<Map<Integer, Set<String>>> queryLayoutGroup(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken loginToken,
            @RequestBody QueryLayoutGroupPO po);

}
