package com.ejuetc.consumer.web.vo;

import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import static com.ejuetc.commons.base.querydomain.api.LoadPolicy.SPECIFY_LOAD;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "经纪人")
@QueryDomain("com.ejuetc.consumer.domain.broker.Broker")
public class BrokerVO {

    @QueryField
    @Schema(description = "主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @QueryField
    @Schema(description = "编码")
    private String code;

    @QueryField
    @Schema(description = "名称")
    private String name;

    @QueryField
    @Schema(description = "头像")
    private String icon;

    @QueryField
    @Schema(description = "介绍")
    private String introduce;

    @QueryField
    @Schema(description = "所属公司")
    private String company;

    @QueryField
    @Schema(description = "从业信息卡地址")
    private String professionInformationCardUrl;

    @QueryField
    @Schema(description = "从业信息卡编号")
    private String professionInformationCardNumber;

    @QueryField
    @Schema(description = "门店名称")
    private String storeName;

    @QueryField(loadPolicy = SPECIFY_LOAD)
    @Schema(description = "手机号")
    private String phone;

}
