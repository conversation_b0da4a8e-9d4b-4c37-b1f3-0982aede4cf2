package com.ejuetc.consumer.web.file;

import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.web.consumer.LoginToken;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.multipart.MultipartFile;

import static com.ejuetc.commons.base.filter.LoginTokenFilter.LOGIN_INFO_ATT;

@Tag(name = "WEB_文件")
@FeignClient(value = "ejuetc.consumer", path = "/consumer", contextId = "FileWeb")
public interface FileWeb {
    @PostMapping("/file/upload")
    ApiResponse<String> upload(@RequestAttribute(value = LOGIN_INFO_ATT) LoginToken consumerLoginToken,
                               MultipartFile file);
}
