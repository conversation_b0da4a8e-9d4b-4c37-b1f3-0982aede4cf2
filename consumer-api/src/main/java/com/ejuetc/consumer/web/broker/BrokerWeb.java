package com.ejuetc.consumer.web.broker;

import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.web.vo.BrokerVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

import static com.ejuetc.commons.base.filter.LoginTokenFilter.LOGIN_INFO_ATT;

@Tag(name = "WEB_经纪人")
@FeignClient(value = "ejuetc.consumer", path = "/consumer", contextId = "BrokerWeb")
public interface BrokerWeb {

    @Operation(summary = "经纪人详情")
    @GetMapping("/broker/detail")
    ApiResponse<BrokerVO> detail(@Parameter(description = "经纪人ID") @RequestParam Long brokerId);

    @Operation(summary = "公司经纪人列表")
    @GetMapping("/broker/brokerList4Company")
    ApiResponse<List<BrokerVO>> brokerList4Company(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken loginToken
    );


}
