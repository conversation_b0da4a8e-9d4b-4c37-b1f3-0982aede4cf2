package com.ejuetc.consumer.web.vo;

import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.ejuetc.consumer.api.delegation.MediaPO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "多媒体")
@QueryDomain("com.ejuetc.consumer.domain.delegation.DelegationMedia")
public class MediaVO{

    @QueryField
    @Schema(description = "主键")
    private Long id;

    @QueryField
    @Schema(description = "名称")
    private String name;

    @QueryField
    @Schema(description = "地址")
    private String url;

    @QueryField
    @Schema(description = "类型")
    private MediaPO.Type type;

    @QueryField("type.title")
    @Schema(description = "类型名")
    private String typeName;

    @QueryField
    @Schema(description = "子类型")
    private String subtype;

    @QueryField
    @Schema(description = "子类型名称")
    private String subtypeName;

}
