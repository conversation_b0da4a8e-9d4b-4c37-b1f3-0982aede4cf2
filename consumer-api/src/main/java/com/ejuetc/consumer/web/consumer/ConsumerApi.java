package com.ejuetc.consumer.web.consumer;

import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.dto.ConsumerShareDTO;
import com.ejuetc.consumer.api.dto.LoginAccountDTO;
import com.ejuetc.consumer.web.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.ejuetc.commons.base.filter.LoginTokenFilter.LOGIN_INFO_ATT;

@Tag(name = "消费者API")
@FeignClient(value = "ejuetc.consumer", path = "/consumer", contextId = "ConsumerApi")
public interface ConsumerApi {

    @Operation(summary = "API_编辑客源关系")
    @PostMapping("/api/consumer/relationEdit")
    ApiResponse<RelationVO> relationEdit(
            @Parameter(description = "经纪人ID") @RequestParam Long brokerId,
            @RequestBody RelationPO<?> ro
    );

    @Operation(summary = "API_清除token")
    @PostMapping("/api/consumer/clearToken")
    void clearToken(@RequestParam String token);
}
