package com.ejuetc.consumer.web.vo;

import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.ejuetc.consumer.api.dto.LoginAccountDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("ALL")
@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "客户关系编辑参数")
@QueryDomain("com.ejuetc.consumer.domain.consumer.ConsumerRelation")
public class RelationPO<Subtype extends RelationPO<?>> {
    @QueryField
    @Schema(description = "主键")
    private Long id;

    @QueryField
    @Schema(description = "客户姓名")
    private String name;

    @QueryField
    @Schema(description = "客户性别(true:男性,false:女性)")
    private Boolean mister;

    @QueryField
    @Schema(description = "备注消费者手机号")
    private List<String> remarkPhones;

    @QueryField
    @Schema(description = "备注信息")
    @Column(name = "remark", columnDefinition = "text COMMENT '备注'")
    private String remark;

    @QueryField
    @Schema(description = "头像URL")
    private String iconUrl;

    @QueryField
    @Schema(description = "数据来源")
    private String sourceType;

    @QueryField
    @Schema(description = "外部ID")
    private String sourceId;

    @QueryField
    @Schema(description = "活动时间")
    private LocalDateTime activityTime;

    @QueryField
    @Schema(description = "活动备注")
    private String activityRemark;

    @QueryField
    @Schema(description = "客户账户类型")
    private LoginAccountDTO.Type accountType;


    public Subtype setId(Long id) {
        this.id = id;
        return (Subtype) this;
    }

    public Subtype setName(String name) {
        this.name = name;
        return (Subtype) this;
    }

    public Subtype setMister(Boolean mister) {
        this.mister = mister;
        return (Subtype) this;
    }

    public Subtype setRemarkPhones(List<String> remarkPhones) {
        this.remarkPhones = remarkPhones;
        return (Subtype) this;
    }

    public Subtype setRemark(String remark) {
        this.remark = remark;
        return (Subtype) this;
    }

    public List<String> getRemarkPhones() {
        return remarkPhones != null ? remarkPhones : new ArrayList<>();
    }
}
