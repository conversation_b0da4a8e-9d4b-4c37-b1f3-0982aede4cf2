package com.ejuetc.consumer.web.vo;

import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.querydomain.api.LoadPolicy;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.ejuetc.consumer.api.delegation.BaseDelegationPO;
import com.ejuetc.consumer.api.delegation.MediaPO;
import com.ejuetc.consumer.api.dto.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Transient;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static com.ejuetc.consumer.api.dto.DelegationDictDTO.Category.*;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "房源委托信息")
@QueryDomain("com.ejuetc.consumer.domain.delegation.Delegation")
public class DelegationVO extends BaseDelegationPO<DelegationVO> {

    @QueryField
    @Schema(description = "编号")
    private String code;

    @QueryField("detail.type.title")
    @Schema(description = "委托类型名")
    private String typeName;

    @QueryField("detail.subType.title")
    @Schema(description = "交易子类型名")
    private String subTypeName;

    @QueryField
    @Schema(description = "封面图URL")
    private String coverUrl;

    @QueryField
    @Schema(description = "多媒体资料")
    private Map<String, List<String>> mediasMap;

    @QueryField
    @Schema(description = "经纪人")
    private BrokerVO broker;

    @QueryField("detail.community")
    @Schema(description = "小区")
    private CommunityVO community;


    @Schema(description = "浏览时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime viewTime;

    @Schema(description = "浏览次数")
    private Long viewCount;

    @QueryField
    @Schema(description = "所属公司userId")
    private Long companyId;

    @QueryField
    @Schema(description = "经纪人名")
    private String brokerName;

    @QueryField
    @Schema(description = "状态")
    private DelegationDTO.Status status;

    @QueryField("detail.city")
    @Schema(description = "城市")
    private RegionDTO city;

    @QueryField("detail.cityName")
    @Schema(description = "城市名")
    private String cityName;

    @QueryField("detail.district")
    @Schema(description = "区县")
    private RegionDTO district;

    @QueryField("detail.districtName")
    @Schema(description = "区县名")
    private String districtName;

    @QueryField("detail.busiName")
    @Schema(description = "商圈")
    private String busiName;

    @QueryField("detail.townName")
    @Schema(description = "城镇/街道")
    private String townName;

    @QueryField("detail.floorCategory")
    @Schema(description = "楼层类别")
    @DictCategory(FLOOR_CATEGORY)
    private String floorCategory;

    @QueryField("detail.layoutName")
    @Schema(description = "户型")
    private String layoutName;

    @Schema(description = "(仅对经纪房源)渠道发布状态映射")
    @QueryField(value = "channelDelegationMap", loadPolicy = LoadPolicy.SPECIFY_LOAD)
    private Map<ChannelDTO.Code, ChannelDelegationVO> channelDelegationMap;

    @JsonIgnore
    @Schema(description = "(仅对经纪房源)渠道状态映射&渠道开通状态")
    @QueryField(value = "channelDelegationMapPlus", loadPolicy = LoadPolicy.SPECIFY_LOAD)
    private Map<ChannelDTO.Code, ChannelDelegationVO> channelDelegationMapPlus;

    @QueryField
    @Schema(description = "(仅对渠道房源)渠道")
    protected ChannelDTO.Code channelCode;

    @QueryField
    @Schema(description = "备注")
    private String remark;

    @Schema(description = "(仅对公司房源)当前用户已创建经纪房源编码")
    private String ownChildCode;

    @QueryField
    @Schema(description = "父编码")
    private String parentCode;

    @QueryField
    @Schema(description = "多媒体资料")
    private List<MediaPO> medias;


    public String getCompanyIdStr() {
        return String.valueOf(getCompanyId());
    }

    public Map<ChannelDTO.Code, ChannelDelegationVO> getChannelDelegationMap() {
        if (channelDelegationMapPlus != null && !channelDelegationMapPlus.isEmpty()) {
            return channelDelegationMapPlus;
        } else {
            return channelDelegationMap;
        }
    }

}
