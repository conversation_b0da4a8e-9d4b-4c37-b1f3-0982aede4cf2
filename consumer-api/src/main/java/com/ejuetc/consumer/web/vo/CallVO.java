package com.ejuetc.consumer.web.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.ejuetc.consumer.api.dto.ImplicitCallDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "隐号呼叫")
@QueryDomain("com.ejuetc.consumer.domain.implicit.ImplicitCall")
public class CallVO extends DelegationProxyVO<CallVO>{
    @QueryField
    @Schema(description = "主键")
    private Long id;

    @QueryField
    @Schema(description = "经纪人手机号")
    private String brokerPhone;

    @QueryField
    @Schema(description = "隐号")
    private String secretPhone;

    @QueryField
    @Schema(description = "发起方枚举")
    private ImplicitCallDTO.Starter starter;

    @QueryField("starter.title")
    @Schema(description = "发起方名称")
    private String starterTitle;

    @QueryField
    @Schema(description = "呼叫时间")
    private LocalDateTime callTime;

    @QueryField
    @Schema(description = "录音下载地址")
    private String recordUrl;

    @QueryField
    @Schema(description = "接通时长")
    private Long duration;


    @QueryField
    @Schema(description = "关联房源ID")
    private Long delegationId;

}
