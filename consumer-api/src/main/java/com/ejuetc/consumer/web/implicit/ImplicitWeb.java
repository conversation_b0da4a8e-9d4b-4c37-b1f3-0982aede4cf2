package com.ejuetc.consumer.web.implicit;

import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.web.consumer.LoginToken;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import static com.ejuetc.commons.base.filter.LoginTokenFilter.LOGIN_INFO_ATT;

@Tag(name = "WEB_隐号")
@FeignClient(value = "ejuetc.consumer", path = "/consumer", contextId = "ImplicitWeb")
public interface ImplicitWeb {

    @Operation(summary = "绑定客户与经纪人手机号")
    @PostMapping("/implicit/bind")
    ApiResponse<String> bind(
            @RequestAttribute(value = LOGIN_INFO_ATT) LoginToken consumerLoginToken,
            @Parameter(description = "房源委托代码") @RequestParam("code") String delegationCode,
            @Parameter(description = "消费者手机号") @RequestParam("phone") String consumerPhone
    );
}
