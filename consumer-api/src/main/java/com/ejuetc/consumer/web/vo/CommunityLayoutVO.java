package com.ejuetc.consumer.web.vo;

import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "小区户型图")
@QueryDomain("com.ejuetc.consumer.domain.community.CommunityLayout")
public class CommunityLayoutVO {

    @QueryField
    @Schema(description = "主键")
    private Long id;

    @QueryField
    @Schema(description = "小区ID")
    private Long communityId;//community_id

    @QueryField
    @Schema(description = "户型名称")
    protected String name;//house_layout_name

    @QueryField
    @Schema(description = "卧室数")
    private Integer roomCount;//room_num

    @QueryField
    @Schema(description = "客厅数")
    private Integer hallCount;//hall_num

    @QueryField
    @Schema(description = "卫生间数")
    private Integer toiletCount;//bath_num

    @QueryField
    @Schema(description = "阳台数")
    private Integer balconyCount;//balcony_num

    @QueryField
    @Schema(description = "厨房数")
    private Integer kitchenCount;//kitchen_num

    @QueryField
    @Schema(description = "面积")
    private BigDecimal area;//area

    @QueryField
    @Schema(description = "套内面积")
    private BigDecimal areaAct;//act_area

    @QueryField
    @Schema(description = "最小面积")
    private BigDecimal areaMin;//min_area

    @QueryField
    @Schema(description = "最大面积")
    private BigDecimal areaMax;//max_area

    @QueryField
    @Schema(description = "主力户型")
    protected Boolean mainLayoutFlag;//main_layout_ind

    @QueryField("picUrl")
    @Schema(description = "户型图")
    protected String picUrl;//layout_pic_path_fang_v2

    @QueryField
    @Schema(description = "总套数")
    private Integer setNum;//set_num
}
