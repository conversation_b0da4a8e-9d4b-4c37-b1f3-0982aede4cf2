package com.ejuetc.consumer.web.delegation;

import com.ejuetc.channel.dto.BusinessOpenDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

@Accessors(chain = true)
@Data
public class QueryListPO2C {
    @Schema(description = "城市ID")
    private Long cityId;
    @Schema(description = "搜索关键字")
    private String keyword;
    @Schema(description = "经纪人ID")
    private Long brokerId;
    @Schema(description = "小区ID")
    private Long communityId;
    @Schema(description = "业务类型(二手房/租房)")
    private BusinessOpenDTO.Code type;
    @Schema(description = "是否精品")
    private Boolean boutique;
    @Schema(description = "排序")
    private QueryListPO2B.Sort sort;


    public boolean canRecommend() {
        return StringUtils.isBlank(keyword) && brokerId == null && communityId == null;
    }

    public String getKeyword() {
        return StringUtils.isBlank(keyword) ? null : keyword;
    }

    public String getSortStr() {
        return sort != null ? sort.name() : null;
    }
}
