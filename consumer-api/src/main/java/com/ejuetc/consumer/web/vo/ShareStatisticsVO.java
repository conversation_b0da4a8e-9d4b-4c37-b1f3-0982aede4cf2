package com.ejuetc.consumer.web.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "分享统计")
public class ShareStatisticsVO {
    @Schema(description = "经纪人分享次数")
    private long brokerSharerCount = 0;

    @Schema(description = "消费者分享次数")
    private long consumerSharerCount = 0;

    @Schema(description = "分享房源数")
    private long distinctDelegationCount = 0;

    @Schema(description = "客源数")
    private long consumerRelationCount = 0;

    public ShareStatisticsVO setBrokerSharerCount(BigDecimal brokerSharerCount) {
        if (brokerSharerCount != null)
            this.brokerSharerCount = brokerSharerCount.longValue();
        return this;
    }

    public ShareStatisticsVO setConsumerSharerCount(BigDecimal consumerSharerCount) {
        if (consumerSharerCount != null)
            this.consumerSharerCount = consumerSharerCount.longValue();
        return this;
    }

    public ShareStatisticsVO setDistinctDelegationCount(Long distinctDelegationCount) {
        if (distinctDelegationCount != null)
            this.distinctDelegationCount = distinctDelegationCount;
        return this;
    }

    public ShareStatisticsVO setConsumerRelationCount(Long consumerRelationCount) {
        if (consumerRelationCount != null)
            this.consumerRelationCount = consumerRelationCount;
        return this;
    }

    public ShareStatisticsVO addBrokerSharerCount(Integer brokerSharerCount) {
        if (brokerSharerCount != null)
            this.brokerSharerCount += brokerSharerCount;
        return this;
    }

    public ShareStatisticsVO addConsumerSharerCount(Integer consumerSharerCount) {
        if (consumerSharerCount != null)
            this.consumerSharerCount += consumerSharerCount;
        return this;
    }

    public ShareStatisticsVO addDistinctDelegationCount(Integer distinctDelegationCount) {
        if (distinctDelegationCount != null)
            this.distinctDelegationCount += distinctDelegationCount;
        return this;
    }

    public ShareStatisticsVO addConsumerRelationCount(Integer consumerRelationCount) {
        if (consumerRelationCount != null)
            this.consumerRelationCount += consumerRelationCount;
        return this;
    }
}
