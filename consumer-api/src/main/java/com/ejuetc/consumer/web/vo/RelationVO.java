package com.ejuetc.consumer.web.vo;

import com.ejuetc.commons.base.querydomain.api.LoadPolicy;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.ejuetc.consumer.api.dto.LoginAccountDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "客户关系")
@QueryDomain("com.ejuetc.consumer.domain.consumer.ConsumerRelation")
public class RelationVO extends RelationPO<RelationVO> {

    @QueryField
    @Schema(description = "消费者id")
    private Long consumerId;

    @QueryField
    @Schema(description = "最近活动时间天数")
    private Long activityTimeDays;

    @QueryField("accountType.title")
    @Schema(description = "账户类型标题")
    private String accountTypeTitle;

    @QueryField
    @Schema(description = "最近呼叫时间")
    private LocalDateTime lastCallTime;

    @QueryField
    @Schema(description = "隐号成功绑定列表")
    private List<BindVO> succBinds;

    @QueryField
    @Schema(description = "隐号拨打记录")
    private List<CallVO> calls;

    @QueryField(loadPolicy = LoadPolicy.SPECIFY_LOAD)
    @Schema(description = "访问最多的房源列表")
    List<TrackCountVO> trackCountVOS;

    @QueryField
    @Schema(description = "客户昵称")
    private String niceName;

}
