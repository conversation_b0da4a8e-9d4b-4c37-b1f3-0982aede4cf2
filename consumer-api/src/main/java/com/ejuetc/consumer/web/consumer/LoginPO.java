package com.ejuetc.consumer.web.consumer;

import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.consumer.api.dto.LoginAccountDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "登录入参")
public class LoginPO {
    @Schema(description = "登录方式")
    protected LoginAccountDTO.Type type;

    @Schema(description = "登录标示(短信&密码登录:手机号,支付宝&微信小程序登录:token)")
    protected String loginIdent;

    @Schema(description = "登录认证(短信登录:验证码,密码登录:密码,支付宝&微信小程序登录:空")
    protected String loginAuth;

    @Schema(description = "分享码")
    protected String shareCode;

    @Schema(description = "渠道编码")
    private ChannelDTO.Code channelCode;

    public ChannelDTO.Code getChannelCode() {
        return channelCode != null
                ? channelCode
                : type != null
                ? type.getChannelCode()
                : null;
    }
}
