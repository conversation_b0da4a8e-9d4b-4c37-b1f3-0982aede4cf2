package com.ejuetc.consumer.web.delegation;

import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.ejuetc.consumer.api.dto.DelegationDTO;
import com.ejuetc.consumer.api.dto.DictCategory;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

import static com.ejuetc.commons.base.utils.NumberUtils.isLong;
import static com.ejuetc.consumer.api.dto.DelegationDictDTO.Category.*;

@Data
@Accessors(chain = true)
public class QueryListPO2B {

    @Schema(description = "房源层级")
    private DelegationDTO.Level level;

    @Schema(description = "委托ID")
    private Long delegationId;

    @Schema(description = "经纪人ID(仅公司管理员可指定)")
    private Long brokerId;

    @Schema(description = "搜索关键字")
    private String keyword;

    @Schema(description = "业务类型(二手房/租房)")
    private BusinessOpenDTO.Code type;

    @Schema(description = "区域ID")
    private Long districtId;

    @Schema(description = "镇/街道名称")
    private String townName;

    @Schema(description = "最小价格")
    private BigDecimal minPrice;

    @Schema(description = "最大价格")
    private BigDecimal maxPrice;

    @Schema(description = "最小面积")
    private BigDecimal minArea;

    @Schema(description = "最大面积")
    private BigDecimal maxArea;

    @Schema(description = "最小楼层")
    private BigDecimal minFloor;

    @Schema(description = "最大楼层")
    private BigDecimal maxFloor;

    @Schema(description = "朝向")
    private String orient;

    @Schema(description = "装修情况")
    private String redo;

    @QueryField("detail.floorCategory")
    @Schema(description = "楼层类别")
    @DictCategory(FLOOR_CATEGORY)
    private String floorCategory;

    @Schema(description = "房间数")
    private Integer roomCount;

    @Schema(description = "客厅数")
    private Integer hallCount;

    @Schema(description = "卫生间数")
    private Integer toiletCount;

    @Schema(description = "是否精品")
    private Boolean boutique;

    @Schema(description = "排序")
    private Sort sort;

    @Schema(description = "渠道码")
    private ChannelDTO.Code channelCode;

    @Schema(description = "当前委托状态")
    private DelegationDTO.Status status;

    @Schema(description = "子委托状态")
    private DelegationDTO.Status childStatus;

    @Schema(description = "是否只查询自己的房源")
    private boolean onlySelf = false;

    public String getSortStr() {
        return sort != null ? sort.name() : null;
    }

    @Getter
    public enum Sort implements TitleEnum {
        PRICE_ASC("售价升序"),
        PRICE_DESC("售价降序"),
        AREA_ASC("面积升序"),
        AREA_DESC("面积降序"),
        BOUTIQUE_DESC("精选在前"),
        ;

        private final String title;

        Sort(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @JsonIgnore
    public String getLevelStr() {
        return level != null ? level.name() : null;
    }

    @JsonIgnore
    public String getChildStatusStr() {
        return childStatus != null ? childStatus.name() : null;
    }

    public Long getDelegationId() {
        return delegationId != null
                ? delegationId
                : isLong(keyword)
                ? Long.parseLong(keyword)
                : null;
    }

    public String getKeyword() {
        return isLong(keyword) ? null : keyword;
    }

}
