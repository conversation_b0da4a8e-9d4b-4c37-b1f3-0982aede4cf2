package com.ejuetc.consumer.web.vo;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.querydomain.api.LoadPolicy;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.ejuetc.consumer.api.dto.CommunityBindDTO;
import com.ejuetc.consumer.api.dto.CommunityDTO;
import com.ejuetc.consumer.api.dto.CommunityLayoutDTO;
import com.ejuetc.consumer.api.dto.CommunityPictureDTO;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.consumer.domain.community.CommunityDetail")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "小区详情")
public class CommunityDetailVO extends BaseDTO<CommunityDetailVO> {

    public CommunityDetailVO(Long id) {
        super(id);
    }

    @QueryField
    @Schema(description = "板块代码")
    private String blockCd;

    @QueryField
    @Schema(description = "板块名称")
    private String blockName;

    @QueryField
    @Schema(description = "主键")
    private Long id;

    @QueryField
    @Schema(description = "邮编")
    private String zipCd;

    @QueryField
    @Schema(description = "物业服务中心地址")
    private String proServiceAddr;

    @QueryField
    @Schema(description = "物业工作时间")
    private String propertyOnTime;//pro_on_time

    @QueryField
    @Schema(description = "是否安装智能道闸")
    private Boolean intelGateFlag;//intel_gate_ind

    @QueryField
    @Schema(description = "是否有门禁")
    private Boolean gateControlFlag;//gate_control_ind

    @QueryField
    @Schema(description = "是否有监控")
    private Boolean monitorFlag;//monitor_ind

    @QueryField
    @Schema(description = "保安岗亭数")
    private Integer securityBoothNum;

    @QueryField
    @Schema(description = "保安人数")
    private Integer securityPersonNum;

    @QueryField
    @Schema(description = "是否24小时值守")
    private Boolean securityAlldayFlag;//security_allday_ind

    @QueryField
    @Schema(description = "保安巡察频率")
    private String securityPatrolFrequency;

    @QueryField
    @Schema(description = "是否110联网")
    private Boolean policeNetworkingFlag;//police_networking_ind

    @QueryField
    @Schema(description = "是否是总盘")
    private Boolean homeNameFlag;//home_name_ind

    @QueryField
    @Schema(description = "物业类型")
    private String propertyType;//property_type

    @QueryField
    @Schema(description = "物业年限")
    private String propertyYears;//property_years

    @QueryField
    @Schema(description = "交易权属")
    private String commBelong;//commBelong

    @QueryField
    @Schema(description = "最大建筑年代")
    private String buildMaxYear;//build_max_year

    @QueryField
    @Schema(description = "最小建筑年代")
    private String buildMinYear;

    @QueryField
    @Schema(description = "楼栋总数")
    private Integer buildNum;//building_num

    @QueryField
    @Schema(description = "房屋总数")
    private Integer houseNum;//house_num

    @QueryField
    @Schema(description = "开发商")
    private String developerCorp;//developer_corp

    @QueryField
    @Schema(description = "品牌商")
    private String brandCorp;//brand_corp

    @QueryField
    @Schema(description = "占地面积")
    private BigDecimal actArea;//act_area

    @QueryField
    @Schema(description = "建筑面积")
    private BigDecimal buildArea;//build_area

    @QueryField
    @Schema(description = "建筑类型")
    private String buildingType;//building_type

    @QueryField
    @Schema(description = "房屋类型")
    private String houseType;//house_type

    @QueryField
    @Schema(description = "建筑类别")
    private String buildingCategory;//building_category

    @QueryField
    @Schema(description = "物业公司")
    private String propertyName;//property_name

    @QueryField
    @Schema(description = "物业费")
    private String propertyFee;//property_fee

    @QueryField
    @Schema(description = "物业电话")
    private String propertyPhone;//property_phone

    @QueryField
    @Schema(description = "小区是否封闭")
    protected Boolean communityCloseFlag;//community_close_ind

    @QueryField
    @Schema(description = "车位配比率(户:车)")
    private String parkingRate;//parking_rate

    @QueryField
    @Schema(description = "地上车位数")
    private Integer upParkingNum;//up_parking_num

    @QueryField
    @Schema(description = "地下车位数")
    private Integer downParkingNum;//down_parking_num

    @QueryField
    @Schema(description = "车位总数")
    private Integer parkingNum;//parking_num

    @QueryField
    @Schema(description = "是否人车分流")
    protected Boolean personDivCarFlag;//person_div_car_ind

    @QueryField
    @Schema(description = "是否出售产权车位")
    protected Boolean parkingSaleFlag;//parking_sale_ind

    @QueryField
    @Schema(description = "固定停车费标准")
    private String setParkingFee;//set_parking_fee

    @QueryField
    @Schema(description = "临停停车费标准")
    private String tempParkingFee;//temp_parking_fee

    @QueryField
    @Schema(description = "容积率")
    private BigDecimal volumeRate;//volume_rate

    @QueryField
    @Schema(description = "绿化率")
    private BigDecimal greenRate;//green_rate

    @QueryField
    @Schema(description = "供电描述")
    private String powerdDesc;//powerd_desc

    @QueryField
    @Schema(description = "供水描述")
    private String waterDesc;//water_desc

    @QueryField
    @Schema(description = "供气描述")
    private String gasDesc;//gas_desc

    @QueryField
    @Schema(description = "供暖描述")
    private String heatingDesc;//heating_desc

    @QueryField
    @Schema(description = "图标")
    private String iconUrl;//community_pic

    @QueryField
    @Schema(description = "小区户型")
    private List<CommunityLayoutVO> layouts;

    @QueryField(loadPolicy = LoadPolicy.SPECIFY_LOAD)
    @Schema(description = "小区图片")
    private Map<CommunityPictureDTO.Type, List<String>> picturesMap;

}
