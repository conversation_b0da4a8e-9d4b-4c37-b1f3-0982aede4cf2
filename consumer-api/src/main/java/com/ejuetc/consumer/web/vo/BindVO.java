package com.ejuetc.consumer.web.vo;

import com.ejuetc.commons.base.querydomain.api.LoadPolicy;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.ejuetc.consumer.api.dto.LoginAccountDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "隐号绑定")
@QueryDomain("com.ejuetc.consumer.domain.implicit.ImplicitBind")
public class BindVO{

    @QueryField
    @Schema(description = "绑定过期时间")
    private LocalDateTime expiration;

    @QueryField
    @Schema(description = "隐号号码")
    private String secretPhone;

    @QueryField
    @Schema(description = "经纪人手机号")
    private String brokerPhone;

}
