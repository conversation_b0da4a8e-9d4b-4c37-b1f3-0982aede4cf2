package com.ejuetc.consumer.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "小区提示词")
public class CommunityTipsVO {
    @Schema(description = "小区名称")
    private String estateName;

    @Schema(description = "小区行政地址")
    private String estateExecutionAddress;

    @Schema(description = "纬度")
    private String latitude;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "consumer侧id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long consumerEstateId;

    @Schema(description = "城市编码")
    private String cityCode;

    @Schema(description = "城市名称")
    private String cityName;

    @Schema(description = "区域名称")
    private String district;

    @Schema(description = "省份名称")
    private String province;
}
