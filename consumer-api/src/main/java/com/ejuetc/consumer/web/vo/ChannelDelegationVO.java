package com.ejuetc.consumer.web.vo;

import com.ejuetc.consumer.api.dto.DelegationDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ChannelDelegationVO {
    @Schema(description = "渠道房源ID")
    private Long id;
    @Schema(description = "渠道房源编码")
    private String code;
    @Schema(description = "渠道房源状态")
    private DelegationDTO.Status status;
    @Schema(description = "备注信息")
    private String remark;
    @Schema(description = "渠道端绑定账号昵称")
    private String outNickName;
    @Schema(description = "渠道绑定账号是否冻结")
    private Boolean freeze;
    @Schema(description = "剩余发房量")
    private Long remainQuantity;
    @Schema(description = "购买发房量")
    private Long businessQuantity;

}
