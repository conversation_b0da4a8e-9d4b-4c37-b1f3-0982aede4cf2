package com.ejuetc.consumer.web.vo;

import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ConsumerVO {
    @QueryField
    @Schema(description = "消费者id")
    private Long id;

    @QueryField
    @Schema(description = "消费者手机号")
    private String consumerPhone4Mask;

    @QueryField
    @Schema(description = "最近浏览时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastViewTime;
}
