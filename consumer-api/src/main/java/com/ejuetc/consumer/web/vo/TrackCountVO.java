package com.ejuetc.consumer.web.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Map;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "访问次数统计")
public class TrackCountVO extends DelegationProxyVO<TrackCountVO>{
    @Schema(description = "关联房源ID")
    private Long delegationId;

    @Schema(description = "访问次数")
    private Long count;

    @Schema(description = "最后访问时间")
    private LocalDateTime time;

    @Schema(description = "访问日期")
    private String date;


    public TrackCountVO(Map<String, Object> item) {
        this.delegationId = (Long) item.get("delegation_id");
        this.count = (Long) item.get("count");
        this.time = ((Timestamp) item.get("last_time")).toLocalDateTime();
        this.date = (String) item.get("dt");
    }
}
