package com.ejuetc.consumer.web.vo;

import com.alibaba.fastjson.JSONObject;
import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.ejuetc.consumer.api.dto.ImplicitCallDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

import static com.ejuetc.consumer.api.dto.ImplicitCallDTO.Starter.CONSUMER;
import static com.ejuetc.consumer.api.dto.ImplicitCallDTO.UnconnectedCause.*;
import static java.time.LocalDateTime.parse;
import static java.time.format.DateTimeFormatter.ofPattern;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.consumer.domain.implicit.ImplicitCall")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "隐号拨打记录")
public class ImplicitCallPO {

    @QueryField
    @Schema(description = "通话记录ID")
    private String callId;

    @QueryField
    @Schema(description = "绑定编号")
    @Column(name = "sub_id", columnDefinition = "varchar(64) COMMENT ''")
    private String subId;

    @QueryField
    @Schema(description = "消费者手机号")
    private String consumerPhone;

    @QueryField
    @Schema(description = "绑定的隐号")
    private String secretPhone;

    @QueryField
    @Schema(description = "隐号所属城市名称")
    private String cityName;

    @QueryField
    @Schema(description = "经纪人手机号")
    private String brokerPhone;

    @QueryField
    @Schema(description = "发起方")
    private ImplicitCallDTO.Starter starter;

    @QueryField
    @Schema(description = "挂断方")
    private ImplicitCallDTO.Closer closer;

    @QueryField
    @Schema(description = "控制类型")
    private ImplicitCallDTO.ControlType controlType;

    @QueryField
    @Schema(description = "控制消息")
    private String controlMsg;

    @QueryField
    @Schema(description = "呼叫状态")
    private String callStatus;

    @QueryField
    @Schema(description = "主叫拨打时间")
    private LocalDateTime callTime;

    @QueryField
    @Schema(description = "被叫响铃时间")
    private LocalDateTime ringTime;

    @QueryField
    @Schema(description = "被叫接听时间（通话计费开始时间）")
    private LocalDateTime startTime;

    @QueryField
    @Schema(description = "通话释放时间（通话计费结束时间）")
    private LocalDateTime releaseTime;

    @QueryField
    @Schema(description = "录音下载URL")
    private String recordUrl;

    @JsonIgnore
    private JSONObject body;

    @QueryField
    @Schema(description = "接通失败原因")
    private ImplicitCallDTO.UnconnectedCause unconnectedCause;


    public ImplicitCallPO(JSONObject body) {
        this.body = body;
        this.callId = body.getString("call_id");
        this.starter = switch (body.getIntValue("call_type")) {
            case 0 -> CONSUMER;
            case 1 -> ImplicitCallDTO.Starter.BROKER;
            case 4 -> ImplicitCallDTO.Starter.HOLD;
            default -> ImplicitCallDTO.Starter.UNKNOW;
        };
        this.callTime = parse(body.getString("call_time"), ofPattern("yyyy-MM-dd HH:mm:ss"));
        this.ringTime = parse(body.getString("ring_time"), ofPattern("yyyy-MM-dd HH:mm:ss"));
        this.startTime = parse(body.getString("start_time"), ofPattern("yyyy-MM-dd HH:mm:ss"));
        this.releaseTime = parse(body.getString("release_time"), ofPattern("yyyy-MM-dd HH:mm:ss"));
        this.closer = switch (body.getIntValue("release_dir")) {
            case 0 -> ImplicitCallDTO.Closer.PLATFORM;
            case 1 -> ImplicitCallDTO.Closer.CONSUMER;
            case 2 -> ImplicitCallDTO.Closer.BROKER;
            default -> ImplicitCallDTO.Closer.UNKNOW;
        };
        this.recordUrl = body.getString("record_url");
        this.cityName = body.getString("city");
        this.unconnectedCause = switch (body.getIntValue("unconnected_cause")) {
            case 0 -> NORMAL;
            case 1 -> BLACK;
            case 2 -> NOBIND;
            case 3 -> LIMIT;
            default -> OTHER;
        };
        this.secretPhone = body.getString("secret_no");
        this.callStatus = body.getString("call_status");
        this.controlMsg = body.getString("control_msg");
        this.controlType = ImplicitCallDTO.ControlType.valueOf(body.getString("control_type"));
        this.subId = body.getString("sub_id");
        this.consumerPhone = body.getString("phone_no");
        this.brokerPhone = body.getString("peer_no");
    }

}
