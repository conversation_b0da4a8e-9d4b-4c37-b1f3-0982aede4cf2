package com.ejuetc.consumer.web.vo;

import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;

import com.ejuetc.consumer.api.dto.RegionDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(name = "地区")
@AllArgsConstructor
@NoArgsConstructor
@Data
@QueryDomain("com.ejuetc.consumer.domain.region.Region")
public class RegionVO {
    @QueryField
    @Schema(description = "ID")
    private Long id;

    @QueryField
    @Schema(description = "地区类型")
    private RegionDTO.Type type;

    @QueryField
    @Schema(description = "国标编码")
    private String code;

    @QueryField
    @Schema(description = "全称")
    private String name;

    @QueryField
    @Schema(description = "上级地区名称")
    private String parentName;

    @QueryField
    @Schema(description = "简称")
    private String shortName;

    @QueryField
    @Schema(description = "全拼")
    private String pinyinFull;

    @QueryField
    @Schema(description = "拼音首字母")
    private String pinyinInitials;

    @QueryField
    @Schema(description = "拼音第一个字母")
    private String pinyinFirst;


    @QueryField
    @Schema(description = "开放业务编码")
    private List<BusinessOpenDTO.Code> businessCodes;

    @QueryField
    @Schema(description = "支持的功能编码")
    private List<RegionDTO.FeatureCode> featureCodes;

}
