package com.ejuetc.consumer.web.vo;

import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "贝壳小区")
@QueryDomain("com.ejuetc.consumer.domain.community.CommunityBeike")
public class BeikeCommunityVO {

    @QueryField
    @Schema(description = "贝壳小区ID")
    private Long id;

    @QueryField
    @Schema(description = "城市名")
    private String cityName;

    @QueryField
    @Schema(description = "城市ID")
    protected Long cityId;

    @QueryField
    @Schema(description = "行政区")
    private String districtName;

    @QueryField
    @Schema(description = "小区名")
    private String name;

    @QueryField
    @Schema(description = "经度")
    private BigDecimal lat;

    @QueryField
    @Schema(description = "纬度")
    private BigDecimal lng;


//    @QueryField
//    @Schema(description = "归一小区ID")
//    private Long communityId;

}
