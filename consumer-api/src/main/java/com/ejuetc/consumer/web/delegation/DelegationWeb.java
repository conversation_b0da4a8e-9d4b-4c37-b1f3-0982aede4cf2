package com.ejuetc.consumer.web.delegation;

import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.delegation.EditDelegationPO;
import com.ejuetc.consumer.api.dto.DelegationDictDTO;
import com.ejuetc.consumer.web.consumer.LoginToken;
import com.ejuetc.consumer.web.vo.DelegationVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.ejuetc.commons.base.filter.LoginTokenFilter.LOGIN_INFO_ATT;

@Tag(name = "WEB_房源")
@FeignClient(value = "ejuetc.consumer", path = "/consumer", contextId = "DelegationWeb2C")
public interface DelegationWeb {

    @Operation(summary = "2C_房源列表")
    @PostMapping("/delegation/list")
    ApiResponse<List<DelegationVO>> list2c(
            @RequestAttribute(value = LOGIN_INFO_ATT) LoginToken consumerLoginToken,
            @RequestBody QueryListPO2C request,
            @RequestParam("pageSize") int pageSize,
            @RequestParam("pageNum") int pageNum
    );

    @Operation(summary = "2B_房源列表")
    @PostMapping("/delegation2b/list")
    ApiResponse<List<DelegationVO>> list2b(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasToken,
            @RequestBody QueryListPO2B request,
            @RequestParam("pageSize") int pageSize,
            @RequestParam("page") int pageNum
    );

    @Operation(summary = "2C_房源详情")
    @GetMapping("/delegation/detail")
    ApiResponse<DelegationVO> detail2c(
            @RequestAttribute(value = LOGIN_INFO_ATT) LoginToken consumerLoginToken,
            @Parameter(description = "房源委托代码") @RequestParam("code") String code
    );

    @Operation(summary = "2B_房源详情")
    @GetMapping("/delegation2b/detail")
    ApiResponse<DelegationVO> detail2b(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken loginToken,
            @Parameter(description = "房源委托代码") @RequestParam Long id
    );

    @Operation(summary = "2C_经纪人更多相关房源")
    @GetMapping("/delegation/brokerMoreHouses")
    ApiResponse<List<DelegationVO>> brokerMoreHouses(
            @RequestAttribute(value = LOGIN_INFO_ATT) LoginToken consumerLoginToken,
            @RequestParam("code") String code,
            @RequestParam(value = "pageSize", defaultValue = "5") int pageSize,
            @RequestParam(value = "pageNum", defaultValue = "1") int pageNum
    );

    @Operation(summary = "2B_查询房源基础字段规则")
    @GetMapping("/delegation2b/baseFieldRule")
    ApiResponse<Collection<String>> baseFieldRule(
            @Parameter(description = "推广渠道列表") @RequestParam List<ChannelDTO.Code> channelCodes,
            @Parameter(description = "业务码") @RequestParam BusinessOpenDTO.Code businessCode
    );

    @Operation(summary = "2B_查询房源政府核验字段规则")
    @GetMapping("/delegation2b/govVerifyFieldRule")
    ApiResponse<Collection<String>> govVerifyFieldRule(
            @Parameter(description = "小区地址") @RequestParam String communityAddress,
            @Parameter(description = "小区名") @RequestParam String communityName,
            @Parameter(description = "业务码") @RequestParam BusinessOpenDTO.Code businessCode
    );

    @Operation(summary = "2B_编辑房源")
    @PostMapping("/delegation2b/edit")
    ApiResponse<DelegationVO> edit(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasToken,
            @RequestBody EditDelegationPO po
    );

    @Operation(summary = "2B_上下架房源")
    @PostMapping("/delegation2b/upDown")
    ApiResponse<DelegationVO> upDown(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasToken,
            @Parameter(description = "渠道房源ID") @RequestParam("id") Long channelDelegationId,
            @Parameter(description = "true:上架 false:下架") @RequestParam boolean up
    );

    @Operation(summary = "2B_批量上架经纪房源到渠道")
    @PostMapping("/delegation2b/batchUp")
    ApiResponse<DelegationVO> batchUp(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasToken,
            @Parameter(description = "经纪房源ID列表") @RequestBody List<Long> brokerDelegationIds,
            @Parameter(description = "上架渠道编码") @RequestParam List<ChannelDTO.Code> channelCodes
    );

    @Operation(summary = "2B_标记房源是否为精品房源")
    @PostMapping("/delegation2b/boutique")
    ApiResponse<DelegationVO> boutique(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasToken,
            @Parameter(description = "房源ID") @RequestParam Long id,
            @Parameter(description = "是否为精品房源") @RequestParam boolean boutique
    );

    @Operation(summary = "2B_删除房源")
    @PostMapping("/delegation2b/delete")
    ApiResponse<DelegationVO> delete(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasToken,
            @Parameter(description = "房源ID") @RequestParam Long id,
            @Parameter(description = "核销原因") @RequestParam String reason
    );

    @Operation(summary = "2B_浏览次数最多的房源")
    @GetMapping("/delegation/mostViewedDelegations")
    ApiResponse<List<DelegationVO>> mostViewedDelegations(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @RequestParam Long relationId
    );

    @Operation(summary = "2B_浏览房源记录")
    @GetMapping("/delegation/viewedDelegations")
    ApiResponse<List<DelegationVO>> viewedDelegations(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @RequestParam Long relationId,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @RequestParam(value = "pageNum", defaultValue = "1") int pageNum
    );

    @Operation(summary = "2B_查询字典列表")
    @PostMapping("/dict/list")
    ApiResponse<Map<DelegationDictDTO.Category, List<String>>> list(@RequestBody List<DelegationDictDTO.Category> categories);


}
