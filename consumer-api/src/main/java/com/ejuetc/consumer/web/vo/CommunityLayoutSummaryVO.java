package com.ejuetc.consumer.web.vo;

import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "小区户型图汇总")
@QueryDomain("com.ejuetc.consumer.domain.community.CommunityLayout")
public class CommunityLayoutSummaryVO {

    @QueryField
    @Schema(description = "户型名称")
    protected String name;//house_layout_name

    @QueryField
    @Schema(description = "卧室数")
    private Integer roomCount;//room_num

    @QueryField
    @Schema(description = "客厅数")
    private Integer hallCount;//hall_num

    @QueryField
    @Schema(description = "卫生间数")
    private Integer toiletCount;//bath_num

    @QueryField
    @Schema(description = "阳台数")
    private Integer balconyCount;//balcony_num

    @QueryField
    @Schema(description = "厨房数")
    private Integer kitchenCount;//kitchen_num

    @Schema(description = "最小面积")
    private BigDecimal areaMin;//min_area

    @Schema(description = "最大面积")
    private BigDecimal areaMax;//max_area

    @Schema(description = "户型图")
    protected List<String> picUrl = new ArrayList<>();

    @Schema(description = "总套数")
    private int setNum = 0;//set_num

    public void addPicUrl(String url) {
        if (url != null && !url.isEmpty() && !this.picUrl.contains(url)) {
            this.picUrl.add(url);
        }
    }

    public void addSetNum(int count) {
        this.setNum += count;
    }

    public void setAreaMin(BigDecimal areaMin) {
        if (areaMin != null && (this.areaMin == null || areaMin.compareTo(this.areaMin) < 0)) {
            this.areaMin = areaMin;
        }
    }

    public void setAreaMax(BigDecimal areaMax) {
        if (areaMax != null && (this.areaMax == null || areaMax.compareTo(this.areaMax) > 0)) {
            this.areaMax = areaMax;
        }
    }
}
