package com.ejuetc.consumer.web.consumer;

import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.dto.ConsumerShareDTO;
import com.ejuetc.consumer.api.dto.LoginAccountDTO;
import com.ejuetc.consumer.web.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.ejuetc.commons.base.filter.LoginTokenFilter.LOGIN_INFO_ATT;

@Tag(name = "WEB_消费者")
@FeignClient(value = "ejuetc.consumer", path = "/consumer", contextId = "ConsumerWeb")
public interface ConsumerWeb {

    @Operation(summary = "WEB_消费者登录")
    @PostMapping("/login")
    ApiResponse<LoginToken> login(@RequestBody LoginPO po);

    @Operation(summary = "WEB_变更分享码")
    @PostMapping("/changeShareCode")
    ApiResponse<Void> changeShareCode(
            @RequestAttribute(LOGIN_INFO_ATT) LoginToken loginToken,
            @Parameter(description = "分享码") @RequestParam String shareCode
    );

    @Operation(summary = "WEB_修改消费者信息")
    @PostMapping("/edit")
    ApiResponse<LoginToken> edit(@RequestAttribute(LOGIN_INFO_ATT) LoginToken loginToken,
                        @RequestBody EditConsumerPO po);

    @Operation(summary = "WEB_授权位置")
    @PostMapping("/location")
    ApiResponse<?> location(
            @RequestAttribute(LOGIN_INFO_ATT) LoginToken loginToken,
            @RequestParam("location") String location
    );

    @Operation(summary = "WEB_根据授权码绑定用户手机号")
    @PostMapping("/bindPhoneByAuth")
    ApiResponse<String> bindPhoneByAuth(
            @RequestAttribute(LOGIN_INFO_ATT) LoginToken loginToken,
            @RequestParam("authCode") String authCode
    );

    @Operation(summary = "WEB_消费者分享")
    @PostMapping("/shareByC")
    ApiResponse<?> shareByConsumer(
            @RequestAttribute(LOGIN_INFO_ATT) LoginToken token,
            @Parameter(description = "分享码") @RequestParam("shareCode") String shareCode,
            @Parameter(description = "分享目标类型") @RequestParam("targetType") ConsumerShareDTO.TargetType targetType,
            @Parameter(description = "分享目标主键") @RequestParam("targetId") Long targetId,
            @Parameter(description = "发起分享经纪人的id") @RequestParam(value = "brokerId", required = false) Long brokerId
    );

    @Operation(summary = "WEB_获取消费者详情(B端)")
    @GetMapping("/detail")
    ApiResponse<ConsumerVO> consumerDetail(@RequestAttribute(LOGIN_INFO_ATT) SaasLoginToken loginToken,
                                           @RequestParam Long consumerId);


    @Operation(summary = "WEB_客源关系列表")
    @PostMapping("/implicit/relationList")
    ApiResponse<List<RelationVO>> relationList(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken loginToken,
            @Parameter(description = "账号类型") @RequestParam(required = false) LoginAccountDTO.Type accountType,
            @Parameter(description = "查询关键字") @RequestParam(required = false) String keyword,
            @Parameter(description = "是否绑定有效") @RequestParam(required = false) Boolean expirationFlag,
            @RequestParam("pageSize") int pageSize,
            @RequestParam("pageNum") int pageNum

    );

    @Operation(summary = "WEB_客源关系详情")
    @PostMapping("/implicit/relationDetail")
    ApiResponse<RelationVO> relationDetail(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken loginToken,
            @Parameter(description = "客户关系ID") @RequestParam("relationId") Long relationId
    );

    @Operation(summary = "WEB_客源访问房源列表")
    @PostMapping("/implicit/relationViewDelegations")
    ApiResponse<List<TrackCountVO>> relationViewDelegations(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken loginToken,
            @Parameter(description = "客户关系ID") @RequestParam("relationId") Long relationId,
            @RequestParam("pageSize") int pageSize,
            @RequestParam("pageNum") int pageNum
    );

    @Operation(summary = "WEB_编辑客源关系")
    @PostMapping("/consumer/relationEdit")
    ApiResponse<RelationVO> relationEdit(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken token,
            @RequestBody RelationPO<?> ro
    );

    @Operation(summary = "WEB_查询分享统计信息")
    @GetMapping("/consumer/shareStatistics")
    ApiResponse<ShareStatisticsVO> shareStatistics(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken token
    );

}
