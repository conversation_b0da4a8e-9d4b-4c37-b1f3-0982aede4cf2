package com.ejuetc.consumer.web.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class DelegationProxyVO<Subtype extends DelegationProxyVO> {

    @QueryField
    @JsonIgnore
    @JSONField(serialize = false)
    @Schema(description = "关联房源")
    private DelegationVO delegation;

    public Subtype setDelegation(DelegationVO delegation) {
        this.delegation = delegation;
        return (Subtype) this;
    }

    public String getDelegationCode() {
        return delegation == null ? null : delegation.getCode();
    }

    public String getDelegationCoverUrl() {
        return delegation == null ? null : delegation.getCoverUrl();
    }

    public String getDistrictName() {
        return delegation == null ? null : delegation.getDistrictName();
    }

    public String getCommunityName() {
        return delegation == null ? null : delegation.getCommunityName();
    }

    public BigDecimal getBuildingArea() {
        return delegation == null ? null : delegation.getBuildingArea();
    }

    public String getBusiName() {
        return delegation == null ? null : delegation.getBusiName();
    }

    public String getTownName() {
        return delegation == null ? null : delegation.getTownName();
    }

    public BigDecimal getPriceTotal() {
        return delegation == null ? null : delegation.getPriceTotal();
    }

    public String getLayoutName() {
        return delegation == null ? null : delegation.getLayoutName();
    }

    public BusinessOpenDTO.Code getDelegationType() {
        return delegation == null ? null : delegation.getType();
    }

    public Integer getRoomCount() {
        return delegation == null ? null : delegation.getRoomCount();
    }

    public Integer getHallCount() {
        return delegation == null ? null : delegation.getHallCount();
    }

    public Integer getToiletCount() {
        return delegation == null ? null : delegation.getToiletCount();
    }
}
