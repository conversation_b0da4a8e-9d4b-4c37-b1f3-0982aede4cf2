package com.ejuetc.consumer.web.consumer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "修改Consumer入参")
public class EditConsumerPO {
    @Schema(description = "昵称")
    private String nickName;
    @Schema(description = "头像")
    private String iconUrl;
}
