package com.ejuetc.consumer.gateway;


import com.ejuetc.consumer.api.delegation.DeleteBrokerDelegatePO;
import com.ejuetc.consumer.api.delegation.EditDelegationPO4Gateway;
import com.ejuetc.saasapi.pro.GetawayResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

@Tag(name = "网关_房源接口")
@FeignClient(value = "ejuetc.consumer", path = "/consumer", contextId = "DelegationGateway")
public interface DelegationGateway {

    @PostMapping("/gateway/delegation/edit")
    GetawayResponse edit(@RequestHeader Map<String, String> headers, @RequestBody EditDelegationPO4Gateway po);

    @PostMapping("/gateway/delegation/deleteBrokerDelegates")
    GetawayResponse deleteBrokerDelegates(@RequestHeader Map<String, String> headers, @RequestBody DeleteBrokerDelegatePO po);
}
