package com.ejuetc.consumer.gateway;


import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.community.QueryCommunityDetailPO;
import com.ejuetc.consumer.api.community.QueryCommunityPO;
import com.ejuetc.consumer.api.community.QueryLayoutGroupPO;
import com.ejuetc.consumer.web.vo.BeikeCommunityVO;
import com.ejuetc.consumer.web.vo.CommunityVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Tag(name = "网关_小区接口")
@FeignClient(value = "ejuetc.consumer", path = "/consumer", contextId = "CommunityGateway")
public interface CommunityGateway {

    @Operation(summary = "查询贝壳小区")
    @PostMapping("/gateway/community/queryBeikeCommunity")
    ApiResponse<List<BeikeCommunityVO>> queryBeikeCommunity(@RequestBody QueryCommunityPO queryCommunityPO);

    @Operation(summary = "查询分组小区户型")
    @PostMapping("/gateway/community/queryLayoutGroup")
    ApiResponse<Map<Integer, Set<String>>> queryLayoutGroup(@RequestBody QueryLayoutGroupPO po);

    @Operation(summary = "查询小区详情")
    @PostMapping("/gateway/community/queryDetail")
    ApiResponse<CommunityVO> queryDetail(@RequestBody QueryCommunityDetailPO po);

}
