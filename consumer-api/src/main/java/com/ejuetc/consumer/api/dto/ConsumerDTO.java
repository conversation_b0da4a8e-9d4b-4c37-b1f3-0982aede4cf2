package com.ejuetc.consumer.api.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.consumer.domain.consumer.Consumer")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "登录账号")
public class ConsumerDTO extends BaseDTO<ConsumerDTO> {

    public ConsumerDTO(Long userId) {
        super(userId);
    }

    @QueryField
    @Schema(description = "手机号")
    private String phone;

    @QueryField
    @Schema(description = "昵称")
    private String nickName;

}
