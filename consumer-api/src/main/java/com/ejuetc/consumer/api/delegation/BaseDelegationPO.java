package com.ejuetc.consumer.api.delegation;

import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.ejuetc.consumer.api.dto.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.FetchType;
import jakarta.persistence.OneToMany;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.ejuetc.channel.dto.BusinessOpenDTO.Code.NEW;
import static com.ejuetc.channel.dto.BusinessOpenDTO.Code.RENT;
import static com.ejuetc.commons.base.querydomain.api.LoadPolicy.SPECIFY_LOAD;
import static com.ejuetc.consumer.api.dto.DelegationDictDTO.Category.*;


@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "房源委托基础类")
public class BaseDelegationPO<T extends BaseDelegationPO<?>> {
    /*************************************** 小区相关 ******************************************/

    @QueryField
    @Schema(description = "父房源外部ID(与parentId二选一或为空)")
    protected String parentSourceId;

    @QueryField("detail.communityBind.address")
    @Schema(description = "小区地址")
    protected String communityAddress;

    @QueryField
    @Schema(description = "主键")
    protected Long id;

    @QueryField("parent.id")
    @Schema(description = "父房源ID")
    protected Long parentId;

    @QueryField
    @Schema(description = "房源层级")
    protected DelegationDTO.Level level;

    @QueryField("detail.type")
    @Schema(description = "委托类型")
    protected BusinessOpenDTO.Code type;

    @QueryField("detail.subType")
    @Schema(description = "交易子类型")
    protected DelegationDTO.SubType subType;

    @QueryField
    @Schema(description = "标题")
    protected String title;

    @DictCategory(COMMUNITY_AROUND)
    @QueryField("detail.around")
    @Schema(description = "周边配套")
    protected List<String> around;

    @QueryField("detail.roomNum")
    @Schema(description = "房号")
    protected String roomNum;

    @QueryField("detail.payMonths")
    @Schema(description = "付款月数")
    protected Integer payMonths;

    @QueryField("detail.depositMonths")
    @Schema(description = "押金月数")
    protected Integer depositMonths;

    @QueryField("detail.checkinDate")
    @Schema(description = "入住日期(空表示随时入住)")
    protected LocalDate checkinDate;

    @DictCategory(ROOM_EQUIPMENTS)
    @QueryField("detail.equipments")
    @Schema(description = " 配套设施")
    protected List<String> equipments;

    @QueryField
    @Schema(description = "描述")
    protected String description;

    @QueryField("labels")
    @DictCategory({LABELS_SALE, LABELS_RENT, LABELS_NEW})
    @Schema(description = "关键字")
    protected List<String> labels;

    @DictCategory(REDO)
    @QueryField("detail.redo")
    @Schema(description = "装修情况编码")
    protected String redo;

    @QueryField("detail.boutique")
    @Schema(description = "是否精品")
    protected Boolean boutique;

    @QueryField(value = "detail.currentFloor", loadPolicy = SPECIFY_LOAD)
    @Schema(description = "当前楼层")
    protected Integer currentFloor;

    @QueryField("detail.totalFloor")
    @Schema(description = "总楼层")
    protected Integer totalFloor;

    @QueryField("detail.tagElevator")
    @Schema(description = "是否有电梯")
    protected Boolean tagElevator;

    @DictCategory(COMPLETION_TIME)
    @QueryField("detail.completionTime")
    @Schema(description = "建成年代")
    protected String completionTime;

    @DictCategory(PROPERTY_TYPE)
    @QueryField("detail.propertyType")
    @Schema(description = "物业类型")
    protected String propertyType;

    @QueryField("detail.elevatorCount")
    @Schema(description = "电梯数量")
    protected Integer elevatorCount;

    @QueryField("detail.roomPerFloor")
    @Schema(description = "每层户数")
    protected Integer roomPerFloor;

    @QueryField("detail.parkingRatio")
    @Schema(description = "车位配比")
    protected String parkingRatio;

    @QueryField("detail.propertyManagementCompany")
    @Schema(description = "物业管理公司")
    protected String propertyManagementCompany;

    @QueryField("detail.propertyManagementFee")
    @Schema(description = "物业管理费用")
    protected BigDecimal propertyManagementFee;

    @QueryField
    @Schema(description = "单价|押金")
    protected BigDecimal priceUnit;

    @QueryField
    @Schema(description = "总价|租金")
    protected BigDecimal priceTotal;

    @QueryField("detail.deadline")
    @Schema(description = "委托到期时间")
    protected LocalDateTime deadline;

    @QueryField("detail.buildName")
    @Schema(description = "楼栋名")
    protected String buildName;

    @QueryField("detail.unitName")
    @Schema(description = "单元名")
    protected String unitName;

    @QueryField("detail.roomName")
    @Schema(description = "房间名")
    protected String roomName;

    @DictCategory(ORIENT)
    @QueryField("detail.orient")
    @Schema(description = "朝向")
    protected String orient;

    @QueryField("detail.roomCount")
    @Schema(description = "房间数")
    protected Integer roomCount;

    @QueryField("detail.hallCount")
    @Schema(description = "客厅数")
    protected Integer hallCount;

    @QueryField("detail.toiletCount")
    @Schema(description = "卫生间数")
    protected Integer toiletCount;

    @QueryField("detail.buildingArea")
    @Schema(description = "建筑面积")
    protected BigDecimal buildingArea;

    @QueryField("detail.useArea")
    @Schema(description = "使用面积")
    protected BigDecimal useArea;

    @QueryField
    @Schema(description = "来源ID")
    protected String sourceId;

    @QueryField
    @Schema(description = "来源类型(SAAS/SHARING)")
    protected String sourceType;


    @DictCategory(ROOM_TYPE)
    @QueryField("detail.roomType")
    @Schema(description = "房间类型编码")
    protected String roomType;

    @QueryField("detail.communityName")
    @Schema(description = "小区名")
    protected String communityName;

    /*************************************** 产权信息 **************************************************/

    @DictCategory(HOUSE_TYPE)
    @QueryField("detail.houseType")
    @Schema(description = "产权类型")
    protected String houseType;

    @DictCategory(HOUSE_CERT_TYPE)
    @QueryField("detail.houseCertType")
    @Schema(description = "产权证类型")
    protected String houseCertType;

    @QueryField("detail.houseCertNO")
    @Schema(description = "产权证证号")
    protected String houseCertNO;

    @QueryField("detail.houseBusiNO")
    @Schema(description = "房屋业务件号(政府核验相关)")
    protected String houseBusiNO;

    @DictCategory(HOUSE_PLAN_PURPOSE)
    @QueryField("detail.housePlanPurpose")
    @Schema(description = "房屋规划用途")
    protected String housePlanPurpose;

    @QueryField("detail.ownerName")
    @Schema(description = "业主姓名")
    protected String ownerName;

    @QueryField("detail.ownerCertNO")
    @Schema(description = "业主证件号")
    protected String ownerCertNO;


    /**************************************** 委托信息 ***************************************/
    @QueryField("detail.bailorName")
    @Schema(description = "委托人姓名")
    protected String bailorName;

    @DictCategory(BAILOR_CERT_TYPE)
    @QueryField("detail.bailorCertType")
    @Schema(description = "委托人证件类型")
    protected String bailorCertType;

    @QueryField("detail.bailorCertNO")
    @Schema(description = "委托人证件号码")
    protected String bailorCertNO;


    @QueryField("detail.bailorNames")
    @Schema(description = "委托人姓名集合")
    protected String bailorNames;


    /*************************************** 政府核验信息 ***************************************/
    @QueryField("detail.govVerifyCode")
    @Schema(description = "政府核验码")
    protected String govVerifyCode;

    @QueryField("detail.govVerifyUrl")
    @Schema(description = "政府核验码图片地址")
    protected String govVerifyUrl;

    @QueryField("detail.govContractCode")
    @Schema(description = "政府委托协议编码")
    protected String govContractCode;

    @QueryField("detail.govPromoCode")
    @Schema(description = "政府房源推广码")
    protected String govPromoCode;

    @QueryField("detail.metro")
    @Schema(description = "地铁")
    private String metro;

    @QueryField("detail.school")
    @Schema(description = "学区")
    private String school;

    @QueryField("detail.listDate")
    @Schema(description = "挂牌日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    protected LocalDate listDate;

    @QueryField("detail.propertyYears")
    @DictCategory(PROPERTY_YEARS)
    @Schema(description = "产权年限")
    protected String propertyYears;

    @QueryField("detail.houseCertVerify")
    @Schema(description = "是否房产证验证")
    protected Boolean houseCertVerify;

    @QueryField("detail.efficiencyRate")
    @Schema(description = "得房率")
    protected BigDecimal efficiencyRate;

    @QueryField("detail.brokerService")
    @DictCategory(BROKER_SERVICE)
    @Schema(description = "中介服务")
    protected List<String> brokerService;

    @QueryField("detail.parking")
    @Schema(description = "是否有车位")
    protected Boolean parking;

    @QueryField("detail.lookType")
    @DictCategory(LOOK_TYPE)
    @Schema(description = "看房时间类型")
    protected String lookType;

    @QueryField("detail.saleReason")
    @Schema(description = "出售原因")
//    @DictCategory(SALE_REASON)
    protected String saleReason;

    @QueryField("detail.houseSituation")
    @DictCategory(HOUSE_SITUATION)
    @Schema(description = "房屋现状")
    protected String houseSituation;

    @QueryField("detail.houseCertAddress")
    @Schema(description = "房屋地址")
    protected String houseCertAddress;

    @QueryField("detail.soleHouse")
    @Schema(description = "唯一住房")
    protected Boolean soleHouse;

    @QueryField("detail.houseStructure")
    @Schema(description = "房屋结构")
    @DictCategory(HOUSE_STRUCTURE)
    protected String houseStructure;

    @QueryField("detail.propertyOwnership")
    @Schema(description = "产权所属")
    @DictCategory(PROPERTY_OWNERSHIP)
    protected String propertyOwnership;

    @QueryField("detail.buildingCategory")
    @Schema(description = "建筑类别")
    @DictCategory(BUILDING_CATEGORY)
    protected String buildingCategory;

    @QueryField("detail.buildingType")
    @Schema(description = "建筑类型")
    @DictCategory(BUILDING_TYPE)
    protected String buildingType;

    @QueryField("detail.houseYears")
    @Schema(description = "房屋年限")
    @DictCategory(HOUSE_YEARS)
    protected String houseYears;

    public T setParentSourceId(String parentSourceId) {
        this.parentSourceId = parentSourceId;
        return (T) this;
    }

    public T setCommunityAddress(String communityAddress) {
        this.communityAddress = communityAddress;
        return (T) this;
    }

    public T setId(Long id) {
        this.id = id;
        return (T) this;
    }

    public T setParentId(Long parentId) {
        this.parentId = parentId;
        return (T) this;
    }

    public T setLevel(DelegationDTO.Level level) {
        this.level = level;
        return (T) this;
    }

    public T setType(BusinessOpenDTO.Code type) {
        this.type = type;
        return (T) this;
    }

    public T setSubType(DelegationDTO.SubType subType) {
        this.subType = subType;
        return (T) this;
    }

    public T setTitle(String title) {
        this.title = title;
        return (T) this;
    }

    public T setAround(List<String> around) {
        this.around = around;
        return (T) this;
    }

    public T setRoomNum(String roomNum) {
        this.roomNum = roomNum;
        return (T) this;
    }

    public T setPayMonths(Integer payMonths) {
        this.payMonths = payMonths;
        return (T) this;
    }

    public T setDepositMonths(Integer depositMonths) {
        this.depositMonths = depositMonths;
        return (T) this;
    }

    public T setCheckinDate(LocalDate checkinDate) {
        this.checkinDate = checkinDate;
        return (T) this;
    }

    public T setEquipments(List<String> equipments) {
        this.equipments = equipments;
        return (T) this;
    }

    public T setDescription(String description) {
        this.description = description;
        return (T) this;
    }

    public T setLabels(List<String> labels) {
        this.labels = labels;
        return (T) this;
    }

    public T setRedo(String redo) {
        this.redo = redo;
        return (T) this;
    }

    public T setBoutique(Boolean boutique) {
        this.boutique = boutique;
        return (T) this;
    }

    public T setCurrentFloor(Integer currentFloor) {
        this.currentFloor = currentFloor;
        return (T) this;
    }

    public T setTotalFloor(Integer totalFloor) {
        this.totalFloor = totalFloor;
        return (T) this;
    }

    public T setTagElevator(Boolean tagElevator) {
        this.tagElevator = tagElevator;
        return (T) this;
    }

    public T setCompletionTime(String completionTime) {
        this.completionTime = completionTime;
        return (T) this;
    }

    public T setPropertyType(String propertyType) {
        this.propertyType = propertyType;
        return (T) this;
    }

    public T setElevatorCount(Integer elevatorCount) {
        this.elevatorCount = elevatorCount;
        return (T) this;
    }

    public T setRoomPerFloor(Integer roomPerFloor) {
        this.roomPerFloor = roomPerFloor;
        return (T) this;
    }

    public T setParkingRatio(String parkingRatio) {
        this.parkingRatio = parkingRatio;
        return (T) this;
    }

    public T setPropertyManagementCompany(String propertyManagementCompany) {
        this.propertyManagementCompany = propertyManagementCompany;
        return (T) this;
    }

    public T setPropertyManagementFee(BigDecimal propertyManagementFee) {
        this.propertyManagementFee = propertyManagementFee;
        return (T) this;
    }

    public T setPriceUnit(BigDecimal priceUnit) {
        this.priceUnit = priceUnit;
        return (T) this;
    }

    public T setPriceTotal(BigDecimal priceTotal) {
        this.priceTotal = priceTotal;
        return (T) this;
    }

    public T setDeadline(LocalDateTime deadline) {
        this.deadline = deadline;
        return (T) this;
    }

    public T setBuildName(String buildName) {
        this.buildName = buildName;
        return (T) this;
    }

    public T setUnitName(String unitName) {
        this.unitName = unitName;
        return (T) this;
    }

    public T setRoomName(String roomName) {
        this.roomName = roomName;
        return (T) this;
    }

    public T setOrient(String orient) {
        this.orient = orient;
        return (T) this;
    }

    public T setRoomCount(Integer roomCount) {
        this.roomCount = roomCount;
        return (T) this;
    }

    public T setHallCount(Integer hallCount) {
        this.hallCount = hallCount;
        return (T) this;
    }

    public T setToiletCount(Integer toiletCount) {
        this.toiletCount = toiletCount;
        return (T) this;
    }

    public T setBuildingArea(BigDecimal buildingArea) {
        this.buildingArea = buildingArea;
        return (T) this;
    }

    public T setUseArea(BigDecimal useArea) {
        this.useArea = useArea;
        return (T) this;
    }

    public T setSourceId(String sourceId) {
        this.sourceId = sourceId;
        return (T) this;
    }

    public T setSourceType(String sourceType) {
        this.sourceType = sourceType;
        return (T) this;
    }

    public T setRoomType(String roomType) {
        this.roomType = roomType;
        return (T) this;
    }

    public T setCommunityName(String communityName) {
        this.communityName = communityName;
        return (T) this;
    }

    public T setHouseCertType(String houseCertType) {
        this.houseCertType = houseCertType;
        return (T) this;
    }

    public T setHouseCertNO(String houseCertNO) {
        this.houseCertNO = houseCertNO;
        return (T) this;
    }

    public T setHouseBusiNO(String houseBusiNO) {
        this.houseBusiNO = houseBusiNO;
        return (T) this;
    }

    public T setHousePlanPurpose(String housePlanPurpose) {
        this.housePlanPurpose = housePlanPurpose;
        return (T) this;
    }

    public T setOwnerName(String ownerName) {
        this.ownerName = ownerName;
        return (T) this;
    }

    public T setOwnerCertNO(String ownerCertNO) {
        this.ownerCertNO = ownerCertNO;
        return (T) this;
    }

    public T setBailorName(String bailorName) {
        this.bailorName = bailorName;
        return (T) this;
    }

    public T setBailorCertType(String bailorCertType) {
        this.bailorCertType = bailorCertType;
        return (T) this;
    }

    public T setBailorCertNO(String bailorCertNO) {
        this.bailorCertNO = bailorCertNO;
        return (T) this;
    }

    public T setBailorNames(String bailorNames) {
        this.bailorNames = bailorNames;
        return (T) this;
    }

    public T setGovVerifyCode(String govVerifyCode) {
        this.govVerifyCode = govVerifyCode;
        return (T) this;
    }

    public T setGovVerifyUrl(String govVerifyUrl) {
        this.govVerifyUrl = govVerifyUrl;
        return (T) this;
    }

    public T setGovContractCode(String govContractCode) {
        this.govContractCode = govContractCode;
        return (T) this;
    }

    public T setGovPromoCode(String govPromoCode) {
        this.govPromoCode = govPromoCode;
        return (T) this;
    }

    public T setListDate(LocalDate listDate) {
        this.listDate = listDate;
        return (T) this;
    }

    public T setPropertyYears(String propertyYears) {
        this.propertyYears = propertyYears;
        return (T) this;
    }

    public T setHouseCertVerify(Boolean houseCertVerify) {
        this.houseCertVerify = houseCertVerify;
        return (T) this;
    }

    public T setEfficiencyRate(BigDecimal efficiencyRate) {
        this.efficiencyRate = efficiencyRate;
        return (T) this;
    }

    public T setBrokerService(List<String> brokerService) {
        this.brokerService = brokerService;
        return (T) this;
    }

    public T setParking(Boolean parking) {
        this.parking = parking;
        return (T) this;
    }

    public T setLookType(String lookType) {
        this.lookType = lookType;
        return (T) this;
    }

    public T setSaleReason(String saleReason) {
        this.saleReason = saleReason;
        return (T) this;
    }

    public T setHouseSituation(String houseSituation) {
        this.houseSituation = houseSituation;
        return (T) this;
    }

    public T setHouseCertAddress(String houseCertAddress) {
        this.houseCertAddress = houseCertAddress;
        return (T) this;
    }

    public T setSoleHouse(Boolean soleHouse) {
        this.soleHouse = soleHouse;
        return (T) this;
    }

    public T setHouseStructure(String houseStructure) {
        this.houseStructure = houseStructure;
        return (T) this;
    }

    public T setPropertyOwnership(String propertyOwnership) {
        this.propertyOwnership = propertyOwnership;
        return (T) this;
    }

    public T setBuildingCategory(String buildingCategory) {
        this.buildingCategory = buildingCategory;
        return (T) this;
    }

    public T setBuildingType(String buildingType) {
        this.buildingType = buildingType;
        return (T) this;
    }

    public T setHouseYears(String houseYears) {
        this.houseYears = houseYears;
        return (T) this;
    }

}

