package com.ejuetc.consumer.api.delegation;

import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.querydomain.api.LoadPolicy;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.ejuetc.consumer.api.dto.DelegationDTO;
import com.ejuetc.consumer.api.dto.DictCategory;
import com.ejuetc.consumer.api.dto.RegionDTO;
import com.ejuetc.consumer.web.vo.BrokerVO;
import com.ejuetc.consumer.web.vo.ChannelDelegationVO;
import com.ejuetc.consumer.web.vo.CommunityVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static com.ejuetc.consumer.api.dto.DelegationDictDTO.Category.FLOOR_CATEGORY;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "房源委托信息")
@QueryDomain("com.ejuetc.consumer.domain.delegation.Delegation")
public class DelegationVO4Gateway{

    @QueryField
    @Schema(description = "来源ID")
    protected String sourceId;

    @QueryField
    @Schema(description = "父房源外部ID(与parentId二选一或为空)")
    protected String parentSourceId;

//    @QueryField
//    @Schema(description = "主键")
//    protected Long id;

//    @QueryField
//    @Schema(description = "房源层级")
//    protected DelegationDTO.Level level;

//    @QueryField("detail.type")
//    @Schema(description = "委托类型")
//    protected BusinessOpenDTO.Code type;

//    @QueryField
//    @Schema(description = "(仅对渠道房源)渠道")
//    protected ChannelDTO.Code channelCode;

//    @QueryField
//    @Schema(description = "状态")
//    private DelegationDTO.Status status;

//    @QueryField
//    @Schema(description = "备注")
//    private String remark;

    @Schema(description = "(仅对经纪房源)渠道发布状态映射")
    @QueryField(value = "channelDelegationMap", loadPolicy = LoadPolicy.SPECIFY_LOAD)
    private Map<ChannelDTO.Code, ChannelDelegationVO> channelDelegationMap;

}
