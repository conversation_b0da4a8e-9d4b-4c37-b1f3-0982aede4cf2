package com.ejuetc.consumer.api.delegation;

import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "网关编辑房源委托")
public class EditDelegationPO4Gateway extends EditDelegationPO {
    @Schema(description = "经纪人主键")
    private Long brokerId;

}

