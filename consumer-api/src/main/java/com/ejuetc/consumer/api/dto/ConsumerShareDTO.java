package com.ejuetc.consumer.api.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.consumer.domain.consumer.ConsumerShare")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "分享消费者")
public class ConsumerShareDTO extends BaseDTO<ConsumerShareDTO> {

    public enum TargetType implements TitleEnum {
        DELEGATION("房源"),
        BROKER("经纪人"),
        ;

        private final String title;

        private TargetType(String title) {
            this.title = title;
        }

        @Override
        public String getTitle() {
            return this.title;
        }

        @Override
        public String toString() {
            return this.name() + "-" + this.title;
        }
    }

    public ConsumerShareDTO(Long userId) {
        super(userId);
    }

}
