package com.ejuetc.consumer.api.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.consumer.domain.broker.Broker")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "经纪人")
public class BrokerDTO extends BaseDTO<BrokerDTO> {

    public BrokerDTO(Long userId) {
        super(userId);
    }

    @QueryField
    @Schema(description = "经纪人编码")
    private String code;

    @QueryField
    @Schema(description = "(身份证)姓名")
    private String name;

    @QueryField
    @Schema(description = "手机号")
    private String phone;

    @QueryField
    @Schema(description = "头像")
    private String icon;

    @QueryField
    @Schema(description = "介绍")
    private String introduce;

    @QueryField
    @Schema(description = "城市ID")
    private RegionDTO city;

    @QueryField
    @Schema(description = "城市名称")
    private String cityName;

    @QueryField
    @Schema(description = "身份证号")
    private String idNum;

    @QueryField
    @Schema(description = "门店名称")
    private String storeName;

    @QueryField
    @Schema(description = "所属公司userId")
    private Long companyId;

    @QueryField
    @Schema(description = "所属商户号")
    private Long merchantId;

    @QueryField
    @Schema(description = "公司名称")
    private String companyName;

    @QueryField
    @Schema(description = "公司营业执照号")
    private String companyLicenseNum;

    @QueryField
    @Schema(description = "公司营业执照图片地址")
    private String companyLicenseUrl;

    @QueryField
    @Schema(description = "公司法人(姓名)")
    private String companyLegal;

    @QueryField
    @Schema(description = "从业信息卡地址")
    private String professionInformationCardUrl;

    @QueryField
    @Schema(description = "从业信息卡编号")
    private String professionInformationCardNumber;

}
