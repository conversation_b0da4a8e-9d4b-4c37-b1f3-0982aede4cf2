package com.ejuetc.consumer.api.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

import static lombok.AccessLevel.PROTECTED;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.consumer.domain.community.CommunityLayout")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "小区户型")
public class CommunityLayoutDTO extends BaseDTO<CommunityLayoutDTO> {

    public CommunityLayoutDTO(Long id) {
        super(id);
    }

    @QueryField
    @Schema(description = "小区详情")
    private CommunityDetailDTO communityDetail;

    @QueryField
    @Schema(description = "小区")
    private CommunityDTO community;

    @QueryField
    @Schema(description = "户型名称")
    protected String name;

    @QueryField
    @Schema(description = "房间数")
    private Integer roomCount;

    @QueryField
    @Schema(description = "客厅数")
    private Integer hallCount;

    @QueryField
    @Schema(description = "卫生间数")
    private Integer toiletCount;

    @QueryField
    @Schema(description = "阳台数")
    private Integer balconyCount;

    @QueryField
    @Schema(description = "厨房数")
    private Integer kitchenCount;

    @QueryField
    @Schema(description = "面积")
    private BigDecimal area;

    @QueryField
    @Schema(description = "套内面积")
    private BigDecimal areaAct;

    @QueryField
    @Schema(description = "最小面积")
    private BigDecimal areaMin;

    @QueryField
    @Schema(description = "最大面积")
    private BigDecimal areaMax;

    @QueryField
    @Schema(description = "主力户型")
    protected Boolean mainLayoutFlag;

    @QueryField
    @Schema(description = "户型图")
    protected String picUrl;

    @QueryField
    @Schema(description = "总套数")
    private Integer setNum;
}
