package com.ejuetc.consumer.api.community;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "绑定小区结果")
public class BindCommunityRO {

    @Schema(description = "绑定ID")
    protected Long bindId;

    @Schema(description = "省份")
    protected String province;

    @Schema(description = "城市")
    protected String city;

    @Schema(description = "行政区")
    protected String district;

    @Schema(description = "小区名")
    protected String name;

    @Schema(description = "小区地址")
    protected String address;

    @Schema(description = "(高德)经度")
    protected String location;

    @Schema(description = "小区id")
    protected Long communityId;
}
