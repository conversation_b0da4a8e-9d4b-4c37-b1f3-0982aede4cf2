package com.ejuetc.consumer.api.community;


import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.web.vo.BeikeCommunityVO;
import com.ejuetc.consumer.web.vo.CommunityVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Tag(name = "API_小区")
@FeignClient(value = "ejuetc.consumer", path = "/consumer", contextId = "communityAPI4Consumer")
public interface CommunityAPI {

    @Operation(summary = "绑定小区")
    @PostMapping("/api/community/bind")
    ApiResponse<BindCommunityRO> bind(
            @Parameter(description = "小区地址") @RequestParam("address") String address,
            @Parameter(description = "小区名称") @RequestParam("name") String name
    );

    @Operation(summary = "绑定小区详情")
    @PostMapping("/api/community/bindCommunityDetail")
    ApiResponse<?> bindCommunityDetail(@RequestBody List<String> cityCodes, @RequestParam int limitCount, @RequestParam int threadCount);

    @Operation(summary = "更新小区名文字重叠率")
    @PostMapping("/api/community/updateOverlapRate")
    ApiResponse<?> updateOverlapRate(@RequestParam int limitCount);

    @Operation(summary = "重新绑定小区为空的绑定信息")
    @PostMapping("/api/community/rebind")
    ApiResponse<?> rebind(@RequestParam int maxRowCount, @RequestParam int maxThreadCount);

    @Operation(summary = "上传小区图片到阿里云")
    @PostMapping("/api/community/convertCommunityPictureUrl")
    void convertCommunityPictureUrl(@RequestParam int maxRowCount, @RequestParam int batchSize);

    @Operation(summary = "上传小区户型图到阿里云")
    @PostMapping("/api/community/convertCommunityLayoutUrl")
    void convertCommunityLayoutUrl(@RequestParam int maxRowCount, @RequestParam int batchSize);

    @Operation(summary = "批量绑定贝壳小区")
    @PostMapping("/api/community/bindCommunityBeike")
    ApiResponse<?> bindCommunityBeike(
            @RequestParam(required = false) List<String> cities,
            @RequestParam int limitCount);

}
