package com.ejuetc.consumer.api.delegation;


import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.dto.DelegationDTO;
import com.ejuetc.consumer.api.dto.DelegationDictDTO;
import com.ejuetc.consumer.web.vo.DelegationVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "API_房源")
@FeignClient(value = "ejuetc.consumer", path = "/consumer", contextId = "delegationAPI4Consumer")
public interface DelegationAPI {

    @Operation(summary = "API_新增房源")
    @PostMapping("/api/delegation/edit")
    ApiResponse<DelegationDTO> edit(@RequestParam Long userId, @RequestBody EditDelegationPO po);


    @Operation(summary = "删除经纪(及其渠道)房源")
    @PostMapping("/api/delegation/deleteBrokerDelegates")
    ApiResponse<?> deleteBrokerDelegates(
            @Parameter(description = "经纪人ID") @RequestParam(required = false) Long brokerId,
            @Parameter(description = "来源端房源父ID") @RequestParam(required = false) String parentSourceId,
            @Parameter(description = "核销原因") @RequestParam String reason
    );

    @Operation(summary = "API_接收渠道状态变更通知")
    @PostMapping("/api/delegation/receiveChannelNotify")
    ApiResponse<DelegationDTO> receiveChannelNotify(
            @Parameter(description = "房源ID") @RequestParam Long id,
            @Parameter(description = "新状态") @RequestParam DelegationDTO.Status newStatus,
            @Parameter(description = "备注信息(原因)") @RequestParam(required = false) String remark
    );

    @Operation(summary = "API_上下架房源")
    @PostMapping("/api/delegation/upDown")
    ApiResponse<DelegationVO> upDown(
            @Parameter(description = "代理房源ID") @RequestParam Long brokerDelegationId,
            @Parameter(description = "渠道代码列表") @RequestParam List<ChannelDTO.Code> channelCodes,
            @Parameter(description = "上架|下架") @RequestParam boolean up,
            @Parameter(description = "备注信息(原因)") @RequestParam(required = false) String remark);

    @Operation(summary = "API_重新加载字典")
    @PostMapping("/api/delegation/refreshDict")
    ApiResponse<?> refreshDict();

    @Operation(summary = "API_手工推送")
    @PostMapping("/api/delegation/manualPush")
    ApiResponse<?> manualPush(
            @Parameter(description = "线程数") @RequestParam int threadCount,
            @Parameter(description = "批次大小") @RequestParam int batchSize,
            @Parameter(description = "单线程处理数量") @RequestParam int maxCount);


    @Operation(summary = "API_查询字典值")
    @GetMapping("/api/dict/list")
    ApiResponse<List<String>> list(@Parameter(description = "字典类型") @RequestParam DelegationDictDTO.Category category);

    @Operation(summary = "API_重新绑定小区")
    @GetMapping("/api/delegation/rebindCommunity")
    void rebindCommunity();

    @Operation(summary = "房源列表查询")
    @PostMapping("/api/delegation/query")
    ApiResponse<List<DelegationVO>> query(@RequestBody ApiQueryListPO po);

}
