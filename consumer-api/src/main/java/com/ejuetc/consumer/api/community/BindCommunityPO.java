package com.ejuetc.consumer.api.community;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "绑定小区")
public class BindCommunityPO {

    @Schema(description = "地址")
    protected String address;

    @Schema(description = "小区名")
    protected String name;

    public BindCommunityPO(String address, String name) {
        this.address = address;
        this.name = name;
    }
}
