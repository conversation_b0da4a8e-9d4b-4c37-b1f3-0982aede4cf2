package com.ejuetc.consumer.api.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.consumer.domain.implicit.ImplicitBind")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "隐号绑定记录")
public class ImplicitBindDTO extends BaseDTO<ImplicitBindDTO> {

    public ImplicitBindDTO(Long userId) {
        super(userId);
    }

    public enum Type implements TitleEnum {
        ALIYUN("阿里云"),
        YIKETONG("移客通"),
        ;

        private final String title;

        Type(String title) {
            this.title = title;
        }

        @Override
        public String getTitle() {
            return this.title;
        }

        @Override
        public String toString() {
            return this.name() + "-" + this.title;
        }
    }

}
