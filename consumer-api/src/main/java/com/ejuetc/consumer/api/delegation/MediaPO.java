package com.ejuetc.consumer.api.delegation;

import com.ejuetc.commons.base.entity.TitleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "媒体信息")
public class MediaPO {
    @Schema(description = "地址")
    private String url;

    @Schema(description = "封面")
    private String cover;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "类型")
    private Type type;

    @Schema(description = "子类型")
    private String subtype;

    @Getter
    public enum Type implements TitleEnum {
        IMAGE("图片"),
        VIDEO("视频"),
        AUDIO("音频"),
        ;

        private final String title;

        Type(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
}
