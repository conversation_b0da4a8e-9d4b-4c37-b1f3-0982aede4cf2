package com.ejuetc.consumer.api.implicit;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

@Tag(name = "API_隐号线索")
@FeignClient(value = "ejuetc.consumer", path = "/consumer", contextId = "ImplicitClueAPI")
public interface ImplicitClueAPI {

    @Operation(summary = "推送所有隐号线索")
    @PostMapping("/api/implicitClue/pushClue")
    void pushClue();

    @Operation(summary = "推送指定隐号线索")
    @GetMapping("/api/implicitClue/pushOneClue")
    void pushOneClue(Long id);
}
