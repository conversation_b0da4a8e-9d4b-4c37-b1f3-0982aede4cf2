package com.ejuetc.consumer.api.delegation;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "删除代理人房源")
public class DeleteBrokerDelegatePO {


    @Schema(description = "经纪人ID")
    private Long brokerId;

    @Schema(description = "房源来源")
    private String sourceType;

    @Schema(description = "来源端房源ID(可为空)")
    private String sourceId;

    @Schema(description = "来源端房源父ID(可为空)")
    private String parentSourceId;

    @Schema(description = "删除原因")
    private String reason;

    public boolean isFieldNull() {
        return brokerId == null && sourceId == null && parentSourceId == null;
    }
}
