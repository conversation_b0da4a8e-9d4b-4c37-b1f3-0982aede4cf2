package com.ejuetc.consumer.api.delegation;

import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "编辑房源委托")
public class EditDelegationPO extends BaseDelegationPO<EditDelegationPO> implements Cloneable {

    @JsonIgnore
    private LocalDateTime createTime = LocalDateTime.now();

    @Schema(description = "是否渠道上架")
    private Boolean channelUp;

    @Schema(description = "是否同步更新公司委托")
    private boolean freshCompanyDelegation;

    @Schema(description = "媒体列表")
    private List<MediaPO> medias;

    public List<MediaPO> getMedias() {
        return medias != null ? medias : new ArrayList<>();
    }

    @QueryField
    @Schema(description = "上架到哪些渠道")
    protected List<ChannelDTO.Code> channelCodes;

    public List<ChannelDTO.Code> getChannelCodes() {
        return channelCodes != null ? channelCodes : new ArrayList<>();
    }


    @SneakyThrows
    @Override
    public EditDelegationPO clone() {
        return (EditDelegationPO) super.clone();
    }

    public ChannelDTO.Code getChannelCode() {
        return channelCodes == null || channelCodes.isEmpty() ? null : channelCodes.get(0);
    }

}

