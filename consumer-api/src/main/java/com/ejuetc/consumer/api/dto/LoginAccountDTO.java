package com.ejuetc.consumer.api.dto;

import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.consumer.domain.consumer.LoginAccount")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "登录账号")
public class LoginAccountDTO extends BaseDTO<LoginAccountDTO> {

    public LoginAccountDTO(Long userId) {
        super(userId);
    }

    @Getter
    public enum Type implements TitleEnum {
        ALIPAY("支付宝", ChannelDTO.Code.ALIPAY),
        WECHAT("微信", ChannelDTO.Code.PRIVATE),
        MANUAL("手工录入", null),
        ;

        private final String title;
        private final ChannelDTO.Code channelCode;

        Type(String title, ChannelDTO.Code channelCode) {
            this.title = title;
            this.channelCode = channelCode;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }


    @QueryField
    @Schema(description = "账号标识")
    private String accountFlag;

    @QueryField
    @Schema(description = "所属消费者")
    private ConsumerDTO consumer;

}
