package com.ejuetc.consumer.api.community;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "小区提示词查询")
public class QueryTipsPO {

    @Schema(description = "地址")
    protected String cityName;

    @Schema(description = "提示词")
    protected String keyword;


    public QueryTipsPO(String cityName, String keyword) {
        this.cityName = cityName;
        this.keyword = keyword;
    }
}
