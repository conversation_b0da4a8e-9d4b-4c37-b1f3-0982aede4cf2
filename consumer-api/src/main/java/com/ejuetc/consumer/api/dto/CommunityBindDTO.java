package com.ejuetc.consumer.api.dto;

import com.alibaba.fastjson.JSONObject;
import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.ejuetc.commons.base.usertype.JsonUT;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.consumer.domain.community.CommunityBind")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "小区绑定")
public class CommunityBindDTO extends BaseDTO<CommunityBindDTO> {


    public CommunityBindDTO(Long id) {
        super(id);
    }

    @QueryField
    @Schema(description = "小区")
    private CommunityDTO community;

    @QueryField
    @Schema(description = "小区地址")
    protected String address;

    @QueryField
    @Schema(description = "小区名称")
    protected String name;

    @QueryField
    @Schema(description = "错误信息")
    private String errorMsg;

    @QueryField
    @Schema(description = "行政区编码")
    private String adcode;

    @QueryField
    @Schema(description = "高德POI ID")
    private String poiId;

}
