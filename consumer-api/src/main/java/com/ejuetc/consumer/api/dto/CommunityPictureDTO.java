package com.ejuetc.consumer.api.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.consumer.domain.community.CommunityPicture")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "小区图片")
public class CommunityPictureDTO extends BaseDTO<CommunityPictureDTO> {
    @Getter
    public enum Type implements TitleEnum {
        PASSAGEWAY("出入口"),
        DISTANT("远景"),
        ROAD("道路"),
        PARK("停车场"),
        OTHER("其他"),
        BUILDING("楼栋"),
        ENTRY_DOOR("入户门"),
        SCENERY("景观带"),
        FACILITY("配套"),
        DISTRIBUTE("分布图    "),
        ;

        private final String title;

        Type(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return this.name() + "-" + this.title;
        }
    }

    public CommunityPictureDTO(Long id) {
        super(id);
    }

    @QueryField
    @Schema(description = "小区")
    private CommunityDTO community;

    @QueryField
    @Schema(description = "小区详情")
    private CommunityDetailDTO communityDetail;

    @QueryField
    @Schema(description = "图片地址")
    private String url;

    @QueryField
    @Schema(description = "来源类型")
    private String type;

}
