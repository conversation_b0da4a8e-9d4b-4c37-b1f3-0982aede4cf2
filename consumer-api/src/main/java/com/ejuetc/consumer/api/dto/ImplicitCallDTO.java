package com.ejuetc.consumer.api.dto;

import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.consumer.domain.implicit.ImplicitCall")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "隐号拨打记录")
public class ImplicitCallDTO extends BaseDTO<ImplicitCallDTO> {

    public ImplicitCallDTO(Long userId) {
        super(userId);
    }

    public enum UnconnectedCause implements TitleEnum {
        NORMAL("正常通话"),
        BLACK("黑名单拦截"),
        NOBIND("无绑定关系"),
        LIMIT("呼叫限制"),
        OTHER("其他"),
        ;

        private final String title;

        UnconnectedCause(String title) {
            this.title = title;
        }

        @Override
        public String getTitle() {
            return this.title;
        }

        @Override
        public String toString() {
            return this.name() + "-" + this.title;
        }
    }

    public enum Starter implements TitleEnum {
        BROKER("经纪人呼消费者"),
        CONSUMER("消费者呼经纪人"),
        HOLD("呼叫被拦截"),
        UNKNOW("未知呼叫类型"),
        ;

        private final String title;

        Starter(String title) {
            this.title = title;
        }

        @Override
        public String getTitle() {
            return this.title;
        }

        @Override
        public String toString() {
            return this.name() + "-" + this.title;
        }
    }

    public enum Closer implements TitleEnum {
        CONSUMER("消费者挂断"),
        BROKER("经纪人挂断"),
        PLATFORM("平台挂断"),
        UNKNOW("未知类型"),
        ;

        private final String title;

        Closer(String title) {
            this.title = title;
        }

        @Override
        public String getTitle() {
            return this.title;
        }

        @Override
        public String toString() {
            return this.name() + "-" + this.title;
        }
    }

    public enum ControlType implements TitleEnum {
        CONTINUE("接听"),
        REJECT("挂断"),
        ;

        private final String title;

        ControlType(String title) {
            this.title = title;
        }

        @Override
        public String getTitle() {
            return this.title;
        }

        @Override
        public String toString() {
            return this.name() + "-" + this.title;
        }
    }

    @QueryField
    @Schema(description = "通话记录ID")
    private String callId;

    @QueryField
    @Schema(description = "绑定编号")
    private String subId;

    @QueryField
    @Schema(description = "消费者手机号")
    private String consumerPhone;

    @QueryField
    @Schema(description = "绑定的隐号")
    private String secretPhone;

    @QueryField
    @Schema(description = "隐号所属城市名称")
    private String cityName;

    @QueryField
    @Schema(description = "经纪人手机号")
    private String brokerPhone;

    @QueryField
    @Schema(description = "发起方")
    private ImplicitCallDTO.Starter starter;

    @QueryField
    @Schema(description = "挂断方")
    private ImplicitCallDTO.Closer closer;

    @QueryField
    @Schema(description = "控制类型")
    private ImplicitCallDTO.ControlType controlType;

    @QueryField
    @Schema(description = "控制消息")
    private String controlMsg;

    @QueryField
    @Schema(description = "主叫拨打时间")
    private LocalDateTime callTime;

    @QueryField
    @Schema(description = "被叫响铃时间")
    private LocalDateTime ringTime;

    @QueryField
    @Schema(description = "被叫接听时间（通话计费开始时间）")
    private LocalDateTime startTime;

    @QueryField
    @Schema(description = "通话释放时间（通话计费结束时间）")
    private LocalDateTime releaseTime;

    @QueryField
    @Schema(description = "录音下载URL")
    private String recordUrl;

    //////////////////////////////////////////////////////////////////
    @QueryField
    @Schema(description = "关联绑定关系")
    private ConsumerRelationDTO relation;

    @QueryField
    @Schema(description = "通话时长")
    private Long duration;

    @QueryField
    @Schema(description = "关联绑定ID")
    private ImplicitBindDTO bind;

    @QueryField
    @Schema(description = "备注")
    private String memo;

    @QueryField
    @Schema(description = "关联委托")
    private DelegationDTO delegation;

    @QueryField
    @Schema(description = "关联委托")
    private Long delegationId;

    @QueryField
    @Schema(description = "关联隐号线索")
    private ImplicitClueDTO clue;

    @QueryField
    @Schema(description = "业务码")
    private BusinessOpenDTO.Code businessCode;

    @QueryField
    @Schema(description = "呼叫状态")
    private String callStatus;

    @QueryField
    @Schema(description = "接通失败原因")
    private ImplicitCallDTO.UnconnectedCause unconnectedCause;

}
