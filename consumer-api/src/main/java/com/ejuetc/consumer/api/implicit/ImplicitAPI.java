package com.ejuetc.consumer.api.implicit;


import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.web.vo.ImplicitCallPO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Tag(name = "API_隐号")
@FeignClient(value = "ejuetc.consumer", path = "/consumer", contextId = "ImplicitAPI")
public interface ImplicitAPI {

    @Operation(summary = "接收呼叫记录")
    @PostMapping("/api/implicit/receiveCall")
    ApiResponse<?> receiveCall(@RequestBody ImplicitCallPO po);

    @Operation(summary = "转换录音文件URL")
    @PostMapping("/api/implicit/convertRecordUrl")
    void convertRecordUrl();

    @Operation(summary = "解绑无效绑定")
    @PostMapping("/api/implicit/unbind")
    void unbind(@RequestParam Long bindId);

}
