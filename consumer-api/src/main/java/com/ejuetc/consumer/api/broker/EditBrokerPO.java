package com.ejuetc.consumer.api.broker;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "编辑经纪人参数")
public class EditBrokerPO {

    @Schema(description = "经纪人用户ID")
    private Long brokerId;

    @Schema(description = "所属公司用户ID")
    private Long companyId;

    @Schema(description = "所属商户号")
    private Long merchantId;

    @Schema(description = "姓名")
    protected String name;

    @Schema(description = "手机号")
    protected String phone;

    @Schema(description = "头像")
    protected String icon;

    @Schema(description = "介绍")
    protected String introduce;

    @Schema(description = "业务城市代码(国标)")
    private String cityCode;

    @Schema(description = "身份证号")
    private String idNum;

    @Schema(description = "门店名称")
    private String storeName;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "公司营业执照号")
    private String companyLicenseNum;

    @Schema(description = "公司营业执照图片地址")
    private String companyLicenseUrl;

    @Schema(description = "公司法人(姓名)")
    private String companyLegal;

    @Schema(description = "从业信息卡地址")
    protected String professionInformationCardUrl;

    @Schema(description = "从业信息卡编号")
    protected String professionInformationCardNumber;
}
