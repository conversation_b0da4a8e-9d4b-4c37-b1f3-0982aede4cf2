package com.ejuetc.consumer.api.region;


import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.broker.EditBrokerPO;
import com.ejuetc.consumer.api.dto.BrokerDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Tag(name = "API_地区")
@FeignClient(value = "ejuetc.consumer", path = "/consumer", contextId = "regionAPI4Consumer")
public interface RegionAPI {

//    @Operation(summary = "初始化城镇&街道")
//    @PostMapping("/api/region/initTown")
//    void initTown(@RequestBody String cityCode);

}
