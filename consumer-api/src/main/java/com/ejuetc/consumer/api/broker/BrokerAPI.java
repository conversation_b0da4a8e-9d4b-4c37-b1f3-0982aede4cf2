package com.ejuetc.consumer.api.broker;


import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.dto.BrokerDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Tag(name = "API_经纪人")
@FeignClient(value = "ejuetc.consumer", path = "/consumer", contextId = "brokerAPI4Consumer")
public interface BrokerAPI {

    @Operation(summary = "编辑经纪人")
    @PostMapping("/api/broker/edit")
    ApiResponse<BrokerDTO> edit(@RequestBody EditBrokerPO po);

}
