package com.ejuetc.consumer.api.dto;

import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.consumer.domain.delegation.DelegationDict")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "枚举字典")
public class DelegationDictDTO {

    @Getter
    public enum Category implements TitleEnum {
        REDO("装修"),
        ORIENT("朝向"),
        MEDIA_SUBTYPE("多媒体子类型"),
        PROPERTY_TYPE("物业类型"),
        ROOM_TYPE("房间类型"),
        BROKER_SERVICE("中介服务"),
        ROOM_EQUIPMENTS("房间设备"),
        COMMUNITY_AROUND("小区周边"),
        HOUSE_STRUCTURE("房屋结构"),
        PROPERTY_OWNERSHIP("产权所属"),
        BUILDING_CATEGORY("建筑类别"),
        HOUSE_YEARS("房屋年限"),
        HOUSE_CERT_TYPE("房产证类型"),
        HOUSE_PLAN_PURPOSE("房屋规划用途"),
        BAILOR_CERT_TYPE("委托人证件类型"),
        BUILDING_TYPE("建筑类型"),
        LOOK_TYPE("看房类型"),
        SALE_REASON("出售原因"),
        HOUSE_SITUATION("房屋现状"),
        PROPERTY_YEARS("产权年限"),
        COMPLETION_TIME("建成年代"),
        FLOOR_CATEGORY("楼层类别"),
        HOUSE_TYPE("产权类型"),
        LABELS_SALE("二手标签"),
        LABELS_RENT("租房标签"),
        LABELS_NEW("新房标签"),
        ;

        private final String title;

        Category(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }


    @QueryField("uk.category")
    @Schema(description = "类别")
    private DelegationDictDTO.Category category;

    @QueryField("uk.code")
    @Schema(description = "编码")
    private String code;

    @QueryField
    @Schema(description = "标题")
    private String title;

}
