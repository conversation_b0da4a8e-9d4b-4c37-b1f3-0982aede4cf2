package com.ejuetc.consumer.api.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.consumer.domain.community.Community")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "小区")
public class CommunityDTO extends BaseDTO<CommunityDTO> {

    public CommunityDTO(Long id) {
        super(id);
    }


    @QueryField
    @Schema(description = "(高德)经度")
    protected String location;

    @QueryField
    @Schema(description = "小区名称")
    protected String name;

    @QueryField
    @Schema(description = "小区地址")
    protected String address;

    @QueryField
    @Schema(description = "省份")
    protected RegionDTO province;

    @QueryField
    @Schema(description = "省份名")
    protected String provinceName;

    @QueryField
    @Schema(description = "城市")
    protected RegionDTO city;

    @QueryField
    @Schema(description = "城市名")
    protected String cityName;

    @QueryField
    @Schema(description = "城市编码")
    protected String cityCode;

    @QueryField
    @Schema(description = "行政区")
    protected RegionDTO district;

    @QueryField
    @Schema(description = "行政区名称")
    protected String districtName;

    @QueryField
    @Schema(description = "高德POI ID")
    private String poiId;

    @QueryField
    @Schema(description = "POI类型编码")
    private String typeCode;

    @QueryField
    @Schema(description = "POI类型")
    private String typeName;

    @QueryField
    @Schema(description = "小区绑定列表")
    private List<CommunityBindDTO> binds;

    @QueryField
    @Schema(description = "(高德)经度")
    private BigDecimal longitude;

    @QueryField
    @Schema(description = "纬度")
    private BigDecimal latitude;

    @QueryField
    @Schema(description = "格式化小区地址")
    protected String formattedAddress;

    @QueryField
    @Schema(description = "城镇/街道名称")
    private String townName;

    @QueryField
    @Schema(description = "商圈名称")
    private String busiName;

    @QueryField
    @Schema(description = "周边查询时间")
    private LocalDateTime aroundTime;

    @QueryField
    @Schema(description = "小区详情")
    private CommunityDetailDTO detail;

    @QueryField
    @Schema(description = "小区户型")
    private List<CommunityLayoutDTO> layouts;

    @QueryField
    @Schema(description = "小区图片")
    private List<CommunityPictureDTO> pictures;


}
