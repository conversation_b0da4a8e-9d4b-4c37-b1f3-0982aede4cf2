package com.ejuetc.consumer.api.dto;

import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.ejuetc.consumer.api.delegation.BaseDelegationPO;
import com.ejuetc.consumer.api.delegation.MediaPO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static com.ejuetc.commons.base.querydomain.api.LoadPolicy.SPECIFY_LOAD;
import static com.ejuetc.consumer.api.dto.DelegationDictDTO.Category.*;
import static com.ejuetc.consumer.api.dto.DelegationDictDTO.Category.HOUSE_YEARS;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.consumer.domain.delegation.Delegation")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "(房源)委托")
public class DelegationDTO extends BaseDelegationPO<DelegationDTO> {
    public static final String SOURCE_SAAS = "SAAS";
    public static final String SOURCE_SHARING = "SHARING";
    public static final String SOURCE_GATEWAY = "GATEWAY";
    public static final List<String> SOURCES_NOTIFY = List.of(SOURCE_SAAS, SOURCE_SHARING);


    public enum SubType implements TitleEnum {
        NEW_NORMAL("标准新房"),
        SALE_FULL("二手整售"),
        RENT_FULL("整租"),
        RENT_SHARE("合租"),
        ;

        private final String title;

        private SubType(String title) {
            this.title = title;
        }

        @Override
        public String getTitle() {
            return this.title;
        }

        @Override
        public String toString() {
            return this.name() + "-" + this.title;
        }
    }

    @Getter
    public enum Level implements TitleEnum {
        COMPANY("公司级", null),
        BROKER("经纪人级", COMPANY),
        CHANNEL("渠道级", BROKER),
        ;

        private final Level parent;
        private final String title;

        Level(String title, Level parent) {
            this.title = title;
            this.parent = parent;
        }

        @Override
        public String toString() {
            return this.name() + "-" + this.title;
        }

        public void check(Level child) {
            if (this != child.parent) {
                throw new IllegalArgumentException("房源委托层级[" + child.title + "]与父委托[" + this.title + "]不匹配");
            }
        }
    }

    @Getter
    public enum Status implements TitleEnum {
        INIT("初始"),
        WITHOUT("未上架"),

        UP_ING("上架中"),
        UP_SUCC("已上架"),
        UP_FAIL("上架失败"),

        DOWN_ING("下架中"),
        DOWN_SUCC("已下架"),
        ;

        private final String title;

        Status(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return this.name() + "-" + this.title;
        }
    }

    public DelegationDTO(Long id) {
        this.id = id;
    }

    @QueryField
    @Schema(description = "编号")
    private String code;

    @QueryField("detail.type.title")
    @Schema(description = "委托类型名")
    private String typeName;


    @QueryField("detail.subType.title")
    @Schema(description = "交易子类型名")
    private String subTypeName;

    @QueryField
    @Schema(description = "经纪人")
    private BrokerDTO broker;

    @QueryField("detail.community")
    @Schema(description = "小区")
    private CommunityDTO community;

    @QueryField
    @Schema(description = "多媒体资料")
    private List<MediaPO> medias;

    @Schema(description = "浏览时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime viewTime;

    @Schema(description = "浏览次数")
    private Long viewCount;

    @QueryField
    @Schema(description = "所属公司userId")
    private Long companyId;

    @QueryField
    @Schema(description = "经纪人名")
    private String brokerName;

    @QueryField
    @Schema(description = "状态")
    private DelegationDTO.Status status;

    @QueryField("detail.city")
    @Schema(description = "城市")
    private RegionDTO city;

    @QueryField("detail.cityName")
    @Schema(description = "城市名")
    private String cityName;

    @QueryField("detail.district")
    @Schema(description = "区县")
    private RegionDTO district;

    @QueryField("detail.metro")
    @Schema(description = "地铁")
    private String metro;

    @QueryField("detail.school")
    @Schema(description = "学区")
    private String school;

    @QueryField("detail.districtName")
    @Schema(description = "区县名")
    private String districtName;

    @QueryField("detail.floorCategory")
    @Schema(description = "楼层类别")
    @DictCategory(FLOOR_CATEGORY)
    private String floorCategory;

    @QueryField
    @Schema(description = "父委托")
    private DelegationDTO parent;

    @QueryField
    @Schema(description = "子委托")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "parent")
    private List<DelegationDTO> children;

    /*************************************** 小区相关 ******************************************/

    @QueryField("detail.communityBind")
    @Schema(description = "小区绑定")
    private CommunityBindDTO communityBind;

    @QueryField("detail.elevatorRatio")
    @Schema(description = "电梯配比")
    private BigDecimal elevatorRatio;

    @QueryField
    @Schema(description = "渠道")
    protected ChannelDTO.Code channelCode;

    @Schema(description = "是否强制同步外部平台")
    private Boolean forceSyncExternal;

    @Schema(description = "调用渠道系统时间")
    private LocalDateTime callChannelTime;

    @QueryField("detail.tagElevator")
    @Schema(description = "是否有电梯")
    private Boolean tagElevator;

    public Boolean getImportHistory() {
        return false;
    }

    @QueryField
    @Schema(description = "备注")
    private String remark;


}
