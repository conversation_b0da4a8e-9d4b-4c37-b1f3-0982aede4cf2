package com.ejuetc.saasapi.domain.gateway.api;

import com.alibaba.fastjson.JSON;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.saasapi.domain.gateway.invoke.Invoke;
import com.ejuetc.saasapi.domain.gateway.invoke.InvokeRpt;
import com.ejuetc.saasapi.pro.GetawayResponse;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.IOUtils.httpPostJson;
import static com.ejuetc.commons.base.utils.StringUtils.replaceJsonValues;
import static com.ejuetc.saasapi.domain.gateway.api.Api4UploadFile.*;

@Slf4j
@Getter
@Entity
@DiscriminatorValue("FORWARD_REQUEST")
@SubtypeCode(parent = Api.class, code = "FORWARD_REQUEST", name = "转发请求")
@NoArgsConstructor
public class Api4ForwardRequest extends Api {

    @Column(name = "url", columnDefinition = "varchar(127) COMMENT '对内请求的url'")
    private String url;

    public GetawayResponse doInvoke(Invoke invoke, MultipartFile file) {
        Map<String, String> headerMap = invoke.getForwardHeaderMap();
        String requestBody = replaceJsonValues(invoke.getRequestBody(), URL_PREFIX, (fileUrl) ->
                getBean(InvokeRpt.class).findByKeyCodeAndApiCodeAndRequestId(
                        invoke.getKeyCode(),
                        getApiCode(fileUrl),
                        getRequestId(fileUrl)
                ).orElseThrow(() -> new BusinessException("bc.ejuetc.saasapi.1003", fileUrl)).getExtendData()
        );
        ResponseEntity<String> responseEntity = httpPostJson(url, headerMap, requestBody);

        return JSON.parseObject(responseEntity.getBody(), GetawayResponse.class);
    }


}
