package com.ejuetc.saasapi.domain.gateway.key;

import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.saasapi.domain.gateway.api.Api;
import com.ejuetc.saasapi.domain.gateway.api.ApiRpt;
import com.ejuetc.saasapi.dto.gateway.KeyDTO;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.saasapi.dto.gateway.KeyDTO.Status.ENABLE;

@Getter
@Entity
@NoArgsConstructor
@Comment("商户接入网关的秘钥")
@Table(name = "tb_gateway_key", uniqueConstraints = @UniqueConstraint(name = "uk_gateway_key_userId", columnNames = {"user_id"}))
@Where(clause = "logic_delete = 0")
//@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.READ_ONLY)
public class Key extends BaseEntity<Key> implements Serializable {

    @Id
    @GeneratedValue(generator = "gateway_key_id")
    @SequenceGenerator(name = "gateway_key_id", sequenceName = "seq_gateway_key_id")
    private Long id;

    @Column(name = "code", columnDefinition = "varchar(127) COMMENT '代码'", nullable = false, unique = true)
    private String code;

    @Column(name = "user_id", columnDefinition = "bigint(20) COMMENT '所属用户号'")
    private Long userId;

    @Column(name = "remark", columnDefinition = "varchar(127) COMMENT '备注'")
    private String remark;

    @Embedded
    private Secret secret;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private KeyDTO.Status status;

    @Column(name = "callback_url", columnDefinition = "varchar(511) COMMENT '回调地址'")
    private String callbackUrl;

    @Comment("秘钥授权接口映射")
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "tb_gateway_key_api",
            joinColumns = @JoinColumn(name = "key_id", columnDefinition = "bigint(20) COMMENT '秘钥ID'", nullable = false),
            inverseJoinColumns = @JoinColumn(name = "api_id", columnDefinition = "bigint(20) COMMENT '接口ID'", nullable = false)
    )
    private List<Api> apis = new ArrayList<>();

    public Key(Long userId, String remark, String callbackUrl) {
        this.userId = userId;
        this.remark = remark;
        this.callbackUrl = callbackUrl;
        this.code = UUID.randomUUID().toString().replaceAll("-", "");
        this.secret = Secret.newSecret();
        this.status = ENABLE;
    }

    public String getSecretText() {
        return secret.getPlaintext();
    }


    public Api getApi(String apiCode) {
        return apis.stream()
                .filter(api -> api.getCode().equals(apiCode))
                .findFirst()
                .orElseThrow(() -> new BusinessException("bc.ejuetc.saasapi.1001", apiCode));
    }
}
