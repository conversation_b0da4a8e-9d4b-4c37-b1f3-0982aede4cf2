package com.ejuetc.saasapi.domain.gateway.invoke;

import com.ejuetc.commons.base.response.ResponseStatus;
import com.ejuetc.commons.base.usertype.JsonUT;
import com.ejuetc.saasapi.pro.GetawayResponse;
import com.ejuetc.saasapi.sdk.SaaSApiSDK;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.hibernate.annotations.Type;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.ejuetc.saasapi.sdk.SaaSApiSDK.*;
import static java.time.Duration.between;
import static java.time.LocalDateTime.now;

@Embeddable
@NoArgsConstructor
public class Headers {

    @Getter
    @Type(JsonUT.class)
    @Column(name = "request_header", columnDefinition = "text COMMENT 'http头信息'")
    private Map<String, String> content = new HashMap<>();

    public Headers(Map<String, String> headers) {
        this.content = new HashMap<>(headers);
    }


    public GetawayResponse checkSign(String body, String secret) {
        Duration duration = between(now(), getRequestTime());
        if (Math.abs(duration.toSeconds()) > 300)
            return new GetawayResponse(ResponseStatus.FAIL_BIZ, "请求已过期", null);

        if (!calcSign(body, secret).equals(getSign()))
            return new GetawayResponse(ResponseStatus.FAIL_BIZ, "签名验证失败", null);

        return null;
    }

    private String getSign() {
        return content.get(SaaSApiSDK.HEADER_SIGN);
    }

    @SneakyThrows
    public String calcSign(String body, String secret) {
        return SaaSApiSDK.calcSign(content, body, secret);
    }

    public Headers add(String header, Object value) {
        content.put(header, String.valueOf(value));
        return this;
    }

    public String getKeyCode() {
        return content.get(SaaSApiSDK.HEADER_KEY_CODE);
    }

    public Headers setKeyCode(String keyCode) {
        content.put(SaaSApiSDK.HEADER_KEY_CODE, keyCode);
        return this;
    }

    public String getApiCode() {
        return content.get(HEADER_API_CODE);
    }

    public Headers setApiCode(String apiCode) {
        content.put(HEADER_API_CODE, apiCode);
        return this;
    }

    public Headers setRequestId(String requestId) {
        content.put(HEADER_REQUEST_ID, requestId);
        return this;
    }

    public String getRequestId() {
        return content.get(HEADER_REQUEST_ID);
    }

    public LocalDateTime getRequestTime() {
        return LocalDateTime.parse(getTime(content), SaaSApiSDK.DATE_TIME_FORMATTER);
    }
}
