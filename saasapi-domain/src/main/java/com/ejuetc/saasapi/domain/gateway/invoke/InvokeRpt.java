package com.ejuetc.saasapi.domain.gateway.invoke;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.Optional;

public interface InvokeRpt extends JpaRepositoryImplementation<Invoke, Long> {

    Optional<Invoke> findByKeyCodeAndApiCodeAndRequestId(String keyCode, String apiCode, String requestId);

    @Query("""
            from Invoke i
            where i.apiCode = :#{#po.apiCode}
            and i.keyCode = :#{#po.keyCode}
            and i.requestId = :#{#po.requestId}
            """)
    Optional<Invoke> findExist(Headers po);
}
