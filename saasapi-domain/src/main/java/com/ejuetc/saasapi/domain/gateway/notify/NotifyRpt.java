package com.ejuetc.saasapi.domain.gateway.notify;

import com.ejuetc.saasapi.pro.NotifyPO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.Optional;

import static com.ejuetc.commons.base.utils.ThreadUtils.redisLock;

public interface NotifyRpt extends JpaRepositoryImplementation<Notify, Long> {
    @Query("select n from Notify n where n.userId = :#{#po.userId} and n.apiCode = :#{#po.apiCode} and n.notifyId = :#{#po.notifyId}")
    Optional<Notify> findExist(NotifyPO po);

    default Optional<Notify> findExistByRedisLock(NotifyPO po) {
        redisLock(50, 50, "com.ejuetc.saasapi.domain.gateway.notify.NotifyRpt.findExist", po.getUserId(), po.getApiCode(), po.getNotifyId());
        return findExist(po);
    }
}
