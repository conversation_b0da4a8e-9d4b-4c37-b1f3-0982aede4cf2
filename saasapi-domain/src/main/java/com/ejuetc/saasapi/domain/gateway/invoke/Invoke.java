package com.ejuetc.saasapi.domain.gateway.invoke;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.response.ResponseStatus;
import com.ejuetc.commons.base.usertype.JsonUT;
import com.ejuetc.saasapi.domain.gateway.api.Api;
import com.ejuetc.saasapi.domain.gateway.api.ApiRpt;
import com.ejuetc.saasapi.domain.gateway.key.Key;
import com.ejuetc.saasapi.domain.gateway.key.KeyRpt;
import com.ejuetc.saasapi.pro.GetawayResponse;
import com.ejuetc.saasapi.sdk.SaaSApiSDK;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.alibaba.fastjson.JSON.toJSONString;
import static com.ejuetc.commons.base.response.ResponseStatus.FAIL_BIZ;
import static com.ejuetc.commons.base.response.ResponseStatus.WAIT;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.saasapi.sdk.SaaSApiSDK.checkSign;

@Getter
@Entity
@NoArgsConstructor
@Slf4j
@Comment("网关对外提供的API接口")
@Table(name = "tb_gateway_invoke", uniqueConstraints = @UniqueConstraint(columnNames = {"key_code", "api_code", "request_id"}))
@Where(clause = "logic_delete = 0")
public class Invoke extends BaseEntity<Invoke> {

    @Id
    @GeneratedValue(generator = "gateway_invoke_id")
    @SequenceGenerator(name = "gateway_invoke_id", sequenceName = "seq_gateway_invoke_id")
    private Long id;

    @Column(name = "request_id", columnDefinition = "varchar(127) COMMENT '商户端请求号'", nullable = false)
    private String requestId;

    @Column(name = "trace_no", columnDefinition = "varchar(127) COMMENT '外部跟踪号'", nullable = false, unique = true)
    private String traceNO = UUID.randomUUID().toString().replaceAll("-", "");

    @Column(name = "request_time", columnDefinition = "datetime(6) COMMENT '请求时间'", nullable = false)
    private LocalDateTime requestTime;

    @Column(name = "request_body", columnDefinition = "text COMMENT '请求报文'")
    private String requestBody;

    @Embedded
    private Headers requestHeaders;

    @Type(JsonUT.class)
    private JSONObject requestParams;

    @Column(name = "response_body", columnDefinition = "text COMMENT '应答报文'")
    private String responseBody;

    @Setter
    @Column(name = "extend_data", columnDefinition = "text COMMENT '扩展数据'")
    private String extendData;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ResponseStatus status = WAIT;

    @Column(name = "message", columnDefinition = "text COMMENT '应答信息'")
    private String message;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "key_id", columnDefinition = "bigint(20) COMMENT '秘钥ID'", nullable = false)
    private Key key;

    @Column(name = "key_code", columnDefinition = "varchar(127) COMMENT '秘钥代码'", nullable = false)
    private String keyCode;

    @Column(name = "user_id", columnDefinition = "bigint(20) COMMENT '所属会员号'")
    private Long userId;

    @Column(name = "duration", columnDefinition = "bigint(20) COMMENT '处理耗时(毫秒,从对内发起请求到收到内部应答)'")
    private Long duration;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "api_id", columnDefinition = "bigint(20) COMMENT '接口ID'", nullable = false)
    private Api api;

    @Column(name = "api_code", columnDefinition = "varchar(127) COMMENT 'API代码'", nullable = false)
    private String apiCode;

    public Invoke(Headers headers, String requestBody, MultipartFile file) {
        this(headers, new HashMap<>(), requestBody, file);
    }

    public Invoke(Headers headers, Map<String, String[]> params, String body, MultipartFile file) {
        this.requestHeaders = headers;
        this.requestParams = new JSONObject(new HashMap<>(params));
        this.requestId = requestHeaders.getRequestId();
        this.requestTime = requestHeaders.getRequestTime();
        this.keyCode = headers.getKeyCode();
        this.apiCode = headers.getApiCode();
        this.key = getBean(KeyRpt.class).findByCode(keyCode).orElseThrow(() -> new BusinessException("bc.ejuetc.saasapi.1002", keyCode));
        this.api = key.getApi(apiCode);
        this.userId = key.getUserId();
        this.requestBody = makeBody(body, file);
    }

    private static String makeBody(String requestBody, MultipartFile file) {
        return requestBody != null ? requestBody : file.getOriginalFilename();
    }

    public Object exec(Headers headers, String requestBody, MultipartFile file) {
        log.info("Invoke exec:\n{}", requestBody);
        String body = makeBody(requestBody, file);
        log.info("Invoke body:{}", body);
        String signErrInfo = checkSign(headers.getContent(), requestParams.toJavaObject(Map.class), body, key.getSecretText());
        log.info("Invoke signErrInfo:\n{}", signErrInfo);
        if (signErrInfo != null)
            return new GetawayResponse(FAIL_BIZ, signErrInfo, null);

        if (status != WAIT) {
            if (!body.equals(this.requestBody)) {
                return new GetawayResponse(FAIL_BIZ, "已存在编号请求但内容不一致!", null);
            } else if (!api.getIdempotent()) {
                return new GetawayResponse(FAIL_BIZ, "已存在请求号,且接口不支持幂等,请更换请求号后重新提交!", null);
            } else if (!status.isUnknow()) {
                return new GetawayResponse(status, message, responseBody);
            }
        }

        long begin = System.currentTimeMillis();
        GetawayResponse invokeResult = api.invoke(this, file);
        long end = System.currentTimeMillis();

        this.duration = end - begin;
        this.responseBody = toJSONString(invokeResult, true);
        this.status = invokeResult.getStatus();
        this.message = invokeResult.getMessage();
        return invokeResult;
    }

    public Map<String, String> getForwardHeaderMap() {
        Map<String, String> map = new HashMap<>();
        getRequestHeaders().getContent().forEach((k, v) -> {
            if (k.startsWith("etc-") || k.startsWith("sw8")) {
                map.put(k, v);
            }
        });
        map.put(SaaSApiSDK.HEADER_TRACE_NO, getTraceNO());
        map.put(SaaSApiSDK.HEADER_USER_ID, String.valueOf(getUserId()));
        map.put(SaaSApiSDK.HEADER_MERCHANT_ID, String.valueOf(getUserId()));
        return map;
    }

    public String getRequestHeader(String header) {
        return getRequestHeaders().getContent().get(header);
    }

    public Map<String, Object[]> getRequestParams() {
        return requestParams.toJavaObject(Map.class);
    }
}
