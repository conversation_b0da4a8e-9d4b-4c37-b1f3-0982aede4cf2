package com.ejuetc.saasapi.domain.gateway.api;

import com.alibaba.fastjson.JSON;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.saasapi.domain.gateway.invoke.Invoke;
import com.ejuetc.saasapi.pro.GetawayResponse;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Map;

import static com.ejuetc.saasapi.sdk.SaaSApiSDK.HEADER_FEIGN_METHOD;
import static com.ejuetc.saasapi.sdk.SaaSApiSDK.HEADER_FEIGN_URL;
import static com.ejuetc.commons.base.utils.IOUtils.*;
import static java.nio.charset.StandardCharsets.UTF_8;

@Slf4j
@Getter
@Entity
@DiscriminatorValue("FEIGN_CLIENT")
@SubtypeCode(parent = Api.class, code = "FEIGN_CLIENT", name = "转发请求")
@NoArgsConstructor
public class Api4FeignClient extends Api {
    public GetawayResponse doInvoke(Invoke invoke, MultipartFile file) {
        String responseBody = "get".equalsIgnoreCase(invoke.getRequestHeader(HEADER_FEIGN_METHOD))
                ? httpGet(invoke.getRequestHeader(HEADER_FEIGN_URL), invoke.getRequestParams())
                : httpPostJson(
                invoke.getRequestHeader(HEADER_FEIGN_URL),
                invoke.getForwardHeaderMap(),
                invoke.getRequestParams(),
                invoke.getRequestBody()
        ).getBody();

        return JSON.parseObject(responseBody, GetawayResponse.class);
    }

}
