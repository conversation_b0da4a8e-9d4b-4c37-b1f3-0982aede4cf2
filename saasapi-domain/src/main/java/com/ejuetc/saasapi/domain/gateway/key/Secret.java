package com.ejuetc.saasapi.domain.gateway.key;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.Transient;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.Properties;
import java.util.Random;

import static com.ejuetc.commons.base.spring.SpringUtil.getProperty;

@Embeddable
@NoArgsConstructor
public class Secret {

    public static final String KEYS_FILE_PATH = "/etc/encryption-keys.properties";
    @Column(name = "secret", columnDefinition = "varchar(127) COMMENT '秘钥'", nullable = false)
    private String secret;

    @Transient
    private String plaintext;

    public Secret(String secret) {
        this.secret = secret;
    }

    private static Properties properties;

    private static Properties properties() {
        if (properties == null) {
            properties = new Properties();
            try (InputStream input = new FileInputStream(getProperty("ejuetc.saasapi.Secret.encryptionFilePath", String.class, KEYS_FILE_PATH))) {
                properties.load(input);
            } catch (IOException ex) {
                throw new RuntimeException("Failed to load encryption keys", ex);
            }
        }

        return properties;
    }


    @SneakyThrows
    public static Secret newSecret() {
        String[] keyCodes = properties().keySet().toArray(new String[0]);
        String randomKeyCode = keyCodes[new Random().nextInt(keyCodes.length)];
        String secretKeyValue = properties().getProperty(randomKeyCode);

        KeyGenerator keyGen = KeyGenerator.getInstance("AES");
        keyGen.init(256, new SecureRandom());
        SecretKey secretKey = keyGen.generateKey();
        String secretMessage = Base64.getEncoder().encodeToString(secretKey.getEncoded());


        SecretKeySpec secretKeySpec = new SecretKeySpec(Base64.getDecoder().decode(secretKeyValue), "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
        byte[] encryptedData = cipher.doFinal(secretMessage.getBytes());

        String encodedEncryptedData = Base64.getEncoder().encodeToString(encryptedData);
        String finalEncryptedString = randomKeyCode + ":" + encodedEncryptedData;

        return new Secret(finalEncryptedString);
    }


    @SneakyThrows
    public String getPlaintext() {
        if (plaintext == null) {
            String[] parts = secret.split(":");
            String keyCode = parts[0];
            String encryptedData = parts[1];

            String secretKeyValue = properties().getProperty(keyCode);

            SecretKeySpec secretKeySpec = new SecretKeySpec(Base64.getDecoder().decode(secretKeyValue), "AES");

            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);

            byte[] decodedEncryptedData = Base64.getDecoder().decode(encryptedData);
            byte[] decryptedData = cipher.doFinal(decodedEncryptedData);

            plaintext = new String(decryptedData);
        }
        return plaintext;
    }

    public static void main(String[] args) throws Exception {
//        generatePropertiesFile(KEYS_FILE_PATH);

//        Secret secret = Secret.newSecret();
//        System.out.println(secret.secret);
//        System.out.println(secret.getPlaintext());

        Secret secret2 = new Secret("keyCode10:dvcJBbEnbLV3ZTJ864JXe7u2ToNWi2X7HKi1TWHBzsd2koURxD9vWSMOGoKteZJ4");
        System.out.println(secret2.secret);
        System.out.println(secret2.getPlaintext());
    }


    public static void generatePropertiesFile(String filePath) throws Exception {
        Properties properties = new Properties();

        // Generate key pairs
        for (int i = 1; i <= 10; i++) {
            String keyCode = "keyCode" + i;
            SecretKey secretKey = generateSecretKey();
            String encodedSecretKey = Base64.getEncoder().encodeToString(secretKey.getEncoded());
            properties.setProperty(keyCode, encodedSecretKey);
        }

        // Write properties to file
        try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
            properties.store(outputStream, "Encryption Keys");
        } catch (IOException e) {
            throw new RuntimeException("Failed to write properties file", e);
        }
    }

    private static SecretKey generateSecretKey() throws Exception {
        KeyGenerator keyGen = KeyGenerator.getInstance("AES");
        keyGen.init(256, new SecureRandom());
        return keyGen.generateKey();
    }
}
