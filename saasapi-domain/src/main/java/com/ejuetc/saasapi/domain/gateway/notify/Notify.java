package com.ejuetc.saasapi.domain.gateway.notify;

import com.alibaba.fastjson.JSONObject;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.usertype.JsonUT;
import com.ejuetc.saasapi.domain.gateway.api.Api;
import com.ejuetc.saasapi.domain.gateway.api.ApiRpt;
import com.ejuetc.saasapi.domain.gateway.key.Key;
import com.ejuetc.saasapi.domain.gateway.key.KeyRpt;
import com.ejuetc.saasapi.dto.gateway.NotifyDTO;
import com.ejuetc.saasapi.pro.NotifyPO;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Map;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.IOUtils.httpPostJson;
import static com.ejuetc.saasapi.dto.gateway.NotifyDTO.Status.RETRY;
import static com.ejuetc.saasapi.dto.gateway.NotifyDTO.Status.SUCC;
import static com.ejuetc.saasapi.sdk.SaaSApiSDK.*;
import static java.time.LocalDateTime.now;

@Getter
@Entity
@NoArgsConstructor
@Slf4j
@Comment("通知商户")
@Table(name = "tb_gateway_notify", uniqueConstraints = @UniqueConstraint(name = "uk_notify_userId_apiCode_notifyId", columnNames = {"user_id", "api_code", "notify_id"}))
@Where(clause = "logic_delete = 0")
public class Notify extends BaseEntity<Notify> {

    @Id
    @GeneratedValue(generator = "gateway_notify_id")
    @SequenceGenerator(name = "gateway_notify_id", sequenceName = "seq_gateway_notify_id")
    private Long id;

    @Column(name = "user_id", columnDefinition = "bigint(20) COMMENT '所属用户号'", nullable = false)
    private Long userId;

    @Column(name = "api_code", columnDefinition = "varchar(127) COMMENT 'API代码'", nullable = false)
    private String apiCode;

    @Column(name = "notify_id", columnDefinition = "varchar(127) COMMENT '通知号'", nullable = false)
    private String notifyId;

    @Type(JsonUT.class)
    @Column(name = "notify_body", columnDefinition = "json COMMENT '通知报文'")
    private JSONObject notifyBody;

    @Column(name = "response_code", columnDefinition = "bigint(20) COMMENT '应答码'")
    private Integer responseCode;

    @Column(name = "response_body", columnDefinition = "text COMMENT '应答报文'")
    private String responseBody;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private NotifyDTO.Status status = NotifyDTO.Status.INIT;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "key_id", columnDefinition = "bigint(20) COMMENT '秘钥ID'", nullable = false)
    private Key key;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "api_id", columnDefinition = "bigint(20) COMMENT '接口ID'", nullable = false)
    private Api api;

    @Column(name = "duration", columnDefinition = "bigint(20) COMMENT '处理耗时(毫秒,从对内发起请求到收到内部应答)'")
    private Long duration;

    public Notify(NotifyPO po) {
        this.userId = po.getUserId();
        this.apiCode = po.getApiCode();
        this.notifyId = po.getNotifyId();
        this.notifyBody = po.getBody();
        this.api = getBean(ApiRpt.class).findByCode(apiCode).orElseThrow(() -> new BusinessException("bc.ejuetc.saasapi.1001", apiCode));
        this.key = getBean(KeyRpt.class).findByUserId(userId).orElseThrow(() -> new BusinessException("bc.ejuetc.saasapi.1001", userId));
    }

    public void exec() {
        if (status == SUCC) return;
        Map<String, String> headers = makeHeaders();
        long begin = System.currentTimeMillis();
        try {
            ResponseEntity<String> response = httpPostJson(key.getCallbackUrl(), headers, notifyBody.toJSONString());
            this.responseBody = response.getBody();
            this.responseCode = response.getStatusCode().value();
            this.status = responseCode == 200 && NOTIFY_SUCCESS.equals(response.getBody()) ? SUCC : RETRY;
        } catch (Exception e) {
            log.error("notify error", e);
            this.responseBody = e.getMessage();
            this.responseCode = 500;
            this.status = RETRY;
        }
        this.duration = System.currentTimeMillis() - begin;
    }

    private Map<String, String> makeHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put(HEADER_NOTIFY_ID, notifyId);
        headers.put(HEADER_KEY_CODE, key.getCode());
        headers.put(HEADER_API_CODE, api.getCode());
        headers.put(HEADER_TIME, now().format(DATE_TIME_FORMATTER));
        String sign = calcSign(headers, notifyBody.toJSONString(), key.getSecretText());
        headers.put(HEADER_SIGN, sign);
        return headers;
    }
}
