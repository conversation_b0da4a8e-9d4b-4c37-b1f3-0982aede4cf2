package com.ejuetc.saasapi.domain.gateway.key;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.Optional;

public interface KeyRpt extends JpaRepositoryImplementation<Key, Long> {

    @Cacheable("KeyRpt.apiByCode")
    Optional<Key> findByCode(String code);

    Optional<Key> findByUserId(Long userId);
}
