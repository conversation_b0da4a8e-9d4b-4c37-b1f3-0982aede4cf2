package com.ejuetc.saasapi.domain.gateway.api;

import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.component.OssComponent;
import com.ejuetc.commons.base.response.ResponseStatus;
import com.ejuetc.saasapi.domain.gateway.invoke.Invoke;
import com.ejuetc.saasapi.pro.GetawayResponse;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.util.UUID;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static java.time.LocalDateTime.now;
import static java.time.format.DateTimeFormatter.ofPattern;

@Slf4j
@Getter
@Entity
@DiscriminatorValue("UPLOAD_FILE")
@SubtypeCode(parent = Api.class, code = "UPLOAD_FILE", name = "上传文件")
@NoArgsConstructor
public class Api4UploadFile extends Api {
    public static final String URL_PREFIX = "UploadFile://";

    public static String getRequestId(String fileUrl) {
        return fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
    }

    public static String getApiCode(String fileUrl) {
        return fileUrl.substring(URL_PREFIX.length(), fileUrl.lastIndexOf("/"));
    }

    @SneakyThrows
    public GetawayResponse doInvoke(Invoke invoke, MultipartFile file) {
        String ossFilePath = invoke.getUserId() + "/" + now().format(ofPattern("yyyyMM")) + "/" + UUID.randomUUID() + invoke.getRequestBody().substring(invoke.getRequestBody().lastIndexOf("."));
        String url = getBean(OssComponent.class).putOSS(ossFilePath, file.getInputStream());
        invoke.setExtendData(url);
        return new GetawayResponse(ResponseStatus.SUCC_DONE, "上传成功", new UploadFile(invoke));
    }

    @Data
    public static class UploadFile {
        String fileName;
        String fileUrl;

        public UploadFile(Invoke invoke) {
            this.fileName = invoke.getRequestBody();
            this.fileUrl = URL_PREFIX + invoke.getApiCode() + "/" + invoke.getRequestId();
        }
    }

    public static void main(String[] args) {
        String fileUrl = "UploadFile://apiCode/requestId";
        System.out.println("apiCode:" + getApiCode(fileUrl));
        System.out.println("requestId:" + getRequestId(fileUrl));
    }

}
