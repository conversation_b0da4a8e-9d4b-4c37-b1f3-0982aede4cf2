package com.ejuetc.saasapi;

import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.saasapi.api.KeyApi;
import com.ejuetc.saasapi.dto.gateway.KeyDTO;
import org.junit.Test;

import static com.alibaba.fastjson.JSON.toJSONString;
import static com.ejuetc.commons.base.spring.SpringUtil.getAPI;

//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = {SaasapiApplication.class})
public class KeyTest {

    //    @Autowired
//    private KeyAPIImpl keyApi;
    private KeyApi keyApi = getAPI(KeyApi.class, "http://ejuetc-saasapi.release.ejucloud.cn");
//        private KeyApi keyApi = getAPI(KeyApi.class, "http://saasapi-uat.ebaas.com");
//        private KeyApi keyApi = getAPI(KeyApi.class, "http://saasapi-test.ebaas.com");
//    private KeyApi keyApi = getAPI(KeyApi.class, "http://localhost:8095");

    @Test
    public void testNewKey() {
        ApiResponse<KeyDTO> response = keyApi.newKey(
                9096838121850457388L,//System.currentTimeMillis(),
                "芒果",
                null);
        System.out.println(toJSONString(response, true));
    }

    @Test
    public void testNewKey2() {
        ApiResponse<KeyDTO> response = keyApi.newKey(
                9096773776397321217L,//System.currentTimeMillis(),
                "芒果找房",
                null);
        System.out.println(toJSONString(response, true));
    }
}
