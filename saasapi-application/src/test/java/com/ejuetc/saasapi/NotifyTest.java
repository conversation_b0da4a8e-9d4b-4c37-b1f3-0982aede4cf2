package com.ejuetc.saasapi;

import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.saasapi.api.NotifyApi;
import com.ejuetc.saasapi.dto.gateway.NotifyDTO;
import com.ejuetc.saasapi.pro.NotifyPO;
import org.junit.Test;

import java.util.UUID;

import static com.alibaba.fastjson.JSON.toJSONString;
import static com.ejuetc.commons.base.spring.SpringUtil.getAPI;

//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = {SaasapiApplication.class})
public class NotifyTest {

    //    @Autowired
//    private KeyAPIImpl keyApi;
    private NotifyApi notifyApi = getAPI(NotifyApi.class, "http://localhost:8095");

    @Test
    public void testNewKey() {
        ApiResponse<NotifyDTO> response = notifyApi.notify(new NotifyPO()
                .setUserId(1745288975421L)
                .setNotifyId(UUID.randomUUID().toString())
                .setBody("""
                        {
                          "requestKey1": "value1",
                          "requestKey2": "value2"
                        }
                        """)
                .setApiCode("mockApi1")
        );
        System.out.println(toJSONString(response, true));
    }
}
