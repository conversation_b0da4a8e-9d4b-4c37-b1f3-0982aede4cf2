<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.ehcache.org/v3"
        xsi:schemaLocation="http://www.ehcache.org/v3 http://www.ehcache.org/schema/ehcache-core-3.0.xsd">
    <!-- 缓存模板: 未填写缓存名时使用的默认缓存，同时也可被继承 -->
    <cache-template name="defaultCache">
        <key-type>java.lang.String</key-type>
        <value-type>java.lang.Object</value-type>
        <resources>
            <heap unit="MB">128</heap>
        </resources>
    </cache-template>

    <cache alias="com.ejuetc.saasapi.domain.gateway.api.Api" uses-template="defaultCache">
        <expiry>
            <ttl unit="minutes">5</ttl>
        </expiry>
    </cache>

    <cache alias="com.ejuetc.saasapi.domain.gateway.key.Key" uses-template="defaultCache">
        <expiry>
            <ttl unit="minutes">5</ttl>
        </expiry>
    </cache>

    <cache alias="ApiRpt.apiByCode" uses-template="defaultCache">
        <expiry>
            <ttl unit="minutes">5</ttl>
        </expiry>
    </cache>

    <cache alias="KeyRpt.apiByCode" uses-template="defaultCache">
        <expiry>
            <ttl unit="minutes">5</ttl>
        </expiry>
    </cache>
</config>