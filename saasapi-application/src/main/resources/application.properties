spring.profiles.active=local
#spring.profiles.active=dev
#spring.profiles.active=test
#spring.profiles.active=uat
#tomcat
spring.application.name=ejuetc.saasapi
server.port=8095
server.servlet.context-path=/
server.servlet.session.timeout=1800s
server.tomcat.uri-encoding=UTF-8
server.tomcat.threads.max=200
server.tomcat.basedir=/tmp

#JPA
spring.jpa.properties.hibernate.default_batch_fetch_size=100
#\u6587\u4EF6\u4E0A\u4F20
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=1000MB
#feign
feign.hystrix.enabled=false
feign.httpclient.enabled=true
#feign.compression.request.enabled=false
#feign.compression.response.enabled=false
#logback file
logging.config=classpath:logback-spring.xml
# \u542F\u7528hiddenMethod\u8FC7\u6EE4\u5668
spring.mvc.hiddenmethod.filter.enabled=true
#swagger
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
com.ejuetc.exception.alarm=true
eureka.instance.status-page-url-path=/doc.html
knife4j.enable=true
knife4j.setting.language=zh_cn
com.ejuetc.commons.base.LoggerFilter.ignoreRequestURI=.*/doc.html,.*/webjars/.*

ejuetc.saasapi.Secret.encryptionFilePath=/etc/encryption-keys.properties

# application.properties
spring.jpa.properties.hibernate.cache.use_second_level_cache=true
spring.jpa.properties.hibernate.cache.region.factory_class=org.hibernate.cache.jcache.JCacheRegionFactory
spring.cache.jcache.config=classpath:ehcache.xml

#OSS\u914D\u7F6E
ejuetc.commons.osscomponent.oss_access_key_id=LTAI5tR7VRT3oCrM2GLe35ck
ejuetc.commons.osscomponent.oss_access_key_secret=******************************
ejuetc.commons.osscomponent.oss_endpoint=https://oss-cn-shanghai.aliyuncs.com/
ejuetc.commons.osscomponent.url_prefix=https://etc-saas-api.oss-cn-shanghai.aliyuncs.com/
ejuetc.commons.osscomponent.bucket_name=etc-saas-api

spring-boot.run.jvmArguments=--add-opens java.base/java.lang.ref=ALL-UNNAMED
spring.datasource.hikari.transaction-isolation=TRANSACTION_READ_COMMITTED
