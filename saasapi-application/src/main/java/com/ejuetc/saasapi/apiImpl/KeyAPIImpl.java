package com.ejuetc.saasapi.apiImpl;

import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.saasapi.api.KeyApi;
import com.ejuetc.saasapi.domain.gateway.key.Key;
import com.ejuetc.saasapi.domain.gateway.key.KeyRpt;
import com.ejuetc.saasapi.dto.gateway.KeyDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class KeyAPIImpl implements KeyApi {

    private final KeyRpt keyRpt;

    @Override
    public ApiResponse<KeyDTO> newKey(Long userId, String remark, String callbackUrl) {
        Key key = userId == null ? null : keyRpt.findByUserId(userId).orElse(null);
        if (key == null) key = new Key(userId, remark, callbackUrl).save();
        return succ(convert2DTO(key, new KeyDTO()));
    }

}
