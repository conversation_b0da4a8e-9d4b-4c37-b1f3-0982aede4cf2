package com.ejuetc.saasapi.apiImpl;

import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.saasapi.api.NotifyApi;
import com.ejuetc.saasapi.domain.gateway.key.KeyRpt;
import com.ejuetc.saasapi.domain.gateway.notify.Notify;
import com.ejuetc.saasapi.domain.gateway.notify.NotifyRpt;
import com.ejuetc.saasapi.dto.gateway.NotifyDTO;
import com.ejuetc.saasapi.pro.NotifyPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.saasapi.sdk.SaaSApiSDK.*;
import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

@Slf4j
@RestController
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class NotifyImpl implements NotifyApi {

    private final NotifyRpt notifyRpt;
    private final KeyRpt keyRpt;

    @PostMapping("/mock/receiveNotify")
    public String mockReceiveNotify(@RequestHeader Map<String, String> headers, @RequestBody String requestBody) {
        log.info("mockReceiveNotify headers:\n{} \nrequestBody:\n{}", headers, requestBody);
        String secretText = keyRpt.findByCode(headers.get(HEADER_KEY_CODE)).get().getSecretText();
        String signErrorInfo = checkSign(headers, requestBody, secretText);
        return signErrorInfo != null ? signErrorInfo : NOTIFY_SUCCESS;
    }

    @Override
    public ApiResponse<NotifyDTO> notify(NotifyPO po) {
        Notify notify = notifyRpt.findExistByRedisLock(po).orElseGet(() -> createNotify(po).merge());
        notify.exec();
        return succ(convert2DTO(notify, new NotifyDTO()));
    }

    @Transactional(propagation = REQUIRES_NEW)
    public Notify createNotify(NotifyPO po) {
        return new Notify(po).save();
    }
}