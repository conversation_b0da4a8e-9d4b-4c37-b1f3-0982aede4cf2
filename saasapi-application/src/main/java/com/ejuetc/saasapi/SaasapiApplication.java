package com.ejuetc.saasapi;

import com.ejuetc.commons.base.application.BaseApplication;
import com.ejuetc.commons.base.filter.ClearJsonTypeFilter;
import jakarta.servlet.Filter;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.List;

import static com.ejuetc.commons.base.filter.BaseJsonFilter.BASE_FILTER_ORDER;

@SpringBootApplication(scanBasePackages = {"com.ejuetc.**"})
@ServletComponentScan(basePackages = {"com.ejuetc.**"})
@EnableJpaRepositories(basePackages = {"com.ejuetc.**"})
@EntityScan("com.ejuetc.**")
@EnableDiscoveryClient
@EnableFeignClients({"com.ejuetc.**"})
@EnableScheduling
@EnableAspectJAutoProxy
public class SaasapiApplication extends BaseApplication {

    public static void main(String[] args) {
        SpringApplication.run(SaasapiApplication.class, args);
    }

    @Bean
    public FilterRegistrationBean<?> jsonTypeInfoFilter() {
        FilterRegistrationBean<Filter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new ClearJsonTypeFilter().addIgnoreURI("/gateway/invoke"));   //设置过滤器
        registrationBean.setUrlPatterns(List.of("/*"));
        registrationBean.setOrder(BASE_FILTER_ORDER + 2);  //设置优先级
        return registrationBean;
    }


}
