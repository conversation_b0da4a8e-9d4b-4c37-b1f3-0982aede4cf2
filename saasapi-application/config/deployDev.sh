#!/bin/bash
set -e
app_name="saasapi-application"
app_file="${app_name}-`date '+%m%d%H%M'`.jar"
ssh_user="ejuetc"
ssh_ip="*************"
ssh_pass="ejuetc@2024"

mvn -DskipTests=true clean install -f ../../

expect -c "
set timeout -1
spawn scp ../target/${app_name}.jar $ssh_user@$ssh_ip:/home/<USER>/${app_name}/${app_file}
expect \"password:\"
send \"${ssh_pass}\r\"
expect eof
"
expect -c "
spawn ssh ${ssh_user}@${ssh_ip}
expect \"password:\"
send \"${ssh_pass}\r\"
expect \"]#\"
send \"ps -ef |grep -v 'grep' |grep ${app_name} && ps -ef |grep -v 'grep' |grep ${app_name} |awk '{print \\\$2}'|xargs kill -9\r\"
expect \"]#\"
send \"nohup java -javaagent:/home/<USER>/skywalking-agent/skywalking-agent.jar -DappName=${app_name} -jar /home/<USER>/${app_name}/$app_file  --spring.profiles.active=dev > /home/<USER>/${app_name}/${app_name}.log 2>&1 &\r\"
expect \"]#\"
send \"ps -ef |grep -v 'grep' |grep ${app_name}\r\"
interact
"