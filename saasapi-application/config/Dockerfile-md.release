FROM registry.ejuops.com/library/python:3.9
MAINTAINER <EMAIL>
ENV TimeZone=Asia/Shanghai
ARG PUB_BU
ARG PUB_MODULE
ARG PUB_ENV
ENV pub_env=${PUB_ENV}

RUN ln -snf /usr/share/zoneinfo/$TimeZone /etc/localtime
RUN echo $TimeZone >/etc/timezone

RUN mkdir -p /opt/app/mkdocs/
add ./docs/apiDocsSite/mkdocs.tgz /opt/app/mkdocs/
WORKDIR   /opt/app/mkdocs/
RUN ls /opt/app/mkdocs/
RUN pip config set global.index-url  https://pypi.tuna.tsinghua.edu.cn/simple
RUN pip install mkdocs
RUN pip install mkdocs-material
CMD [ "/usr/local/bin/mkdocs","serve","-a","0.0.0.0:8000" ]
