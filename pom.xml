<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ejuetc.commons</groupId>
        <artifactId>commons</artifactId>
        <version>0.7.2</version>
    </parent>

    <groupId>com.ejuetc.saasapi</groupId>
    <artifactId>saasapi</artifactId>
    <packaging>pom</packaging>
    <version>0.0.3</version>
    <modules>
        <module>saasapi-sdk</module>
        <module>saasapi-api</module>
        <module>saasapi-application</module>
        <module>saasapi-domain</module>
        <module>saasapi-integration</module>
    </modules>

    <properties>
        <commons.version>0.7.2</commons.version>
        <message.version>0.0.7</message.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.ejuetc.message</groupId>
                <artifactId>message-api</artifactId>
                <version>${message.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ejuetc.commons</groupId>
                <artifactId>commons-base</artifactId>
                <version>${commons.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ejuetc.commons</groupId>
                <artifactId>commons-querydomain</artifactId>
                <version>${commons.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <scm>
        <connection>scm:git:http://git.eju-inc.com/ejuetc/java/ai.git</connection>
        <developerConnection>scm:git:http://git.eju-inc.com/ejuetc/java/ai.git</developerConnection>
        <tag>HEAD</tag>
    </scm>

</project>