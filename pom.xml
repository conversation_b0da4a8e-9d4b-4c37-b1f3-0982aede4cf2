<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ejuetc.commons</groupId>
        <artifactId>commons</artifactId>
        <version>0.7.1</version>
    </parent>

    <groupId>com.ejuetc.consumer</groupId>
    <artifactId>consumer</artifactId>
    <packaging>pom</packaging>
    <version>0.1.7</version>
    <modules>
        <module>consumer-api</module>
        <module>consumer-application</module>
        <module>consumer-domain</module>
        <module>consumer-integration</module>
    </modules>

    <properties>
        <commons.version>0.7.1</commons.version>
        <message.version>0.0.6</message.version>
        <rocketmq.version>2.2.0</rocketmq.version>
        <mall.version>0.0.1</mall.version>
        <channel.version>0.2.8</channel.version>
        <saasapi.version>0.0.3</saasapi.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.ejuetc.saasapi</groupId>
                <version>${saasapi.version}</version>
                <artifactId>saasapi-api</artifactId>
            </dependency>
            <dependency>
                <groupId>com.ejuetc.mall</groupId>
                <artifactId>mall-api</artifactId>
                <version>${mall.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ejuetc.channel</groupId>
                <artifactId>channel-api</artifactId>
                <version>${channel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ejuetc.message</groupId>
                <artifactId>message-api</artifactId>
                <version>${message.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ejuetc.commons</groupId>
                <artifactId>commons-base</artifactId>
                <version>${commons.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ejuetc.commons</groupId>
                <artifactId>commons-querydomain</artifactId>
                <version>${commons.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <scm>
        <connection>scm:git:http://git.eju-inc.com/ejuetc/java/consumer.git</connection>
        <developerConnection>scm:git:http://git.eju-inc.com/ejuetc/java/consumer.git</developerConnection>
        <tag>HEAD</tag>
    </scm>

</project>