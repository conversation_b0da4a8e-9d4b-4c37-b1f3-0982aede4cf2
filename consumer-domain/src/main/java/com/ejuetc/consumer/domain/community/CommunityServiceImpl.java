package com.ejuetc.consumer.domain.community;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.consumer.domain.region.Region;
import com.ejuetc.consumer.domain.region.RegionRpt;
import com.ejuetc.consumer.web.vo.CommunityTipsVO;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.ThreadUtils.redisLock;
import static com.ejuetc.commons.base.utils.ThreadUtils.asyncExec;
import static java.lang.Thread.sleep;
import static java.time.Duration.ofSeconds;
import static org.springframework.data.domain.PageRequest.ofSize;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class CommunityServiceImpl implements CommunityService {
    private final RegionRpt regionRpt;
    private final CommunityRpt communityRpt;
    private final CommunityBindRpt communityBindRpt;
    private final CommunityBeikeRpt communityBeikeRpt;
    private final RedisTemplate<String, String> redisTemplate;
    private final EntityManager entityManager;
    private final CommunityPictureRpt pictureRpt;
    private final CommunityDetailRpt detailRpt;
    private final CommunityTipsRpt tipsRpt;

    @Override
    public CommunityBind queryOrBind(String address, String name) {
        CommunityBind bind = communityBindRpt.findByAddressAndName(address, name);
        if (bind == null || bind.isNeedExec()) {
            Long bindId = getBean(CommunityServiceImpl.class).doBind(address, name);
            return getBean(CommunityBindRpt.class).findById(bindId).orElseThrow();
        }
        return bind;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public Long doBind(String address, String name) {
        log.info("开始绑定小区：{}-{}", address, name);
        redisLock(50, 50, "CommunityBindRpt.findByAddressAndName", address, name);
        CommunityBind bind = communityBindRpt.findByAddressAndName(address, name);
        if (bind == null) bind = new CommunityBind(address, name).save();
        if (bind.isNeedExec()) bind.exec();
        log.info("绑定小区：{}-{} 成功({})", address, name, bind.getId());
        return bind.getId();
    }

    /**
     * 批量绑定贝壳小区
     *
     * @param cities     支持的城市列表，为空时不限定城市
     * @param limitCount 处理总数限制
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    @Override
    public void bindCommunityBeike(List<String> cities, int limitCount) {
        log.info("开始批量绑定贝壳小区，城市列表：{}，限制数量：{}", cities, limitCount);

        // 查询需要绑定的贝壳小区ID列表
        List<Long> beikeIds = communityBeikeRpt.findWaitBind(cities, ofSize(limitCount));

        if (beikeIds.isEmpty()) {
            log.info("没有需要绑定的贝壳小区，城市列表：{}", cities);
            return;
        }

        log.info("找到 {} 个需要绑定的贝壳小区，城市列表：{}", beikeIds.size(), cities);

        asyncExec(() -> {
            beikeIds.forEach(beikeId -> {
                try {
                    getBean(CommunityService.class).doBindCommunityBeike(beikeId);
                } catch (Throwable e) {
                    log.error("绑定贝壳小区失败，ID: {}", beikeId, e);
                }
            });
        });

    }

    /**
     * 绑定单个贝壳小区
     *
     * @param beikeId 贝壳小区ID
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public void doBindCommunityBeike(Long beikeId) {
        CommunityBeike beike = communityBeikeRpt.findById(beikeId).orElse(null);
        if (beike == null) {
            log.warn("贝壳小区不存在，ID: {}", beikeId);
            return;
        }

        // 检查是否需要绑定
        if (!beike.needsBind()) {
            log.debug("贝壳小区已绑定，跳过，ID: {}", beikeId);
            return;
        }

        String address = beike.getFullAddress();
        String name = beike.getName();

        if (address == null || name == null) {
            log.warn("贝壳小区地址或名称为空，跳过绑定，ID: {}, address: {}, name: {}", beikeId, address, name);
            return;
        }

        try {
            log.debug("开始绑定贝壳小区，ID: {}, address: {}, name: {}", beikeId, address, name);

            // 调用现有的queryOrBind方法进行绑定
            CommunityBind bind = queryOrBind(address, name);

            // 更新贝壳小区的绑定信息
            beike.bindCommunity(bind);

            log.info("贝壳小区绑定成功，ID: {}, bindId: {}, communityId: {}",
                    beikeId, bind.getId(), bind.getCommunity() != null ? bind.getCommunity().getId() : null);

        } catch (Exception e) {
            log.error("绑定贝壳小区异常，ID: {}", beikeId, e);
            beike.setMatchType(e.getMessage());
        }
    }

    @Override
    public List<CommunityTipsVO> queryTips(String cityName, String keyword) {
        if (keyword == null || keyword.length() < 2) throw new BusinessException("bc.ejuetc.consumer.1021", keyword);
        Region city = regionRpt.findRegion(cityName.replace("市", ""));
        CommunityTips tips = tipsRpt.findByCityAndKeyword(city, keyword);
        if (tips == null) {
            tips = new CommunityTips(city, keyword).save();
            tips.exec();
        }

        List<CommunityTipsVO> vos = new ArrayList<>();
        for (Object tip : tips.getResponse().getJSONArray("tips")) {
            JSONObject tipObj = (JSONObject) tip;
            vos.add(new CommunityTipsVO()
                    .setEstateName(tipObj.getString("name"))
                    .setEstateExecutionAddress(tipObj.get("address") instanceof JSONArray ? tipObj.getString("name") : tipObj.getString("address"))
                    .setDistrict(tipObj.get("district") instanceof JSONArray ? city.getCityName() : tipObj.getString("district"))
            );
        }
        return vos;
    }

}

