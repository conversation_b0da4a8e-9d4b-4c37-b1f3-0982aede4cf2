package com.ejuetc.consumer.domain.implicit.bind;

import com.alibaba.fastjson.JSON;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.usertype.ApiResponseUT;
import jakarta.persistence.AttributeOverride;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.Embedded;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CompositeType;

@Accessors(chain = true)
@Getter
@Setter(AccessLevel.PROTECTED)
@Embeddable
@NoArgsConstructor
public class BindInfo {

    @Column(name = "bind_request", columnDefinition = "json COMMENT '绑定请求'")
    private String request;

    @Column(name = "bind_response", columnDefinition = "json COMMENT '绑定响应'")
    private String response;

    @Embedded
    @CompositeType(ApiResponseUT.class)
    @AttributeOverride(name = "status", column = @Column(name = "bind_status", columnDefinition = "varchar(64) COMMENT '绑定状态'"))
    @AttributeOverride(name = "message", column = @Column(name = "bind_message", columnDefinition = "text COMMENT '绑定信息'"))
    protected ApiResponse<?> result;

    @Column(name = "secret_pone", columnDefinition = "varchar(64) COMMENT '绑定的号码'")
    private String secretPhone;

    @Column(name = "sub_id", columnDefinition = "varchar(64) COMMENT '绑定编号'", unique = true)
    private String subId;

    public void setRequest(Object bindRequest) {
        this.request = JSON.toJSONString(bindRequest, true);
    }

    public void setResponse(Object bindResponse) {
        this.response = JSON.toJSONString(bindResponse, true);
    }
}
