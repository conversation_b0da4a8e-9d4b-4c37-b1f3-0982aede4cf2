package com.ejuetc.consumer.domain.implicit.bind;

import com.aliyun.dyplsapi20170525.Client;
import com.aliyun.dyplsapi20170525.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.domain.consumer.ConsumerLogin;
import com.ejuetc.consumer.domain.delegation.ChannelDelegation;
import com.ejuetc.consumer.domain.implicit.ImplicitBase;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;

import static com.ejuetc.commons.base.response.ApiResponse.apiResponse;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.response.ResponseStatus.FAIL_SYS;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.spring.SpringUtil.getProperty;
import static java.time.LocalDateTime.now;
import static java.time.format.DateTimeFormatter.ofPattern;
import static lombok.AccessLevel.PROTECTED;


@Entity
@Getter
@Setter(PROTECTED)
@DiscriminatorValue("ALIYUN")
@SubtypeCode(parent = ImplicitBase.class, code = "ALIYUN", name = "阿里隐号")
@NoArgsConstructor
public class ImplicitBind4Aliyun extends ImplicitBind {

    @Getter(lazy = true)
    @Accessors(fluent = true)
    private static final Client client = makeClient();

    @SneakyThrows
    private static Client makeClient() {
        return new Client(new Config()
                .setAccessKeyId(getProperty("ejuetc.consumer.implicit.accessKey"))
                .setAccessKeySecret(getProperty("ejuetc.consumer.implicit.accessSecret"))
                .setEndpoint("dyplsapi.aliyuncs.com"));
    }

    @Getter(lazy = true)
    @Accessors(fluent = true)
    private static final String poolKey = getProperty("ejuetc.consumer.implicit.poolKey", String.class, "FC100000181794217");

    public ImplicitBind4Aliyun(ConsumerLogin login, ChannelDelegation delegation, String consumerPhone) {
        super(login, delegation, consumerPhone);
    }

    protected void doBind(BindInfo bindInfo) throws Exception {
        BindAxbRequest request = new BindAxbRequest()
                .setPoolKey(poolKey())
                .setPhoneNoA(getConsumerPhone())
                .setPhoneNoB(getBrokerPhone())
                .setExpiration(getExpiration().format(ofPattern("yyyy-MM-dd hh:mm:ss")))
                .setIsRecordingEnabled(true)
                .setOutId(getId().toString())
                .setCallDisplayType(1)
                .setCallTimeout(20);
        bindInfo.setRequest(request);
        BindAxbResponse response = client().bindAxbWithOptions(request, new RuntimeOptions());
        bindInfo.setResponse(response);
        BindAxbResponseBody responseBody = response.getBody();
        ApiResponse<Object> result = response.getStatusCode() == 200 && responseBody.code.equals("OK")
                ? succ()
                : apiResponse(FAIL_SYS, responseBody != null ? responseBody.message : "绑定隐号失败", null);
        bindInfo.setResult(result);

        if (result.isSucc()) {
            bindInfo.setSecretPhone(responseBody.secretBindDTO.secretNo);
            bindInfo.setSubId(responseBody.secretBindDTO.subsId);
        }
    }

    protected void doRefresh(RefreshInfo refreshInfo) throws Exception {
        UpdateSubscriptionRequest request = new UpdateSubscriptionRequest()
                .setPoolKey(poolKey())
                .setSubsId(getBindInfo().getSubId())
                .setPhoneNoX(getBindInfo().getSecretPhone())
                .setOperateType("updateExpire")
                .setExpiration(getExpiration().format(ofPattern("yyyy-MM-dd hh:mm:ss")));
        refreshInfo.setRequest(request);
        UpdateSubscriptionResponse response = client().updateSubscription(request);
        refreshInfo.setResponse(response);
        UpdateSubscriptionResponseBody responseBody = response.getBody();
        refreshInfo.setResult(response.getStatusCode() == 200 && responseBody.code.equals("OK")
                ? succ()
                : apiResponse(FAIL_SYS, responseBody != null ? responseBody.message : "解绑隐号失败", null)
        );
    }

    protected void doUnbind(UnbindInfo unbindInfo) throws Exception {
        UnbindSubscriptionRequest request = new UnbindSubscriptionRequest()
                .setPoolKey(poolKey())
                .setSubsId(getBindInfo().getSubId())
                .setSecretNo(getBindInfo().getSecretPhone());
        unbindInfo.setRequest(request);
        UnbindSubscriptionResponse unbindResponse = client().unbindSubscription(request);
        unbindInfo.setResponse(unbindResponse);
        UnbindSubscriptionResponseBody responseBody = unbindResponse.getBody();
        unbindInfo.setResult(unbindResponse.getStatusCode() == 200 && responseBody.code.equals("OK")
                ? succ()
                : apiResponse(FAIL_SYS, responseBody != null ? responseBody.message : "解绑隐号失败", null));
    }

}
