package com.ejuetc.consumer.domain.consumer;

import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.usertype.EntityUT;
import com.ejuetc.consumer.api.dto.ConsumerShareDTO;
import com.ejuetc.consumer.domain.broker.Broker;
import com.ejuetc.consumer.domain.broker.BrokerRpt;
import com.ejuetc.consumer.domain.delegation.Delegation;
import com.ejuetc.consumer.domain.delegation.DelegationRpt;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.CompositeType;
import org.hibernate.annotations.Where;

import java.time.LocalDateTime;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@Getter
@Setter
@Entity
@NoArgsConstructor
@Comment("分享消费者")
@Table(name = "tb_consumer_share", uniqueConstraints = {
        @UniqueConstraint(name = "uk_consumer_share_code", columnNames = {"code"})
}, indexes = {
        @Index(name = "idx_consumer_share_targetBrokerId_createTime", columnList = "target_broker_id, create_time"),
})
@Where(clause = "logic_delete = 0")
public class ConsumerShare extends BaseEntity<ConsumerShare> {

    @Id
    @GeneratedValue(generator = "consumer_share")
    @SequenceGenerator(name = "consumer_share", sequenceName = "seq_consumer_share_id")
    private Long id;

    @Column(name = "code", columnDefinition = "varchar(63) COMMENT '分享码'", nullable = false)
    private String code;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", columnDefinition = "bigint(20) COMMENT '父分享主键'")
    private ConsumerShare parent;

    @Setter(AccessLevel.PRIVATE)
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "sharer_broker_id", columnDefinition = "bigint(20) COMMENT '分享者经纪人主键'")
    private Broker sharerBroker;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "sharer_login_id", columnDefinition = "bigint(20) COMMENT '分享人登录主键'")
    private ConsumerLogin sharerLogin;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "sharer_account_id", columnDefinition = "bigint(20) COMMENT '分享人账号主键'")
    private ConsumerAccount sharerAccount;


    @Embedded
    @CompositeType(EntityUT.class)
    @AttributeOverride(name = "type", column = @Column(name = "target_type", columnDefinition = "varchar(127) COMMENT '分享目标类型'", nullable = false))
    @AttributeOverride(name = "id", column = @Column(name = "target_id", columnDefinition = "bigint(20) COMMENT '分享目标外键'", nullable = false))
    private BaseEntity target;

    @Setter(AccessLevel.PRIVATE)
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "target_broker_id", columnDefinition = "bigint(20) COMMENT '分享目标所属经纪人主键'")
    private Broker targetBroker;


    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "aim_account_id", columnDefinition = "bigint(20) COMMENT '分享目标账号主键'")
    private ConsumerAccount aimAccount;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "aim_login_id", columnDefinition = "bigint(20) COMMENT '分享目标登录主键'")
    private ConsumerLogin aimLogin;

    @Column(name = "aim_time", columnDefinition = "datetime COMMENT '分享目标生效时间'")
    private LocalDateTime aimTime;

    public ConsumerShare(Long loginId, String shareCode, ConsumerShareDTO.TargetType targetType, Long targetId, Long brokerId) {
        this.setSharerLogin(loginId);
        this.setSharerBroker(brokerId);
        this.setCode(shareCode);
        this.setTarget(targetType, targetId);
    }

    private void setSharerBroker(Long brokerId) {
        if (brokerId == null) return;
        this.sharerBroker = getBean(BrokerRpt.class).getReferenceById(brokerId);
    }

    private void setSharerLogin(Long loginId) {
        this.sharerLogin = getBean(ConsumerLoginRpt.class).getReferenceById(loginId);
    }

    private void setTarget(ConsumerShareDTO.TargetType targetType, Long targetId) {
        this.target = switch (targetType) {
            case DELEGATION -> {
                Delegation delegation = getBean(DelegationRpt.class).getReferenceById(targetId);
                this.targetBroker = delegation.getBroker();
                yield delegation;
            }
            case BROKER -> {
                Broker broker = getBean(BrokerRpt.class).getReferenceById(targetId);
                this.targetBroker = broker;
                yield broker;
            }
        };

    }

    private void setSharerLogin(ConsumerLogin sharerLogin) {
        this.sharerLogin = sharerLogin;
        if (sharerLogin != null) {
            this.sharerAccount = sharerLogin.getAccount();
            setParent(sharerLogin.getShare());
        }
    }

    public void setParent(ConsumerShare parent) {
        this.parent = parent;
        if (parent != null) {
            this.sharerBroker = parent.getSharerBroker();
        }
    }

    public void setAimLogin(ConsumerLogin aimLogin) {
        this.aimLogin = aimLogin;
        if (aimLogin != null) {
            this.aimAccount = aimLogin.getAccount();
        }
    }
}
