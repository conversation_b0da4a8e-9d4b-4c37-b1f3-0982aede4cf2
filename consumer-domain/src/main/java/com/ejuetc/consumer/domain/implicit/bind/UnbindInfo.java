package com.ejuetc.consumer.domain.implicit.bind;

import com.alibaba.fastjson.JSON;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.usertype.ApiResponseUT;
import jakarta.persistence.AttributeOverride;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.Embedded;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CompositeType;

import java.time.LocalDateTime;

@Accessors(chain = true)
@Getter
@Setter(AccessLevel.PROTECTED)
@Embeddable
@NoArgsConstructor
public class UnbindInfo {

    @Column(name = "unbind_request", columnDefinition = "json COMMENT '解绑请求'")
    private String request;

    @Column(name = "unbind_response", columnDefinition = "json COMMENT '解绑响应'")
    private String response;

    @Embedded
    @CompositeType(ApiResponseUT.class)
    @AttributeOverride(name = "status", column = @Column(name = "unbind_status", columnDefinition = "varchar(64) COMMENT '解绑状态'"))
    @AttributeOverride(name = "message", column = @Column(name = "unbind_message", columnDefinition = "text COMMENT '解绑信息'"))
    protected ApiResponse<?> result;

    @Column(name = "unbind_time", columnDefinition = "datetime COMMENT '解绑时间'")
    private LocalDateTime unbindTime = LocalDateTime.now();

    public void setRequest(Object bindRequest) {
        this.request = JSON.toJSONString(bindRequest, true);
    }

    public void setResponse(Object bindResponse) {
        this.response = JSON.toJSONString(bindResponse, true);
    }
}
