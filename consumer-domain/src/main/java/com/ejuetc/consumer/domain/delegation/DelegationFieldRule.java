package com.ejuetc.consumer.domain.delegation;

import com.alibaba.fastjson.JSON;
import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.exception.ParamException;
import com.ejuetc.commons.base.tuple.Tuple;
import com.ejuetc.commons.base.tuple.Tuple2;
import com.ejuetc.consumer.api.delegation.EditDelegationPO;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import static com.ejuetc.commons.base.clazz.ClassUtils.getFieldValue;
import static com.ejuetc.commons.base.clazz.ClassUtils.getPropertyValue;
import static lombok.AccessLevel.PROTECTED;

@Accessors(chain = true)
@Setter(PROTECTED)
@Getter
public class DelegationFieldRule implements Serializable {
    private Map<String, Rule> ruleMap = new HashMap<>();

    public Map<String, Boolean> getFields(ChannelDTO.Code channelCode, BusinessOpenDTO.Code businessCode) {
        Rule rule = getRule(channelCode, businessCode);
        return rule != null ? rule.fields : null;
    }

    public Rule getRule(ChannelDTO.Code channelCode, BusinessOpenDTO.Code code) {
        return ruleMap.get(getKey(channelCode, code));
    }

    private static @NotNull String getKey(ChannelDTO.Code channelCode, BusinessOpenDTO.Code code) {
        return channelCode.name() + "-" + code.name();
    }


    @Getter
    @Setter(PROTECTED)
    @Accessors(chain = true)
    public static class Rule {
        //呈现字段及是否必填
        private Map<String, Boolean> fields = new HashMap<>();
        //是否需要验证
        private boolean needGovVerify = false;

        public void check(EditDelegationPO po) {
            fields.forEach((field, required) -> {
                if (required && getPropertyValue(po, field) == null) {
                    throw new ParamException(field, "[" + field + "]不能为空");
                }
            });
//            if (needGovVerify) delegation.govVerify(); todo
        }
    }

    public static void main(String[] args) {
        Rule rentRule = new Rule();
        rentRule.needGovVerify = true;
        rentRule.fields.put("communityAddress", false);
        rentRule.fields.put("level", false);
        rentRule.fields.put("type", false);
        rentRule.fields.put("title", false);
        rentRule.fields.put("around", false);
        rentRule.fields.put("roomNum", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);
        rentRule.fields.put("", false);

        rentRule.fields.put("houseCertType", true);
        rentRule.fields.put("houseCertNO", true);
        rentRule.fields.put("ownerCertNO", true);
        rentRule.fields.put("housePlanPurpose", true);
        rentRule.fields.put("community.townName", true);

        Rule saleRule = new Rule();
        saleRule.needGovVerify = true;
        saleRule.fields.put("district", true);
        saleRule.fields.put("broker.name", true);
        saleRule.fields.put("broker.idNum", true);
        saleRule.fields.put("broker.professionInformationCardUrl", true);
        saleRule.fields.put("broker.companyName", true);
        saleRule.fields.put("broker.companyLicenseNum", true);
        saleRule.fields.put("houseBusiNO", true);
        saleRule.fields.put("ownerCertNO", true);
        saleRule.fields.put("bailorNames", true);
        saleRule.fields.put("bailorName", true);
        saleRule.fields.put("bailorCertType", true);
        saleRule.fields.put("bailorCertNO", true);
        saleRule.fields.put("priceTotal", true);
        saleRule.fields.put("deadline", true);

        DelegationFieldRule rule = new DelegationFieldRule();
        rule.ruleMap.put(getKey(ChannelDTO.Code.XIANYU, BusinessOpenDTO.Code.SALE), saleRule);
        rule.ruleMap.put(getKey(ChannelDTO.Code.XIANYU, BusinessOpenDTO.Code.RENT), rentRule);
        String jsonString = JSON.toJSONString(rule, true);
        System.out.println(jsonString);
    }
}
