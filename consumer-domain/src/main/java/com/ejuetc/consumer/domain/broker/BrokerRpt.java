package com.ejuetc.consumer.domain.broker;

import jakarta.persistence.LockModeType;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.List;
import java.util.Optional;

import static com.ejuetc.commons.base.utils.ThreadUtils.redisLock;

public interface BrokerRpt extends JpaRepositoryImplementation<Broker, Long> {
    Optional<Broker> findByCode(String brokerCode);

    @Query("""
            select b
            from Broker b
            join Broker b2 on b.companyId = b2.companyId
            where b2.id = ?1
            """)
    List<Broker> findCompanyBrokers(Long brokerId);

    default Broker findAndLockById(Long brokerId) {
        redisLock(10, 10, "BrokerRpt.findAndLockById", brokerId);
        return findById(brokerId).orElse(null);
    }


}
