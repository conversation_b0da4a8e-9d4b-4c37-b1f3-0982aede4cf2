package com.ejuetc.consumer.domain.broker;

import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.consumer.api.broker.EditBrokerPO;
import com.ejuetc.consumer.domain.region.Region;
import com.ejuetc.consumer.domain.region.RegionRpt;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.util.UUID;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@Getter
@Entity
@NoArgsConstructor
@Comment("经纪人")
@Table(name = "tb_broker", indexes = {
        @Index(name = "idx_broker_companyId", columnList = "company_id"),
        @Index(name = "idx_broker_cityId", columnList = "city_id"),
})
@Where(clause = "logic_delete = 0")
public class Broker extends BaseEntity<Broker> {

    @Id
    private Long id;

    @Column(name = "code", columnDefinition = "varchar(63) COMMENT '经纪人编码'")
    private String code = UUID.randomUUID().toString().replaceAll("-", "");

    @Column(name = "name", columnDefinition = "varchar(255) COMMENT '(身份证)姓名'")
    private String name;

    @Column(name = "phone", columnDefinition = "varchar(63) COMMENT '手机号'")
    private String phone;

    @Column(name = "icon", columnDefinition = "varchar(255) COMMENT '头像'")
    private String icon;

    @Column(name = "introduce", columnDefinition = "varchar(255) COMMENT '介绍'")
    private String introduce;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "city_id", columnDefinition = "bigint(20) COMMENT '城市ID'")
    private Region city;

    @Column(name = "city_name", columnDefinition = "varchar(255) COMMENT '城市名称'")
    private String cityName;

    @Column(name = "id_num", columnDefinition = "varchar(255) COMMENT '身份证号'")
    private String idNum;

    @Column(name = "store_name", columnDefinition = "varchar(255) COMMENT '门店名称'")
    private String storeName;

    @Column(name = "company_id", columnDefinition = "bigint(20) COMMENT '所属公司userId'", nullable = false)
    private Long companyId;

    @Column(name = "merchant_id", columnDefinition = "bigint(20) COMMENT '所属商户号'")
    private Long merchantId;

    @Column(name = "company_name", columnDefinition = "varchar(255) COMMENT '公司名称'")
    private String companyName;

    @Column(name = "company_license_num", columnDefinition = "varchar(255) COMMENT '公司营业执照号'")
    private String companyLicenseNum;

    @Column(name = "company_license_url", columnDefinition = "varchar(511) COMMENT '公司营业执照图片地址'")
    private String companyLicenseUrl;

    @Column(name = "company_legal", columnDefinition = "varchar(63) COMMENT '公司法人(姓名)'")
    private String companyLegal;

    @Column(name = "profession_information_card_url", columnDefinition = "varchar(511) COMMENT '从业信息卡地址'")
    private String professionInformationCardUrl;

    @Column(name = "profession_information_card_number", columnDefinition = "varchar(255) COMMENT '从业信息卡编号'")
    private String professionInformationCardNumber;

    public Broker(EditBrokerPO po) {
        this.id = po.getBrokerId();
        edit(po);
    }

    public void edit(EditBrokerPO po) {
        this.companyId = po.getCompanyId();
        this.merchantId = po.getMerchantId();
        this.name = po.getName();
        this.idNum = po.getIdNum();
        this.storeName = po.getStoreName();
        this.companyName = po.getCompanyName();
        this.companyLicenseNum = po.getCompanyLicenseNum();
        this.companyLicenseUrl = po.getCompanyLicenseUrl();
        this.companyLegal = po.getCompanyLegal();
        this.phone = po.getPhone();
        this.icon = po.getIcon();
        this.introduce = po.getIntroduce();
        this.professionInformationCardUrl = po.getProfessionInformationCardUrl();
        this.professionInformationCardNumber = po.getProfessionInformationCardNumber();
        setCity(getBean(RegionRpt.class).findByCode(po.getCityCode()));
    }

    public void setCity(Region city) {
        this.city = city;
        if (city != null) {
            this.cityName = city.getName();
        }
    }

    public String getCompany() {
        return companyName;
    }

    public Long getCityId() {
        return city != null ? city.getId() : null;
    }
}
