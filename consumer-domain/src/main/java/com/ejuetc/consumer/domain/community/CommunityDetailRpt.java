package com.ejuetc.consumer.domain.community;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.List;

public interface CommunityDetailRpt extends JpaRepositoryImplementation<CommunityDetail, Long> {

    @Query(value = """
            select cd.id from CommunityDetail cd
            where (:cityCodes is null or cd.cityCd in :cityCodes)
            and cd.communityBind is null
            """)
    List<Long> findWaitBind(List<String> cityCodes, Pageable pageable);

    @Query(value = """
            select cd from CommunityDetail cd
            where cd.community is not null
            and cd.verify=false
            and cd.overlapRate is null
            """)
    List<CommunityDetail> findOverlapRateIsNull(Pageable pageable);
}
