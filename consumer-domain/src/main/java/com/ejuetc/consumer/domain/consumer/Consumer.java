package com.ejuetc.consumer.domain.consumer;

import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.exception.BusinessException;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import static com.ejuetc.commons.base.utils.StringUtils.*;
import static lombok.AccessLevel.PROTECTED;

@Setter(PROTECTED)
@Getter
@Entity
@NoArgsConstructor
@Comment("消费者")
@Table(name = "tb_consumer")
@Where(clause = "logic_delete = 0")
public class Consumer extends BaseEntity<Consumer> implements Serializable {

    @Id
    @GeneratedValue(generator = "consumer_id")
    @SequenceGenerator(name = "consumer_id", sequenceName = "seq_consumer_id")
    private Long id;

    @Column(name = "phone", columnDefinition = "varchar(64) COMMENT '手机号'")
    private String phone;

    @Column(name = "nick_name", columnDefinition = "varchar(64) COMMENT '昵称'")
    private String nickName;

    @Column(name = "icon_url", columnDefinition = "varchar(64) COMMENT '头像URL'")
    private String iconUrl;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "consumer")
    private final List<ConsumerAccount> consumerAccounts = new ArrayList<>();

    public Consumer(String phone) {
        if (!isMp(phone)) throw new BusinessException("bc.ejuetc.consumer.1025", "手机号");
        this.phone = phone;
    }

    public void addLoginAccount(ConsumerAccount consumerAccount) {
        if (consumerAccounts.contains(consumerAccount)) return;

        consumerAccounts.add(consumerAccount);
        if (getNickName() == null) {
            setNickName(consumerAccount.getNiceName());
        }
        if (getIconUrl() == null) {
            setIconUrl(consumerAccount.getIconUrl());
        }
    }

    public String getConsumerPhone4Mask() {
        return maskMobilePhone(phone);
    }

    public void setNickName(String nickName) {
        if (notBlank(this.nickName)) return;
        this.nickName = nickName;
    }

    public void setIconUrl(String iconUrl) {
        if (notBlank(this.iconUrl)) return;
        this.iconUrl = iconUrl;
    }
}
