package com.ejuetc.consumer.domain.community;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.List;

public interface CommunityBeikeRpt extends JpaRepositoryImplementation<CommunityBeike, Long> {

    /**
     * 查询需要绑定的贝壳小区（community_id和community_bind_id均为空）
     *
     * @param cities   城市列表，为空时不限定城市
     * @param pageable 分页参数
     */
    @Query("""
            select cb.id
            from CommunityBeike cb
            where cb.communityId is null
            and cb.communityBindId is null
            and cb.matchType is null
            and (:cities is null or cb.cityName in :cities)
            """)
    List<Long> findWaitBind(List<String> cities, Pageable pageable);

    @Query("""
                select bk from CommunityBeike bk
                join fetch bk.community c
                where bk.cityId = :cityId
                and bk.communityDetailId is not null
                and (bk.name like concat('%',:keyword,'%'))
            """)
    Page<CommunityBeike> findHasDetailByName(Long cityId, String keyword, Pageable pageable);


}
