package com.ejuetc.consumer.domain.community;

import com.ejuetc.commons.base.component.OssComponent;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.valueobj.Command;
import com.ejuetc.consumer.web.vo.CommunityLayoutSummaryVO;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.codehaus.groovy.util.ListHashMap;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.consumer.domain.commons.AMapUtils.callAMap;
import static java.time.LocalDateTime.now;
import static lombok.AccessLevel.PROTECTED;

@Getter
@Entity
@NoArgsConstructor
@Comment("小区户型")
@Table(name = "tb_community_layout",indexes = @Index(name = "idx_community_layout_communityId", columnList = "community_id"))
@Where(clause = "logic_delete = 0")
public class CommunityLayout extends BaseEntity<CommunityLayout> implements Command {

    @Id
    @GeneratedValue(generator = "community_layout_id")
    @SequenceGenerator(name = "community_layout_id", sequenceName = "seq_community_layout_id")
    private Long id;

    @Column(name = "src_layout_id", columnDefinition = "bigint COMMENT '原户型ID'")
    private Long srcLayoutId;//house_layout_id;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "community_detail_id", columnDefinition = "bigint(20) COMMENT '小区详情ID'")
    private CommunityDetail communityDetail;

    @Setter(PROTECTED)
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "community_id", columnDefinition = "bigint(20) COMMENT '小区ID'")
    private Community community;

    @Column(name = "src_community_id", columnDefinition = "bigint COMMENT '原关联小区ID'")
    private Long srcCommunityId;//community_id

    @Column(name = "name", columnDefinition = "varchar(255) COMMENT '户型名称'")
    protected String name;//house_layout_name

    @Column(name = "room_count", columnDefinition = "int(10) default 0 null comment '房间数'")
    private Integer roomCount;//room_num

    @Column(name = "hall_count", columnDefinition = "int(10) default 0 null comment '客厅数'")
    private Integer hallCount;//hall_num

    @Column(name = "toilet_count", columnDefinition = "int(10) default 0 null comment '卫生间数'")
    private Integer toiletCount;//bath_num

    @Column(name = "balcony_count", columnDefinition = "int(10) default 0 null comment '阳台数'")
    private Integer balconyCount;//balcony_num

    @Column(name = "kitchen_count", columnDefinition = "int(10) default 0 null comment '厨房数'")
    private Integer kitchenCount;//kitchen_num

    @Column(name = "area", columnDefinition = "decimal(10,2) COMMENT '面积'")
    private BigDecimal area;//area

    @Column(name = "area_act", columnDefinition = "decimal(10,2) COMMENT '套内面积'")
    private BigDecimal areaAct;//act_area

    @Column(name = "area_min", columnDefinition = "decimal(10,2) COMMENT '最小面积'")
    private BigDecimal areaMin;//min_area

    @Column(name = "area_max", columnDefinition = "decimal(10,2) COMMENT '最大面积'")
    private BigDecimal areaMax;//max_area

    @Column(name = "main_layout_flag", columnDefinition = "bit(1) COMMENT '主力户型'")
    protected Boolean mainLayoutFlag;//main_layout_ind

    @Column(name = "pic_url", columnDefinition = "varchar(255) COMMENT '户型图'")
    protected String picUrl;//layout_pic_path_fang_v2

    @Column(name = "orientation", columnDefinition = "varchar(255) COMMENT '房间朝向'")
    protected String orientation;//room_orientation

    @Column(name = "set_num", columnDefinition = "int(10) default 0 null comment '总套数'")
    private Integer setNum;//set_num


    public void convertUrl() {
        if (picUrl != null) return;

        try {
            OssComponent ossComponent = getBean(OssComponent.class);
            this.picUrl = ossComponent.urlConvert(
                    picUrl,
                    "community_layout/" + communityDetail.getId() + "/" + UUID.randomUUID() + picUrl.substring(picUrl.lastIndexOf("."))
            );
        } catch (Exception e) {
            log.error("convertUrl error", e);
            picUrl = "error:" + e.getMessage();
        }
    }

    @Override
    public void exec() {
        convertUrl();
    }


    public static Map<String, CommunityLayoutSummaryVO> getLayoutsSummaryMap(List<CommunityLayout> layouts) {
        Map<String, CommunityLayoutSummaryVO> result = new ListHashMap<>();
        layouts.stream()
                .sorted(Comparator.comparing(CommunityLayout::getName))
                .forEach(layout -> {
                    CommunityLayoutSummaryVO layoutVO = result.computeIfAbsent(layout.getName(), k -> convert2DTO(layout, new CommunityLayoutSummaryVO()));
                    layoutVO.addPicUrl(layout.getPicUrl());
                    layoutVO.addSetNum(layout.getSetNum());
                    layoutVO.setAreaMin(layout.getArea());
                    layoutVO.setAreaMin(layout.getAreaMin());
                    layoutVO.setAreaMax(layout.getArea());
                    layoutVO.setAreaMax(layout.getAreaMax());
                });
        return result;
    }

}
