package com.ejuetc.consumer.domain.community;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.List;
import java.util.Optional;

public interface CommunityPictureRpt extends JpaRepositoryImplementation<CommunityPicture, Long> {


    @Query("""
            from CommunityPicture cp
            where cp.url4Oss is null
            """)
    List<CommunityPicture> findWaitConvertUrl(Pageable pageable);
}
