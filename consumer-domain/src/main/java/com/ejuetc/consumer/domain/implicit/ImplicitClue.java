package com.ejuetc.consumer.domain.implicit;

import com.alibaba.fastjson.JSON;
import com.ejuetc.channel.api.ClueAPI;
import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelClueDTO;
import com.ejuetc.channel.pro.ReceiveAliPayCluePO;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.spring.SpringUtil;
import com.ejuetc.commons.base.usertype.JsonUT;
import com.ejuetc.consumer.api.dto.ImplicitClueDTO;
import com.ejuetc.consumer.domain.consumer.ConsumerRelation;
import com.ejuetc.consumer.domain.delegation.Delegation;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


@Slf4j
@Getter
@Entity
@NoArgsConstructor
@Comment("隐号线索")
@Table(name = "tb_implicit_clue")
@Where(clause = "logic_delete = 0")
public class ImplicitClue extends ImplicitBase<ImplicitClue> {

    @Id
    @GeneratedValue(generator = "implicit_clue_id")
    @SequenceGenerator(name = "implicit_clue_id", sequenceName = "seq_implicit_clue_id")
    private Long id;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "relation_id", columnDefinition = "bigint(20) COMMENT '关联绑定关系ID'")
    private ConsumerRelation relation;

    @Column(name = "start_time", columnDefinition = "datetime COMMENT '绑定过期时间'")
    private LocalDateTime startTime;

    @Column(name = "end_time", columnDefinition = "datetime COMMENT '解绑时间'")
    private LocalDateTime endTime;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "clue")
    private List<ImplicitCall> calls = new ArrayList<>();

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "call_id", columnDefinition = "bigint(20) COMMENT '(首条)呼叫ID'")
    private ImplicitCall call;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "delegation_id", columnDefinition = "bigint(20) COMMENT '(首条)关联委托ID'")
    private Delegation delegation;

    @Enumerated(EnumType.STRING)
    @Column(name = "business_code")
    private BusinessOpenDTO.Code businessCode;

    @Enumerated(EnumType.STRING)
    @Comment("推送线索状态")
    @Column(name = "push_status")
    private ImplicitClueDTO.PushStatus pushStatus = ImplicitClueDTO.PushStatus.PENDING;

    @Type(JsonUT.class)
    @Column(name = "push_clue_resp", columnDefinition = "json COMMENT '推送线索响应'")
    private Object pushClueResp;

    @Setter(AccessLevel.PROTECTED)
    @Column(name = "consumer_phone", columnDefinition = "varchar(64) COMMENT '消费者手机号'")
    private String consumerPhone;

    public ImplicitClue(ImplicitCall call) {
        super(call.getBroker(), call.getAccount());
        this.call = call;
        this.consumerPhone = call.getConsumerPhone();
        this.businessCode = call.getBusinessCode();
        this.startTime = call.getStartTime();
        this.endTime = startTime.plusDays(7);
        this.delegation = call.getDelegation();
        this.relation = call.getRelation();
        this.relation.addClue(this);
    }

    public void addCall(ImplicitCall call) {
        if (!calls.contains(call)) {
            this.calls.add(call);
        }
        relation.addCall(call);
    }

    public void pushClue() {
        if (pushStatus != ImplicitClueDTO.PushStatus.PENDING && pushStatus != ImplicitClueDTO.PushStatus.FAIL) {
            throw new CodingException("隐号线索推送状态错误, %s=%s", getId(), getPushStatus());
        }
        ReceiveAliPayCluePO cluePO = new ReceiveAliPayCluePO();
        cluePO.setOutId(this.getId());
        cluePO.setUserId(this.getRelation().getBroker().getId());
        cluePO.setDelegationId(this.getDelegation().getId());
        cluePO.setBusinessCode(this.getBusinessCode());
        cluePO.setClueDate(this.getCreateTime().toLocalDate());
        try {
            log.info("push clue req, po={}", JSON.toJSONString(cluePO));
            ApiResponse<ChannelClueDTO> response = SpringUtil.getBean(ClueAPI.class).receiveAliPayClue(cluePO);
            log.info("push clue resp, {}", JSON.toJSONString(response));
            pushClueResp = response;
            pushStatus = response.isSucc() ? ImplicitClueDTO.PushStatus.SUCCESS : ImplicitClueDTO.PushStatus.FAIL;
        } catch (Exception e) {
            pushStatus = ImplicitClueDTO.PushStatus.FAIL;
            log.error("push clue error, id={}", this.getId(), e);
        }
    }

}
