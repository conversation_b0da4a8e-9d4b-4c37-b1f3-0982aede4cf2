package com.ejuetc.consumer.domain.implicit.bind;

import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.domain.consumer.ConsumerLogin;
import com.ejuetc.consumer.domain.delegation.ChannelDelegation;
import com.ejuetc.consumer.domain.implicit.ImplicitBase;
import com.fangyou.service.hiddencall.client.HiddenCallClient;
import com.fangyou.service.hiddencall.model.CallBindByConsumerPO;
import com.fangyou.service.hiddencall.model.CallBindDTO;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import static com.ejuetc.commons.base.response.ApiResponse.apiResponse;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.*;
import static java.time.LocalDateTime.now;
import static java.time.format.DateTimeFormatter.ofPattern;
import static lombok.AccessLevel.PROTECTED;


@Entity
@Getter
@Setter(PROTECTED)
@DiscriminatorValue("YIKETONG")
@SubtypeCode(parent = ImplicitBase.class, code = "YIKETONG", name = "移号通")
@NoArgsConstructor
public class ImplicitBind4Yihaotong extends ImplicitBind {

    @Accessors(fluent = true)
    @Getter(lazy = true)
    private static final HiddenCallClient callClient = getAPIByProperty(HiddenCallClient.class, "ImplicitBind4Yihaotong.callClient.url");

    public ImplicitBind4Yihaotong(ConsumerLogin login, ChannelDelegation delegation, String consumerPhone) {
        super(login, delegation, consumerPhone);
    }

    protected void doBind(BindInfo info) throws Exception {
        CallBindByConsumerPO po = newPO4Edit();
        info.setRequest(po);
        ApiResponse<CallBindDTO> response = callClient().editBindByConsumer(po);
        info.setResponse(response);
        info.setResult(response);

        if (response.isSucc()) {
            CallBindDTO data = response.getData();
            info.setSecretPhone(data.getVirtualNumber());
            info.setSubId(data.getId() + "");
        }
    }

    protected void doRefresh(RefreshInfo info) throws Exception {
        CallBindByConsumerPO po = newPO4Edit().setBindId(Long.valueOf(getBindInfo().getSubId()));
        info.setRequest(po);
        ApiResponse<CallBindDTO> response = callClient().editBindByConsumer(po);
        info.setResponse(response);
        info.setResult(response);
    }

    private CallBindByConsumerPO newPO4Edit() {
        return new CallBindByConsumerPO()
                .setUserPhone(getConsumerPhone())
                .setBrokerPhone(getBrokerPhone())
                .setExpireTime(getExpiration());
    }

    protected void doUnbind(UnbindInfo info) throws Exception {
        CallBindByConsumerPO po = new CallBindByConsumerPO().setBindId(Long.valueOf(getBindInfo().getSubId()));
        info.setRequest(po);
        ApiResponse<CallBindDTO> response = callClient().unbindByConsumer(po);
        info.setResponse(response);
        info.setResult(response);
    }

}
