package com.ejuetc.consumer.domain.community;

import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.response.ApiResponse;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.time.Duration;
import java.util.List;
import java.util.Objects;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

public interface CommunityBindRpt extends JpaRepositoryImplementation<CommunityBind, Long> {

    CommunityBind findByAddressAndName(String address, String name);

    @Query("select c.id from CommunityBind c where c.community is null and c.errorMsg is null")
    List<Long> findWaitRebinds(Pageable pageable);

}
