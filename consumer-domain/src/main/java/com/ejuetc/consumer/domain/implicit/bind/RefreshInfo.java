package com.ejuetc.consumer.domain.implicit.bind;

import com.alibaba.fastjson.JSON;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.usertype.ApiResponseUT;
import jakarta.persistence.AttributeOverride;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.Embedded;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CompositeType;

import java.time.LocalDateTime;

@Accessors(chain = true)
@Getter
@Setter(AccessLevel.PROTECTED)
@Embeddable
@NoArgsConstructor
public class RefreshInfo {

    @Column(name = "refresh_request", columnDefinition = "json COMMENT '延期请求'")
    private String request;

    @Column(name = "refresh_response", columnDefinition = "json COMMENT '延期响应'")
    private String response;

    @Column(name = "refresh_time", columnDefinition = "datetime COMMENT '延期时间'")
    private LocalDateTime refreshTime = LocalDateTime.now();


    @Embedded
    @CompositeType(ApiResponseUT.class)
    @AttributeOverride(name = "status", column = @Column(name = "refresh_status", columnDefinition = "varchar(64) COMMENT '延期状态'"))
    @AttributeOverride(name = "message", column = @Column(name = "refresh_message", columnDefinition = "text COMMENT '延期信息'"))
    protected ApiResponse<?> result;

    public void setRequest(Object bindRequest) {
        this.request = JSON.toJSONString(bindRequest, true);
    }

    public void setResponse(Object bindResponse) {
        this.response = JSON.toJSONString(bindResponse, true);
    }
}
