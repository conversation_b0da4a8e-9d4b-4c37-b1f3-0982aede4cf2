package com.ejuetc.consumer.domain.consumer;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.usertype.JsonUT;
import com.ejuetc.consumer.domain.region.Region;
import com.ejuetc.consumer.domain.region.RegionRpt;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import java.util.Map;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.StringUtils.isBlank;
import static com.ejuetc.commons.base.utils.StringUtils.notBlank;
import static com.ejuetc.consumer.domain.commons.AMapUtils.callAMap;
import static com.ejuetc.consumer.domain.commons.AMapUtils.makeUrl;

@Getter
@Entity
@NoArgsConstructor
@Comment("登录历史")
@Table(name = "tb_consumer_login")
@Where(clause = "logic_delete = 0")
public class ConsumerLogin extends BaseEntity<ConsumerLogin> {

    @Id
    @GeneratedValue(generator = "login_history_id")
    @SequenceGenerator(name = "login_history_id", sequenceName = "seq_login_history_id")
    private Long id;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "account_id", columnDefinition = "bigint(20) COMMENT '登录账号ID'")
    private ConsumerAccount account;

    @Column(name = "account_id", columnDefinition = "bigint(20) COMMENT '登录账号ID'", updatable = false, insertable = false)
    private Long accountId;

    @Enumerated(EnumType.STRING)
    @Comment("渠道编码")
    @Column(name = "channel_code")
    private ChannelDTO.Code channelCode;

    @Setter
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "consumer_id", columnDefinition = "bigint(20) COMMENT '消费者ID'")
    private Consumer consumer;

    @Column(name = "location", columnDefinition = "varchar(64) COMMENT '纬度'")
    private String location;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "region_id", columnDefinition = "bigint(20) COMMENT '所属地区'")
    private Region region;

    @Column(name = "region_name", columnDefinition = "varchar(127) COMMENT '地区名称'")
    private String regionName;

    @Column(name = "street_number", columnDefinition = "varchar(511) COMMENT '路牌号'")
    private String streetNumber;

    @Column(name = "township", columnDefinition = "varchar(127) COMMENT '城镇名称'")
    private String township;

    @Column(name = "towncode", columnDefinition = "varchar(127) COMMENT '城镇编码'")
    private String towncode;

    @Column(name = "busi_area", columnDefinition = "varchar(127) COMMENT '所属商圈'")
    private String busiArea;

    @Column(name = "address", columnDefinition = "varchar(511) COMMENT '地址'")
    private String address;

    @Column(name = "request", columnDefinition = "text COMMENT '位置查询请求'")
    protected String request;

    @Type(JsonUT.class)
    @Column(name = "response", columnDefinition = "json COMMENT '位置查询应答'")
    protected JSONObject response;

    @Setter
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "share_id", columnDefinition = "bigint(20) COMMENT '分享ID'")
    private ConsumerShare share;

    public ConsumerLogin(ConsumerAccount account, ChannelDTO.Code channelCode) {
        setAccount(account);
        this.channelCode = channelCode;
    }

    private void setAccount(ConsumerAccount account) {
        this.account = account;
        this.accountId = account.getId();
        this.consumer = account.getConsumer();
        this.account.setLastLogin(this);
    }

    public void setLocation(String location) {
        if (isBlank(location)) return;
        this.location = location;
        this.request = makeUrl("https://restapi.amap.com/v3/geocode/regeo", Map.of("location", location));
        this.response = callAMap(request);

        JSONObject regeocode = response.getJSONObject("regeocode");
        if (regeocode == null) return;
        Object formattedAddress = regeocode.get("formatted_address");
        if (!(formattedAddress instanceof JSONArray)) {
            this.address = regeocode.getString("formatted_address");
        }
        JSONObject addressComponent = regeocode.getJSONObject("addressComponent");

        if (addressComponent == null) return;
        if (!(addressComponent.get("township") instanceof JSONArray)) {
            this.township = addressComponent.getString("township");
        }
        if (!(addressComponent.get("towncode") instanceof JSONArray)) {
            this.towncode = addressComponent.getString("towncode");
        }

        String adCode = null;
        if (!(addressComponent.get("adcode") instanceof JSONArray)) {
            adCode = addressComponent.getString("adcode");
        }
        if (notBlank(adCode)) {
            this.region = getBean(RegionRpt.class).findByAmapAdcode(adCode);
            if (this.region != null) {
                this.regionName = this.region.getFullName();
            }
        }

        JSONObject streetNumberObj = addressComponent.getJSONObject("streetNumber");
        if (streetNumberObj != null) {
            String street = "";
            if (!(streetNumberObj.get("street") instanceof JSONArray)) {
                street = streetNumberObj.getString("street");
            }
            String number = "";
            if (!(streetNumberObj.get("number") instanceof JSONArray)) {
                number = streetNumberObj.getString("number");
            }
            this.streetNumber = street + number;
        }

        JSONArray businessAreas = addressComponent.getJSONArray("businessAreas");
        if (businessAreas != null && !businessAreas.isEmpty()) {
            // 高德返回的数据有两种格式，有数据是[{"name":"xxx"}]，没数据是[[]]
            Object o = businessAreas.get(0);
            if (o instanceof JSONObject businessArea) {
                this.busiArea = businessArea.getString("name");
            }
        }
    }

}
