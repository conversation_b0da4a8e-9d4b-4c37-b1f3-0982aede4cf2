package com.ejuetc.consumer.domain.delegation;

import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.consumer.api.delegation.EditDelegationPO;
import com.ejuetc.consumer.api.dto.DelegationDTO;
import com.ejuetc.consumer.domain.broker.Broker;
import com.ejuetc.consumer.domain.broker.BrokerRpt;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@Entity
@Getter
@DiscriminatorValue("COMPANY")
@SubtypeCode(parent = Delegation.class, code = "COMPANY", name = "公司房源")
@NoArgsConstructor
public class CompanyDelegation extends Delegation {

    public CompanyDelegation(Long userId, EditDelegationPO po) {
        super(userId, po);
    }

    @Override
    protected void setUserAndParent(Long userId, Delegation parent) {
        if (userId == null) throw new CodingException("公司房源必须指定公司");
        Broker broker = getBean(BrokerRpt.class).getReferenceById(userId);
        setBroker(broker);
    }

    @Override
    public boolean doDelete(String reason) {
        getChildren4CompanyDelegation().stream()
                .filter(d -> d.getLevel() == DelegationDTO.Level.BROKER)
                .forEach(d -> {
                    if (!d.doDelete(reason))
                        d.setCompanyDelegation(null);
                });
        logicDelete();
        return true;
    }

    @Override
    @Transient
    public Delegation getParent() {
        return null;
    }

    @Override
    public void upDown(Boolean channelUp, String reason, List<ChannelDTO.Code> channelCodes) {
    }

}
