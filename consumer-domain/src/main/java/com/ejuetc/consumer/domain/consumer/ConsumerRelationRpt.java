package com.ejuetc.consumer.domain.consumer;

import com.ejuetc.consumer.api.dto.LoginAccountDTO;
import jakarta.persistence.LockModeType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import jakarta.persistence.criteria.Predicate;

import java.util.List;
import java.util.Optional;

import static com.ejuetc.commons.base.utils.ThreadUtils.redisLock;

public interface ConsumerRelationRpt extends JpaRepositoryImplementation<ConsumerRelation, Long> {

    @Query("""
            from ConsumerRelation cr
            where cr.broker.id = :brokerId
            and (:accountType is null or cr.accountType = :accountType)
            and (:keyword is null or cr.remarkPhones like concat('%',:keyword,'%') or cr.name like concat('%',:keyword,'%') or cr.niceName like concat('%',:keyword,'%'))
            and (:expirationFlag is null or :expirationFlag = false or cr.remarkPhones is not null or exists (select 1 from cr.binds b where b.expiration > current_timestamp))
            and (:expirationFlag is null or :expirationFlag = true or (cr.remarkPhones is null and not exists (select 1 from cr.binds b where b.expiration > current_timestamp)))
            order by cr.activityTime desc
            """)
    Page<ConsumerRelation> findList(Long brokerId, LoginAccountDTO.Type accountType, String keyword, Boolean expirationFlag, Pageable pageable);

    @Query("""
            from ConsumerRelation cr
            where cr.brokerId = :brokerId
            and cr.accountId = :accountId
            """)
    Optional<ConsumerRelation> doFindAndLockExist(Long brokerId, Long accountId);

    default Optional<ConsumerRelation> findAndLockExist(Long brokerId, Long accountId) {
        redisLock(10, 20, "ConsumerRelationRpt.findAndLockExist", brokerId, accountId);
        return doFindAndLockExist(brokerId, accountId);
    }

    @Query("""
            from ConsumerRelation cr
            where cr.sourceId = :sourceId
            and cr.sourceType = :sourceType
            """)
    ConsumerRelation findBySource(String sourceId, String sourceType);

    Long countByBrokerId(Long userId);

    default List<ConsumerRelation> findByRemarkPhones(Long brokerId, List<String> remarkPhones) {
        if (remarkPhones == null || remarkPhones.isEmpty()) return List.of();

        return findAll((root, query, cb) -> {
            Predicate[] predicates = remarkPhones.stream()
                    .map(phone -> cb.like(root.get("remarkPhones"), "%" + phone + "%"))
                    .toArray(Predicate[]::new);
            return cb.and(cb.equal(root.get("brokerId"), brokerId), cb.or(predicates));
        });
    }
}
