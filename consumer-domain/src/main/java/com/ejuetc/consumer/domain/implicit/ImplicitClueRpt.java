package com.ejuetc.consumer.domain.implicit;

import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.List;
import java.util.Optional;

public interface ImplicitClueRpt extends JpaRepositoryImplementation<ImplicitClue, Long> {
    @Query("""
            from ImplicitClue c
            where c.consumerPhone = :#{#call.consumerPhone}
            and c.broker = :#{#call.broker}
            and c.businessCode=:#{#call.businessCode}
            and :#{#call.startTime} between c.startTime and c.endTime
            """)
    Optional<ImplicitClue> findExist(ImplicitCall call);

    @Query("""
            select c
            from ImplicitClue c
            where c.pushStatus = 'PENDING'
            or (c.pushStatus = 'FAIL' and c.createTime > current_timestamp - 90 second )
            """)
    @Lock(jakarta.persistence.LockModeType.PESSIMISTIC_WRITE)
    List<ImplicitClue> findNotPushClue();

    @Query("""
            select c
            from ImplicitClue c
            where c.id = :id
            """)
    @Lock(jakarta.persistence.LockModeType.PESSIMISTIC_WRITE)
    Optional<ImplicitClue> findByIdAndLock(Long id);
}
