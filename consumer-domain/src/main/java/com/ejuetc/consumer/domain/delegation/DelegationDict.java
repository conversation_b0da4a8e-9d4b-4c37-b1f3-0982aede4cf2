package com.ejuetc.consumer.domain.delegation;

import com.ejuetc.consumer.api.dto.DelegationDictDTO;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Comment;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Entity
@NoArgsConstructor
@Comment("枚举字典")
@Table(name = "tb_delegation_dict")
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.READ_ONLY)
public class DelegationDict implements Serializable {
    @EmbeddedId
    private UK uk;

    @Column(name = "sort", columnDefinition = "int default 0 COMMENT '排序'")
    private Integer sort;

    public DelegationDict(UK uk) {
        this.uk = uk;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof DelegationDict dict)) return false;
        return Objects.equals(getUk(), dict.getUk());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getUk());
    }

    public String getName() {
        return uk.getName();
    }

    public DelegationDictDTO.Category getCategory() {
        return uk.getCategory();
    }

    @Getter
    @NoArgsConstructor
    @Embeddable
    public static class UK implements Serializable {
        @Enumerated(EnumType.STRING)
        @Column(name = "category", columnDefinition = "varchar(63) COMMENT '类别'")
        private DelegationDictDTO.Category category;

        @Column(name = "name", columnDefinition = "varchar(63) COMMENT '名称'")
        private String name;

        public UK(DelegationDictDTO.Category category, String name) {
            this.category = category;
            this.name = name;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof UK uk)) return false;
            return getCategory() == uk.getCategory() && Objects.equals(getName(), uk.getName());
        }

        @Override
        public int hashCode() {
            return Objects.hash(getCategory(), getName());
        }
    }
}
