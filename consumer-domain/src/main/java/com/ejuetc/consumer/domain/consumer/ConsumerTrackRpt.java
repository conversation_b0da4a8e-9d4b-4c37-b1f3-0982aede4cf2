package com.ejuetc.consumer.domain.consumer;

import com.ejuetc.consumer.api.dto.LoginAccountDTO;
import com.ejuetc.consumer.web.vo.TrackCountVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface ConsumerTrackRpt extends JpaRepositoryImplementation<ConsumerTrack, Long> {

    @Query("""
            select v.createTime
            from ConsumerTrack v
            where v.broker.id = :brokerId
            and v.consumer.id = :consumerId
            order by v.createTime desc
            """)
    LocalDateTime findLastViewTime(Long brokerId, Long consumerId);

    @Query("""
               select v.delegation.id, count(v.delegation.id) as viewCount
               from ConsumerTrack v
               where v.broker.id = :brokerId
                 and v.consumer.id = :consumerId
                 and v.createTime >= :startDate
               group by v.delegation.id
               order by viewCount desc
               limit :limit
            """)
    List<Object[]> findMostViewedDelegations(Long brokerId, Long consumerId, LocalDateTime startDate, int limit);

    @Query("""
                select v
                from ConsumerTrack v
                join fetch v.delegation
                join fetch v.delegation.detail.community
                where v.broker.id = :brokerId
                 and v.consumer.id = :consumerId
                 and v.createTime >= :startDate
                 order by v.createTime desc
            """)
    Page<ConsumerTrack> findDelegationViews(Long brokerId, Long consumerId, LocalDateTime startDate, Pageable pageable);

    @Query("""
            select v.delegation.id, count(v.delegation.id) as viewCount
            from ConsumerTrack v
            where v.delegation.id in (:delegationIds)
              and v.consumer.id = :consumerId
              and v.createTime >= :startDate
            group by v.delegation.id
            """)
    List<Object[]> countDelegationView(List<Long> delegationIds, Long consumerId, LocalDateTime startDate);

    @Query(value = """
            select *
            from (select ct.delegation_id, count(1) as count, max(ct.create_time) as last_time
                  from tb_consumer_track ct
                  where ct.relation_id = :relationId
                    and ct.create_time > current_timestamp - interval 3 month
                  group by ct.delegation_id) t1
            order by t1.count desc
            limit 3
            """, nativeQuery = true)
    List<Map<String, Object>> doCountByRelationId(Long relationId);

    default List<TrackCountVO> countByRelationId(Long relationId) {
        return doCountByRelationId(relationId).stream().map(TrackCountVO::new).toList();
    }

    @Query(value = """
            select *
            from (select date_format(ct.create_time, '%Y-%m-%d') dt,
                         ct.delegation_id,
                         count(1)                                count,
                         max(create_time)                        last_time
                  from tb_consumer_track ct
                  where ct.relation_id = :relationId
                  group by date_format(ct.create_time, '%Y-%m-%d'), ct.delegation_id) t1
            order by t1.dt desc,last_time desc
            """, nativeQuery = true)
    Page<Map<String, Object>> doDayGroupAndCountByRelationId(Long relationId, Pageable pageable);

    default Page<TrackCountVO> dayGroupAndCountByRelationId(Long relationId, Pageable pageable) {
        return doDayGroupAndCountByRelationId(relationId, pageable).map(TrackCountVO::new);
    }

    List<ConsumerTrack> findByBrokerIdAndAccountId(Long brokerId, Long accountId);
}
