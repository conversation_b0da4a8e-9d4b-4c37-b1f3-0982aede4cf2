package com.ejuetc.consumer.domain.consumer;

import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.usertype.ListUT;
import com.ejuetc.consumer.domain.broker.BrokerRpt;
import com.ejuetc.consumer.domain.delegation.Delegation;
import com.ejuetc.consumer.domain.delegation.DelegationRpt;
import com.ejuetc.consumer.domain.implicit.ImplicitBase;
import com.ejuetc.consumer.domain.implicit.bind.ImplicitBind;
import com.ejuetc.consumer.domain.implicit.ImplicitCall;
import com.ejuetc.consumer.domain.implicit.ImplicitClue;
import com.ejuetc.consumer.web.vo.DelegationVO;
import com.ejuetc.consumer.web.vo.RelationPO;
import com.ejuetc.consumer.web.vo.TrackCountVO;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.StringUtils.*;
import static java.time.LocalDateTime.now;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static lombok.AccessLevel.PROTECTED;

@Slf4j
@Getter
//@Setter(PROTECTED)
@Entity
@NoArgsConstructor
@Comment("客户关系")
@Table(name = "tb_consumer_relation", uniqueConstraints = {
        @UniqueConstraint(name = "uk_consumer_relation_brokerId_accountId", columnNames = {"broker_id", "account_id"}),
        @UniqueConstraint(name = "uk_consumer_relation_sourceId_sourceType", columnNames = {"source_id", "source_type"}),
})
@Where(clause = "logic_delete = 0")
public class ConsumerRelation extends ImplicitBase<ConsumerRelation> {

    @Id
    @GeneratedValue(generator = "consumer_relation_id")
    @SequenceGenerator(name = "consumer_relation_id", sequenceName = "seq_consumer_relation_id")
    private Long id;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "relation")
    private List<ConsumerTrack> tracks = new ArrayList<>();

    @OrderBy("expiration DESC")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "relation")
    private List<ImplicitBind> binds = new ArrayList<>();

    @OrderBy("callTime DESC")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "relation")
    private List<ImplicitCall> calls = new ArrayList<>();

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "relation")
    private List<ImplicitClue> clues = new ArrayList<>();

    @Column(name = "last_call_time", columnDefinition = "datetime COMMENT '最近呼叫时间'")
    private LocalDateTime lastCallTime;

    @Column(name = "name", columnDefinition = "varchar(64) COMMENT '客户姓名'")
    private String name;

    @Column(name = "nice_name", columnDefinition = "varchar(64) COMMENT '客户昵称'")
    private String niceName;

    @Column(name = "icon_url", columnDefinition = "varchar(255) COMMENT '头像URL'")
    private String iconUrl;

    @Column(name = "source_type", columnDefinition = "varchar(127) COMMENT '数据来源'")
    private String sourceType;

    @Column(name = "source_id", columnDefinition = "varchar(127) COMMENT '外部ID'")
    private String sourceId;

    @Column(name = "remark", columnDefinition = "text COMMENT '备注'")
    private String remark;

    @Column(name = "mister", columnDefinition = "tinyint(1) COMMENT '客户性别(1:男性,0:女性)'")
    private Boolean mister;

    @Setter(PROTECTED)
    @Type(value = ListUT.class)
    @Column(name = "remark_phones", columnDefinition = "varchar(64) COMMENT '备注消费者手机号'")
    private ArrayList remarkPhones = new ArrayList<>();

    @Column(name = "activity_time", columnDefinition = "datetime COMMENT '最近活动时间'")
    private LocalDateTime activityTime;

    @Column(name = "activity_remark", columnDefinition = "text COMMENT '最近活动备注'")
    private String activityRemark;

    public ConsumerRelation(ImplicitBind bind) {
        super(bind.getBroker(), bind.getAccount());
        addBind(bind);
    }

    public ConsumerRelation(ConsumerTrack track) {
        super(track.getBroker(), track.getAccount());
    }

    public ConsumerRelation(Long brokerId, RelationPO ro) {
        super(getBean(BrokerRpt.class).getReferenceById(brokerId));
        edit(ro);
    }

    public void edit(Long brokerId, RelationPO ro) {
        if (!getBrokerId().equals(brokerId)) throw new BusinessException("bc.ejuetc.consumer.1012");
        edit(ro);
    }

    private void edit(RelationPO po) {
        this.setName(po.getName());
        this.setMister(po.getMister());
        this.setRemark(po.getRemark());
        this.setIconUrl(po.getIconUrl());
        this.setSourceType(po.getSourceType());
        this.setSourceId(po.getSourceId());
        this.setRemarkPhones(new ArrayList<>(po.getRemarkPhones()));
        this.setActivityTime(po.getActivityTime());
        this.setActivityRemark(po.getActivityRemark());
        this.setAccountType(po.getAccountType());
    }

    private void setActivityRemark(String activityRemark) {
        if (notBlank(activityRemark)) {
            this.activityRemark = activityRemark;
        }
    }

    private void setActivityTime(LocalDateTime activityTime) {
        if (activityTime != null) {
            this.activityTime = activityTime;
        }
    }

    private void setSourceId(String sourceId) {
        if (notBlank(sourceId)) {
            this.sourceId = sourceId;
        }
    }

    private void setSourceType(String sourceType) {
        if (notBlank(sourceType)) {
            this.sourceType = sourceType;
        }
    }

    private void setIconUrl(String iconUrl) {
        if (notBlank(iconUrl)) {
            this.iconUrl = iconUrl;
        }
    }

    private void setRemark(String remark) {
        if (notBlank(remark)) {
            this.remark = remark;
        }
    }

    private void setMister(Boolean mister) {
        if (mister != null) {
            this.mister = mister;
        }
    }

    private void setName(String name) {
        if (notBlank(name)) {
            this.name = name;
        }
    }

    public void addCall(ImplicitCall call) {
        if (!calls.contains(call)) {
            calls.add(call);
            if (lastCallTime == null || call.getCallTime().isAfter(lastCallTime)) {
                lastCallTime = call.getCallTime();
            }
        }
    }

    public List<ImplicitBind> getSuccBinds() {
        return binds.stream().filter(ImplicitBind::isBindSucc).collect(toList());
    }

    public void addBind(ImplicitBind bind) {
        if (bind == null || binds.contains(bind)) return;

        binds.add(bind);
        bind.setRelation(this);
        bind.getTracks().forEach(this::addTrack);
    }

    public void addTrack(ConsumerTrack track) {
        if (track == null || tracks.contains(track)) return;

        tracks.add(track);
        track.setRelation(this);
        if (activityTime == null || track.getCreateTime().isAfter(activityTime)) {
            activityTime = track.getCreateTime();
            activityRemark = track.getActivityRemark();
        }
    }

    public void addClue(ImplicitClue clue) {
        if (!clues.contains(clue)) {
            clues.add(clue);
        }
    }

    public String getNiceName() {
        return notBlank(niceName) ? niceName : super.getNiceName();
    }

    public String getIconUrl() {
        return notBlank(iconUrl) ? iconUrl : super.getIconUrl();
    }

    public Long getActivityTimeDays() {
        return activityTime == null ? null : Duration.between(activityTime, now()).toDays();
    }

    public List<TrackCountVO> getTrackCountVOS() {
        DelegationRpt delegationRpt = getBean(DelegationRpt.class);
        List<TrackCountVO> voPage = getBean(ConsumerTrackRpt.class).countByRelationId(getId());
        Map<Long, DelegationVO> id2DelegationMap = delegationRpt.findListByIds(voPage.stream().map(TrackCountVO::getDelegationId).toList()).stream().collect(toMap(Delegation::getId, d -> convert2DTO(d, new DelegationVO())));
        return voPage.stream().map(vo -> vo.setDelegation(id2DelegationMap.get(vo.getDelegationId()))).toList();
    }
}
