package com.ejuetc.consumer.domain.delegation;

import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.commons.base.clazz.ClassUtils;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.usertype.ListUT;
import com.ejuetc.consumer.api.delegation.EditDelegationPO;
import com.ejuetc.consumer.api.dto.DelegationDTO;
import com.ejuetc.consumer.api.dto.DictCategory;
import com.ejuetc.consumer.domain.broker.Broker;
import com.ejuetc.consumer.domain.community.Community;
import com.ejuetc.consumer.domain.community.CommunityBind;
import com.ejuetc.consumer.domain.community.CommunityService;
import com.ejuetc.consumer.domain.community.CommunityServiceImpl;
import com.ejuetc.consumer.domain.region.Region;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.ejuetc.channel.dto.BusinessOpenDTO.Code.NEW;
import static com.ejuetc.channel.dto.BusinessOpenDTO.Code.RENT;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.StringUtils.notBlank;
import static com.ejuetc.consumer.api.dto.DelegationDTO.SubType.*;
import static com.ejuetc.consumer.api.dto.DelegationDictDTO.Category.*;
import static java.util.Objects.requireNonNullElse;
import static lombok.AccessLevel.PROTECTED;

@Getter
@Setter(PROTECTED)
@Accessors(chain = true)
@Embeddable
@NoArgsConstructor
@Slf4j
public class DelegationDetail implements Serializable, Cloneable {
    /*************************************** 交易相关 ******************************************/

    @Comment("交易类型")
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private BusinessOpenDTO.Code type;

    @Enumerated(EnumType.STRING)
    @Comment("交易子类型")
    @Column(name = "sub_type")
    private DelegationDTO.SubType subType;

    @DictCategory(ROOM_TYPE)
    @Column(name = "room_type", columnDefinition = "varchar(63) comment '房间类型'")
    private String roomType;

    @Column(name = "deadline", columnDefinition = "datetime(6) null comment '委托到期时间'")
    private LocalDateTime deadline;

    @Column(name = "boutique", columnDefinition = "bit(1) null default b'0' comment '是否精品房源'")
    private Boolean boutique = false;

    @Column(name = "list_date", columnDefinition = "date comment '上架时间'")
    private LocalDate listDate;

    @DictCategory(PROPERTY_YEARS)
    @Column(name = "property_years", columnDefinition = "varchar(63) comment '产权年限'")
    private String propertyYears;

    @Column(name = "house_cert_verify", columnDefinition = "tinyint(1) null comment '是否房产证验证'")
    private Boolean houseCertVerify;

    @DictCategory(HOUSE_STRUCTURE)
    @Column(name = "house_structure", columnDefinition = "varchar(63) COMMENT '房屋结构'")
    private String houseStructure;

    @DictCategory(PROPERTY_OWNERSHIP)
    @Column(name = "property_ownership", columnDefinition = "varchar(63) COMMENT '产权所属'")
    private String propertyOwnership;

    @DictCategory(BUILDING_CATEGORY)
    @Column(name = "building_category", columnDefinition = "varchar(63) COMMENT '建筑类别'")
    private String buildingCategory;

    @DictCategory(BUILDING_TYPE)
    @Column(name = "building_type", columnDefinition = "varchar(63) COMMENT '建筑类型'")
    private String buildingType;

    @DictCategory(HOUSE_YEARS)
    @Column(name = "house_years", columnDefinition = "varchar(63) COMMENT '房屋年限'")
    private String houseYears;

    @Column(name = "efficiency_rate", columnDefinition = "decimal(20,2) COMMENT '得房率'")
    private BigDecimal efficiencyRate;

    /*************************************** 小区相关 ******************************************/

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "community_bind_id", columnDefinition = "bigint(20) COMMENT '小区绑定ID'")
    private CommunityBind communityBind;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "community_id", columnDefinition = "bigint(20) COMMENT '小区ID'")
    private Community community;

    @Column(name = "community_id", columnDefinition = "bigint(20) COMMENT '小区ID'", insertable = false, updatable = false)
    private Long communityId;

    @Column(name = "community_name", columnDefinition = "varchar(255) COMMENT '小区名'")
    private String communityName;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "city_id", columnDefinition = "bigint(20) COMMENT '城市ID'")
    private Region city;

    @Column(name = "city_id", columnDefinition = "bigint(20) COMMENT '城市ID'", insertable = false, updatable = false)
    private Long cityId;

    @Column(name = "city_name", columnDefinition = "varchar(255) COMMENT '城市名'")
    private String cityName;

    @Column(name = "community_address", columnDefinition = "varchar(255) COMMENT '简化小区地址'")
    protected String communityAddress;

    @Column(name = "busi_name", columnDefinition = "varchar(127) COMMENT '商圈名称'")
    private String busiName;

    @Column(name = "town_name", columnDefinition = "varchar(127) COMMENT '城镇/街道名称'")
    private String townName;

    @DictCategory(COMMUNITY_AROUND)
    @Type(ListUT.class)
    @Column(name = "around", columnDefinition = "varchar(255) COMMENT '周边配套'")
    private List<String> around = new ArrayList<>();

    @DictCategory(BROKER_SERVICE)
    @Type(ListUT.class)
    @Column(name = "broker_services", columnDefinition = "varchar(255) COMMENT '中介服务'")
    private List<String> brokerService = new ArrayList<>();

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "district_id", columnDefinition = "bigint(20) COMMENT '行政区ID'")
    private Region district;

    @Column(name = "district_id", columnDefinition = "bigint(20) COMMENT '行政区ID'", insertable = false, updatable = false)
    private Long districtId;

    @Column(name = "district_name", columnDefinition = "varchar(255) COMMENT '行政区名称'")
    private String districtName;

    @DictCategory(PROPERTY_TYPE)
    @Column(name = "property_type", columnDefinition = "varchar(63) comment '物业类型'")
    private String propertyType;

    @Column(name = "parking_ratio", columnDefinition = "varchar(32) comment '车位配比'")
    private String parkingRatio;

    @Column(name = "parking", columnDefinition = "bit(1) default b'0' comment '是否有车位'")
    private Boolean parking;

    @DictCategory(LOOK_TYPE)
    @Column(name = "look_type", columnDefinition = "varchar(63) comment '看房时间类型'")
    private String lookType;

    @DictCategory(SALE_REASON)
    @Column(name = "sale_reason", columnDefinition = "varchar(63) comment '出售原因'")
    private String saleReason;

    @DictCategory(HOUSE_SITUATION)
    @Column(name = "house_situation", columnDefinition = "varchar(63) comment '房屋现状'")
    private String houseSituation;

    @Column(name = "property_management_company", columnDefinition = "varchar(63) comment '物业管理公司'")
    private String propertyManagementCompany;

    @Column(name = "property_management_fee", columnDefinition = "decimal(20,2) comment '物业管理费用'")
    private BigDecimal propertyManagementFee;

    /*************************************** 楼栋单元楼层 **************************************************/

    @Column(name = "build_name", columnDefinition = "varchar(255) COMMENT '楼栋名'")
    private String buildName;

    @Column(name = "unit_name", columnDefinition = "varchar(255) COMMENT '单元名'")
    private String unitName;

    @Column(name = "room_num", columnDefinition = "varchar(255) COMMENT '房号'")
    private String roomNum;

    @Column(name = "current_floor", columnDefinition = "int(10) null comment '当前楼层'")
    private Integer currentFloor;

    @DictCategory(FLOOR_CATEGORY)
    @Column(name = "floor_category", columnDefinition = "varchar(63) comment '楼层类别'")
    private String floorCategory;

    @Column(name = "total_floor", columnDefinition = "int(10) null comment '总楼层'")
    private Integer totalFloor;

    @Column(name = "tag_elevator", columnDefinition = "tinyint(1) null comment '是否有电梯'")
    private Boolean tagElevator;

    @Column(name = "elevator_count", columnDefinition = "int(10) comment '电梯数量'")
    private Integer elevatorCount;

    @Column(name = "room_per_floor", columnDefinition = "int(10) comment '每层户数'")
    private Integer roomPerFloor;

    @DictCategory(COMPLETION_TIME)
    @Column(name = "completion_time", columnDefinition = "varchar(63) comment '建成年代'")
    private String completionTime;

    /************************************** 租赁特有 **************************************************/

    @Column(name = "room_name", columnDefinition = "varchar(255) COMMENT '房间名(用于合租指定房间名)'")
    private String roomName;

    @Column(name = "pay_months", columnDefinition = "int(10) null comment '付款月数'")
    private Integer payMonths;

    @Column(name = "deposit_months", columnDefinition = "int(10) null comment '押金月数'")
    private Integer depositMonths;

    @Column(name = "checkin_date", columnDefinition = "date null comment '入住日期(空表示随时入住)'")
    private LocalDate checkinDate;

    @DictCategory(ROOM_EQUIPMENTS)
    @Type(ListUT.class)
    @Column(name = "equipments", columnDefinition = "varchar(255) comment '配套设施'")
    private List<String> equipments;

    /*************************************** 户型 **************************************************/

    @DictCategory(ORIENT)
    @Column(name = "orient", columnDefinition = "varchar(63) comment '朝向'")
    private String orient;

    @Column(name = "room_count", columnDefinition = "int(10) default 0 null comment '房间数'")
    private Integer roomCount;

    @Column(name = "hall_count", columnDefinition = "int(10) default 0 null comment '客厅数'")
    private Integer hallCount;

    @Column(name = "toilet_count", columnDefinition = "int(10) default 0 null comment '卫生间数'")
    private Integer toiletCount;

    @Column(name = "building_area", columnDefinition = "decimal(10, 2) null comment '建筑面积'")
    private BigDecimal buildingArea;

    @Column(name = "use_area", columnDefinition = "decimal(10, 2) null comment '使用面积'")
    private BigDecimal useArea;

    @DictCategory(REDO)
    @Column(name = "redo", columnDefinition = "varchar(63) comment '装修情况'")
    private String redo;

    /*************************************** 产权信息 **************************************************/

    @DictCategory(HOUSE_TYPE)
    @Column(name = "house_type", columnDefinition = "varchar(63) null comment '产权类型'")
    protected String houseType;

    @DictCategory(HOUSE_CERT_TYPE)
    @Column(name = "house_cert_type", columnDefinition = "varchar(63) null comment '产证类型'")
    private String houseCertType;

    @Column(name = "house_cert_no", columnDefinition = "varchar(255) null comment '产证号'")
    private String houseCertNO;

    @Column(name = "house_cert_address", columnDefinition = "varchar(255) null comment '产证地址'")
    private String houseCertAddress;

    @Column(name = "sole_house", columnDefinition = "bit(1) default b'0' comment '是否唯一住房'")
    private Boolean soleHouse;

    @Column(name = "house_busi_no", columnDefinition = "varchar(255) null comment '房屋业务件号(政府核验相关)'")
    private String houseBusiNO;

    @DictCategory(HOUSE_PLAN_PURPOSE)
    @Column(name = "house_plan_purpose", columnDefinition = "varchar(255) null comment '房屋规划用途'")
    private String housePlanPurpose;

    @Column(name = "owner_name", columnDefinition = "varchar(255) null comment '业主姓名'")
    private String ownerName;

    @Column(name = "owner_cert_no", columnDefinition = "varchar(63) null comment '业主证件号'")
    private String ownerCertNO;

    /**************************************** 委托信息 ***************************************/

    @Column(name = "bailor_name", columnDefinition = "varchar(255) null comment '委托人姓名'")
    private String bailorName;

    @DictCategory(BAILOR_CERT_TYPE)
    @Column(name = "bailor_cert_type", columnDefinition = "varchar(63) null comment '委托人证件类型'")
    private String bailorCertType;

    @Column(name = "bailor_cert_no", columnDefinition = "varchar(255) null comment '委托人证件号码'")
    private String bailorCertNO;

    @Column(name = "bailor_names", columnDefinition = "varchar(255) null comment '委托人姓名集合'")
    private String bailorNames;

    /*************************************** 政府核验信息 ***************************************/
    @Column(name = "gov_verify_code", columnDefinition = "varchar(64) null comment '政府核验码'")
    private String govVerifyCode;


    @Column(name = "gov_verify_url", columnDefinition = "varchar(1024) null comment '政府核验码图片地址'")
    private String govVerifyUrl;


    @Column(name = "gov_contract_code", columnDefinition = "varchar(64) null comment '政府委托协议编码'")
    private String govContractCode;


    @Column(name = "gov_promo_code", columnDefinition = "varchar(64) null comment '政府房源推广码'")
    private String govPromoCode;

    /**************************************** 地铁AI新增字段 ***************************************/

    @Column(name = "metro", columnDefinition = "varchar(255) null comment '地铁'")
    private String metro;

    @Column(name = "school", columnDefinition = "varchar(255) null comment '学区'")
    private String school;

    @Transient
    @Getter(lazy = true)
    @Accessors(fluent = true)
    private final CommunityService communityService = getBean(CommunityService.class);

    @Transient
    @Getter(lazy = true)
    @Accessors(fluent = true)
    private final DelegationDictRpt dictRpt = getBean(DelegationDictRpt.class);

    public void setCommunityBind(String communityAddress, String communityName) {
        this.communityBind = communityService().queryOrBind(communityAddress, communityName);
        if (communityBind.getCommunity() == null)
            throw new BusinessException("bc.ejuetc.consumer.1007", communityAddress, communityName);
        this.community = communityBind.getCommunity();
        this.communityId = communityBind.getId();
        this.communityName = communityBind.getName();
        this.communityAddress = community.getAddress();
        this.city = community.getCity();
        this.cityId = city.getId();
        this.cityName = community.getCity().getName();
        this.townName = community.getTownName();
        this.busiName = community.getBusiName();
        this.district = community.getDistrict();
        this.districtName = district != null ? district.getName() : null;

    }

    public DelegationDetail(Broker broker, EditDelegationPO po) {
        this.setCommunityBind(po.getCommunityAddress(), po.getCommunityName());
        if (broker != null && broker.getCity() != null && !getCity().equals(broker.getCity())) throw new CodingException("所属业务城市[%s:%s]与房源所在城市[%s:%s]不一致(经纪人:[%s])", broker.getCityId(), broker.getCityName(), getCity().getId(), getCity().getName(), broker.getId());

        this.setType(po.getType());
        this.setSubType(po.getSubType());
        this.setAround(po.getAround());
        this.setRoomNum(po.getRoomNum());
        this.setPayMonths(po.getPayMonths());
        this.setDepositMonths(po.getDepositMonths());
        this.setCheckinDate(po.getCheckinDate());
        this.setEquipments(po.getEquipments());
        this.setRedo(po.getRedo());
        this.setCurrentFloor(po.getCurrentFloor());
        this.setTotalFloor(po.getTotalFloor());
        this.setTagElevator(po.getTagElevator());
        this.setCompletionTime(po.getCompletionTime());
        this.setPropertyType(po.getPropertyType());
        this.setElevatorCount(po.getElevatorCount());
        this.setRoomPerFloor(po.getRoomPerFloor());
        this.setParkingRatio(po.getParkingRatio());
        this.setPropertyManagementCompany(po.getPropertyManagementCompany());
        this.setPropertyManagementFee(po.getPropertyManagementFee());
        this.setRoomType(po.getRoomType());
        this.setDeadline(po.getDeadline());
        this.setBuildName(po.getBuildName());
        this.setUnitName(po.getUnitName());
        this.setRoomName(po.getRoomName());
        this.setOrient(po.getOrient());
        this.setRoomCount(po.getRoomCount());
        this.setHallCount(po.getHallCount());
        this.setToiletCount(po.getToiletCount());
        this.setBuildingArea(po.getBuildingArea());
        this.setUseArea(po.getUseArea());
        this.setEfficiencyRate(po.getBuildingArea(), po.getUseArea());
        this.setGovVerifyCode(po.getGovVerifyCode());
        this.setGovVerifyUrl(po.getGovVerifyUrl());
        this.setGovContractCode(po.getGovContractCode());
        this.setGovPromoCode(po.getGovPromoCode());
        this.setMetro(po.getMetro());
        this.setSchool(po.getSchool());
        this.setHousePlanPurpose(po.getHousePlanPurpose());
        this.setHouseBusiNO(po.getHouseBusiNO());
        this.setHouseCertType(po.getHouseCertType());
        this.setHouseCertNO(po.getHouseCertNO());
        this.setOwnerName(po.getOwnerName());
        this.setOwnerCertNO(po.getOwnerCertNO());
        this.setBailorNames(po.getBailorNames());
        this.setBailorCertNO(po.getBailorCertNO());
        this.setBailorCertType(po.getBailorCertType());
        this.setBailorName(po.getBailorName());
        this.setCompletionTime(po.getCompletionTime());
        this.setListDate(po.getListDate());
        this.setPropertyYears(po.getPropertyYears());
        this.setHouseCertVerify(po.getHouseCertVerify());
        this.setHouseStructure(po.getHouseStructure());
        this.setPropertyOwnership(po.getPropertyOwnership());
        this.setBuildingCategory(po.getBuildingCategory());
        this.setHouseYears(po.getHouseYears());
        this.setBuildingType(po.getBuildingType());
        this.setParking(po.getParking());
        this.setLookType(po.getLookType());
        this.setSaleReason(po.getSaleReason());
        this.setHouseSituation(po.getHouseSituation());
        this.setHouseCertAddress(po.getHouseCertAddress());
        this.setSoleHouse(po.getSoleHouse());
        this.setBrokerService(po.getBrokerService());
        this.setFloorCategory(calcFloorCategory());
        this.setHouseType(po.getHouseType());
        this.setBoutique(po.getBoutique());
    }


    protected void setAround(List<String> around) {
        this.around = around != null ? around : community.getAround().getTypes();
    }

    protected void setPayMonths(Integer payMonths) {
        this.payMonths = payMonths != null ? payMonths : getType() == BusinessOpenDTO.Code.RENT ? 3 : null;
    }

    protected void setDepositMonths(Integer depositMonths) {
        this.depositMonths = depositMonths != null ? depositMonths : getType() == BusinessOpenDTO.Code.RENT ? 1 : null;
    }

    protected void setCheckinDate(LocalDate checkinDate) {
        this.checkinDate = checkinDate != null ? checkinDate : getType() == BusinessOpenDTO.Code.RENT ? LocalDate.now() : null;
    }

    protected void setEquipments(List<String> equipments) {
        this.equipments = equipments != null ? equipments : getType() == RENT ? List.of("热水器", "燃气灶", "空调", "冰箱", "衣柜") : null;
    }

    protected void setPropertyType(String propertyType) {
        this.propertyType = propertyType != null ? propertyType : "住宅";
    }

    protected void setCurrentFloor(Integer currentFloor) {
        this.currentFloor = currentFloor != null ? currentFloor : getType() == NEW ? 1 : null;
    }

    protected void setTagElevator(Boolean tagElevator) {
        this.tagElevator = tagElevator != null ? tagElevator : false;
    }

    protected void setCompletionTime(String completionTime) {
        this.completionTime = notBlank(completionTime) ? completionTime : "2010";
    }

    protected void setOrient(String orient) {
        this.orient = orient != null ? orient : "朝南";
    }

    protected void setRoomCount(Integer roomCount) {
        this.roomCount = roomCount != null ? roomCount : 3;
    }

    protected void setHallCount(Integer hallCount) {
        this.hallCount = hallCount != null ? hallCount : 1;
    }

    protected void setToiletCount(Integer toiletCount) {
        this.toiletCount = toiletCount != null ? toiletCount : 1;
    }

    protected void setRedo(String redo) {
        this.redo = redo != null ? redo : "精装";
    }

    protected void setHousePlanPurpose(String housePlanPurpose) {
        this.housePlanPurpose = housePlanPurpose != null ? housePlanPurpose : "住宅";
    }

    protected void setListDate(LocalDate listDate) {
        this.listDate = listDate != null ? listDate : getType() == NEW ? LocalDate.of(2025, 1, 1) : LocalDate.now();
    }

    protected void setPropertyYears(String propertyYears) {
        this.propertyYears = propertyYears != null ? propertyYears : "70";
    }

    protected void setHouseCertVerify(Boolean houseCertVerify) {
        this.houseCertVerify = houseCertVerify != null ? houseCertVerify : switch (getType()) {
            case NEW -> false;
            case SALE -> true;
            case RENT -> null;
        };
    }

    protected void setLookType(String lookType) {
        this.lookType = lookType != null ? lookType : "随时可看";
    }

    private void setEfficiencyRate(BigDecimal buildingArea, BigDecimal useArea) {
        if (buildingArea == null || useArea == null) {
            return;
        }
        if (buildingArea.compareTo(BigDecimal.ZERO) == 0) {
            this.efficiencyRate = BigDecimal.ZERO;
        } else {
            this.efficiencyRate = useArea.divide(buildingArea, 4, RoundingMode.HALF_UP);
        }
    }

    protected void setPropertyManagementCompany(String propertyManagementCompany) {
        this.propertyManagementCompany = propertyManagementCompany != null ? propertyManagementCompany : getCommunity().getPropertyName();
    }

    protected void setParkingRatio(String parkingRatio) {
        this.parkingRatio = parkingRatio != null ? parkingRatio : community.getParkingRatio();
    }

    public String calcFloorCategory() {
        if (this.currentFloor == null || this.totalFloor == null) {
            return null;
        }
        if (this.totalFloor <= 3) {
            return "低楼层";
        }

        double baseLevel = Math.ceil(totalFloor / 3.0);
        double highStart = baseLevel * 2;

        if (this.currentFloor <= baseLevel) {
            return "低楼层";
        } else if (this.currentFloor >= highStart) {
            return "高楼层";
        }
        return "中楼层";
    }


    public BigDecimal getElevatorRatio() {
        if (elevatorCount == null || roomPerFloor == null) {
            return null;
        }
        return new BigDecimal(elevatorCount).divide(new BigDecimal(roomPerFloor), 2, RoundingMode.HALF_UP);
    }

    @Override
    @SneakyThrows
    public DelegationDetail clone() {
        return (DelegationDetail) super.clone();
    }

    public void setHouseType(String houseType) {
        this.houseType = requireNonNullElse(houseType, "商品房");
    }

    public void setType(BusinessOpenDTO.Code type) {
        this.type = type;
        if (subType == null) {
            subType = switch (type) {
                case NEW -> NEW_NORMAL;
                case SALE -> SALE_FULL;
                case RENT -> RENT_FULL;
            };
        }
    }

    public void setSubType(DelegationDTO.SubType subType) {
        if (subType != null) {
            this.subType = subType;
        }
    }

    public void setDeadline(LocalDateTime deadline) {
        this.deadline = deadline == null ? LocalDateTime.now().plusMonths(3) : deadline;
    }

    public void setBuildName(String buildName) {
        this.buildName = notBlank(buildName) ? buildName : "1栋";
    }

    public void setUnitName(String unitName) {
        this.unitName = notBlank(unitName) ? unitName : "1单元";
    }

    public void setRoomNum(String roomNum) {
        this.roomNum = notBlank(roomNum) ? roomNum : "101";
    }

    @Override
    public int hashCode() {
        return Objects.hash(getType(), getSubType(), getRoomType(), getDeadline(), getBoutique(), getListDate(), getPropertyYears(), getHouseCertVerify(), getHouseStructure(), getPropertyOwnership(), getBuildingCategory(), getBuildingType(), getHouseYears(), getEfficiencyRate(), getCommunityBind(), getCommunity(), getCommunityId(), getCommunityName(), getCity(), getCityId(), getCityName(), getCommunityAddress(), getBusiName(), getTownName(), getAround(), getBrokerService(), getDistrict(), getDistrictId(), getDistrictName(), getPropertyType(), getParkingRatio(), getParking(), getLookType(), getSaleReason(), getHouseSituation(), getPropertyManagementCompany(), getPropertyManagementFee(), getBuildName(), getUnitName(), getRoomNum(), getCurrentFloor(), getFloorCategory(), getTotalFloor(), getTagElevator(), getElevatorCount(), getRoomPerFloor(), getCompletionTime(), getRoomName(), getPayMonths(), getDepositMonths(), getCheckinDate(), getEquipments(), getOrient(), getRoomCount(), getHallCount(), getToiletCount(), getBuildingArea(), getUseArea(), getRedo(), getHouseType(), getHouseCertType(), getHouseCertNO(), getHouseCertAddress(), getSoleHouse(), getHouseBusiNO(), getHousePlanPurpose(), getOwnerName(), getOwnerCertNO(), getBailorName(), getBailorCertType(), getBailorCertNO(), getBailorNames(), getGovVerifyCode(), getGovVerifyUrl(), getGovContractCode(), getGovPromoCode());
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof DelegationDetail other)) return false;

        return ClassUtils.equals(getType(), other.getType())
               && ClassUtils.equals(getSubType(), other.getSubType())
               && ClassUtils.equals(getRoomType(), other.getRoomType())
               && ClassUtils.equals(getDeadline(), other.getDeadline())
               && ClassUtils.equals(getListDate(), other.getListDate())
               && ClassUtils.equals(getPropertyYears(), other.getPropertyYears())
               && ClassUtils.equals(getHouseCertVerify(), other.getHouseCertVerify())
               && ClassUtils.equals(getHouseStructure(), other.getHouseStructure())
               && ClassUtils.equals(getPropertyOwnership(), other.getPropertyOwnership())
               && ClassUtils.equals(getBuildingCategory(), other.getBuildingCategory())
               && ClassUtils.equals(getBuildingType(), other.getBuildingType())
               && ClassUtils.equals(getHouseYears(), other.getHouseYears())
               && ClassUtils.equals(getEfficiencyRate(), other.getEfficiencyRate())
               && ClassUtils.equals(getCommunityBind(), other.getCommunityBind())
               && ClassUtils.equals(getCommunityName(), other.getCommunityName())
               && ClassUtils.equals(getCity(), other.getCity())
               && ClassUtils.equals(getCityName(), other.getCityName())
               && ClassUtils.equals(getAround(), other.getAround())
               && ClassUtils.equals(getBrokerService(), other.getBrokerService())
               && ClassUtils.equals(getDistrict(), other.getDistrict())
               && ClassUtils.equals(getDistrictName(), other.getDistrictName())
               && ClassUtils.equals(getPropertyType(), other.getPropertyType())
               && ClassUtils.equals(getParkingRatio(), other.getParkingRatio())
               && ClassUtils.equals(getParking(), other.getParking())
               && ClassUtils.equals(getLookType(), other.getLookType())
               && ClassUtils.equals(getSaleReason(), other.getSaleReason())
               && ClassUtils.equals(getHouseSituation(), other.getHouseSituation())
               && ClassUtils.equals(getPropertyManagementCompany(), other.getPropertyManagementCompany())
               && ClassUtils.equals(getPropertyManagementFee(), other.getPropertyManagementFee())
               && ClassUtils.equals(getBuildName(), other.getBuildName())
               && ClassUtils.equals(getUnitName(), other.getUnitName())
               && ClassUtils.equals(getRoomName(), other.getRoomName())
               && ClassUtils.equals(getCurrentFloor(), other.getCurrentFloor())
               && ClassUtils.equals(getFloorCategory(), other.getFloorCategory())
               && ClassUtils.equals(getTotalFloor(), other.getTotalFloor())
               && ClassUtils.equals(getTagElevator(), other.getTagElevator())
               && ClassUtils.equals(getElevatorCount(), other.getElevatorCount())
               && ClassUtils.equals(getRoomPerFloor(), other.getRoomPerFloor())
               && ClassUtils.equals(getCompletionTime(), other.getCompletionTime())
               && ClassUtils.equals(getRoomName(), other.getRoomName())
               && ClassUtils.equals(getPayMonths(), other.getPayMonths())
               && ClassUtils.equals(getDepositMonths(), other.getDepositMonths())
               && ClassUtils.equals(getCheckinDate(), other.getCheckinDate())
               && ClassUtils.equals(getEquipments(), other.getEquipments())
               && ClassUtils.equals(getOrient(), other.getOrient())
               && ClassUtils.equals(getRoomCount(), other.getRoomCount())
               && ClassUtils.equals(getHallCount(), other.getHallCount())
               && ClassUtils.equals(getToiletCount(), other.getToiletCount())
               && ClassUtils.equals(getBuildingArea(), other.getBuildingArea())
               && ClassUtils.equals(getUseArea(), other.getUseArea())
               && ClassUtils.equals(getRedo(), other.getRedo())
               && ClassUtils.equals(getHouseType(), other.getHouseType())
               && ClassUtils.equals(getHouseCertType(), other.getHouseCertType())
               && ClassUtils.equals(getHouseCertNO(), other.getHouseCertNO())
               && ClassUtils.equals(getHouseCertAddress(), other.getHouseCertAddress())
               && ClassUtils.equals(getSoleHouse(), other.getSoleHouse())
               && ClassUtils.equals(getHouseBusiNO(), other.getHouseBusiNO())
               && ClassUtils.equals(getHousePlanPurpose(), other.getHousePlanPurpose())
               && ClassUtils.equals(getOwnerName(), other.getOwnerName())
               && ClassUtils.equals(getOwnerCertNO(), other.getOwnerCertNO())
               && ClassUtils.equals(getBailorName(), other.getBailorName())
               && ClassUtils.equals(getBailorCertType(), other.getBailorCertType())
               && ClassUtils.equals(getBailorCertNO(), other.getBailorCertNO())
               && ClassUtils.equals(getBailorNames(), other.getBailorNames())
               && ClassUtils.equals(getGovVerifyCode(), other.getGovVerifyCode())
               && ClassUtils.equals(getGovVerifyUrl(), other.getGovVerifyUrl())
               && ClassUtils.equals(getGovContractCode(), other.getGovContractCode())
               && ClassUtils.equals(getGovPromoCode(), other.getGovPromoCode())
               && ClassUtils.equals(getBoutique(), other.getBoutique());
    }

    public void setBoutique(Boolean boutique) {
        this.boutique = boutique != null ? boutique : false;
    }

    public void setUseArea(BigDecimal useArea) {
        if (getSubType() == RENT_SHARE && (useArea == null || useArea.compareTo(BigDecimal.ZERO) <= 0)) {
            throw new BusinessException("bc.ejuetc.consumer.1023");
        }
        this.useArea = useArea;
    }


    public String getLayoutName() {
        StringBuilder buffer = new StringBuilder();
        if (getRoomCount() != null) {
            buffer.append(getRoomCount()).append("室");
        }
        if (getHallCount() != null) {
            buffer.append(getHallCount()).append("厅");
        }
        if (getToiletCount() != null) {
            buffer.append(getToiletCount()).append("卫");
        }

        return buffer.toString();
    }

}