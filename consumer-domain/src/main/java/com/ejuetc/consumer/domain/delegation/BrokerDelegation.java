package com.ejuetc.consumer.domain.delegation;

import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.consumer.api.delegation.EditDelegationPO;
import com.ejuetc.consumer.api.dto.DelegationDTO;
import com.ejuetc.consumer.domain.broker.Broker;
import com.ejuetc.consumer.domain.broker.BrokerRpt;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static java.util.stream.Collectors.toMap;

@Entity
@Getter
@DiscriminatorValue("BROKER")
@SubtypeCode(parent = Delegation.class, code = "BROKER", name = "经纪人房源")
@NoArgsConstructor
public class BrokerDelegation extends Delegation {

    public BrokerDelegation(Long userId, EditDelegationPO po) {
        super(userId, po);
    }

    @Override
    protected void setUserAndParent(Long userId, Delegation parent) {
        if (userId == null) throw new CodingException("经纪人房源必须指定经纪人");
        Broker broker = getBean(BrokerRpt.class).getReferenceById(userId);
        setBroker(broker);

        if (parent == null) return;
        if (parent.getLevel() != DelegationDTO.Level.COMPANY)
            throw new CodingException("经纪人房源的上级必须是公司房源");
        if (!parent.getCompanyId().equals(broker.getCompanyId()))
            throw new CodingException("经纪人房源的上级必须是同一公司的房源");
        setParent(parent);
    }

    @Override
    public boolean doDelete(String reason) {
        setDeleteFlag(true);
        boolean childrenDeleted = true;
        for (Delegation child : getChildren4BrokerDelegation()) {
            if (!child.doDelete(reason))
                childrenDeleted = false;
        }
        if (childrenDeleted) logicDelete();
        return childrenDeleted;
    }

    @Override
    public void setInfo(EditDelegationPO po) {
        super.setInfo(po);
        if (po.isFreshCompanyDelegation() && getCompanyDelegation() != null)
            getCompanyDelegation().setInfo(this);
    }

    @Override
    public void upDown(Boolean up, String remark, List<ChannelDTO.Code> channelCodes) {
        List<ChannelDelegation> modifyChildren = updateChild(channelCodes);
        modifyChildren.forEach(delegation -> delegation.upDown(up, remark));
    }

    private List<ChannelDelegation> updateChild(List<ChannelDTO.Code> channelCodes) {
        List<ChannelDelegation> channelDelegations = new ArrayList<>();
        if (channelCodes.isEmpty()) return channelDelegations;

        Map<ChannelDTO.Code, Delegation> channelDelegationMap = getChildren4BrokerDelegation().stream().collect(toMap(Delegation::getChannelCode, cd -> cd));
        for (ChannelDTO.Code channelCode : channelCodes) {
            ChannelDelegation channelDelegation;
            if (channelDelegationMap.containsKey(channelCode)) {
                channelDelegation = channelDelegationMap.get(channelCode).subType(ChannelDelegation.class);
                channelDelegation.setInfo(this);
            } else {
                channelDelegation = new ChannelDelegation(getBroker().getId(), null, channelCode, this);
                addChildren(channelDelegation);
                channelDelegation.save();
            }
            channelDelegations.add(channelDelegation);
        }
        return channelDelegations;
    }

    @Override
    @Transient
    public Delegation getParent() {
        return getCompanyDelegation();
    }


    @Override
    protected void setDetail(DelegationDetail newDetail) {
        if (newDetail.equals(getDetail())) return;
        super.setDetail(newDetail);
        getChildren4BrokerDelegation().forEach(child -> child.setDetail(newDetail));
    }

}
