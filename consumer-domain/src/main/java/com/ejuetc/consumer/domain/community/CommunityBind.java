package com.ejuetc.consumer.domain.community;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.usertype.JsonUT;
import com.ejuetc.consumer.api.community.BindCommunityRO;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import java.util.*;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.spring.SpringUtil.getProperty;
import static com.ejuetc.commons.base.utils.StringUtils.isBlank;
import static com.ejuetc.consumer.domain.commons.AMapUtils.callAMap;
import static com.ejuetc.consumer.domain.commons.AMapUtils.makeUrl;

@Slf4j
@Getter
@Entity
@NoArgsConstructor
@Comment("小区表")
@Table(name = "tb_community_bind",
        indexes = {
                @Index(name = "idx_community_bind_communityId", columnList = "community_id")
        },
        uniqueConstraints = {
                @UniqueConstraint(name = "uk_community_bind_address_name", columnNames = {"address", "name"})
        })
@Where(clause = "logic_delete = 0")
public class CommunityBind extends BaseEntity<CommunityBind> {

    public static final String COMMUNITY_NOT_FUND = "小区未找到";
    public static final String NOT_FIND_ADDRESS = "地址未找到";
    public static final List<String> COMMUNITY_NOT_FUND_LIST = Arrays.asList(COMMUNITY_NOT_FUND, NOT_FIND_ADDRESS, "ENGINE_RESPONSE_DATA_ERROR");
    public static final String[] REGION_POSTFIXES = {"区", "县", "旗", "市"};

    @Id
    @GeneratedValue(generator = "community_bind_id")
    @SequenceGenerator(name = "community_bind_id", sequenceName = "seq_community_bind_id")
    private Long id;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "community_id", columnDefinition = "bigint(20) COMMENT '小区ID'")
    private Community community;

    @Column(name = "address", columnDefinition = "varchar(255) COMMENT '小区地址'")
    protected String address;

    @Column(name = "name", columnDefinition = "varchar(255) COMMENT '小区名称'")
    protected String name;

    @Column(name = "region_request", columnDefinition = "text COMMENT '区域查询请求'")
    protected String regionReq;

    @Type(JsonUT.class)
    @Column(name = "region_response", columnDefinition = "json COMMENT '区域查询应答'")
    protected JSONObject regionResp;

    @Column(name = "poi_request", columnDefinition = "text COMMENT 'POI查询请求'")
    protected String poiReq;

    @Type(JsonUT.class)
    @Column(name = "poi_response", columnDefinition = "json COMMENT 'POI查询应答'")
    protected JSONObject poiResp;

    @Setter
    @Accessors(chain = true)
    @Column(name = "error_msg", columnDefinition = "text COMMENT '错误信息'")
    private String errorMsg;

    @Column(name = "adcode", columnDefinition = "varchar(63) COMMENT '行政区编码'")
    private String adcode;

    @Column(name = "poi_id", columnDefinition = "varchar(63) COMMENT '高德POI ID'")
    private String poiId;

    @Column(name = "poi_address", columnDefinition = "varchar(63) COMMENT '高德POI地址'")
    private String poiAddress;

    @Column(name = "poi_name", columnDefinition = "varchar(63) COMMENT '高德POI名称'")
    private String poiName;

    @Column(name = "city_name", columnDefinition = "varchar(127) COMMENT '城市'")
    protected String cityName;

    @Column(name = "district_name", columnDefinition = "varchar(127) COMMENT '行政区名称'")
    protected String districtName;

    public CommunityBind(String address, String name) {
        this.address = address;
        this.name = name;
    }

    public void exec() {
        adcode = queryAdcode();
        if (adcode == null) return;

        JSONObject poi = queryPoi(adcode);
        if (poi != null) bindCommunity(poi);
    }

    private String queryAdcode() {
        regionReq = makeUrl("https://restapi.amap.com/v3/geocode/geo", Map.of("address", formatAddress(address)));
        try {
            regionResp = callAMap(regionReq);
        } catch (Exception e) {
            errorMsg = e.getMessage();
            return null;
        }
        if (regionResp.getIntValue("count") == 0) {
            errorMsg = NOT_FIND_ADDRESS;
            return null;
        }
        JSONArray geocodes = regionResp.getJSONArray("geocodes");
        if (geocodes.isEmpty()) {
            errorMsg = "未找到地址";
            return null;
        }
        return geocodes.getJSONObject(0).getString("adcode");
    }

    private JSONObject queryPoi(String adcode) {
        poiReq = makeUrl("https://restapi.amap.com/v5/place/text", Map.of(
                "keywords", format4POI(name), // notBlank(tipsAddress) ? tipsAddress : name,
                "region", adcode,
                "types", "120000|100000",
                "city_limit", "true"
        ));
        poiResp = callAMap(poiReq);
        JSONArray pois = poiResp.getJSONArray("pois");
        if (pois.isEmpty()) {
            errorMsg = COMMUNITY_NOT_FUND;
            return null;
        } else {
            return pois.getJSONObject(0);
        }
    }

    private String format4POI(String name) {
        return name.replaceAll("·", "");
//        return name;
    }

    private void bindCommunity(JSONObject poi) {
        poiId = poi.getString("id");
        poiName = poi.getString("name");
        poiAddress = isBlank(poi.getString("address")) ? poi.getString("name") : poi.getString("address");
        if (poiId != null) {
            community = getBean(CommunityRpt.class).findAndLockByPoiIdAndNameAndAddress(poiId, poiName, poiAddress);
        }
        if (community == null) {
            community = new Community(this, poi).save();
        }
        community.addBind(this);
    }

    private static String formatAddress(String address) {
        if (StringUtils.isBlank(address)) throw new IllegalArgumentException("address is blank");

        for (String regionPostfix : REGION_POSTFIXES) {
            int index = address.lastIndexOf(regionPostfix);
            if (index != -1 && (index < 2 || !"自治区".equals(address.substring(index - 2, index + 1)))) {
                return address.substring(0, index + 1);
            }
        }

        return address.replaceAll("\\s+", "")
                .replaceAll("\\(.*?\\)", "")
                .replaceAll("（.*?）", "")
                .replaceAll("[，、]", "");
    }


    public BindCommunityRO makeBindRO() {
        return community.makeBindRO(id);
    }

    public boolean isSucc() {
        return community != null;
    }

    public static void main(String[] args) {
        String address1 = "河北省保定市莲池区合作路219号";
        String address2 = "北京朝阳望京街道(南京路)";
        String address3 = "内蒙古自治区呼和浩特市新城旗农村信用社";
        String address4 = "内蒙古自治区呼和浩特区新城旗农村信用社";

        System.out.println(formatAddress(address1)); // Output: 河北省保定市莲池区
        System.out.println(formatAddress(address2)); // Output: 北京市朝阳区
        System.out.println(formatAddress(address3)); // Output: 内蒙古自治区呼和浩特市新城区
        System.out.println(formatAddress(address4)); // Output: 上海市浦东新区
    }

    public boolean isNeedExec() {
        return community == null && (errorMsg == null || COMMUNITY_NOT_FUND_LIST.stream().noneMatch(m -> errorMsg.contains(m)));
    }
}
