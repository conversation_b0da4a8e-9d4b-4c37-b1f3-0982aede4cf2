package com.ejuetc.consumer.domain.delegation;

import com.alibaba.fastjson.JSON;
import com.ejuetc.channel.api.DelegateAPI;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.channel.dto.ChannelDelegateDTO;
import com.ejuetc.channel.pro.DownDelegatePo;
import com.ejuetc.channel.pro.UpDelegatePO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.response.ResponseStatus;
import com.ejuetc.commons.base.usertype.JsonUT;
import com.ejuetc.consumer.api.delegation.EditDelegationPO;
import com.ejuetc.consumer.api.delegation.MediaPO;
import com.ejuetc.consumer.api.dto.BrokerDTO;
import com.ejuetc.consumer.api.dto.CommunityDTO;
import com.ejuetc.consumer.api.dto.DelegationDTO;
import com.ejuetc.consumer.domain.community.Community;
import com.fangyou.common.base.response.Result;
import com.fangyou.repo.property.api.*;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.alibaba.fastjson.JSON.parseObject;
import static com.alibaba.fastjson.JSON.toJSONString;
import static com.ejuetc.channel.dto.BusinessOpenDTO.Code.NEW;
import static com.ejuetc.channel.dto.BusinessOpenDTO.Code.RENT;
import static com.ejuetc.channel.dto.ChannelDTO.Code.XIANYU;
import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getAPIByProperty;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.consumer.api.dto.DelegationDTO.Level.BROKER;
import static com.ejuetc.consumer.api.dto.DelegationDTO.SOURCE_GATEWAY;
import static com.ejuetc.consumer.api.dto.DelegationDTO.Status.*;
import static com.ejuetc.consumer.api.dto.RegionDTO.*;
import static lombok.AccessLevel.PROTECTED;
import static org.apache.commons.lang.StringUtils.isBlank;

@Entity
@Getter
@Setter(PROTECTED)
@DiscriminatorValue("CHANNEL")
@SubtypeCode(parent = Delegation.class, code = "CHANNEL", name = "渠道房源")
@NoArgsConstructor
public class ChannelDelegation extends Delegation {

    //成都街道转行政区(非标)
    private static final Map<String, Long> GOV_VERIFY_TOWN_MAP = new HashMap<>();

    static {
        //成都高新区
        GOV_VERIFY_TOWN_MAP.put("肖家河街道", 510109L);
        GOV_VERIFY_TOWN_MAP.put("合作街道", 510109L);
        GOV_VERIFY_TOWN_MAP.put("华阳街道", 510109L);
        GOV_VERIFY_TOWN_MAP.put("中和街道", 510109L);
        GOV_VERIFY_TOWN_MAP.put("西园街道", 510109L);

        //成都天府新区
        GOV_VERIFY_TOWN_MAP.put("万安街道", 510142L);
        GOV_VERIFY_TOWN_MAP.put("正兴街道", 510142L);
        GOV_VERIFY_TOWN_MAP.put("兴隆街道", 510142L);
        GOV_VERIFY_TOWN_MAP.put("煎茶街道", 510142L);
        GOV_VERIFY_TOWN_MAP.put("新兴街道", 510142L);
        GOV_VERIFY_TOWN_MAP.put("永兴街道", 510142L);
        GOV_VERIFY_TOWN_MAP.put("籍田街道", 510142L);
        GOV_VERIFY_TOWN_MAP.put("太平街道", 510142L);
    }

    //成都行政区编码转换
    private static final Map<Long, Long> GOV_VERIFY_DISTRICT_MAP = Map.of(
            510116L, 510122L,
            510117L, 510124L,
            510185L, 510180L,
            510118L, 510132L
    );

    public static final int MAX_PICTURE_COUNT = 9;
    @Type(value = JsonUT.class, parameters = {@org.hibernate.annotations.Parameter(name = "type", value = "com.ejuetc.channel.pro.UpDelegatePO")})
    @Column(name = "up_po", columnDefinition = "json COMMENT '上架请求信息'")
    private UpDelegatePO upPO;

    @Column(name = "manual_push", columnDefinition = "tinyint(1) COMMENT '手工推送标(1:上架,0:下架)'")
    private Boolean manualPush;

    public ChannelDelegation(Long userId, EditDelegationPO po) {
        this(userId, po, po.getChannelCode(), null);
    }

    public ChannelDelegation(Long userId, EditDelegationPO po, ChannelDTO.Code channelCode, BrokerDelegation parent) {
        super(userId, po, parent);

        setChannelCode(channelCode);
    }

    public void setChannelCode(ChannelDTO.Code channelCode) {
        if (channelCode == null) throw new CodingException("渠道房源必须指定渠道");
        if (channelCode == ChannelDTO.Code.ALIPAY && getType() == NEW) throw new CodingException("支付宝渠道不支持发布新房房源!");
        super.setChannelCode(channelCode);
    }

    @Override
    protected void setUserAndParent(Long userId, Delegation parent) {
        if (parent == null) throw new CodingException("渠道房源必须指定父级房源");
        if (parent.getLevel() != BROKER) throw new CodingException("渠道房源的上级必须是经纪人房源");

        setParent(parent);
        setBroker(parent.getBroker());

    }

    @Override
    protected void setDetail(DelegationDetail detail) {
        if (detail.equals(getDetail())) return;
        super.setDetail(detail);
        getBrokerDelegation().setDetail(detail);
        if (getChannelCode() == XIANYU && getStatus() == UP_SUCC) up();//标记需要重新上架(以更新渠道端状态)
    }


    private void govVerify() {
        if (getChannelCode() != XIANYU) return;
        if (getType() != RENT) return;
        Community community = getDetail().getCommunity();
        if (!GOV_VERIFY_CITY_IDS.contains(community.getCityId())) return;

        RentPutCheckInfoVo checkVO = new RentPutCheckInfoVo();
        checkVO.setHousePromotionCode(getDetail().getGovPromoCode());
        checkVO.setHouseUniqueCode(getCode());
        checkVO.setQuanShuFangName(getDetail().getOwnerName());
        checkVO.setQuanShuFangNumber(getDetail().getOwnerCertNO());
        checkVO.setChanQuanZhengNumber(getDetail().getHouseCertNO());
        checkVO.setChanQuanZhengType(getDetail().getHouseCertType());
        checkVO.setHouseOwnerCardNo(getDetail().getOwnerCertNO());
        checkVO.setHouseNo(getDetail().getHouseCertNO());
        checkVO.setHouseOwnerName(getDetail().getOwnerName());
        checkVO.setDistrict(
                GOV_VERIFY_TOWN_MAP.getOrDefault(community.getTownName(),
                        GOV_VERIFY_DISTRICT_MAP.getOrDefault(
                                community.getDistrictId(),
                                community.getDistrictId()
                        )
                ) + ""
        );
        checkVO.setStreet(community.getTownName());
        checkVO.setEndDate(Date.from(getDetail().getDeadline().atZone(ZoneId.systemDefault()).toInstant()));
        checkVO.setVerificationCode(getDetail().getGovVerifyCode());

        RentVerificationVo vo = new RentVerificationVo();
        vo.setCheckInfoVo(checkVO);
        vo.setPrice(getPriceTotal());
        vo.setArea(getDetail().getBuildingArea().intValue());
        vo.setPropertyManagementType(getDetail().getPropertyType());
        vo.setTitle(getTitle());
        vo.setPayModePaid(getDetail().getPayMonths());
        vo.setDepositType(switch (getDetail().getDepositMonths()) {//押金方式 1(押一)2(押二) 0（免押金）99(其他)
            case 1 -> 1;
            case 2 -> 2;
            case 0 -> 0;
            default -> 99;
        });
        vo.setOrientation(getDetail().getOrient());
        vo.setDecorate(getDetail().getRedo());
        vo.setCurrentFloorNum(getDetail().getCurrentFloor());
        vo.setTotalFloorNum(getDetail().getTotalFloor());
        vo.setEstateName(community.getName());
        vo.setEstateExecutionAddress(community.getAddress());
        vo.setDistrictId(community.getDistrictId());
        vo.setRoomCount(getDetail().getRoomCount());
        vo.setHallCount(getDetail().getHallCount());
        vo.setToiletCount(getDetail().getToiletCount());
        vo.setRentType(switch (getDetail().getSubType()) {
            case RENT_FULL -> 1;
            case RENT_SHARE -> 2;
            default -> throw new CodingException("错误的房源类型");
        });
        vo.setDelegationRoomNo(getDetail().getRoomName());
        vo.setLongitude(community.getLongitude() + "");
        vo.setLatitude(community.getLatitude() + "");
        vo.setImageUrls(getMedias().stream().map(MediaPO::getUrl).toList());

        VerificationAPI api = getAPIByProperty(VerificationAPI.class, "ejuetc.consumer.Delegation.saasVerificationAPI.url");
        if (CITY_ID_CHENGDU.equals(community.getCityId())) {//成都租房核验
            log.info("成都租房核验参数:{}\t{}\n{}", getBroker().getId(), getId(), toJSONString(vo, true));
            Result<GovRentRoomCheck> resp = api.chengDuVerification(getBroker().getId(), getId() + "", vo);
            log.info("成都租房核验响应:\n{}", toJSONString(resp, true));
            if (resp.getResult().getGovStatus() != 1)
                throw new BusinessException("bc.ejuetc.consumer.1016", resp.getResult().getReason());
        } else if (CITY_ID_XUZHOU.equals(community.getCityId())) {//徐州租房核验
            log.info("徐州租房核验参数:{}\t{}\n{}", getBroker().getId(), getId(), toJSONString(vo, true));
            Result<GovRentRoomCheckXuZhou> resp = api.xuZhouVerification(getBroker().getId(), getId() + "", vo);
            log.info("徐州租房核验响应:\n{}", toJSONString(resp, true));
            String fywybs = resp.getResult().getFywybs();
            if (isBlank(fywybs))
                throw new BusinessException("bc.ejuetc.consumer.1016", resp.getResult().getErrorMessage());
            getDetail().setGovPromoCode(fywybs);
        }
    }


    public void up() {
        DelegationDTO.Status newStatus = null;
        try {
            govVerify();
            ApiResponse<ChannelDelegateDTO> response = switch (getChannelCode()) {
                case PRIVATE -> succ(new ChannelDelegateDTO());
                case ALIPAY, XIANYU -> {
                    if (getBroker().getCity() == null || getBroker().getCompanyLicenseNum() == null) throw new BusinessException("bc.ejuetc.consumer.1020", getBroker().getId());
                    if (getChannelCode() == XIANYU) {
                        if (getTitle().length() > FIELD_LENGTH_TITLE)
                            throw new BusinessException("bc.ejuetc.consumer.1024", "房源标题", FIELD_LENGTH_TITLE);
                        if (getDescription().length() > FIELD_LENGTH_DESCRIPTION) {
                            throw new BusinessException("bc.ejuetc.consumer.1024", "房源描述", FIELD_LENGTH_DESCRIPTION);
                        }
                    }

                    DelegationDTO delegationDTO = convert2DTO(this, new DelegationDTO()
                            .setCurrentFloor(0)
                            .setCommunity(new CommunityDTO())
                            .setBroker(new BrokerDTO())
                    );
                    delegationDTO.setCallChannelTime(LocalDateTime.now());
                    if (delegationDTO.getSourceType() != null && delegationDTO.getSourceType().startsWith(SOURCE_GATEWAY)) {
                        delegationDTO.setSourceType(null);//渠道系统无需识别网关请求
                    }
                    this.upPO = new UpDelegatePO()
                            .setUserId(getBroker().getId())
                            .setMerchantUserId(getBroker().getCompanyId())
                            .setCityId(getBroker().getCity().getCode())
                            .setCityName(getBroker().getCity().getName())
                            .setDelegateJson(parseObject(toJSONString(delegationDTO)))
                            .setCommunityId(getDetail().getCommunity().getId())
                            .setBusinessCode(getDetail().getType())
                            .setChannelCode(getChannelCode());
                    log.info("上架调用渠道系统:\n{}", JSON.toJSONString(upPO, true));
                    ApiResponse<ChannelDelegateDTO> upRO = getBean(DelegateAPI.class).putUp(upPO);
                    log.info("上架渠道系统返回:\n{}", JSON.toJSONString(upRO, true));
                    yield upRO;
                }
            };

            newStatus = switch (response.getStatus()) {
                case SUCC_DONE -> UP_SUCC;
                case FAIL_BIZ, FAIL_PRM, FAIL_SYS -> UP_FAIL;
                default -> UP_ING;
            };
            addMemo("上架", response.getMessage());
        } catch (BusinessException e) {
            log.error("上架渠道房源失败", e);
            newStatus = UP_FAIL;
            addMemo("上架", e.getMessage());
        } catch (Exception e) {
            log.error("上架渠道房源失败", e);
            newStatus = UP_ING;
            addMemo("上架", e.getMessage());
        }
        notifySource(newStatus);
        setStatus(newStatus);
    }

    @Override
    @Transient
    public Delegation getParent() {
        return getBrokerDelegation();
    }

    public void down(String reason) {
        DelegationDTO.Status newStatus = null;
        try {
            ApiResponse<?> response = switch (getChannelCode()) {
                case PRIVATE -> succ();
                case ALIPAY, XIANYU -> {
                    log.info("下架调用渠道系统:{}", getId());
                    DelegationDTO delegationDTO = convert2DTO(this, new DelegationDTO()
                            .setCurrentFloor(0)
                            .setCommunity(new CommunityDTO())
                            .setBroker(new BrokerDTO())
                    );
                    DownDelegatePo upPO = new DownDelegatePo();
                    upPO.setDelegateJson(parseObject(toJSONString(delegationDTO)));

                    ApiResponse<ChannelDelegateDTO> resp = getBean(DelegateAPI.class).makeDown(
                            ChannelDelegateDTO.Source.CONSUMER,
                            getId(),
                            getChannelCode(),
                            false,
                            getSourceType() != null && !getSourceType().startsWith(SOURCE_GATEWAY) ? ChannelDelegateDTO.Source.valueOf(getSourceType()) : null,
                            getSourceId() != null ? Long.valueOf(getSourceId()) : null,
                            upPO
                    );
                    log.info("下架渠道系统返回:\n{}", resp);
                    yield resp;
                }
            };

            newStatus = response.getStatus() == ResponseStatus.SUCC_DONE ? DOWN_SUCC : DOWN_ING;
            addMemo("下架", "(" + reason + ") " + (response.getMessage() != null ? response.getMessage() : ""));
        } catch (Throwable e) {
            log.error("下架渠道房源异常:", e);
            newStatus = DOWN_ING;
            addMemo("下架", "(" + reason + ") " + e.getMessage());
        }

        if (!isDeleteFlag()) notifySource(newStatus);
        setStatus(newStatus);

    }


    public void receiveChannelNotify(DelegationDTO.Status newStatus, String remark) {
        addMemo("接收渠道通知", remark);
        setStatus(newStatus);
        notifySource(getStatus());
        if (isDeleteFlag()) getParent().delete("渠道通知后删除房源");
    }

    @Override
    public boolean doDelete(String reason) {
        setDeleteFlag(true);
        down(reason);
        if (getStatus() == DOWN_SUCC) {
            logicDelete();
            return true;
        } else {
            return false;
        }
    }

    public void manualPush() {
        try {
            if (getManualPush()) up();
            else down("历史数据导入");
            setManualPush(null);
        } catch (Exception e) {
            log.error("推送历史数据异常:", e);
            addMemo("推送历史数据", e.getMessage());
        }
    }

    public void upDown(Boolean channelUp, String reason) {
        upDown(channelUp, reason, List.of(getChannelCode()));
    }

    @Override
    public void upDown(Boolean channelUp, String reason, List<ChannelDTO.Code> channelCodes) {
        if (channelUp == null) return;

        if (channelUp) up();
        else down(reason);
    }
}
