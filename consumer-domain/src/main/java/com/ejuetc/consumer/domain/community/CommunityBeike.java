package com.ejuetc.consumer.domain.community;

import com.ejuetc.commons.base.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;

import static lombok.AccessLevel.PROTECTED;

@Getter
@Entity
@NoArgsConstructor
@Comment("贝壳小区表")
@Table(name = "tb_community_beike",
        indexes = {
                @Index(name = "idx_community_beike_cityName", columnList = "city_name")
        })
public class CommunityBeike extends BaseEntity<CommunityBeike> {

    @Id
    @Column(name = "id", columnDefinition = "bigint(20) NOT NULL COMMENT '贝壳小区ID'")
    private Long id;

    @Column(name = "city_name", columnDefinition = "varchar(127) COMMENT '城市'")
    private String cityName;

    @Column(name = "city_id", columnDefinition = "bigint(20) COMMENT '城市ID'")
    protected Long cityId;

    @Setter(PROTECTED)
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "community_detail_id", columnDefinition = "bigint(20) COMMENT '小区详情ID'", updatable = false, insertable = false)
    private CommunityDetail detail;

    @Column(name = "district_name", columnDefinition = "varchar(127) COMMENT '行政区'")
    private String districtName;

    @Column(name = "name", columnDefinition = "varchar(127) COMMENT '小区名'")
    private String name;

    @Column(name = "lat", columnDefinition = "decimal(10,6) COMMENT '纬度'")
    private BigDecimal lat;

    @Column(name = "lng", columnDefinition = "decimal(10,6) COMMENT '经度'")
    private BigDecimal lng;

    @Setter(PROTECTED)
    @Column(name = "community_id", columnDefinition = "bigint COMMENT '小区ID'")
    private Long communityId;

    @Setter(PROTECTED)
    @Column(name = "community_bind_id", columnDefinition = "bigint COMMENT '小区绑定ID'")
    private Long communityBindId;

    @Column(name = "community_detail_id", columnDefinition = "bigint COMMENT '小区详情ID'")
    private Long communityDetailId;

    @Setter
    @Column(name = "match_type", columnDefinition = "varchar(127) COMMENT '匹配类型'")
    private String matchType;

    @Setter(PROTECTED)
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "community_id", columnDefinition = "bigint(20) COMMENT '小区ID'", insertable = false, updatable = false)
    private Community community;

    @Setter(PROTECTED)
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "community_bind_id", columnDefinition = "bigint(20) COMMENT '小区绑定ID'", insertable = false, updatable = false)
    private CommunityBind communityBind;

    /**
     * 获取完整地址（城市+行政区）
     */
    public String getFullAddress() {
        if (cityName == null && districtName == null) {
            return null;
        }
        StringBuilder address = new StringBuilder();
        if (cityName != null) {
            address.append(cityName);
        }
        if (districtName != null) {
            address.append(districtName);
        }
        return address.toString();
    }

    /**
     * 绑定小区信息
     */
    public void bindCommunity(CommunityBind bind) {
        this.matchType = "queryOrBind_gaode";
        this.communityBind = bind;
        this.communityBindId = bind.getId();
        if (bind.getCommunity() != null) {
            this.community = bind.getCommunity();
            this.communityId = bind.getCommunity().getId();
        }
    }

    /**
     * 检查是否需要绑定
     */
    public boolean needsBind() {
        return matchType == null && communityId == null && communityBindId == null;
    }
}
