package com.ejuetc.consumer.domain.consumer;

import com.ejuetc.commons.base.clazz.ClassUtils;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.consumer.api.dto.LoginAccountDTO;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.StringUtils.maskMobilePhone;

@Setter(AccessLevel.PROTECTED)
@Getter
@Entity
@NoArgsConstructor
@Comment("消费者账号")
@Table(name = "tb_consumer_account", uniqueConstraints = {
        @UniqueConstraint(name = "uk_consumer_type_accountFlag", columnNames = {"type", "account_flag"})
})
@Where(clause = "logic_delete = 0")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", columnDefinition = "enum('WECHAT','ALIPAY','SMS') COMMENT '登录方式'")
public abstract class ConsumerAccount extends BaseEntity<ConsumerAccount> {

    @Id
    @GeneratedValue(generator = "login_account_id")
    @SequenceGenerator(name = "login_account_id", sequenceName = "seq_login_account_id")
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", insertable = false, updatable = false)
    private LoginAccountDTO.Type type;

    @Column(name = "account_flag", columnDefinition = "varchar(64) COMMENT '账号标识'", nullable = false)
    private String accountIdent;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "consumer_id", columnDefinition = "bigint(20) COMMENT '消费者主键'")
    private Consumer consumer;

    @Column(name = "phone", columnDefinition = "varchar(64) COMMENT '手机号'")
    private String phone;

    @Setter
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "login_history_id", columnDefinition = "bigint(20) COMMENT '最后一次登录历史主键'")
    private ConsumerLogin lastLogin;

    @Column(name = "nice_name", columnDefinition = "varchar(64) COMMENT '账户昵称'")
    private String niceName;

    @Column(name = "icon_url", columnDefinition = "varchar(255) COMMENT '头像URL'")
    private String iconUrl;


    public ConsumerAccount(String accountIdent) {
        this.accountIdent = accountIdent;
    }

    public static ConsumerAccount of(LoginAccountDTO.Type type, String accountIdent) {
        return switch (type) {
            case ALIPAY -> new ConsumerAccount4Alipay(accountIdent);
            case WECHAT -> new ConsumerAccount4Wechat(accountIdent);
            default -> throw new CodingException("不支持的登录方式[%s]", type);
        };
    }

    public LoginAccountDTO.Type getType() {
        if (type == null)
            type = LoginAccountDTO.Type.valueOf(ClassUtils.getClassAnnotation(this.getClass(), DiscriminatorValue.class).value());
        return type;
    }

    public abstract boolean checkAuth(String loginAuth);

    public void bindPhone(String phone) {
        this.phone = phone;
        if (consumer == null) {
            consumer = getBean(ConsumerRpt.class).findByPhone(phone)
                    .orElseGet(() -> new Consumer(phone));
            consumer.addLoginAccount(this);
            lastLogin.setConsumer(consumer);
        } else if (!consumer.getPhone().equals(phone)) {
            throw new CodingException("登录账号已绑定手机号,且新手机号[%s]与老手机号[%s]不一致!", phone, consumer.getPhone());
        }
    }

    public void setNiceName(String niceName) {
        this.niceName = niceName;
        if (consumer != null)
            consumer.setNickName(niceName);
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
        if (consumer != null)
            consumer.setIconUrl(iconUrl);
    }

    public abstract String bindPhoneByCode(String authCode);

    public String getMaskPhone() {
        return maskMobilePhone(phone);
    }

    public void edit(String nickName, String iconUrl) {
        setNiceName(nickName);
        setIconUrl(iconUrl);
    }
}
