package com.ejuetc.consumer.domain.implicit;

import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.consumer.api.dto.LoginAccountDTO;
import com.ejuetc.consumer.domain.broker.Broker;
import com.ejuetc.consumer.domain.consumer.Consumer;
import com.ejuetc.consumer.domain.consumer.ConsumerAccount;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Comment;

import static com.ejuetc.consumer.api.dto.LoginAccountDTO.Type.MANUAL;

@Setter(AccessLevel.PROTECTED)
@Getter
@MappedSuperclass
@Accessors(chain = true)
@NoArgsConstructor
public abstract class ImplicitBase<Subtype extends ImplicitBase<?>> extends BaseEntity<Subtype> {

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "broker_id", columnDefinition = "bigint(20) COMMENT '联系经纪人'")
    private Broker broker;

    @Column(name = "broker_id", columnDefinition = "bigint(20) COMMENT '联系经纪人'", updatable = false, insertable = false)
    private Long brokerId;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "account_id", columnDefinition = "bigint(20) COMMENT '账号ID'", nullable = false)
    private ConsumerAccount account;

    @Column(name = "account_id", columnDefinition = "bigint(20) COMMENT '账号ID'", updatable = false, insertable = false)
    private Long accountId;

    @Enumerated(EnumType.STRING)
    @Comment("客户账户类型")
    @Column(name = "account_type")
    private LoginAccountDTO.Type accountType = MANUAL;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "consumer_id", columnDefinition = "bigint(20) COMMENT '发起消费者'", nullable = false)
    private Consumer consumer;

    @Column(name = "consumer_id", columnDefinition = "bigint(20) COMMENT '发起消费者'", updatable = false, insertable = false)
    private Long consumerId;

    public ImplicitBase(Broker broker) {
        this(broker, null);
    }

    public ImplicitBase(Broker broker, ConsumerAccount account) {
        this.setBroker(broker);
        this.setAccount(account);
    }

    protected void setAccount(ConsumerAccount account) {
        if (account == null) return;
        this.account = account;
        this.accountId = account.getId();
        this.setAccountType(account.getType());
        setConsumer(account.getConsumer());
    }

    public void setAccountType(LoginAccountDTO.Type accountType) {
        if (accountType != null) {
            this.accountType = accountType;
        }
    }

    protected void setConsumer(Consumer consumer) {
        if (consumer == null) return;
        this.consumer = consumer;
        this.consumerId = consumer.getId();
    }

    protected void setBroker(Broker broker) {
        this.broker = broker;
        this.brokerId = broker.getId();
    }

    public boolean equalsBrokerId(Long userId) {
        return brokerId.equals(userId);
    }

    public String getNiceName() {
        return account != null ? account.getNiceName() : null;
    }

    public String getIconUrl() {
        return account != null ? account.getIconUrl() : null;
    }
}
