package com.ejuetc.consumer.domain.implicit.bind;

import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.clazz.ClassUtils;
import com.ejuetc.commons.base.exception.ThrowableUtils;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.valueobj.Command;
import com.ejuetc.consumer.api.dto.ImplicitBindDTO;
import com.ejuetc.consumer.domain.consumer.*;
import com.ejuetc.consumer.domain.delegation.ChannelDelegation;
import com.ejuetc.consumer.domain.delegation.Delegation;
import com.ejuetc.consumer.domain.implicit.ImplicitBase;
import com.ejuetc.consumer.domain.implicit.ImplicitCall;
import jakarta.persistence.*;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.ejuetc.commons.base.response.ApiResponse.apiResponse;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.spring.SpringUtil.getProperty;
import static java.time.LocalDateTime.now;
import static java.time.format.DateTimeFormatter.ofPattern;


@Slf4j
@Getter
@Entity
@NoArgsConstructor
@Comment("隐号绑定")
@Table(name = "tb_implicit_bind")
@Where(clause = "logic_delete = 0")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", columnDefinition = "enum('ALIYUN','YIKETONG') default 'ALIYUN' COMMENT '类型'")
public abstract class ImplicitBind extends ImplicitBase<ImplicitBind> implements Command {

    @Id
    @GeneratedValue(generator = "implicit_bind_id")
    @SequenceGenerator(name = "implicit_bind_id", sequenceName = "seq_implicit_bind_id")
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, insertable = false, updatable = false)
    private ImplicitBindDTO.Type type;

    @Setter
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "relation_id", columnDefinition = "bigint(20) COMMENT '关联绑定关系ID'")
    private ConsumerRelation relation;

    @ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinTable(name = "tb_implicit_bind_delegation",
            joinColumns = @JoinColumn(name = "bind_id", columnDefinition = "bigint(20) COMMENT '绑定ID'"),
            inverseJoinColumns = @JoinColumn(name = "delegation_id", columnDefinition = "bigint(20) COMMENT '委托ID'")
    )
    @Comment("隐号绑定关联委托多对多关联表")
    private List<Delegation> delegations = new ArrayList<>();

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "last_delegation_id", columnDefinition = "bigint(20) COMMENT '最近一次委托ID'")
    private Delegation lastDelegation;

    @Column(name = "expiration", columnDefinition = "datetime COMMENT '绑定过期时间'")
    private LocalDateTime expiration;

    @Embedded
    private RefreshInfo refreshInfo;

    @Column(name = "memo", columnDefinition = "text COMMENT '备注'")
    private String memo;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "bind")
    private List<ImplicitCall> calls = new ArrayList<>();

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "login_id", columnDefinition = "bigint(20) COMMENT '登录账号'", nullable = false)
    private ConsumerLogin login;

    @Embedded
    private BindInfo bindInfo;

    @Embedded
    private UnbindInfo unbindInfo;

    @Column(name = "broker_phone", columnDefinition = "varchar(64) COMMENT '经纪人手机号'")
    private String brokerPhone;

    @Setter(AccessLevel.PROTECTED)
    @Column(name = "consumer_phone", columnDefinition = "varchar(64) COMMENT '消费者手机号'")
    private String consumerPhone;

    @Enumerated(EnumType.STRING)
    @Comment("渠道编码")
    @Column(name = "channel_code")
    private ChannelDTO.Code channelCode;

    public ImplicitBind(ConsumerLogin login, ChannelDelegation delegation, String consumerPhone) {
        super(delegation.getBroker(), login.getAccount());
        this.login = login;
        this.channelCode = login.getChannelCode();
        this.relation = getBean(ConsumerRelationRpt.class).findAndLockExist(getBrokerId(), getAccountId()).orElseGet(() -> new ConsumerRelation(this));
        this.relation.addBind(this);
        this.brokerPhone = delegation.getBrokerPhone();
        this.consumerPhone = consumerPhone;
        addDelegation(delegation);
    }

    public void bind() {
        try {
            updateExpiration();
            bindInfo = new BindInfo();
            doBind(bindInfo);
        } catch (Throwable t) {
            log.error("绑定阿里隐号绑定服务报错:", t);
            memo = ThrowableUtils.throwStackTrace(t);
            bindInfo = new BindInfo().setResult(apiResponse(t));
        }
    }

    public void refresh() {
        try {
            updateExpiration();
            this.refreshInfo = new RefreshInfo();
            doRefresh(refreshInfo);
        } catch (Throwable t) {
            log.error("延期隐号报错:", t);
            memo = ThrowableUtils.throwStackTrace(t);
            this.refreshInfo = new RefreshInfo().setResult(apiResponse(t));
        }

    }

    public void unbind() {
        try {
            expiration = now();
            unbindInfo = new UnbindInfo();
            doUnbind(unbindInfo);
        } catch (Throwable t) {
            log.error("解绑阿里隐号报错:", t);
            memo = ThrowableUtils.throwStackTrace(t);
            unbindInfo.setResult(apiResponse(t));
        }
    }

    protected abstract void doBind(BindInfo bindInfo) throws Exception;

    protected abstract void doRefresh(RefreshInfo refreshInfo) throws Exception;

    protected abstract void doUnbind(UnbindInfo unbindInfo) throws Exception;

    private void updateExpiration() {
        expiration = now().plusDays(7);
    }

    public void addCall(ImplicitCall call) {
        if (calls.stream().anyMatch(c -> c.callIdEquals(call.getCallId()))) return;

        calls.add(call);
    }

    public void addDelegation(Delegation delegation) {
        lastDelegation = delegation;
        if (!delegations.contains(delegation)) {
            delegations.add(delegation);
        }
    }

    @Override
    public void exec() {
        unbind();
    }

    public boolean isBindSucc() {
        return bindInfo != null && bindInfo.getSecretPhone() != null;
    }

////////////////////////////////////////// static //////////////////////////////////////////

    public boolean checkPhone(String consumerPhone, String brokerPhone) {
        return this.consumerPhone.equals(consumerPhone) && this.brokerPhone.equals(brokerPhone);
    }

    public List<ConsumerTrack> getTracks() {
        return getBean(ConsumerTrackRpt.class).findByBrokerIdAndAccountId(getBrokerId(), getAccountId());
    }

    public ImplicitBindDTO.Type getType() {
        if (type == null)
            type = ImplicitBindDTO.Type.valueOf(ClassUtils.getClassAnnotation(this.getClass(), DiscriminatorValue.class).value());
        return type;
    }

    public String getSecretPhone() {
        return bindInfo != null ? bindInfo.getSecretPhone() : null;
    }

    public ApiResponse<?> getBindResult() {
        return bindInfo != null ? bindInfo.getResult() : null;
    }
}
