package com.ejuetc.consumer.domain.community;

import com.ejuetc.consumer.domain.region.Region;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.List;
import java.util.Optional;

import static com.ejuetc.commons.base.utils.ThreadUtils.redisLock;

public interface CommunityRpt extends JpaRepositoryImplementation<Community, Long> {
    Community findByPoiIdAndNameAndAddress(String poiId, String poiName, String poiAddress);

    default Community findAndLockByPoiIdAndNameAndAddress(String poiId, String poiName, String poiAddress) {
        redisLock(15, 15, "CommunityRpt.findByPoiIdAndNameAndAddress", poiId, poiName, poiAddress);
        return findByPoiIdAndNameAndAddress(poiId, poiName, poiAddress);
    }

    Optional<Community> findByBindsId(Long bindId);

    @Query("""
            from Community c
            where c.city.id=:cityId
            and (c.name like concat('%',:keyword,'%')
            or c.address like concat('%',:keyword,'%')
            or c.busiName like concat('%',:keyword,'%')
            or c.townName like concat('%',:keyword,'%')
            or c.districtName like concat('%',:keyword,'%'))
            """)
    List<Community> findByKeyword(Long cityId, String keyword);

    @Query("""
                select c from Community c
                where c.city.id = :cityId
                and c.name like concat('%',:keyword,'%')
            """)
    Page<Community> findByName(Long cityId, String keyword, Pageable pageable);

    @Query("""
                select c from Community c
                where c.city = :city
                and c.name like concat('%',:keyword,'%')
            """)
    Page<Community> findByCityAndKeyword(Region city, String keyword, Pageable pageable);

    @Query("""
                select c from Community c
                where c.city.id = :cityId
                and c.detail is not null
                and (c.name like concat('%',:keyword,'%') or c.address like concat('%',:keyword,'%'))
            """)
    Page<Community> findHasDetailByName(Long cityId, String keyword, Pageable pageable);
}
