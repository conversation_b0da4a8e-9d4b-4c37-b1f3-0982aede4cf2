package com.ejuetc.consumer.domain.commons;

import com.alibaba.fastjson.JSONObject;
import com.ejuetc.commons.base.exception.CodingException;
import lombok.SneakyThrows;
import org.slf4j.LoggerFactory;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.Map;

import static com.ejuetc.commons.base.spring.SpringUtil.getProperty;
import static com.ejuetc.commons.base.utils.IOUtils.readUrlToByteArray;
import static java.nio.charset.StandardCharsets.UTF_8;

public class AMapUtils {
    @SneakyThrows
    public static JSONObject callAMap(String url, Map<String, String> params) {
        return callAMap(makeUrl(url, params));
    }

    public static String makeUrl(String url, Map<String, String> params) {
        HashMap<String, String> map = new HashMap<>(params);
        map.put("key", getProperty("amap.key", String.class, "65bd4aab1dbd7c434a797be65488ce9b"));
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        map.forEach(builder::queryParam);
        return builder.toUriString();
    }

    @SneakyThrows
    public static JSONObject callAMap(String url) {
        LoggerFactory.getLogger(AMapUtils.class).info("高德请求, url={}", url);
        String response = new String(readUrlToByteArray(url), UTF_8);
        LoggerFactory.getLogger(AMapUtils.class).info("高德响应, response:\n{}", response);
        JSONObject detail = JSONObject.parseObject(response);
        if (detail.getIntValue("status") != 1) {
            throw new CodingException("请求高德失败:%s\n%s", detail.getString("info"), url);
        }
        return detail;
    }
}
