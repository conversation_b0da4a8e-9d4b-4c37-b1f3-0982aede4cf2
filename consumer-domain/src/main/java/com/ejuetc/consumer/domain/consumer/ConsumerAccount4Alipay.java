package com.ejuetc.consumer.domain.consumer;

import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.consumer.integration.alipay.AliPayService;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@Slf4j
@Entity
@DiscriminatorValue("ALIPAY")
@SubtypeCode(parent = ConsumerAccount.class, code = "ALIPAY", name = "支付宝小程序授权登录")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class ConsumerAccount4Alipay extends ConsumerAccount {

    public ConsumerAccount4Alipay(String accountIdent) {
        super(accountIdent);
    }

    @Override
    public boolean checkAuth(String loginAuth) {
        return true;
    }

    @Override
    public String bindPhoneByCode(String authCode) {
        String phone = getBean(AliPayService.class).getPhone(authCode);
        bindPhone(phone);
        return phone;
    }

}
