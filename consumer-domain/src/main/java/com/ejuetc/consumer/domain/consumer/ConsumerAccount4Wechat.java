package com.ejuetc.consumer.domain.consumer;

import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.consumer.integration.wechat.WechatService;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@Slf4j
@Entity
@DiscriminatorValue("WECHAT")
@SubtypeCode(parent = ConsumerAccount.class, code = "WECHAT", name = "微信小程序授权登录")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class ConsumerAccount4Wechat extends ConsumerAccount {

    public ConsumerAccount4Wechat(String accountIdent) {
        super(accountIdent);
    }

    @Override
    public boolean checkAuth(String loginAuth) {
        return true;
    }

    @Override
    public String bindPhoneByCode(String authCode) {
        String phone = getBean(WechatService.class).getPhone(authCode);
        bindPhone(phone);
        return phone;
    }

}
