package com.ejuetc.consumer.domain.consumer;

import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.consumer.domain.broker.Broker;
import com.ejuetc.consumer.domain.delegation.ChannelDelegation;
import com.ejuetc.consumer.domain.delegation.DelegationRpt;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@Getter
@Entity
@NoArgsConstructor
@Comment("消费者轨迹")
@Table(name = "tb_consumer_track", indexes = {
        @Index(name = "idx_consumer_track_relationId", columnList = "relation_id"),
})
@Where(clause = "logic_delete = 0")
public class ConsumerTrack extends BaseEntity<ConsumerTrack> {
    @Id
    @GeneratedValue(generator = "consumer_track_id")
    @SequenceGenerator(name = "consumer_track_id", sequenceName = "seq_consumer_track_id")
    private Long id;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "delegation_id", columnDefinition = "bigint(20) COMMENT '委托主键'")
    private ChannelDelegation delegation;

    @Column(name = "delegation_id", columnDefinition = "bigint(20) COMMENT '委托主键'", updatable = false, insertable = false)
    private Long delegationId;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "broker_id", columnDefinition = "bigint(20) COMMENT '经纪人主键'")
    private Broker broker;

    @Column(name = "broker_id", columnDefinition = "bigint(20) COMMENT '经纪人主键'", updatable = false, insertable = false)
    private Long brokerId;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "login_history_id", columnDefinition = "bigint(20) COMMENT '登录历史主键'")
    private ConsumerLogin login;

    @Column(name = "login_history_id", columnDefinition = "bigint(20) COMMENT '登录历史主键'", updatable = false, insertable = false)
    private Long loginId;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "login_account_id", columnDefinition = "bigint(20) COMMENT '登录账号主键'")
    private ConsumerAccount account;

    @Column(name = "login_account_id", columnDefinition = "bigint(20) COMMENT '登录账号主键'", updatable = false, insertable = false)
    private Long accountId;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "consumer_id", columnDefinition = "bigint(20) COMMENT '消费者主键'")
    private Consumer consumer;

    @Column(name = "consumer_id", columnDefinition = "bigint(20) COMMENT '消费者主键'", updatable = false, insertable = false)
    private Long consumerId;

    @Setter
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "relation_id", columnDefinition = "bigint(20) COMMENT '隐式关系主键'")
    private ConsumerRelation relation;

    @Column(name = "relation_id", columnDefinition = "bigint(20) COMMENT '隐式关系主键'", updatable = false, insertable = false)
    private Long relationId;

    @Enumerated(EnumType.STRING)
    @Comment("渠道编码")
    @Column(name = "channel_code")
    private ChannelDTO.Code channelCode;

    public ConsumerTrack(Long delegationId, Long loginId) {
        ChannelDelegation delegation = setDelegation(delegationId);
        ConsumerLogin login = setLogin(loginId);

        setRelation(getBean(ConsumerRelationRpt.class)
                .findAndLockExist(delegation.getBrokerId(), login.getAccountId())
                .orElseGet(() -> login.getChannelCode() == ChannelDTO.Code.PRIVATE
                        ? new ConsumerRelation(this).save()
                        : null
                )
        );
    }

    private ChannelDelegation setDelegation(Long delegationId) {
        if (delegationId == null) throw new CodingException("委托号不能为空!");
        if (delegationId.equals(this.delegationId)) return delegation;

        this.delegationId = delegationId;
        this.delegation = getBean(DelegationRpt.class).getReferenceById(this.delegationId).subType(ChannelDelegation.class);
        this.setBroker(delegation.getBroker());
        return delegation;
    }

    public void setBroker(Broker broker) {
        if (broker == null || broker.equals(this.broker)) return;

        this.broker = broker;
        this.brokerId = broker.getId();
    }

    private ConsumerLogin setLogin(Long loginId) {
        if (loginId == null) throw new CodingException("登录ID为空!");
        if (loginId.equals(this.loginId)) return login;

        this.loginId = loginId;
        this.login = getBean(ConsumerLoginRpt.class).getReferenceById(loginId);
        this.channelCode = login.getChannelCode();
        this.setAccount(login.getAccount());
        this.setConsumer(login.getConsumer());
        return login;

    }

    public void setAccount(ConsumerAccount account) {
        if (account == null || account.equals(this.account)) return;

        this.account = account;
        this.accountId = account.getId();
    }

    private void setConsumer(Consumer consumer) {
        if (consumer == null || consumer.equals(this.consumer)) return;

        this.consumer = consumer;
        this.consumerId = consumer.getId();
    }

    public String getActivityRemark() {
        return "浏览[" + delegation.getTitle() + "]";
    }

    public void setRelation(ConsumerRelation relation) {
        if (relation == null || relation.equals(this.relation)) return;

        this.relation = relation;
        this.relation.addTrack(this);
        this.relationId = relation.getId();
    }
}
