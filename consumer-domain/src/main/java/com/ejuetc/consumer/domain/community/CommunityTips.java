package com.ejuetc.consumer.domain.community;

import com.alibaba.fastjson.JSONObject;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.usertype.JsonUT;
import com.ejuetc.consumer.domain.region.Region;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import java.util.Map;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.consumer.domain.commons.AMapUtils.callAMap;
import static com.ejuetc.consumer.domain.commons.AMapUtils.makeUrl;

@Slf4j
@Getter
@Entity
@NoArgsConstructor
@Comment("小区提示词")
@Table(name = "tb_community_tips", uniqueConstraints = {@UniqueConstraint(columnNames = {"city_id", "keyword"})})
@Where(clause = "logic_delete = 0")
public class CommunityTips extends BaseEntity<CommunityTips> {

    @Id
    @GeneratedValue(generator = "community_tips_id")
    @SequenceGenerator(name = "community_tips_id", sequenceName = "seq_community_tips_id")
    private Long id;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "city_id", columnDefinition = "bigint(20) COMMENT '城市ID'")
    private Region city;

    @Column(name = "keyword", columnDefinition = "varchar(255) COMMENT '查询关键字'")
    protected String keyword;

    @Column(name = "request", columnDefinition = "text COMMENT '查询请求'")
    protected String request;

    @Type(JsonUT.class)
    @Column(name = "response", columnDefinition = "json COMMENT '查询应答'")
    protected JSONObject response;

    public CommunityTips(Region city, String keyword) {
        this.city = city;
        this.keyword = keyword;
    }

    public void exec() {
        request = makeUrl(
                "https://restapi.amap.com/v3/assistant/inputtips",
                Map.of(
                        "city", city.getAmapCitycode(),
                        "citylimit", "true",
//                        "type", "120000|120100|120200|120201|120202|120203|120300|120301|120302|120303|120304|190108|100000|100100|100101|100102|100103|100104|100105|100200|100201",
                        "keywords", keyword
                ));
        response = callAMap(request);
    }
}
