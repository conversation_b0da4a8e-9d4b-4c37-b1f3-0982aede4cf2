package com.ejuetc.consumer.domain.implicit;

import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.List;
import java.util.Optional;

import static jakarta.persistence.LockModeType.PESSIMISTIC_WRITE;

public interface ImplicitCallRpt extends JpaRepositoryImplementation<ImplicitCall, Long> {

    ImplicitCall findByCallId(String callId);

    @Query("""
            select c
            from ImplicitCall c
            where c.createTime between current_timestamp - 1 day and current_timestamp - 2 minute
            and c.recordUrl is not null
            and c.recordUrl not like concat(:urlPrefix,'%')
            """)
    @Lock(PESSIMISTIC_WRITE)
    List<ImplicitCall> findWaitConvert(String urlPrefix);
}
