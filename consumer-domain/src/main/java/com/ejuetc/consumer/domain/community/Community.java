package com.ejuetc.consumer.domain.community;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.usertype.JsonUT;
import com.ejuetc.commons.base.usertype.ListUT;
import com.ejuetc.consumer.api.community.BindCommunityRO;
import com.ejuetc.consumer.domain.region.Region;
import com.ejuetc.consumer.domain.region.RegionRpt;
import com.ejuetc.consumer.web.vo.CommunityLayoutSummaryVO;
import com.ejuetc.consumer.web.vo.CommunityVO;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.codehaus.groovy.util.ListHashMap;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;
import org.hibernate.annotations.Parameter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.consumer.domain.commons.AMapUtils.callAMap;
import static com.ejuetc.consumer.domain.commons.AMapUtils.makeUrl;
import static java.time.LocalDateTime.now;
import static java.util.stream.Collectors.*;
import static lombok.AccessLevel.PROTECTED;

@Getter
@Entity
@NoArgsConstructor
@Comment("小区表")
@Table(name = "tb_community", uniqueConstraints = {
        @UniqueConstraint(name = "uk_community_poiId_name_address", columnNames = {"poi_id", "name", "address"})
})
@Where(clause = "logic_delete = 0")
public class Community extends BaseEntity<Community> {

    @Id
    @GeneratedValue(generator = "community_id")
    @SequenceGenerator(name = "community_id", sequenceName = "seq_community_id")
    private Long id;

    @Type(value = ListUT.class, parameters = @Parameter(name = "elementType", value = "java.lang.Long"))
    @Column(name = "beike_ids", columnDefinition = "text COMMENT '贝壳小区ID列表'")
    private List<Long> beikeIds = new ArrayList<>();

    @Column(name = "location", columnDefinition = "varchar(255) COMMENT '(高德)经度'")
    protected String location;

    @Column(name = "longitude", columnDefinition = "decimal(10,6) COMMENT '纬度'")
    private BigDecimal longitude;

    @Column(name = "latitude", columnDefinition = "decimal(10,6) COMMENT '纬度'")
    private BigDecimal latitude;

    @Column(name = "name", columnDefinition = "varchar(255) COMMENT '小区名称'")
    protected String name;

    @Column(name = "address", columnDefinition = "varchar(255) COMMENT '简化小区地址'")
    protected String address;

    @Column(name = "formatted_address", columnDefinition = "varchar(255) COMMENT '格式化小区地址'")
    protected String formattedAddress;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "community_bind_id", columnDefinition = "bigint(20) COMMENT '小区绑定ID'")
    protected CommunityBind communityBind;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "province_id", columnDefinition = "bigint(20) COMMENT '省份ID'")
    protected Region province;

    @Column(name = "province_name", columnDefinition = "varchar(127) COMMENT '省份'")
    protected String provinceName;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "city_id", columnDefinition = "bigint(20) COMMENT '城市ID'")
    protected Region city;

    @Column(name = "city_name", columnDefinition = "varchar(127) COMMENT '城市'")
    protected String cityName;

    @Column(name = "city_code", columnDefinition = "varchar(127) COMMENT '城市编码'")
    protected String cityCode;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "district_id", columnDefinition = "bigint(20) COMMENT '行政区ID'")
    protected Region district;

    @Column(name = "district_name", columnDefinition = "varchar(127) COMMENT '行政区名称'")
    protected String districtName;

    @Column(name = "poi_id", columnDefinition = "varchar(127) COMMENT '高德POI ID'")
    private String poiId;

    @Column(name = "type_code", columnDefinition = "varchar(127) COMMENT 'POI类型编码'")
    private String typeCode;

    @Column(name = "type_name", columnDefinition = "varchar(127) COMMENT 'POI类型'")
    private String typeName;

    @Column(name = "town_request", columnDefinition = "text COMMENT '街道查询请求'")
    protected String townReq;

    @Type(JsonUT.class)
    @Column(name = "town_response", columnDefinition = "json COMMENT '街道查询应答'")
    protected JSONObject townResp;

    @Column(name = "town_name", columnDefinition = "varchar(127) COMMENT '城镇/街道名称'")
    private String townName;

    @Column(name = "busi_name", columnDefinition = "varchar(127) COMMENT '商圈名称'")
    private String busiName;


    @Column(name = "around_request", columnDefinition = "text COMMENT '街道查询请求'")
    protected String aroundReq;

    @Type(JsonUT.class)
    @Column(name = "around_response", columnDefinition = "json COMMENT '街道查询应答'")
    protected JSONObject aroundResp;

    @Column(name = "around_time", columnDefinition = "datetime(6) COMMENT '周边查询时间'")
    private LocalDateTime aroundTime;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "community")
    private List<CommunityBind> binds = new ArrayList<>();

    @Setter(PROTECTED)
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "detail_id", columnDefinition = "bigint(20) COMMENT '小区详情ID'")
    private CommunityDetail detail;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "community")
    private List<CommunityLayout> layouts = new ArrayList<>();

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "community")
    private List<CommunityPicture> pictures = new ArrayList<>();

    public Community(CommunityBind communityBind, JSONObject poi) {
        this.communityBind = communityBind;
        this.address = communityBind.getPoiAddress();
        this.poiId = communityBind.getPoiId();
        this.name = communityBind.getPoiName();

        //可能是区，也可能是城市(比如东莞)
        Region region = getBean(RegionRpt.class).findByAmapAdcode(poi.getString("adcode"));
        if (region.isDistrict()) {
            this.district = region;
            this.districtName = district.getName();
            this.city = district.getParent();
        } else {
            this.city = region;
        }
        this.cityCode = city.getCode();
        this.cityName = city.getName();

        this.province = city.getParent();
        this.provinceName = province.getName();

        this.location = poi.getString("location");
        this.longitude = new BigDecimal(location.split(",")[0]);
        this.latitude = new BigDecimal(location.split(",")[1]);
        this.typeCode = poi.getString("typecode");
        this.typeName = poi.getString("type");
        this.townReq = makeUrl("https://restapi.amap.com/v3/geocode/regeo", Map.of("location", location));
        this.townResp = callAMap(townReq);
        JSONObject regeocode = townResp.getJSONObject("regeocode");
        this.formattedAddress = regeocode.getString("formatted_address");
        JSONObject addressComponent = regeocode.getJSONObject("addressComponent");
        this.townName = addressComponent.getString("township");
        JSONArray businessAreas = addressComponent.getJSONArray("businessAreas");
        if (businessAreas != null && !businessAreas.isEmpty()) {
            Object obj0 = businessAreas.get(0);
            if (obj0 instanceof JSONObject busi) {
                this.busiName = busi.getString("name");
            }
        }

        refreshAround();

    }

    public void refreshAround() {
        this.aroundReq = makeUrl("https://restapi.amap.com/v3/place/around", Map.of(
                "location", location,
                "types", "150700|150500|141204|141203|141202|090000|090600|060000|050000"
        ));
        this.aroundResp = callAMap(aroundReq);
        this.aroundTime = now();
    }

    public BindCommunityRO makeBindRO(Long bindId) {
        return new BindCommunityRO()
                .setBindId(bindId)
                .setProvince(getProvinceName())
                .setCity(getCityName())
                .setDistrict(getDistrictName())
                .setName(getName())
                .setAddress(getAddress())
                .setLocation(getLocation())
                .setCommunityId(id)
                ;
    }

    public void addBind(CommunityBind bind) {
        if (binds.contains(bind)) return;
        binds.add(bind);
    }


    public CommunityVO.AroundWrapper getAround() {
        if (aroundResp == null) {
            return new CommunityVO.AroundWrapper();
        }

        JSONArray pois = aroundResp.getJSONArray("pois");
        if (pois == null || pois.isEmpty()) {
            return new CommunityVO.AroundWrapper();
        }

        CommunityVO.AroundWrapper wrapper = new CommunityVO.AroundWrapper();
        List<CommunityVO.Around> aroundList = pois.toJavaList(CommunityVO.Around.class);
        for (CommunityVO.Around around : aroundList) {
            String code = around.getTypecode();
            if (code.startsWith("1507")) {
                wrapper.getBus().add(around);
            } else if (code.startsWith("1505")) {
                wrapper.getSubway().add(around);
            } else if (code.equals("141204")) {
                wrapper.getKindergarten().add(around);
            } else if (code.equals("141203")) {
                wrapper.getPrimarySchool().add(around);
            } else if (code.equals("141202")) {
                wrapper.getMiddleSchool().add(around);
            } else if (code.equals("090000")
                       || (code.startsWith("090") && !code.startsWith("0906"))) {
                wrapper.getHospital().add(around);
            } else if (code.equals("090600") || code.startsWith("0906")) {
                wrapper.getPharmacy().add(around);
            } else if (code.equals("060000") || code.startsWith("060")) {
                wrapper.getShopping().add(around);
            } else if (code.equals("050000") || code.startsWith("050")) {
                wrapper.getRestaurant().add(around);
            }
        }
        return wrapper;
    }

    public String getPropertyName() {
        return detail == null ? null : detail.getPropertyName();
    }

    public String getParkingRatio() {
        return detail == null ? null : detail.getParkingRate();
    }

    public Long getCityId() {
        return city.getId();
    }

    public Long getDistrictId() {
        return district != null ? district.getId() : null;
    }

    public Map<String, List<String>> getPicturesMap() {
        return CommunityPicture.getPicturesMap(pictures);
    }

    public Map<String, CommunityLayoutSummaryVO> getLayoutsSummaryMap() {
        return CommunityLayout.getLayoutsSummaryMap(layouts);
    }

    public Map<Integer, Set<String>> getLayoutNames() {
        return layouts.stream()
                .sorted(Comparator.comparing(CommunityLayout::getName))
                .collect(groupingBy(CommunityLayout::getRoomCount, mapping(CommunityLayout::getName, toCollection(TreeSet::new))));
    }

    public Set<Integer> getLayoutRoomCounts() {
        return layouts.stream().map(CommunityLayout::getRoomCount).filter(Objects::nonNull).collect(toCollection(TreeSet::new));
    }

}
