package com.ejuetc.consumer.domain.implicit;

import com.alibaba.fastjson.JSONObject;
import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.commons.base.component.OssComponent;
import com.ejuetc.commons.base.usertype.JsonUT;
import com.ejuetc.commons.base.valueobj.Command;
import com.ejuetc.consumer.api.dto.ImplicitBindDTO;
import com.ejuetc.consumer.api.dto.ImplicitCallDTO;
import com.ejuetc.consumer.domain.consumer.ConsumerRelation;
import com.ejuetc.consumer.domain.delegation.Delegation;
import com.ejuetc.consumer.domain.implicit.bind.ImplicitBind;
import com.ejuetc.consumer.domain.implicit.bind.ImplicitBindRpt;
import com.ejuetc.consumer.web.vo.ImplicitCallPO;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import java.time.LocalDateTime;

import static com.ejuetc.commons.base.response.ApiResponse.apiResponse;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.spring.SpringUtil.getProperty;
import static com.ejuetc.consumer.api.dto.ImplicitCallDTO.ControlType.CONTINUE;
import static com.ejuetc.consumer.api.dto.ImplicitCallDTO.Starter.CONSUMER;
import static com.ejuetc.consumer.api.dto.ImplicitCallDTO.UnconnectedCause.*;
import static java.time.Duration.between;
import static java.time.LocalDateTime.now;
import static java.time.LocalDateTime.parse;
import static java.time.format.DateTimeFormatter.ofPattern;


@Slf4j
@Getter
@Entity
@NoArgsConstructor
@Comment("隐号绑定")
@Table(name = "tb_implicit_call", indexes = {
        @Index(name = "idx_implicit_call_createTime", columnList = "create_time"),
})
@Where(clause = "logic_delete = 0")
public class ImplicitCall extends ImplicitBase<ImplicitCall> implements Command {

    @Id
    @GeneratedValue(generator = "implicit_call_id")
    @SequenceGenerator(name = "implicit_call_id", sequenceName = "seq_implicit_call_id")
    private Long id;

    @Type(JsonUT.class)
    @Column(name = "body", columnDefinition = "json COMMENT '消息报问题'")
    private JSONObject body;

    @Column(name = "call_id", columnDefinition = "varchar(63) COMMENT '通话记录ID'", unique = true)
    private String callId;

    @Column(name = "city_name", columnDefinition = "varchar(63) COMMENT '隐号所属城市名称'")
    private String cityName;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "relation_id", columnDefinition = "bigint(20) COMMENT '关联绑定关系ID'")
    private ConsumerRelation relation;

    @Enumerated(EnumType.STRING)
    @Comment("发起方")
    @Column(name = "starter")
    private ImplicitCallDTO.Starter starter;

    @Column(name = "call_time", columnDefinition = "datetime COMMENT '主叫拨打时间'")
    private LocalDateTime callTime;

    @Column(name = "ring_time", columnDefinition = "datetime COMMENT '被叫响铃时间'")
    private LocalDateTime ringTime;

    @Column(name = "start_time", columnDefinition = "datetime COMMENT '被叫接听时间（通话计费开始时间）'")
    private LocalDateTime startTime;

    @Column(name = "release_time", columnDefinition = "datetime COMMENT '通话释放时间（通话计费结束时间）'")
    private LocalDateTime releaseTime;

    @Column(name = "duration", columnDefinition = "bigint(20) COMMENT '通话时长'")
    private Long duration;

    @Enumerated(EnumType.STRING)
    @Comment("挂断方")
    @Column(name = "closer")
    private ImplicitCallDTO.Closer closer;

    @Column(name = "record_url", columnDefinition = "varchar(511) COMMENT '录音下载URL'")
    private String recordUrl;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "bind_id", columnDefinition = "bigint(20) COMMENT '关联绑定ID'")
    private ImplicitBind bind;

    @Enumerated(EnumType.STRING)
    @Comment("接通失败原因")
    @Column(name = "unconnected_cause")
    private ImplicitCallDTO.UnconnectedCause unconnectedCause;

    @Column(name = "secret_pone", columnDefinition = "varchar(64) COMMENT '绑定的号码'")
    private String secretPhone;

    @Column(name = "call_status", columnDefinition = "varchar(64) COMMENT '呼叫状态'")
    private String callStatus;

    @Column(name = "control_msg", columnDefinition = "varchar(64) COMMENT '控制消息'")
    private String controlMsg;

    @Enumerated(EnumType.STRING)
    @Column(name = "control_type", columnDefinition = "varchar(64) COMMENT '控制类型'")
    private ImplicitCallDTO.ControlType controlType;

    @Column(name = "memo", columnDefinition = "text COMMENT '备注'")
    private String memo;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "delegation_id", columnDefinition = "bigint(20) COMMENT '关联委托ID'")
    private Delegation delegation;

    @Column(name = "delegation_id", columnDefinition = "bigint(20) COMMENT '关联委托ID'", insertable = false, updatable = false)
    private Long delegationId;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "clue_id", columnDefinition = "bigint(20) COMMENT '关联隐号线索ID'")
    private ImplicitClue clue;

    @Enumerated(EnumType.STRING)
    @Column(name = "business_code")
    private BusinessOpenDTO.Code businessCode;

    @Column(name = "sub_id", columnDefinition = "varchar(64) COMMENT '绑定编号'")
    private String subId;

    @Column(name = "broker_phone", columnDefinition = "varchar(64) COMMENT '经纪人手机号'")
    private String brokerPhone;

    @Setter(AccessLevel.PROTECTED)
    @Column(name = "consumer_phone", columnDefinition = "varchar(64) COMMENT '消费者手机号'")
    private String consumerPhone;

    public ImplicitCall(ImplicitCallPO po) {
        this.body = po.getBody();
        this.recordUrl = po.getRecordUrl();
        this.unconnectedCause = po.getUnconnectedCause();
        this.callId = po.getCallId();
        this.subId = po.getSubId();
        this.consumerPhone = po.getConsumerPhone();
        this.secretPhone = po.getSecretPhone();
        this.cityName = po.getCityName();
        this.brokerPhone = po.getBrokerPhone();
        this.starter = po.getStarter();
        this.closer = po.getCloser();
        this.controlType = po.getControlType();
        this.controlMsg = po.getControlMsg();
        this.callStatus = po.getCallStatus();
        this.callTime = po.getCallTime();
        this.ringTime = po.getRingTime();
        this.startTime = po.getStartTime();
        this.releaseTime = po.getReleaseTime();
    }


    public void process() {
        if (startTime != null && releaseTime != null) this.duration = between(startTime, releaseTime).getSeconds();
        this.bind = getBean(ImplicitBindRpt.class).findByBindInfoSubId(subId);
        if (bind == null) {
            this.memo = "未找到绑定记录";
            return;
        }
        if (!bind.checkPhone(consumerPhone, brokerPhone)) {
            this.memo = "绑定号码不匹配";
            return;
        }

        this.bind.addCall(this);
        setBroker(bind.getBroker());
        setAccount(bind.getAccount());
        this.relation = bind.getRelation();
        this.relation.addCall(this);
        this.delegation = bind.getLastDelegation();
        this.businessCode = delegation.getType();

        if (!isValid()) return;
        if (starter == CONSUMER) bind.refresh();

        this.clue = getBean(ImplicitClueRpt.class).findExist(this).orElseGet(() -> new ImplicitClue(this).save());
        this.clue.addCall(this);

        if (bind.getType() == ImplicitBindDTO.Type.YIKETONG && recordUrl != null)
            convertRecordUrl();
    }

    public boolean callIdEquals(String callId) {
        return this.callId.equals(callId);
    }

    public void convertRecordUrl() {
        OssComponent ossComponent = getBean(OssComponent.class);
        if (this.recordUrl.startsWith(ossComponent.getUrlPrefix())) return;
        this.recordUrl = ossComponent.urlConvert(
                recordUrl,
                "call_record/" + now().format(ofPattern("yyyyMM")) + "/" + callId + ".mp3"
        );
    }

    @Override
    public void exec() {
        convertRecordUrl();
    }

    public boolean isValid() {
        return CONTINUE.equals(controlType);
    }

}
