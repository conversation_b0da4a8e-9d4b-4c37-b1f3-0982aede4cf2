package com.ejuetc.consumer.domain.consumer;

import com.ejuetc.consumer.web.vo.ShareStatisticsVO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface ConsumerShareRpt extends JpaRepositoryImplementation<ConsumerShare, Long> {

    Optional<ConsumerShare> findByCode(String shareCode);

    @Query(value = """
            select sum(if(sharer_broker_id is not null, 1, 0))                            broker_sharer_count
                 , sum(if(sharer_broker_id is null, 1, 0))                                consumer_sharer_count
                 , count(distinct if(target_type = 'ChannelDelegation', target_id, null)) distinct_delegation_count
            from tb_consumer_share cs
            where cs.target_broker_id = :targetBrokerId
              and cs.create_time > current_timestamp - interval 3 month
            """, nativeQuery = true)
    Map<String, Object> doShareStatistics(Long targetBrokerId);

    default ShareStatisticsVO shareStatistics(Long targetBrokerId) {
        Map<String, Object> map = doShareStatistics(targetBrokerId);
        return new ShareStatisticsVO()
                .setBrokerSharerCount((BigDecimal) map.get("broker_sharer_count"))
                .setConsumerSharerCount((BigDecimal) map.get("consumer_sharer_count"))
                .setDistinctDelegationCount((Long) map.get("distinct_delegation_count"))
                ;
    }
}
