package com.ejuetc.consumer.domain.delegation;

import com.ejuetc.channel.api.BrokerAPI;
import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelBrokerDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.channel.pro.BindBrokerPO;
import com.ejuetc.commons.base.clazz.ClassUtils;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.filter.LoginTokenFilter;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.response.ResponseStatus;
import com.ejuetc.commons.base.usertype.JsonUT;
import com.ejuetc.commons.base.usertype.ListUT;
import com.ejuetc.commons.base.usertype.MemoUT;
import com.ejuetc.commons.base.valueobj.Memo;
import com.ejuetc.consumer.api.delegation.DelegationVO4Gateway;
import com.ejuetc.consumer.api.delegation.EditDelegationPO;
import com.ejuetc.consumer.api.delegation.MediaPO;
import com.ejuetc.consumer.api.dto.DelegationDTO;
import com.ejuetc.consumer.domain.broker.Broker;
import com.ejuetc.consumer.domain.community.CommunityBind;
import com.ejuetc.consumer.web.vo.ChannelDelegationVO;
import com.ejuetc.consumer.web.vo.TrackCountVO;
import com.ejuetc.saasapi.api.NotifyApi;
import com.ejuetc.saasapi.dto.gateway.NotifyDTO;
import com.ejuetc.saasapi.pro.GetawayResponse;
import com.ejuetc.saasapi.pro.NotifyPO;
import com.fangyou.repo.property.api.CallBackConsumerVo;
import com.fangyou.repo.property.api.VerificationAPI;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

import static com.alibaba.fastjson.JSON.toJSONString;
import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.spring.SpringUtil.*;
import static com.ejuetc.commons.base.utils.StringUtils.notBlank;
import static com.ejuetc.commons.base.utils.ThreadUtils.asyncExec;
import static com.ejuetc.consumer.api.dto.DelegationDTO.*;
import static com.ejuetc.consumer.api.dto.DelegationDTO.Level.*;
import static com.ejuetc.consumer.api.dto.DelegationDTO.Status.*;
import static java.util.stream.Collectors.toMap;
import static lombok.AccessLevel.PROTECTED;
import static org.hibernate.Hibernate.initialize;

@Entity
@Getter
@Setter(PROTECTED)
@NoArgsConstructor
@Comment("(房源)委托")
@Table(name = "tb_delegation",
        indexes = {
                @Index(name = "idx_delegation_brokerId", columnList = "broker_id"),
                @Index(name = "idx_delegation_companyId", columnList = "company_id"),
                @Index(name = "idx_delegation_communityId", columnList = "community_id"),
                @Index(name = "idx_delegation_brokerDelegationId", columnList = "broker_delegation_id"),
                @Index(name = "idx_delegation_companyDelegationId", columnList = "company_delegation_id"),
                @Index(name = "idx_delegation_parentSourceId", columnList = "parent_source_id"),
                @Index(name = "idx_delegation_cityId_level_type_channelCode_status", columnList = "city_id,level,type,channel_code,status")
        },
        uniqueConstraints = {
                @UniqueConstraint(name = "uk_delegation_code", columnNames = {"code"}),
                @UniqueConstraint(name = "uk_delegation_sourceId_sourceType", columnNames = {"source_id", "source_type"})
        }
)
@Where(clause = "logic_delete = 0")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "level", columnDefinition = "enum('COMPANY','BROKER','CHANNEL') default 'CHANNEL' COMMENT '类型'")
public abstract class Delegation extends BaseEntity<Delegation> {

    public static final int FIELD_LENGTH_DESCRIPTION = 220;
    public static final int FIELD_LENGTH_TITLE = 30;
    @Embedded
    private DelegationDetail detail = new DelegationDetail();

    @Id
    @GeneratedValue(generator = "delegation_id")
    @SequenceGenerator(name = "delegation_id", sequenceName = "seq_delegation_id")
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "level", nullable = false, insertable = false, updatable = false)
    private DelegationDTO.Level level;

    @Column(name = "code", columnDefinition = "varchar(63) COMMENT '委托编码'")
    private String code = UUID.randomUUID().toString().replaceAll("-", "");

    @Column(name = "source_type", columnDefinition = "varchar(64) COMMENT '来源(SaaS/QiaoFang/MingCe/...)'")
    private String sourceType;

    @Column(name = "source_id", columnDefinition = "varchar(127) COMMENT '外部ID'")
    private String sourceId;

    @Column(name = "parent_source_id", columnDefinition = "varchar(127) COMMENT '外部父ID'")
    private String parentSourceId;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "broker_delegation_id", columnDefinition = "bigint(20) COMMENT '关联经纪房源ID'")
    private BrokerDelegation brokerDelegation;

    @Column(name = "broker_delegation_id", columnDefinition = "bigint(20) COMMENT '关联经纪房源ID'", insertable = false, updatable = false)
    private Long brokerDelegationId;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "brokerDelegation")
    private List<Delegation> children4BrokerDelegation = new ArrayList<>();

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "company_delegation_id", columnDefinition = "bigint(20) COMMENT '关联公司房源ID'")
    private CompanyDelegation companyDelegation;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "companyDelegation")
    private List<Delegation> children4CompanyDelegation = new ArrayList<>();

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "broker_id", columnDefinition = "bigint(20) COMMENT '经纪人ID'")
    private Broker broker;

    @Column(name = "broker_id", columnDefinition = "bigint(20) COMMENT '经纪人ID'", insertable = false, updatable = false)
    private Long brokerId;

    @Setter(PROTECTED)
    @Column(name = "company_id", columnDefinition = "bigint(20) COMMENT '所属公司userId'")
    private Long companyId;

    @Column(name = "broker_name", columnDefinition = "varchar(255) COMMENT '经纪人名'")
    private String brokerName;

    /*************************************** 基础信息 ******************************************/

    @Column(name = "title", columnDefinition = "varchar(255) COMMENT '标题'")
    private String title;

    @Column(name = "description", columnDefinition = "varchar(1024) COMMENT '描述'")
    private String description;

    @Type(ListUT.class)
    @Column(name = "labels", columnDefinition = "varchar(511) comment '标签'")
    private List<String> labels = new ArrayList<>();

    @Type(value = JsonUT.class, parameters = {@org.hibernate.annotations.Parameter(name = "type", value = "com.ejuetc.consumer.api.delegation.MediaPO")})
    @Column(name = "medias", columnDefinition = "json COMMENT '媒体'")
    private List<MediaPO> medias = new ArrayList<>();

    @Column(name = "price_total", columnDefinition = "decimal(20,2) comment '总价|租金'")
    private BigDecimal priceTotal;

    @Column(name = "price_unit", columnDefinition = "decimal(20,2) comment '单价|押金'")
    private BigDecimal priceUnit;

    @Comment("状态")
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private DelegationDTO.Status status = INIT;

    @Column(name = "remark", columnDefinition = "text COMMENT '备注(对外展示)'")
    private String remark;

    @Type(MemoUT.class)
    @Column(name = "memo", columnDefinition = "text COMMENT '备注(内部异常)'")
    private Memo memo = new Memo();

    @Enumerated(EnumType.STRING)
    @Comment("渠道编码")
    @Column(name = "channel_code")
    private ChannelDTO.Code channelCode;

    @Column(name = "delete_flag", columnDefinition = "tinyint(1) default 0 COMMENT '删除(隐藏)标志位(用于)'")
    private boolean deleteFlag = false;

    @Type(value = JsonUT.class, parameters = {@org.hibernate.annotations.Parameter(name = "type", value = "com.ejuetc.consumer.api.delegation.EditDelegationPO")})
    @Column(name = "edit_po", columnDefinition = "json COMMENT '编辑请求信息'")
    private EditDelegationPO editPO;


    public static Delegation make(Long userId, EditDelegationPO po) {
        return switch (po.getLevel()) {
            case COMPANY -> new CompanyDelegation(userId, po);
            case BROKER -> new BrokerDelegation(userId, po);
            case CHANNEL -> new ChannelDelegation(userId, po);
        };
    }

    public Delegation(Long userId, EditDelegationPO editPO) {
        this(userId, editPO, null);
    }

    public Delegation(Long userId, EditDelegationPO editPO, Delegation parent) {
        if (editPO != null) {
            this.sourceType = editPO.getSourceType();
            this.sourceId = editPO.getSourceId();
            this.parentSourceId = editPO.getParentSourceId();
            parent = parent != null ? parent
                    : editPO.getParentId() != null
                    ? getBean(DelegationRpt.class).getReferenceById(editPO.getParentId())
                    : editPO.getParentSourceId() != null
                    ? getBean(DelegationRpt.class).findAndLockBySourceTypeAndSourceId(editPO.getSourceType(), editPO.getParentSourceId())
                    : null;
            setUserAndParent(userId, parent);
            setInfo(editPO);
        } else if (parent != null) {
            setUserAndParent(userId, parent);
            setInfo(parent);
        } else throw new CodingException("参数错误");
    }

    protected abstract void setUserAndParent(Long userId, Delegation parent);

    protected void setInfo(EditDelegationPO po) {
        this.editPO = po;
        DelegationDetail detail = new DelegationDetail(getBroker(), po);
        this.setTitle(po.getTitle(), detail);
        this.setDescription(po.getDescription());
        this.setLabels(po.getType(), po.getLabels());
        this.setPriceTotal(po.getPriceTotal());
        this.setPriceUnit(po.getBuildingArea(), po.getPriceTotal(), po.getPriceUnit(), po.getType());
        this.setMedias(po.getMedias());
        this.setDetail(detail);
    }

    protected void setInfo(Delegation aim) {
        this.setTitle(aim.getTitle(), aim.getDetail());
        this.setDescription(aim.getDescription());
        this.setLabels(aim.getType(), aim.getLabels());
        this.setPriceTotal(aim.getPriceTotal());
        this.setPriceUnit(aim.getDetail().getBuildingArea(), aim.getPriceTotal(), aim.getPriceUnit(), aim.getType());
        this.setMedias(aim.getMedias());
        this.setDetail(aim.getDetail());
    }

    public void setPriceUnit(BigDecimal buildingArea, BigDecimal priceTotal, BigDecimal priceUnit, BusinessOpenDTO.Code type) {
        if (priceUnit != null) {
            this.priceUnit = priceUnit;
        } else if (priceTotal != null && buildingArea != null && buildingArea.compareTo(BigDecimal.ZERO) > 0) {
            this.priceUnit = switch (type) {
                case NEW, SALE -> priceTotal.multiply(new BigDecimal("10000")).divide(buildingArea, 2, RoundingMode.HALF_UP);
                case RENT -> priceTotal.divide(buildingArea, 2, RoundingMode.HALF_UP);
            };
        }
    }

    public void modifyBoutique(boolean boutique) {
        DelegationDetail clone = getDetail().clone();
        clone.setBoutique(boutique);
        setDetail(clone);
    }

    public void setTitle(String title, DelegationDetail detail) {
        if (notBlank(title)) {
            this.title = title;
        } else {
            StringBuilder buffer = new StringBuilder(detail.getCommunityName());

            String layoutName = detail.getLayoutName();
            if (notBlank(layoutName)) buffer.append(" ").append(layoutName);

            if (detail.getBuildingArea() != null) {
                buffer.append(" ").append(detail.getBuildingArea().stripTrailingZeros().toPlainString()).append("平");
            }
            if (detail.getRedo() != null) {
                buffer.append(" ").append(detail.getRedo());
            }
            this.title = buffer.toString();
        }
    }

    public void modify(EditDelegationPO po) {
        setInfo(po);
    }

    public DelegationDTO.Level getLevel() {
        if (level == null)
            level = DelegationDTO.Level.valueOf(ClassUtils.getClassAnnotation(this.getClass(), DiscriminatorValue.class).value());
        return level;
    }


    protected void setBroker(Broker broker) {
        this.broker = broker;
        this.brokerName = this.broker.getName();
        this.companyId = broker.getCompanyId();
    }

    protected void setParent(Delegation parent) {
        if (parent == null) return;
        if (parent.getId() != null) getBean(EntityManager.class).lock(parent, LockModeType.PESSIMISTIC_WRITE);
        setParentSourceId(parent.getSourceId());
        switch (parent.getLevel()) {
            case COMPANY -> setCompanyDelegation(parent.subType(CompanyDelegation.class));
            case BROKER -> setBrokerDelegation(parent.subType(BrokerDelegation.class));
            default -> throw new CodingException("未知的父房源委托类型:%s", parent.getLevel());
        }
    }

    protected void setBrokerDelegation(BrokerDelegation brokerDelegation) {
        this.brokerDelegation = brokerDelegation;
        if (brokerDelegation != null) {
            this.brokerDelegationId = brokerDelegation.getId();
            brokerDelegation.addChildren(this);
            setCompanyDelegation(brokerDelegation.getCompanyDelegation());
            this.sourceType = brokerDelegation.getSourceType();
        }
    }

    protected void setCompanyDelegation(CompanyDelegation companyDelegation) {
        this.companyDelegation = companyDelegation;
        if (companyDelegation != null) {
            companyDelegation.addChildren(this);
            this.sourceType = companyDelegation.getSourceType();
        }
    }

    protected void addChildren(Delegation child) {
        if (!getChildren().contains(child))
            getChildren().add(child);
    }

    public String getBrokerPhone() {
        return broker.getPhone();
    }

    public boolean isUser(Long userId) {
        return broker.getId().equals(userId);
    }

    public BusinessOpenDTO.Code getType() {
        return detail.getType();
    }

    public boolean notBelong(SaasLoginToken saasToken) {
        return !saasToken.getUserId().equals(getBroker().getId());
    }


    public boolean cantManage(SaasLoginToken saasToken) {
        return saasToken.getManagerRole() || getLevel() == COMPANY
                ? !broker.getCompanyId().equals(getCompanyId())
                : !saasToken.getUserId().equals(getBroker().getId());
    }

    public void delete(String reason) {
        doDelete(reason);
        addMemo("删除房源", reason);
        notifySource(WITHOUT);
    }

    @Override
    public void logicDelete() {
        setSourceId4Deleted();
        super.logicDelete();
    }

    private void setSourceId4Deleted() {
        if (sourceId != null) sourceId = "%s[delete:%s]".formatted(getSourceId(), UUID.randomUUID());
    }

    public String getSourceId() {
        if (sourceId == null || !sourceId.contains("[delete:")) return sourceId;
        return sourceId.substring(0, sourceId.indexOf("["));
    }

    public abstract boolean doDelete(String reason);

    protected void notifySource(DelegationDTO.Status newStatus) {
        if (getSourceType() == null
            || (getLevel() == CHANNEL && newStatus == WITHOUT)
            || getLevel() == COMPANY
            || newStatus == null
            || newStatus == getStatus()
        ) return;

        initialize(getBrokerDelegation());
        initialize(getParent());
        if (getSourceType().equals(SOURCE_SAAS) || getSourceType().equals(SOURCE_SHARING)) {
            asyncExec(() -> notifySaaS(newStatus));
        } else if (getSourceType().startsWith(SOURCE_GATEWAY)) {
            asyncExec(() -> notifyGateway(newStatus));
        }
    }

    private void notifyGateway(Status newStatus) {
        if (newStatus == WITHOUT || getLevel() == COMPANY) return;

        Delegation target = getLevel() == CHANNEL ? this.getBrokerDelegation() : this;
        NotifyPO notifyPO = new NotifyPO()
                .setNotifyId(getId() + "-" + getVersion())
                .setApiCode("editDelegation")
                .setUserId(getCompanyId())
                .setBody(new GetawayResponse(
                        ResponseStatus.SUCC_DONE,
                        "状态变更通知",
                        convert2DTO(target, new DelegationVO4Gateway().setChannelDelegationMap(Map.of()))
                ));
        try {
            log.info("通知Gateway房源状态变更:\n{}", toJSONString(notifyPO, true));
            ApiResponse<NotifyDTO> response = getBean(NotifyApi.class).notify(notifyPO);
            log.info("通知Gateway房源状态变更响应:\n{}", toJSONString(response, true));
        } catch (Exception e) {
            log.error("通知Gateway房源状态变更异常", e);
        }
    }

    private void notifySaaS(Status newStatus) {
        try {
            CallBackConsumerVo vo = new CallBackConsumerVo();
            vo.setStatus(switch (newStatus) {
                case DOWN_SUCC -> 0;
                case UP_ING -> 1;
                case UP_SUCC -> 2;
                case UP_FAIL -> 3;
                case DOWN_ING -> 4;
                case WITHOUT -> -1;
                default -> throw new CodingException("预期之外的房源通知状态:%s", newStatus);
            });
            vo.setChannelType(getChannelCode() == null ? null : switch (getChannelCode()) {
                case XIANYU -> 1;
                case ALIPAY -> 4;
                case PRIVATE -> 5;
            });
            vo.setBusinessType(switch (getType()) {
                case SALE -> 1;
                case RENT -> 2;
                default -> throw new CodingException("预期之外的房源业务类型:%s", getType());
            });
            vo.setSourceType(switch (getSourceType()) {
                case SOURCE_SAAS -> 1;
                case SOURCE_SHARING -> 2;
                default -> throw new CodingException("预期之外的房源来源类型:%s", getBrokerDelegation().getSourceType());
            });
            vo.setRentType(switch (getDetail().getSubType()) {
                case RENT_FULL -> 1;
                case RENT_SHARE -> 2;
                default -> null;
            });
            vo.setSourceId(getSourceId());
            vo.setParentSourceId(getParentSourceId());
            vo.setErrorMsg(getRemark());
            vo.setConsumerChannelId(getLevel() == CHANNEL ? getId() + "" : null);
            vo.setConsumerBrokeId(switch (getLevel()) {
                case BROKER -> getId();
                case CHANNEL -> getParent().getId();
                default -> throw new CodingException("预期之外的房源委托类型:%s", getLevel());
            } + "");
            log.info("通知房源状态变更:\n{}", toJSONString(vo, true));
            Map<String, String> resp = getAPIByProperty(VerificationAPI.class, "ejuetc.consumer.Delegation.saasVerificationAPI.url").callBackConsumer(vo);
            log.info("房源状态变更通知响应:\n{}", toJSONString(resp, true));
        } catch (Exception e) {
            log.error("通知房源状态变更异常", e);
        }
    }


    public String getSourceType() {
        return sourceType != null
                ? sourceType
                : getParent() != null
                ? getParent().getSourceType()
                : null;
    }


    public String getParentSourceId() {
        return parentSourceId != null
                ? parentSourceId
                : getParent() != null
                ? getParent().getSourceId()
                : null;
    }

    public String getOwnChildCode(Long userId) {
        return getChildren4BrokerDelegation().stream().filter(child -> child.isUser(userId)).map(Delegation::getCode).findFirst().orElse(null);
    }

    public Map<ChannelDTO.Code, ChannelDelegationVO> getChannelDelegationMap() {
        return getChannelDelegationMap(false);
    }

    public Map<ChannelDTO.Code, ChannelDelegationVO> getChannelDelegationMapPlus() {
        return getChannelDelegationMap(true);
    }

    public Map<ChannelDTO.Code, ChannelDelegationVO> getChannelDelegationMap(boolean plus) {
        Long brokerId = switch (getLevel()) {
            case COMPANY -> LoginTokenFilter.getUserId();
            case BROKER -> getBroker().getId();
            case CHANNEL -> null;
        };
        if (brokerId == null) return Map.of();

        Map<ChannelDTO.Code, ChannelDelegationVO> resultMap = new HashMap<>();
        for (ChannelDTO.Code channelCode : ChannelDTO.Code.values()) {
            ChannelDelegationVO channelVO = queryChannelBroker(plus, channelCode, brokerId);
            Delegation channelDelegation = getChildren().stream().filter(cd -> channelCode.equals(cd.getChannelCode()) && brokerId.equals(cd.getBrokerId())).findFirst().orElse(null);
            if (channelDelegation != null) {
                channelVO.setStatus(channelDelegation.getStatus());
                channelVO.setRemark(channelDelegation.getRemark());
                channelVO.setId(channelDelegation.getId());
                channelVO.setCode(channelDelegation.getCode());
            } else {
                channelVO.setStatus(DelegationDTO.Status.WITHOUT);
            }

            resultMap.put(channelCode, channelVO);
        }

        return resultMap;
    }

    private @NotNull ChannelDelegationVO queryChannelBroker(boolean plus, ChannelDTO.Code channelCode, Long brokerId) {
        ChannelDelegationVO channelVO = new ChannelDelegationVO();
        if (channelCode == ChannelDTO.Code.PRIVATE) {
            channelVO.setFreeze(false);
//            channelVO.setRemainQuantity(999999L);
//            channelVO.setBusinessQuantity(999999L);
        } else if (plus) {
            BindBrokerPO queryPO = new BindBrokerPO()
                    .setUserId(brokerId)
                    .setChannelCode(channelCode)
                    .setBusinessCode(getType());
            log.info("查询渠道经纪人信息:\n{}", toJSONString(queryPO, true));
            ApiResponse<ChannelBrokerDTO> query = getBean(BrokerAPI.class).query(queryPO);
            log.info("渠道经纪人信息:\n{}", toJSONString(query, true));
            if (query.isSucc()) {
                ChannelBrokerDTO channelBrokerDTO = query.getData();
                channelVO.setOutNickName(channelBrokerDTO.getOutNickName());
                channelVO.setFreeze(channelBrokerDTO.getFreezeCodes() != null & !channelBrokerDTO.getFreezeCodes().isEmpty());
                channelVO.setRemainQuantity(channelBrokerDTO.getRemainQuantity());
                channelVO.setBusinessQuantity(channelBrokerDTO.getBusinessQuantity());
            } else if (query.getStatus() == ResponseStatus.FAIL_BIZ) {
                channelVO.setRemainQuantity(0L);
                channelVO.setBusinessQuantity(0L);
            } else
                throw new CodingException("查询渠道经纪人信息失败:\n%s\n\n%s", toJSONString(queryPO, true), query.getMessage());
        }
        return channelVO;
    }

    public Long getBrokerId() {
        return getBroker() != null ? getBroker().getId() : null;
    }

    public List<Delegation> getChildren() {
        return switch (getLevel()) {
            case COMPANY -> getChildren4CompanyDelegation();
            case BROKER -> getChildren4BrokerDelegation();
            case CHANNEL -> Collections.emptyList();
        };
    }

    public Map<String, List<String>> getMediasMap() {
        return getMediasMap(getMedias());
    }

    public Map<String, List<String>> getMediasMap(List<MediaPO> medias) {
        Map<String, List<String>> mediasMap = new LinkedHashMap<>();
        if (medias == null) return mediasMap;
        medias.forEach(media -> {
            List<String> urls = mediasMap.computeIfAbsent(media.getSubtype(), k -> new ArrayList<>());
            if ("视频".equals(media.getSubtype())) urls.add(media.getCover());
            urls.add(media.getUrl());
        });
        return mediasMap;
    }

    protected void addMemo(String active, String message) {
        getMemo().addMemo(active, message);
        setRemark(message);
    }

    public void setRemark(String remark) {
        this.remark = remark != null && remark.length() > 1000 ? remark.substring(0, 1000) : remark;
    }

    public String getRemark() {
        return remark == null || getStatus() == UP_SUCC ? "" : remark;
    }


    public void setLabels(BusinessOpenDTO.Code type, List<String> labels) {
        if (labels == null || labels.isEmpty()) {
            this.labels = List.of((switch (type) {
                case SALE -> "随时可看";
                case RENT -> "拎包入住";
                case NEW -> "绿化率高";
            }));
        } else {
            this.labels = labels;
        }
    }


    public String getCoverUrl() {
        return medias == null || medias.isEmpty() ? null : medias.get(0).getUrl();
    }

    public abstract Delegation getParent();

    public String getParentCode() {
        return getParent() != null ? getParent().getCode() : null;
    }

    public void rebindCommunity() {
        try {
            DelegationDetail detail = getDetail();
            CommunityBind bind = detail.getCommunityBind();
            if (bind != null && detail.getCommunity() == null) {
                detail.setCommunityBind(bind.getAddress(), bind.getName());
            }
        } catch (Exception e) {
            log.error("房源[" + getId() + "]重新绑定小区失败", e);
        }
    }

    public int getMediasCount() {
        return getMedias() == null ? 0 : getMedias().size();
    }

    public abstract void upDown(Boolean channelUp, String reason, List<ChannelDTO.Code> channelCodes);
}
