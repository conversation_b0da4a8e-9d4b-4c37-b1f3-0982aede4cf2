package com.ejuetc.consumer.domain.delegation;

import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.consumer.api.delegation.ApiQueryListPO;
import com.ejuetc.consumer.api.delegation.DeleteBrokerDelegatePO;
import com.ejuetc.consumer.api.delegation.EditDelegationPO;
import com.ejuetc.consumer.web.delegation.QueryListPO2B;
import com.ejuetc.consumer.web.delegation.QueryListPO2C;
import jakarta.persistence.LockModeType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.ThreadUtils.redisLock;
import static com.ejuetc.consumer.api.dto.DelegationDTO.Level.*;
import static java.util.stream.Collectors.toMap;

public interface DelegationRpt extends JpaRepositoryImplementation<Delegation, Long> {

    @Query(value = """
            select cd
            from ChannelDelegation cd
            where cd.channelCode=:channelCode
            and cd.detail.cityId = :cityId
            and cd.detail.type = :type
            and cd.status = 'UP_SUCC'
            order by sqrt(cast(pow(cd.detail.community.longitude - :longitude, 2) as double) +
                              cast(pow(cd.detail.community.latitude - :latitude, 2) as double)),
                     cd.updateTime desc
            """)
    Page<Delegation> findOrderByLocation(ChannelDTO.Code channelCode, Long cityId, BigDecimal longitude, BigDecimal latitude, BusinessOpenDTO.Code type, Pageable pageable);

    @Query("""
            select cd
            from ChannelDelegation cd
            where cd.code = :code
            and cd.status = 'UP_SUCC'
            """)
    Optional<ChannelDelegation> findChannelUpByCode(String code);

    @Query("""
            select d
            from ChannelDelegation d
            left join fetch d.broker
            left join fetch d.detail.community c
            where d.channelCode=:channelCode and d.detail.type=:#{#request.type} and d.status = 'UP_SUCC'
            and (:#{#request.brokerId} is null or d.broker.id=:#{#request.brokerId})
            and (:#{#request.cityId} is null or d.detail.cityId=:#{#request.cityId})
            and (:#{#request.communityId} is null or d.detail.communityId=:#{#request.communityId})
            and (:#{#request.boutique} is null or d.detail.boutique=:#{#request.boutique})
            and (:#{#request.keyword} is null or
                (d.detail.communityName like concat('%',:#{#request.keyword},'%')
                or d.detail.communityAddress like concat('%',:#{#request.keyword},'%')
                or d.detail.busiName like concat('%',:#{#request.keyword},'%')
                or d.detail.townName like concat('%',:#{#request.keyword},'%')
                or d.detail.districtName like concat('%',:#{#request.keyword},'%')))
            order by
                case when :#{#request.sortStr} = 'PRICE_ASC' then d.priceTotal end asc,
                case when :#{#request.sortStr} = 'PRICE_DESC' then d.priceTotal end desc,
                case when :#{#request.sortStr} = 'AREA_ASC' then d.detail.buildingArea end asc,
                case when :#{#request.sortStr} = 'AREA_DESC' then d.detail.buildingArea end desc,
                case when :#{#request.sortStr} = 'BOUTIQUE_DESC' then d.detail.boutique end desc,
                case when :#{#request.sortStr} is null then d.updateTime end desc
            """)
    Page<Delegation> findList2C(ChannelDTO.Code channelCode,
                                QueryListPO2C request,
                                Pageable pageable
    );

    @Query("""
            select d
            from Delegation d
            where d.companyId=:companyId and d.level=:#{#request.level} and d.detail.type=:#{#request.type}
                    and (:#{#request.brokerId} is null or d.brokerId=:#{#request.brokerId})
                    and (:managerRole=true or :#{#request.levelStr} = 'COMPANY' or d.brokerId=:userId)
                    and (:#{#request.levelStr} <> 'COMPANY' or :#{#request.channelCode} is null or (:#{#request.childStatusStr}<>'WITHOUT' and exists (
                            select 1
                            from d.children4CompanyDelegation c
                            where c.channelCode = :#{#request.channelCode}
                            and c.status = :#{#request.childStatus}
                            and c.brokerId = :userId
                        )) or (:#{#request.childStatusStr}='WITHOUT' and not exists (
                            select 1
                            from d.children4CompanyDelegation c
                            where c.channelCode = :#{#request.channelCode}
                            and c.brokerId = :userId
                    )))
                    and (:#{#request.channelCode} is null or :#{#request.levelStr} <> 'BROKER' or (:#{#request.childStatusStr}<>'WITHOUT' and exists (
                            select 1
                            from d.children4BrokerDelegation c
                            where c.channelCode = :#{#request.channelCode}
                            and c.status = :#{#request.childStatus}
                        )) or (:#{#request.childStatusStr}='WITHOUT' and not exists (
                            select 1
                            from d.children4BrokerDelegation c
                            where c.channelCode = :#{#request.channelCode}
                    )))
                    and (:#{#request.channelCode} is null or :#{#request.levelStr} <> 'CHANNEL' or d.channelCode = :#{#request.channelCode})
                    and (:#{#request.keyword} is null or d.detail.communityName like concat('%',:#{#request.keyword},'%') or d.detail.communityAddress like concat('%',:#{#request.keyword},'%'))
                    and (:#{#request.delegationId} is null or d.id=:#{#request.delegationId})
                    and (:#{#request.districtId} is null or d.detail.districtId=:#{#request.districtId})
                    and (:#{#request.townName} is null or d.detail.townName=:#{#request.townName})
                    and (:#{#request.boutique} is null or d.detail.boutique = :#{#request.boutique})
                    and (:#{#request.floorCategory} is null or d.detail.floorCategory = :#{#request.floorCategory})
                    and (:#{#request.orient} is null or d.detail.orient = :#{#request.orient})
                    and (:#{#request.redo} is null or d.detail.redo = :#{#request.redo})
                    and (:#{#request.status} is null or d.status = :#{#request.status})
                    and (:#{#request.minPrice} is null or d.priceTotal >= :#{#request.minPrice})
                    and (:#{#request.maxPrice} is null or d.priceTotal <= :#{#request.maxPrice})
                    and (:#{#request.minArea} is null or d.detail.buildingArea >= :#{#request.minArea})
                    and (:#{#request.maxArea} is null or d.detail.buildingArea <= :#{#request.maxArea})
                    and (:#{#request.minFloor} is null or d.detail.currentFloor >= :#{#request.minFloor})
                    and (:#{#request.maxFloor} is null or d.detail.currentFloor <= :#{#request.maxFloor})
                    and (:#{#request.roomCount} is null or d.detail.roomCount = :#{#request.roomCount} or (:#{#request.roomCount}=6 and d.detail.roomCount>5))
                    and (:#{#request.hallCount} is null or d.detail.hallCount = :#{#request.hallCount} or (:#{#request.hallCount}=6 and d.detail.hallCount>5))
                    and (:#{#request.toiletCount} is null or d.detail.toiletCount = :#{#request.toiletCount} or (:#{#request.toiletCount}=6 and d.detail.toiletCount>5))
                    order by d.detail.boutique desc,
                        case when :#{#request.sortStr} = 'PRICE_ASC' then d.priceTotal end asc,
                        case when :#{#request.sortStr} = 'PRICE_DESC' then d.priceTotal end desc,
                        case when :#{#request.sortStr} = 'AREA_ASC' then d.detail.buildingArea end asc,
                        case when :#{#request.sortStr} = 'AREA_DESC' then d.detail.buildingArea end desc,
                        case when :#{#request.sortStr} is null then d.updateTime end desc
            """)
    Page<Delegation> findList2B(Long companyId, Long userId, Boolean managerRole, QueryListPO2B request, Pageable pageable);

    Optional<Delegation> findByCode(String code);

    @Query("""
                select d
                from ChannelDelegation d
                inner join ChannelDelegation o
                    on d.broker = o.broker
                    and d.detail.type = o.detail.type
                    and d.detail.city = o.detail.city
                    and d.channelCode = o.channelCode
                    and d.id <> o.id
                where o.code = :code
                and d.status = 'UP_SUCC'
                order by sqrt(cast(pow(d.detail.community.longitude - o.detail.community.longitude, 2) as double) +
                                     cast(pow(d.detail.community.latitude - o.detail.community.latitude, 2) as double))
            """)
    Page<Delegation> findMoreHouses(String code, Pageable pageable);

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    Delegation findAndLockBySourceTypeAndSourceId(String source, String parentOutId);

    default Delegation findExist(EditDelegationPO po, Long brokerId) {
        if (po.getId() != null) {
            return findAndLockById(po.getId());
        }

        if (po.getLevel() == CHANNEL) {
            ChannelDTO.Code channelCode = po.getChannelCode();
            if (channelCode == null)
                throw new CodingException("渠道房源必须指定渠道编码");

            if (po.getParentId() != null) {
                redisLock(50, 50, "DelegationRpt.findAndLockByBrokerDelegationIdAndChannelCode", po.getParentId(), channelCode);
                return findAndLockByBrokerDelegationIdAndChannelCode(po.getParentId(), channelCode);
            }
            if (po.getParentSourceId() != null && po.getSourceType() != null) {
                redisLock(50, 50, "DelegationRpt.findAndLockBySourceTypeAndParentSourceIdAndChannelCode", po.getSourceType(), po.getParentSourceId(), channelCode);
                return findAndLockBySourceTypeAndParentSourceIdAndChannelCode(po.getSourceType(), po.getParentSourceId(), channelCode);
            }
        }

        if (po.getLevel() == BROKER) {
            if (brokerId == null)
                throw new CodingException("经纪人房源必须指定经纪人ID");

            if (po.getParentId() != null) {
                redisLock(50, 50, "DelegationRpt.findAndLockByCompanyDelegationIdAndBrokerId", po.getParentId(), brokerId);
                return findAndLockByCompanyDelegationIdAndBrokerId(po.getParentId(), brokerId);
            }
            if (po.getParentSourceId() != null && po.getSourceType() != null) {
                redisLock(50, 50, "DelegationRpt.findAndLockBySourceTypeAndParentSourceIdAndBrokerId", po.getSourceType(), po.getParentSourceId(), brokerId);
                return findAndLockBySourceTypeAndParentSourceIdAndBrokerId(po.getSourceType(), po.getParentSourceId(), brokerId);
            }
        }

        if (po.getSourceId() != null && po.getSourceType() != null) {
            redisLock(50, 50, "DelegationRpt.findAndLockBySourceTypeAndSourceId", po.getSourceType(), po.getSourceId());
            return findAndLockBySourceTypeAndSourceId(po.getSourceType(), po.getSourceId());
        }

        return null;
    }

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    Delegation findAndLockBySourceTypeAndParentSourceIdAndBrokerId(String sourceType, String parentSourceId, Long userId);

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    Delegation findAndLockBySourceTypeAndParentSourceIdAndChannelCode(String sourceType, String parentSourceId, ChannelDTO.Code channelCode);

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    Delegation findAndLockById(Long id);

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    BrokerDelegation findAndLockByCompanyDelegationIdAndBrokerId(Long parentId, Long userId);

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    Delegation findAndLockByBrokerDelegationIdAndChannelCode(Long parentId, ChannelDTO.Code channelCode);

    @Query("""
            from ChannelDelegation d
            where d.id = :id
            """)
    ChannelDelegation findChannelDelegationById(Long id);

    @Query("""
            from BrokerDelegation d
            where (:#{#po.brokerId} is null or d.brokerId = :#{#po.brokerId})
            and (:#{#po.sourceType} is null or d.sourceType = :#{#po.sourceType})
            and (:#{#po.sourceId} is null or d.sourceId = :#{#po.sourceId})
            and (:#{#po.parentSourceId} is null or d.parentSourceId = :#{#po.parentSourceId})
            """)
    List<BrokerDelegation> findBrokerDelegations(DeleteBrokerDelegatePO po);

    Optional<Delegation> findBrokerUpByCode(String code);

    @Query("""
            from Delegation d
            where d.id in :ids
            """)
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    List<Delegation> findAndLockListByIds(Collection<Long> ids);

    @Query("""
            from Delegation d
            where d.id in :ids
            """)
    List<Delegation> findListByIds(Collection<Long> ids);

    BrokerDelegation findBrokerDelegationById(Long brokerDelegationId);

    @Query("""
            from ChannelDelegation d
            left join fetch d.broker
            left join fetch d.detail.community c
            left join fetch d.detail.communityBind cb
            where d.manualPush is not null
            and mod(d.id, :threadCount) = :threadNum
            """)
    List<ChannelDelegation> findHistoryDelegations(int threadCount, int threadNum, Pageable pageable);

    @Query("""
            from Delegation d
            where d.detail.community is null
            """)
    List<Delegation> findByCommunityIsNull();

    @Query("""
            from ChannelDelegation d
            where d.id = :id
            """)
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    ChannelDelegation findAndLockChannelDelegationById(Long id);

    @Query("""
            select d
            from Delegation d
            where d.id in :#{#po.delegationIds}
            """)
    List<Delegation> findList2Api(ApiQueryListPO po);
}
