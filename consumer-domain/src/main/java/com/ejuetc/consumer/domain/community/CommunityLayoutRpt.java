package com.ejuetc.consumer.domain.community;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.List;

public interface CommunityLayoutRpt extends JpaRepositoryImplementation<CommunityLayout, Long> {


    @Query("""
            from CommunityLayout cl
            where cl.picUrl is not null
             and cl.picUrl is null
            """)
    List<CommunityLayout> findWaitConvertUrl(Pageable pageable);
}
