package com.ejuetc.consumer.domain.community;

import com.ejuetc.commons.base.component.OssComponent;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.valueobj.Command;
import com.ejuetc.consumer.api.dto.CommunityPictureDTO;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static java.time.LocalDateTime.now;
import static java.time.format.DateTimeFormatter.ofPattern;
import static java.util.stream.Collectors.*;
import static lombok.AccessLevel.PROTECTED;

@Slf4j
@Getter
@Entity
@NoArgsConstructor
@Comment("小区图片表")
@Table(name = "tb_community_picture", indexes = @Index(name = "idx_community_picture_communityId", columnList = "community_id"))
@Where(clause = "logic_delete = 0")
public class CommunityPicture extends BaseEntity<CommunityPicture> implements Command {

    @Id
    @GeneratedValue(generator = "community_picture_id")
    @SequenceGenerator(name = "community_picture_id", sequenceName = "seq_community_picture_id")
    private Long id;

    @Setter(PROTECTED)
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "community_id", columnDefinition = "bigint(20) COMMENT '小区ID'")
    private Community community;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "community_detail_id", columnDefinition = "bigint(20) COMMENT '小区详情ID'")
    private CommunityDetail communityDetail;

    @Column(name = "url", columnDefinition = "varchar(511) COMMENT '图片地址'")
    private String url;

    @Comment("图片类型")
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private CommunityPictureDTO.Type type;

    @Column(name = "url_oss", columnDefinition = "varchar(511) COMMENT '图片地址'")
    private String url4Oss;

    public String getTypeTitle() {
        return type.getTitle();
    }

    public void convertUrl() {
        if (url4Oss != null) return;

        try {
            OssComponent ossComponent = getBean(OssComponent.class);
            this.url4Oss = ossComponent.urlConvert(
                    url,
                    "community_picture/" + communityDetail.getId() + "/" + UUID.randomUUID() + url.substring(url.lastIndexOf("."))
            );
        } catch (Exception e) {
            log.error("convertUrl error", e);
            url4Oss = "error:" + e.getMessage();
        }
    }

    @Override
    public void exec() {
        convertUrl();
    }

    public static Map<String, List<String>> getPicturesMap(List<CommunityPicture> pictures) {
        return pictures.stream().collect(
                groupingBy(CommunityPicture::getTypeTitle, mapping(CommunityPicture::getUrl4Oss, toList()))
        );
    }

}
