package com.ejuetc.consumer.domain.community;

import com.ejuetc.consumer.web.vo.CommunityTipsVO;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface CommunityService {
    CommunityBind queryOrBind(String address, String name);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    Long doBind(String address, String name);

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    void bindCommunityBeike(List<String> cities, int limitCount);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    void doBindCommunityBeike(Long beikeId);

    List<CommunityTipsVO> queryTips(String cityName, String keyword);
}
