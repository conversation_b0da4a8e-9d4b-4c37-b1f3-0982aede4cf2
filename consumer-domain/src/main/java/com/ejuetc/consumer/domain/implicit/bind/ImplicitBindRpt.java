package com.ejuetc.consumer.domain.implicit.bind;

import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.List;

import static jakarta.persistence.LockModeType.PESSIMISTIC_WRITE;

public interface ImplicitBindRpt extends JpaRepositoryImplementation<ImplicitBind, Long> {

    ImplicitBind findByBindInfoSubId(String subId);

    @Query("""
            from ImplicitBind ib
            where ib.consumerPhone = :consumerPhone
            and ib.brokerPhone = :brokerPhone
            and ib.bindInfo.result.status = 'SUCC_DONE'
            and ib.expiration > current_timestamp
            """)
    ImplicitBind findExist(String consumerPhone, String brokerPhone);

    @Query("""
            from ImplicitBind ib
            where ib.bindInfo.result.status = 'SUCC_DONE'
            and ib.unbindInfo.result.status is null
            and ib.createTime < current_timestamp - 1 hour
            and ib.refreshInfo.refreshTime is null
            """)
    @Lock(PESSIMISTIC_WRITE)
    List<ImplicitBind> findWaitUnbind();
}
