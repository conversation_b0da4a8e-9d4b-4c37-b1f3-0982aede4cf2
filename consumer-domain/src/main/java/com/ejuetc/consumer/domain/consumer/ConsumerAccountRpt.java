package com.ejuetc.consumer.domain.consumer;

import com.ejuetc.consumer.api.dto.LoginAccountDTO;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.Optional;

public interface ConsumerAccountRpt extends JpaRepositoryImplementation<ConsumerAccount, Long> {
    Optional<ConsumerAccount> findByTypeAndAccountIdent(LoginAccountDTO.Type type, String openId);

}
