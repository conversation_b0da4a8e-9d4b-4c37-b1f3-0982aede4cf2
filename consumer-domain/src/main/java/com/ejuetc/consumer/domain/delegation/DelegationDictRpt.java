package com.ejuetc.consumer.domain.delegation;

import com.ejuetc.commons.base.clazz.ClassUtils;
import com.ejuetc.commons.base.exception.ParamException;
import com.ejuetc.consumer.api.dto.DictCategory;
import com.ejuetc.consumer.api.dto.DelegationDictDTO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ejuetc.commons.base.utils.StringUtils.isBlank;
import static com.ejuetc.commons.base.utils.StringUtils.makeString;
import static java.util.stream.Collectors.toList;

public interface DelegationDictRpt extends JpaRepositoryImplementation<DelegationDict, DelegationDict.UK> {

    Map<DelegationDictDTO.Category, List<DelegationDict>> dictMap = new HashMap<>();

    default List<DelegationDict> findByCategory(List<DelegationDictDTO.Category> categories) {
        if (dictMap.isEmpty()) refresh();
        return categories.stream().map(category -> dictMap.getOrDefault(category, List.of())).flatMap(List::stream).collect(toList());
    }

    default void refresh() {
        dictMap.clear();
        dictMap.putAll(findAllOrderBySort().stream().collect(Collectors.groupingBy(DelegationDict::getCategory)));
    }

    @Query("""
            select d
            from DelegationDict d
            order by d.sort
            """)
    List<DelegationDict> findAllOrderBySort();

    default List<String> findNameByCategories(DelegationDictDTO.Category... category) {
        return findByCategory(List.of(category)).stream().map(DelegationDict::getName).collect(toList());
    }

    default void checkDictFieldsValue(Object target) {
        checkDictFieldsValue(target, target.getClass());
    }

    private void checkDictFieldsValue(Object target, Class<?> aimClass) {
        for (Field field : aimClass.getDeclaredFields()) {
            DictCategory category = field.getAnnotation(DictCategory.class);
            if (category == null) continue;

            Object value = ClassUtils.getPropertyValue(target, field.getName());
            checkDictValue(field, category.value(), value);
        }

        Class<?> superclass = aimClass.getSuperclass();
        if (!superclass.equals(Object.class))
            checkDictFieldsValue(target, superclass);
    }

    default void checkDictValue(Field field, DelegationDictDTO.Category[] category, Object value) {
        if (value == null || isBlank(String.valueOf(value))) return;

        if (!(value instanceof List<?>)) {
            value = List.of(value.toString());
        }

        List<String> names = findNameByCategories(category);
        for (Object v : (List<?>) value) {
            if (!names.contains(String.valueOf(v))) {
                throw new ParamException(field.getName(), v, "可选值:" + makeString(names, ","));
            }
        }
    }

}
