package com.ejuetc.consumer.domain.community;

import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.consumer.api.delegation.EditDelegationPO;
import com.ejuetc.consumer.api.dto.CommunityPictureDTO;
import com.ejuetc.consumer.web.vo.CommunityLayoutSummaryVO;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.StringUtils.overlapRate;
import static com.ejuetc.consumer.domain.commons.AMapUtils.callAMap;
import static java.lang.Math.max;
import static java.time.LocalDateTime.now;
import static java.util.stream.Collectors.*;

@Getter
@Entity
@NoArgsConstructor
@Comment("小区详情")
@Table(name = "tb_community_detail", indexes = @Index(name = "idx_community_detail_communityId", columnList = "community_id"))
@Where(clause = "logic_delete = 0")
public class CommunityDetail extends BaseEntity<CommunityDetail> {

    @Id
    @GeneratedValue(generator = "community_detail_id")
    @SequenceGenerator(name = "community_detail_id", sequenceName = "seq_community_detail_id")
    private Long id;

    @Column(name = "src_community_id", columnDefinition = "bigint COMMENT '原数据小区ID'")
    private Long srcCommunityId;//community_id;

    @Column(name = "province_cd", columnDefinition = "varchar(10) COMMENT '关联省份代码'")
    private String provinceCd;

    @Column(name = "province_name", columnDefinition = "varchar(20) COMMENT '关联省份名称'")
    private String provinceName;

    @Column(name = "beike_id", columnDefinition = "bigint(20) COMMENT '贝壳小区ID'")
    private Long beikeId;

    @Column(name = "city_cd", columnDefinition = "varchar(10) COMMENT '关联城市代码'")
    private String cityCd;

    @Column(name = "city_name", columnDefinition = "varchar(20) COMMENT '关联城市名称'")
    private String cityName;

    @Column(name = "district_cd", columnDefinition = "varchar(10) COMMENT '关联区域代码'")
    private String districtCd;

    @Column(name = "district_name", columnDefinition = "varchar(20) COMMENT '关联区域名称'")
    private String districtName;

    @Column(name = "block_cd", columnDefinition = "varchar(10) COMMENT '板块代码'")
    private String blockCd;

    @Column(name = "block_name", columnDefinition = "varchar(20) COMMENT '板块名称'")
    private String blockName;

    @Column(name = "community_no", columnDefinition = "varchar(20) COMMENT '小区编号'")
    private String communityNo;

    @Column(name = "home_name", columnDefinition = "varchar(30) COMMENT '大盘名称'")
    private String homeName;

    @Column(name = "zip_cd", columnDefinition = "varchar(20) COMMENT '邮编'")
    private String zipCd;

    @Column(name = "pro_service_addr", columnDefinition = "varchar(100) COMMENT '物业服务中心地址'")
    private String proServiceAddr;

    @Column(name = "property_on_time", columnDefinition = "varchar(20) COMMENT '物业工作时间'")
    private String propertyOnTime;//pro_on_time

    @Column(name = "intel_gate_flag", columnDefinition = "bit(1) COMMENT '是否安装智能道闸'")
    private Boolean intelGateFlag;//intel_gate_ind

    @Column(name = "gate_control_flag", columnDefinition = "bit(1) COMMENT '是否有门禁'")
    private Boolean gateControlFlag;//gate_control_ind

    @Column(name = "monitor_flag", columnDefinition = "bit(1) COMMENT '是否有监控'")
    private Boolean monitorFlag;//monitor_ind

    @Column(name = "security_booth_num", columnDefinition = "int(4) COMMENT '保安岗亭数'")
    private Integer securityBoothNum;

    @Column(name = "security_person_num", columnDefinition = "int(4) COMMENT '保安人员数'")
    private Integer securityPersonNum;

    @Column(name = "security_allday_flag", columnDefinition = "bit(1) COMMENT '是否24小时值守'")
    private Boolean securityAlldayFlag;//security_allday_ind

    @Column(name = "security_patrol_frequency", columnDefinition = "varchar(63) COMMENT '保安巡逻频次,小时/次'")
    private String securityPatrolFrequency;

    @Column(name = "police_networking_flag", columnDefinition = "bit(1) COMMENT '是否110联网'")
    private Boolean policeNetworkingFlag;//police_networking_ind

    @Column(name = "home_name_flag", columnDefinition = "bit(1) COMMENT '是否是总盘'")
    private Boolean homeNameFlag;//home_name_ind

    @Column(name = "address", columnDefinition = "varchar(255) COMMENT '小区地址'")
    private String address;//community_addr

    @Column(name = "community_name", columnDefinition = "varchar(255) COMMENT '小区名称'")
    private String communityName;//community_name

    @Column(name = "property_type", columnDefinition = "varchar(255) COMMENT '物业类型'")
    private String propertyType;//property_type

    @Column(name = "property_years", columnDefinition = "varchar(255) COMMENT '产权年限'")
    private String propertyYears;//property_years

    @Column(name = "comm_belong", columnDefinition = "varchar(255) COMMENT '交易权属'")
    private String commBelong;//commBelong

    @Column(name = "build_max_year", columnDefinition = "varchar(255) COMMENT '建筑年代最大'")
    private String buildMaxYear;//build_max_year

    @Column(name = "build_min_year", columnDefinition = "varchar(255) COMMENT '建成年代最小'")
    private String buildMinYear;

    @Column(name = "build_num", columnDefinition = "int COMMENT '楼栋总数'")
    private Integer buildNum;//building_num

    @Column(name = "house_num", columnDefinition = "int COMMENT '房屋总数'")
    private Integer houseNum;//house_num

    @Column(name = "developer_corp", columnDefinition = "varchar(255) COMMENT '开发公司'")
    private String developerCorp;//developer_corp

    @Column(name = "brand_corp", columnDefinition = "varchar(255) COMMENT '品牌商'")
    private String brandCorp;//brand_corp

    @Column(name = "act_area", columnDefinition = "decimal(20,2) COMMENT '占地面积'")
    private BigDecimal actArea;//act_area

    @Column(name = "build_area", columnDefinition = "decimal(20,2) COMMENT '建筑面积'")
    private BigDecimal buildArea;//build_area

    @Column(name = "building_type", columnDefinition = "varchar(255) COMMENT '建筑类型'")
    private String buildingType;//building_type

    @Column(name = "house_type", columnDefinition = "varchar(255) COMMENT '房屋类型'")
    private String houseType;//house_type

    @Column(name = "building_category", columnDefinition = "varchar(255) COMMENT '建筑类别'")
    private String buildingCategory;//building_category

    @Column(name = "property_name", columnDefinition = "varchar(255) COMMENT '物业公司'")
    private String propertyName;//property_name

    @Column(name = "property_fee", columnDefinition = "varchar(255) COMMENT '物业费'")
    private String propertyFee;//property_fee

    @Column(name = "property_phone", columnDefinition = "varchar(255) COMMENT '物业电话'")
    private String propertyPhone;//property_phone

    @Column(name = "community_close_flag", columnDefinition = "bit(1) COMMENT '小区是否封闭'")
    protected Boolean communityCloseFlag;//community_close_ind

    @Column(name = "parking_rate", columnDefinition = "varchar(255) COMMENT '车位配比率(户:车)'")
    private String parkingRate;//parking_rate

    @Column(name = "up_parking_num", columnDefinition = "int COMMENT '地上车位数'")
    private Integer upParkingNum;//up_parking_num

    @Column(name = "down_parking_num", columnDefinition = "int COMMENT '地下车位数'")
    private Integer downParkingNum;//down_parking_num

    @Column(name = "parking_num", columnDefinition = "int COMMENT '车位总数'")
    private Integer parkingNum;//parking_num

    @Column(name = "person_div_car_flag", columnDefinition = "bit(1) COMMENT '是否人车分流'")
    protected Boolean personDivCarFlag;//person_div_car_ind

    @Column(name = "parking_sale_flag", columnDefinition = "bit(1) COMMENT '是否出售产权车位'")
    protected Boolean parkingSaleFlag;//parking_sale_ind

    @Column(name = "set_parking_fee", columnDefinition = "varchar(255) COMMENT '固定停车费标准'")
    private String setParkingFee;//set_parking_fee

    @Column(name = "temp_parking_fee", columnDefinition = "varchar(255) COMMENT '临时停车费标准'")
    private String tempParkingFee;//temp_parking_fee


    @Column(name = "volume_rate", columnDefinition = "decimal(20,2) COMMENT '容积率'")
    private BigDecimal volumeRate;//volume_rate

    @Column(name = "green_rate", columnDefinition = "decimal(20,2) COMMENT '绿化率'")
    private BigDecimal greenRate;//green_rate

    @Column(name = "powerd_desc", columnDefinition = "varchar(255) COMMENT '供电描述'")
    private String powerdDesc;//powerd_desc

    @Column(name = "water_desc", columnDefinition = "varchar(255) COMMENT '供水描述'")
    private String waterDesc;//water_desc

    @Column(name = "gas_desc", columnDefinition = "varchar(255) COMMENT '供气描述'")
    private String gasDesc;//gas_desc

    @Column(name = "heating_desc", columnDefinition = "varchar(255) COMMENT '供暖情况'")
    private String heatingDesc;//heating_desc

    @Column(name = "icon_url", columnDefinition = "varchar(255) COMMENT '小区图标地址'")
    private String iconUrl;//community_pic

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "community_bind_id", columnDefinition = "bigint(20) COMMENT '小区绑定ID'")
    private CommunityBind communityBind;

    @Column(name = "verify", columnDefinition = "tinyint(1) default 0 null comment '绑定小区验证通过'")
    private boolean verify = false;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "community_id", columnDefinition = "bigint(20) COMMENT '小区ID'")
    private Community community;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "communityDetail")
    private List<CommunityLayout> layouts = new ArrayList<>();

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "communityDetail")
    private List<CommunityPicture> pictures = new ArrayList<>();

    @Setter
    @Column(name = "memo", columnDefinition = "text COMMENT '备注'")
    private String memo;

    @Column(name = "overlap_rate", columnDefinition = "decimal(20,2) COMMENT '小区名称&地址与绑定小区名称地址文字重叠度'")
    private Double overlapRate;

    @Column(name = "lat", columnDefinition = "decimal(10,6) COMMENT '(高德)纬度'")
    private BigDecimal lat;

    @Column(name = "lng", columnDefinition = "decimal(10,6) COMMENT '(高德)经度'")
    private BigDecimal lng;

    public List<String> getPictureUrls() {
        return pictures.stream().map(CommunityPicture::getUrl).toList();
    }

    public Map<String, CommunityLayoutSummaryVO> getLayoutsSummaryMap() {
        return CommunityLayout.getLayoutsSummaryMap(layouts);
    }

    public void bindCommunity(CommunityBind bind) {
        this.communityBind = bind;
        this.community = bind.getCommunity();
        if (this.community != null) {
            layouts.forEach(l -> l.setCommunity(community));
            pictures.forEach(p -> p.setCommunity(community));
            this.verify = verify(community);
            if (verify) {
                this.community.setDetail(this);
            } else {
                this.overlapRate = max(
                        max(overlapRate(communityName, community.getName()), overlapRate(community.getName(), communityName)),
                        max(overlapRate(address, community.getAddress()), overlapRate(community.getAddress(), address))
                );
            }
        }
    }

    private boolean verify(Community community) {
        return communityName.contains(community.getName())
               || communityName.contains(community.getAddress())
               || address.contains(community.getName())
               || address.contains(community.getAddress())
               || community.getName().contains(communityName)
               || community.getName().contains(address)
               || community.getAddress().contains(communityName)
               || community.getAddress().contains(address);
    }

    public String getFullAddress() {
        return cityName + districtName;
    }

    public void updateOverlapRate() {
        if (community == null || verify) return;
        this.overlapRate = max(
                max(overlapRate(communityName, community.getName()), overlapRate(community.getName(), communityName)),
                max(overlapRate(address, community.getAddress()), overlapRate(community.getAddress(), address))
        );
    }

    public Map<String, List<String>> getPicturesMap() {
        return CommunityPicture.getPicturesMap(pictures);
    }

}
