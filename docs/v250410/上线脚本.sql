UPDATE tb_region SET gov_verify_field_map = REPLACE(gov_verify_field_map, '*', '')
# select short_name,gov_verify_field_map,REPLACE(gov_verify_field_map, '*', '') from tb_region
where gov_verify_field_map like '%*%' and short_name<>'徐州'
;

# select labels,if(labels='电梯房',null,if(labels LIKE '%,电梯房%',REPLACE(labels, ',电梯房', ''),REPLACE(labels, '电梯房,', ''))) from tb_delegation
UPDATE tb_delegation
SET tag_elevator = 1,
    labels       = if(labels='电梯房',null,if(labels LIKE '%,电梯房%',REPLACE(labels, ',电梯房', ''),REPLACE(labels, '电梯房,', ''))),
    manual_push=if(status = 'UP_SUCC', 1, null)
WHERE labels LIKE '%电梯房%'
  and logic_delete = 0
;

# SELECT t.* FROM dev_consumer.tb_delegation_dict t
delete from dev_consumer.tb_delegation_dict
WHERE name='电梯房' and category like 'LABELS_%'
;