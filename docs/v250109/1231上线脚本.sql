rename table tb_dict to tb_common_dict;

rename table tb_region to tb_common_region;


rename table tb_media to tb_common_media;

rename table tb_login_account to tb_consumer_account;

rename table tb_login_history to tb_consumer_login;


alter table tb_common_region
    drop foreign key FK7p2073gpoke0x9pkpxqwd8yyu;

alter table tb_common_region
    add constraint FK7p2073gpoke0x9pkpxqwd8yyu
        foreign key (parent_id) references tb_common_region (id);

alter table tb_community
    drop foreign key FK57jw4ir1kqg9in8004ykbrklr;

alter table tb_community
    add constraint FK57jw4ir1kqg9in8004ykbrklr
        foreign key (city_id) references tb_common_region (id);

alter table tb_community
    drop foreign key FK8faxxu0xvmwf30wswmio8ro33;

alter table tb_community
    add constraint FK8faxxu0xvmwf30wswmio8ro33
        foreign key (province_id) references tb_common_region (id);

alter table tb_community
    drop foreign key FKl41pfuaxlcxrcfx70c6mtce6c;

alter table tb_community
    add constraint FKl41pfuaxlcxrcfx70c6mtce6c
        foreign key (district_id) references tb_common_region (id);

alter table tb_consumer_login
    drop foreign key FKhlnsosdtkhknbfwq5i0liafwk;

alter table tb_consumer_login
    add constraint FKhlnsosdtkhknbfwq5i0liafwk
        foreign key (region_id) references tb_common_region (id);

alter table tb_consumer_login
    drop foreign key FKkooa06qy9eeqayca8j3x8xxto;

alter table tb_consumer_login
    add constraint FKkooa06qy9eeqayca8j3x8xxto
        foreign key (account_id) references tb_consumer_account (id);

alter table tb_delegation
    drop foreign key FKe1qnxl3kmdk6u6cjt7d47kddi;

alter table tb_delegation
    add constraint FKe1qnxl3kmdk6u6cjt7d47kddi
        foreign key (city_id) references tb_common_region (id);

alter table tb_delegation
    drop foreign key FKgty2g9rbh8pkr3hnol9a8v1au;

alter table tb_delegation
    add constraint FKgty2g9rbh8pkr3hnol9a8v1au
        foreign key (district_id) references tb_common_region (id);

alter table tb_consumer_account
    drop foreign key FK1e3cyajok52chxh6hjxtaym08;

alter table tb_consumer_account
    add constraint FK1e3cyajok52chxh6hjxtaym08
        foreign key (login_history_id) references tb_consumer_login (id);

alter table tb_consumer_track
    drop foreign key FK1grlph5nt9vhgqokuj4jpk0yy;

alter table tb_consumer_track
    add constraint FK1grlph5nt9vhgqokuj4jpk0yy
        foreign key (login_account_id) references tb_consumer_account (id);

alter table tb_consumer_track
    drop foreign key FKa0jrm0hqh66gn1epseejeyvbi;

alter table tb_consumer_track
    add constraint FKa0jrm0hqh66gn1epseejeyvbi
        foreign key (login_history_id) references tb_consumer_login (id);

alter table tb_implicit_bind
    drop foreign key FKijo02ydcy4scy3geo9asqw4xp;

alter table tb_implicit_bind
    add constraint FKijo02ydcy4scy3geo9asqw4xp
        foreign key (login_id) references tb_consumer_login (id);

alter table tb_implicit_bind
    drop foreign key FKpa18e303vuqt7hsyn7y1kwvkq;

alter table tb_implicit_bind
    add constraint FKpa18e303vuqt7hsyn7y1kwvkq
        foreign key (acount_id) references tb_consumer_account (id);


