create or replace view agent_clearing_deal as
select ac.id                                                                                   as clearing_id,             -- 清分记录ID
       d.id                                                                                     as deal_id,-- 交易明细ID
       d.create_time,--  创建时间
       d.update_time,--  更新时间
       CONCAT(d.type, '')                                                                       as deal_type,-- 交易类型
       case d.type
           when 'CHANNEL_OPEN' then '渠道开通'
           when 'CLUE_BUY' then '线索购买'
           when 'CLUE_CONSUME' then '线索消耗'
           else '未知'
           end                                                                                  as deal_type_name,-- 交易类型名称
       if(d.agent_employee_flag = 1, null, d.agent_sub_type_name)                               as agent_sub_type_name,     -- 代理商类型
       if(d.agent_employee_flag = 1, null, d.city_name)                                         as city_name,--  代理城市名称&客户所在城市
       if(d.agent_employee_flag = 1, null, d.agent_user_type_name)                              as agent_user_type_name,--  代理商类型
       if(d.agent_employee_flag = 1, null, d.agent_user_id)                                     as agent_user_id,--  代理商公司/个人编号
       if(d.agent_employee_flag = 1, null, d.agent_user_name)                                   as agent_user_name,--  代理商公司/个人名称&返佣对象一
       d.business_code_name,--  产品类型
       d.pay_amount,--  售价&产品总额
       if(d.agent_employee_flag = 1, null, d.agent_price)                                       as agent_price,--  线索费结算价
       if(d.agent_employee_flag = 1, null, d.agent_discount)                                    as agent_discount,-- 结算价折扣率
       if(d.agent_employee_flag = 1, null, d.agent_discount_price)                              as agent_discount_price,-- 线索费结算价（折后）
       d.cost_price,-- 内部成本价
       if(d.agent_employee_flag = 1 or d.partner_employee_flag = 1, null, d.partner_user_name)  as partner_user_name,       -- 返佣对象二&归属合伙人名称
       if(d.agent_employee_flag = 1 or d.partner_employee_flag = 1, null, d.partner_comm_rate)  as partner_comm_rate,-- 返佣对象二比例
       if(d.agent_employee_flag = 1 or d.partner_employee_flag = 1, null, d.partner_user_id)    as partner_user_id,         -- 归属合伙人ID
       d.crm_order_no,                                                                                                      -- 产品订单ID
       if(d.type = 'CLUE_CONSUME', d.order_num, null)                                           as order_num,-- 线索ID
       d.children_amount,-- 已用金额
       d.children_quantity                                                                      as clue_quantity,-- 产品线索数
       d.pay_time,-- 订单支付日期&交付完成日期&成销日期
       if(d.agent_employee_flag = 1, null, ac.report_pre_tax)                                  as agent_report_pre_tax,-- 应计返佣一税前
       if(d.agent_employee_flag = 1, null, ac.report_after_tax)                                as agent_report_after_tax,-- 应计返佣一税后
       if(d.agent_employee_flag = 1 or d.partner_employee_flag = 1, null, pc.report_pre_tax)   as partner_report_pre_tax,-- 应计返佣二税前
       if(d.agent_employee_flag = 1 or d.partner_employee_flag = 1, null, pc.report_after_tax) as partner_report_after_tax,-- 应计返佣二税后
       ac.fact_pre_tax                                                                         as agent_fact_pre_tax,-- 实计返佣一税前
       ac.fact_after_tax                                                                       as agent_fact_after_tax,-- 实计返佣一税后
       ac.fact_time                                                                            as agent_fact_time,-- 实计返佣一时间
       pc.fact_pre_tax                                                                         as partner_fact_pre_tax,-- 实计返佣二税前
       pc.fact_after_tax                                                                       as partner_fact_after_tax,-- 实计返佣二税后
       pc.fact_time                                                                            as partner_fact_time,-- 实计返佣二时间
       d.broker_user_id,-- 客户编号
       d.broker_user_name,-- 客户名称
       d.broker_contract_sub_types_name,-- 客户身份
       d.standard_price,-- 平台定价
       ac.excess_amount,-- 超出售价金额
       ac.excess_comm_ratio                                                                    as agent_excess_comm_ratio,-- 超出账号费返佣比例
       ac.excess_comm                                                                          as agent_excess_comm,-- 超出账号费返佣金额
       ac.comm                                                                                 as agent_comm,-- 代理商总返佣金额
       d.tm_forward,-- 是否天猫结转
       d.tm_bail,-- 天猫保证金
       d.tm_new_trade,-- 天猫新交易
       d.tm_service_fee,-- 天猫服务费
       d.employee_name,-- 业务员名称
       d.up_delegate_flag,-- 是否上翻房源
       d.up_delegate_time,-- 上翻房源时间
       d.employee_no,-- 业务员工号
       ac.crm_split agent_crm_split, -- 返佣对象一:CRM是否逐月拆分
       pc.crm_split partner_crm_split, -- 返佣对象二:CRM是否逐月拆分
       null agent_invoice_check_time, -- 返佣对象一:发票审核时间
       null partner_invoice_check_time -- 返佣对象二:发票审核时间
from tb_deal d
         left join tb_clearing ac on ac.id = d.agent_clearing_id
         left join tb_clearing pc on pc.id = d.partner_clearing_id
where d.status = 'CLEARED'
  and d.test_flag = 0

union all

select c.id                                                                                                                 as clearing_id,             -- 清分记录ID
       d.id                                                                                                                 as deal_id,-- 交易明细ID
       d.create_time,--  创建时间
       d.update_time,--  更新时间
       CONCAT(d.type, '')                                                                                                   as deal_type,-- 交易类型
       case d.type
           when 'CHANNEL_OPEN' then '渠道开通'
           when 'CLUE_BUY' then '线索购买'
           when 'CLUE_CONSUME' then '线索消耗'
           else '未知'
           end                                                                                                              as deal_type_name,-- 交易类型名称
       if(d.agent_employee_flag = 1, null, d.agent_sub_type_name)                                                           as agent_sub_type_name,     -- 代理商类型
       if(d.agent_employee_flag = 1, null, d.city_name)                                                                     as city_name,--  代理城市名称&客户所在城市
       if(d.agent_employee_flag = 1, null, d.agent_user_type_name)                                                          as agent_user_type_name,--  代理商类型
       if(d.agent_employee_flag = 1, null, d.agent_user_id)                                                                 as agent_user_id,--  代理商公司/个人编号
       if(d.agent_employee_flag = 1, null, d.agent_user_name)                                                               as agent_user_name,--  代理商公司/个人名称&返佣对象一
       d.business_code_name,--  产品类型
       d.pay_amount,--  售价&产品总额
       if(d.agent_employee_flag = 1, null, d.agent_price)                                                                   as agent_price,--  线索费结算价
       if(d.agent_employee_flag = 1, null, d.agent_discount)                                                                as agent_discount,-- 结算价折扣率
       if(d.agent_employee_flag = 1, null, d.agent_discount_price)                                                          as agent_discount_price,-- 线索费结算价（折后）
       d.cost_price,-- 内部成本价
       if(d.agent_employee_flag = 1 or d.partner_employee_flag = 1, null, d.partner_user_name)                              as partner_user_name,       -- 返佣对象二&归属合伙人名称
       if(d.agent_employee_flag = 1 or d.partner_employee_flag = 1, null, d.partner_comm_rate)                              as partner_comm_rate,-- 返佣对象二比例
       if(d.agent_employee_flag = 1 or d.partner_employee_flag = 1, null, d.partner_user_id)                                as partner_user_id,         -- 归属合伙人ID
       d.crm_order_no,                                                                                                                                  -- 产品订单ID
       if(d.type = 'CLUE_CONSUME', d.order_num, null)                                                                       as order_num,-- 线索ID
       d.children_amount,-- 已用金额
       d.children_quantity                                                                                                  as clue_quantity,-- 产品线索数
       d.pay_time,-- 订单支付日期&交付完成日期&成销日期
       if(c.type = 'AGENT', if(d.agent_employee_flag = 1, null, c.report_pre_tax), null)                                    as agent_report_pre_tax,-- 应计返佣一税前
       if(c.type = 'AGENT', if(d.agent_employee_flag = 1, null, c.report_after_tax), null)                                  as agent_report_after_tax,-- 应计返佣一税后
       if(c.type = 'PARTNER', if(d.agent_employee_flag = 1 or d.partner_employee_flag = 1, null, c.report_pre_tax), null)   as partner_report_pre_tax,-- 应计返佣二税前
       if(c.type = 'PARTNER', if(d.agent_employee_flag = 1 or d.partner_employee_flag = 1, null, c.report_after_tax), null) as partner_report_after_tax,-- 应计返佣二税后
       if(c.type = 'AGENT', c.fact_pre_tax, null)                                                                           as agent_fact_pre_tax,-- 实计返佣一税前
       if(c.type = 'AGENT', c.fact_after_tax, null)                                                                         as agent_fact_after_tax,-- 实计返佣一税后
       if(c.type = 'AGENT', c.fact_time, null)                                                                              as agent_fact_time,-- 实计返佣一时间
       if(c.type = 'PARTNER', c.fact_pre_tax, null)                                                                         as partner_fact_pre_tax,-- 实计返佣二税前
       if(c.type = 'PARTNER', c.fact_after_tax, null)                                                                       as partner_fact_after_tax,-- 实计返佣二税后
       if(c.type = 'PARTNER', c.fact_time, null)                                                                            as partner_fact_time,-- 实计返佣二时间
       d.broker_user_id,-- 客户编号
       d.broker_user_name,-- 客户名称
       d.broker_contract_sub_types_name,-- 客户身份
       d.standard_price,-- 平台定价
       if(c.type = 'AGENT', c.excess_amount, null),-- 超出售价金额
       if(c.type = 'AGENT', c.excess_comm_ratio, null)                                                                      as agent_excess_comm_ratio,-- 超出账号费返佣比例
       if(c.type = 'AGENT', c.excess_comm, null)                                                                            as agent_excess_comm,-- 超出账号费返佣金额
       if(c.type = 'AGENT', c.comm, null)                                                                                   as agent_comm,-- 代理商总返佣金额
       d.tm_forward,-- 是否天猫结转
       d.tm_bail,-- 天猫保证金
       d.tm_new_trade,-- 天猫新交易
       d.tm_service_fee,-- 天猫服务费
       d.employee_name,-- 业务员名称
       d.up_delegate_flag,-- 是否上翻房源
       d.up_delegate_time,-- 上翻房源时间
       d.employee_no,-- 业务员工号,
       if(c.type = 'AGENT', c.crm_split,null) agent_crm_split, -- 返佣对象一:CRM是否逐月拆分
       if(c.type = 'PARTNER', c.crm_split,null) partner_crm_split, -- 返佣对象二:CRM是否逐月拆分
       if(c.type = 'AGENT', s.invoice_check_time,null) agent_invoice_check_time, -- 返佣对象一:发票审核时间
       if(c.type = 'PARTNER', s.invoice_check_time,null) partner_invoice_check_time -- 返佣对象二:发票审核时间
from tb_clearing c
         left join tb_deal d on d.id = c.deal_id
         left join tb_statement s on s.id = c.statement_id
where c.flag_diff = 1
  and c.logic_delete = 0
  and d.test_flag = 0
   and s.invoice_check_time is not null
;

-- 只输出已生成结算单的