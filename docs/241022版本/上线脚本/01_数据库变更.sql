-- 打开手工提交事务
drop table tb_deal;

update tb_contract c
set c.sub_type='AGENT_NORMAL'
where c.template_id in (1,4)
  and type='AGENT'
;
update tb_contract c
set c.sub_type='AGENT_XIANYU_EXCLUSIVE'
where c.template_id is null
  and type='AGENT'
;

update tb_contract c
set c.sub_type = c.type
where c.type in ('PARTNER','BROKER') and sub_type is null;

update tb_contract
set commission_rate='0.2'
where type='PARTNER';

update tb_contract
set contract_begin_time = '2024-09-01 00:00:00'
where 1=1;

update tb_user
set tax_rate = if(type='COMPANY',0.06,0)
where 1=1;

alter table tb_user
    modify contact_address varchar(255) null comment '联系地址';

# delete from tb_account where version=100;

insert into tb_account (id,version,type, status,sort,self, user_id, account_no,account_name,bank_address,bank_province,bank_city)
select NEXTVAL(seq_account_id),100,'BANK_ACCOUNT','BIND_SUCC',0,1,u.id,u.bank_account,u.company_name,u.bank_address,u.bank_province,u.bank_city
from tb_user u
where u.type='COMPANY' and u.bank_account is not null;

alter table tb_user
    drop column bank_account;

alter table tb_user
    drop column bank_address;

alter table tb_user
    drop column bank_city;

alter table tb_user
    drop column bank_province;

alter table tb_user
    drop column certified;

alter table tb_user
    drop column bind_xy;

alter table tb_user
    drop column join_comm;

update tb_user
set employee_flag = 1
where id in (9095213084541236736,9095376821982095616,9095325246068674816);

update tb_deal d
set d.up_delegate_flag=1,d.up_delegate_time=d.pay_time
where id in (AAAAAAAAAAAAAAAAA);

delete FROM tb_price t WHERE deal_type='YEARLY';

alter table tb_price
    modify deal_type enum ('CHANNEL', 'CONSUME_CLUE', 'YEARLY', 'CHANNEL_OPEN', 'CLUE_BUY', 'CLUE_CONSUME') not null;


update tb_price p
set p.deal_type=case p.deal_type
               when 'CHANNEL' then 'CHANNEL_OPEN'
               when 'CONSUME_CLUE' then 'CLUE_CONSUME'
            else P.deal_type
    end
;

alter table tb_price
    modify deal_type enum ('CHANNEL_OPEN','CLUE_CONSUME') not null;


delete from tb_price p
where p.id in (select distinct tp.price_id from tb_template_price tp);

delete from tb_contract_price cp
where cp.price_id in (select distinct tp.price_id from tb_template_price tp);

truncate table tb_template_price;

insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,700,350,350,'440100','广州');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,700,350,350,'310100','上海');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,700,350,350,'120100','天津');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,600,300,300,'510100','成都');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,500,250,250,'320100','南京');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,500,250,250,'440600','佛山');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,500,250,250,'500100','重庆');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,700,350,350,'440300','深圳');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,700,350,350,'110100','北京');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,500,250,250,'320500','苏州');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,400,200,200,'441900','东莞');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,400,200,200,'340100','合肥');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,400,200,200,'350100','福州');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'530100','昆明');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'360100','南昌');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'210100','沈阳');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'430100','长沙');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'210200','大连');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'320200','无锡');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'230100','哈尔滨');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'650100','乌鲁木齐');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'440700','江门');

insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,12,10,10,'440100','广州');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,12,9,9,'310100','上海');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,9,6,6,'120100','天津');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,9,7,7,'510100','成都');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,9,6,6,'320100','南京');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,6,4,4,'440600','佛山');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,6,4,4,'500100','重庆');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,8,6,6,'440300','深圳');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,8,6,6,'110100','北京');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,6,4,4,'320500','苏州');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,6,4,4,'441900','东莞');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,6,4,4,'340100','合肥');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,6,4,4,'350100','福州');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4,3,3,'530100','昆明');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4,3,3,'360100','南昌');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4,3,3,'210100','沈阳');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4,3,3,'430100','长沙');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4,3,3,'210200','大连');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4,3,3,'320200','无锡');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,3,2,2,'230100','哈尔滨');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,3,2,2,'650100','乌鲁木齐');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'SALE','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,3,2,2,'440700','江门');

insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'440100','广州');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'310100','上海');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2024-10-22 23:59:59', 1, 0.9,1,400,150,150,'120100','天津');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-10-23 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,400,200,200,'120100','天津');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'510100','成都');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'320100','南京');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'440600','佛山');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'500100','重庆');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'440300','深圳');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2024-10-22 23:59:59', 1, 0.9,1,300,150,150,'110100','北京');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-10-23 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,400,200,200,'110100','北京');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'320500','苏州');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'441900','东莞');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'340100','合肥');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'350100','福州');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'530100','昆明');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'360100','南昌');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'210100','沈阳');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'430100','长沙');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'210200','大连');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'320200','无锡');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'230100','哈尔滨');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'650100','乌鲁木齐');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CHANNEL_OPEN','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,300,150,150,'440700','江门');

insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'440100','广州');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'310100','上海');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'120100','天津');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'510100','成都');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'320100','南京');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'440600','佛山');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'500100','重庆');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'440300','深圳');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'110100','北京');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'320500','苏州');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'441900','东莞');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'340100','合肥');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'350100','福州');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'530100','昆明');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'360100','南昌');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'210100','沈阳');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'430100','长沙');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'210200','大连');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'320200','无锡');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'230100','哈尔滨');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'650100','乌鲁木齐');
insert into tb_price(id,version, business_code, channel_code, deal_type,begin_time,end_time,agent_discount,excess_comm_ratio,normal_comm_rate,standard_price,agent_discount_price,agent_price, city_code, city_name) values(NEXTVAL(seq_price_id),100,'RENT','XIANYU','CLUE_CONSUME','2024-09-01 00:00:00', '2025-12-31 23:59:59', 1, 0.9,1,4.5,3.5,3.5,'440700','江门');

update tb_price p
set begin_time='2024-09-01 00:00:00',end_time='2025-12-31 23:59:59'
;

insert into tb_template_price (template_id, price_id)
select t.id,p.id from tb_template t
left join tb_price p on p.version=100
where t.type='AGENT'
;

insert into tb_contract_price (contract_id, price_id)
select c.id,tp.price_id from tb_contract c
    inner join tb_template_price tp on tp.template_id=c.template_id
# where c.id=50086
;

update tb_price p
set standard_price=9,agent_price=6,agent_discount=0.8,agent_discount_price=4.8,normal_comm_rate=1,excess_comm_ratio=1
where city_code = '410100' and deal_type='CLUE_CONSUME' and business_code='SALE';
update tb_price p
set standard_price=4.5,agent_price=3.5,agent_discount=0.8,agent_discount_price=2.8,normal_comm_rate=1,excess_comm_ratio=1
where city_code = '410100' and deal_type='CLUE_CONSUME' and business_code='RENT';
update tb_price p
set standard_price=600,agent_price=300,agent_discount=1,agent_discount_price=300,normal_comm_rate=1,excess_comm_ratio=0.9
where city_code = '410100' and deal_type='CHANNEL_OPEN' and business_code='SALE';
update tb_price p
set standard_price=300,agent_price=150,agent_discount=1,agent_discount_price=150,normal_comm_rate=1,excess_comm_ratio=0.9
where city_code = '410100' and deal_type='CHANNEL_OPEN' and business_code='RENT';


-- 提交事务