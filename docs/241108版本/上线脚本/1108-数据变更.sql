#################################################### 自动更新表结构之前 ######################################################################

alter table tb_deal
    drop foreign key FK5m3x394j2961667h6jt9juu7k;

alter table tb_deal
    drop column clearing_id;

alter table tb_deal
    drop column agent_tax_rate;

alter table tb_deal
    drop column partner_tax_rate;

drop table tb_clearing_item;
drop table tb_clearing;
drop table tb_statement;

#################################################### 自动更新表结构之后 ######################################################################
alter table tb_template
    drop column avoid_period;

alter table tb_template
    modify doc_param json null comment '生成合同文件参数模板';

alter table tb_template
    modify flow_param json null comment '发起签名参数模板';

alter table tb_template
    drop column target_amount;

alter table tb_template
    drop column target_person;



update tb_template
set type = contract_type
where type is null;

alter table tb_template
    drop column contract_type;

update tb_esign
set target_id=contract_id,
    target_type='AgentContract'
    `status`=if(callback_result is null,'SIGN_ING',if(callback_result='2','COMPLE','REJECT'))
;

# update tb_esign
# set `status`=if(callback_result is null,'SIGN_ING',if(callback_result='2','COMPLE','REJECT'))
# ;

alter table tb_esign
    add constraint tb_esign_uq_target
        unique (target_type, target_id);

alter table tb_esign
    drop foreign key contract_id;

alter table tb_esign
    drop column contract_id;

alter table tb_account modify type enum ('BANK_ACCOUNT', 'YZH_ACCOUNT') not null comment '账户类型';


update tb_deal
set goods_name = concat('闲鱼',if(business_code='SALE','二手房','租房'),'端口账号费')
where type='CHANNEL_OPEN';

update tb_deal
set broker_user_id = broker_buyer_id
;

alter table tb_deal
    drop foreign key FKcf1h4cqf013icunbwcune21dg;
alter table tb_deal
    drop column broker_buyer_id;
alter table tb_deal
    drop column broker_buyer_name;

# update tb_deal
# set status = 'CONFIG',statement_time='2024-11-01 00:00:00',agent_clearing_id=null,partner_clearing_id=null
# where status='CLEARED';

truncate table tb_statement_account;
truncate table tb_statement_log;
truncate table tb_statement;
truncate table tb_clearing;

update tb_deal
set status = 'INIT',statement_time='2024-11-01 00:00:00',agent_clearing_id=null,partner_clearing_id=null
where status='CLEARED';

