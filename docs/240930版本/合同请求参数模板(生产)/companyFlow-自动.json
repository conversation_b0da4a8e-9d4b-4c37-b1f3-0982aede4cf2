{"docs": [{"fileId": "${docId}", "fileName": "${docName}"}], "signFlowConfig": {"signFlowTitle": "乐居好房信息发布服务合作协议", "autoFinish": true, "notifyUrl": "https://agent-etc.ebaas.com/gateway/esign/callback", "redirectConfig": {"redirectUrl": "http://www.xx.cn/"}}, "signers": [{"signConfig": {"signOrder": 1}, "noticeConfig": {"noticeTypes": "1"}, "signerType": 1, "orgSignerInfo": {"orgName": "${user.certName}", "orgInfo": {"orgIDCardNum": "${user.certNum}", "orgIDCardType": "CRED_ORG_USCC"}, "transactorInfo": {"psnAccount": "${user.contactMobile}", "psnInfo": {"psnName": "${user.contactName}"}}}, "signFields": [{"customBizNum": "userSign", "fileId": "${docId}", "normalSignFieldConfig": {"signFieldStyle": 1, "signFieldPosition": {"positionPage": "5", "positionX": 423, "positionY": 460}}, "signDateConfig": {"showSignDate": 1}}]}, {"signConfig": {"signOrder": 2}, "signerType": 1, "signFields": [{"customBizNum": "platformSign", "fileId": "${docId}", "normalSignFieldConfig": {"signFieldStyle": 1, "autoSign": true, "assignedSealId": "e65fb735-014e-4019-b8a2-8725240eb2d4", "signFieldPosition": {"positionPage": "5", "positionX": 423, "positionY": 593}}, "signDateConfig": {"showSignDate": 1}}]}]}