{"docs": [{"fileId": "${fileId}", "fileName": "${fileName}"}], "signFlowConfig": {"signFlowTitle": "经纪代理合同", "autoFinish": true, "notifyUrl": "https://agent-test.ebaas.com/gateway/esign/callback", "redirectConfig": {"redirectUrl": "http://www.xx.cn/"}}, "signers": [{"signConfig": {"signOrder": 1}, "noticeConfig": {"noticeTypes": "1"}, "signerType": 0, "psnSignerInfo": {"psnAccount": "${operatorMp}", "psnInfo": {"psnName": "${operatorName}"}}, "signFields": [{"customBizNum": "firstSign", "fileId": "${fileId}", "normalSignFieldConfig": {"signFieldStyle": 1, "signFieldPosition": {"positionPage": "1", "positionX": 163, "positionY": 291}}, "signDateConfig": {"showSignDate": 1}}]}, {"signConfig": {"signOrder": 2}, "signerType": 1, "signFields": [{"customBizNum": "secondSign", "fileId": "${fileId}", "normalSignFieldConfig": {"autoSign": true, "signFieldStyle": 1, "assignedSealId": "e65fb735-014e-4019-b8a2-8725240eb2d4", "signFieldPosition": {"positionPage": "1", "positionX": 454, "positionY": 294}}, "signDateConfig": {"showSignDate": 1}}]}]}