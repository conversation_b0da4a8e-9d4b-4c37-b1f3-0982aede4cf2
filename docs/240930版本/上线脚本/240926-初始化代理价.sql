INSERT INTO tb_template (id, create_time, logic_delete, update_time, version, avoid_period, contract_period, contract_type, doc_param, flow_param, name, target_amount, target_person, user_type, contract_end_time) VALUES (1, '2024-09-23 17:22:33.321381', false, '2024-09-26 13:47:56.425226', 17, 'P3M', 'P1Y', 'AGENT', '{
  "docTemplateId": "8000a949e38646388b469f054cbbd6f7",
  "fileName": "测试模板.pdf",
  "components": [
    {
      "componentKey": "firstName",
      "componentValue": "${firstName}"
    },
    {
      "componentKey": "firstNum",
      "componentValue": "${firstNum}"
    },
    {
      "componentKey": "secondName",
      "componentValue": "壹家易（上海）网络科技有限公司"
    },
    {
      "componentKey": "secondNum",
      "componentValue": "91310120MA1HL5H27C"
    },
    {
      "componentKey": "targetAmount",
      "componentValue": "${targetAmount}"
    },
    {
      "componentKey": "targetPerson",
      "componentValue": "${targetPerson}"
    },
    {
      "componentKey": "contractBegin",
      "componentValue": "${contractBegin}"
    },
    {
      "componentKey": "contractEnd",
      "componentValue": "${contractEnd}"
    },
    {
      "componentKey": "avoidBegin",
      "componentValue": "${avoidBegin}"
    },
    {
      "componentKey": "avoidEnd",
      "componentValue": "${avoidEnd}"
    },
    {
      "componentKey": "invoiceName",
      "componentValue": "上海添玑好房网络服务有限公司"
    },
    {
      "componentKey": "invoiceNum",
      "componentValue": "91310000676211967N"
    },
    {
      "componentKey": "invoiceAddress",
      "componentValue": "上海广中路788号引力楼9楼"
    },
    {
      "componentKey": "invoiceTel",
      "componentValue": "021-********"
    },
    {
      "componentKey": "invoiceBankName",
      "componentValue": "招商银行上海分行大宁支行"
    },
    {
      "componentKey": "invoiceBankAccount",
      "componentValue": "***************"
    },
    {
      "componentKey": "prices",
      "componentValue": "${prices}"
    }
  ]
}', '{
  "docs": [
    {
      "fileId": "${fileId}",
      "fileName": "${fileName}"
    }
  ],
  "signFlowConfig": {
    "signFlowTitle": "经纪代理合同",
    "autoFinish": true,
    "notifyUrl": "https://agent-etc.ebaas.com/gateway/esign/callback",
    "redirectConfig": {
      "redirectUrl": "http://www.xx.cn/"
    }
  },
  "signers": [
    {
      "signConfig": {
        "signOrder": 1
      },
      "noticeConfig": {
        "noticeTypes": "1"
      },
      "signerType": 1,
      "orgSignerInfo": {
        "orgName": "${userCertName}",
        "orgInfo": {
          "orgIDCardNum": "${userCertNum}",
          "orgIDCardType": "CRED_ORG_USCC"
        },
        "transactorInfo": {
          "psnAccount": "${operatorMp}",
          "psnInfo": {
            "psnName": "${operatorName}"
          }
        }
      },
      "signFields": [
        {
          "customBizNum": "firstSign",
          "fileId": "${fileId}",
          "normalSignFieldConfig": {
            "signFieldStyle": 1,
            "signFieldPosition": {
              "positionPage": "1",
              "positionX": 163,
              "positionY": 291
            }
          },
          "signDateConfig": {
            "showSignDate": 1
          }
        }
      ]
    },
    {
      "signConfig": {
        "signOrder": 2
      },
      "signerType": 1,
      "signFields": [
        {
          "customBizNum": "secondSign",
          "fileId": "${fileId}",
          "normalSignFieldConfig": {
            "autoSign": true,
            "signFieldStyle": 1,
            "assignedSealId": "e65fb735-014e-4019-b8a2-8725240eb2d4",
            "signFieldPosition": {
              "positionPage": "1",
              "positionX": 454,
              "positionY": 294
            }
          },
          "signDateConfig": {
            "showSignDate": 1
          }
        }
      ]
    }
  ]
}', 'test', 100000.00, 2000, 'COMPANY', null);
INSERT INTO tb_template (id, create_time, logic_delete, update_time, version, avoid_period, contract_period, contract_type, doc_param, flow_param, name, target_amount, target_person, user_type, contract_end_time) VALUES (2, '2024-09-23 18:39:43.497214', false, '2024-09-23 18:39:43.497214', 0, null, 'P100Y', 'PARTNER', null, null, 'test', 0.00, 0, null, null);
INSERT INTO tb_template (id, create_time, logic_delete, update_time, version, avoid_period, contract_period, contract_type, doc_param, flow_param, name, target_amount, target_person, user_type, contract_end_time) VALUES (3, '2024-09-23 18:41:33.958166', false, '2024-09-23 18:41:33.958166', 0, null, 'P100Y', 'BROKER', null, null, 'test', 0.00, 0, null, null);
INSERT INTO tb_template (id, create_time, logic_delete, update_time, version, avoid_period, contract_period, contract_type, doc_param, flow_param, name, target_amount, target_person, user_type, contract_end_time) VALUES (4, '2024-09-25 10:42:19.606767', false, '2024-09-26 10:47:58.695070', 8, 'P3M', 'P1Y', 'AGENT', '{
  "docTemplateId": "8000a949e38646388b469f054cbbd6f7",
  "fileName": "测试模板.pdf",
  "components": [
    {
      "componentKey": "firstName",
      "componentValue": "${firstName}"
    },
    {
      "componentKey": "firstNum",
      "componentValue": "${firstNum}"
    },
    {
      "componentKey": "secondName",
      "componentValue": "壹家易（上海）网络科技有限公司"
    },
    {
      "componentKey": "secondNum",
      "componentValue": "91310120MA1HL5H27C"
    },
    {
      "componentKey": "targetAmount",
      "componentValue": "${targetAmount}"
    },
    {
      "componentKey": "targetPerson",
      "componentValue": "${targetPerson}"
    },
    {
      "componentKey": "contractBegin",
      "componentValue": "${contractBegin}"
    },
    {
      "componentKey": "contractEnd",
      "componentValue": "${contractEnd}"
    },
    {
      "componentKey": "avoidBegin",
      "componentValue": "${avoidBegin}"
    },
    {
      "componentKey": "avoidEnd",
      "componentValue": "${avoidEnd}"
    },
    {
      "componentKey": "invoiceName",
      "componentValue": "上海添玑好房网络服务有限公司"
    },
    {
      "componentKey": "invoiceNum",
      "componentValue": "91310000676211967N"
    },
    {
      "componentKey": "invoiceAddress",
      "componentValue": "上海广中路788号引力楼9楼"
    },
    {
      "componentKey": "invoiceTel",
      "componentValue": "021-********"
    },
    {
      "componentKey": "invoiceBankName",
      "componentValue": "招商银行上海分行大宁支行"
    },
    {
      "componentKey": "invoiceBankAccount",
      "componentValue": "***************"
    },
    {
      "componentKey": "prices",
      "componentValue": "${prices}"
    }
  ]
}', '{
  "docs": [
    {
      "fileId": "${fileId}",
      "fileName": "${fileName}"
    }
  ],
  "signFlowConfig": {
    "signFlowTitle": "经纪代理合同",
    "autoFinish": true,
    "notifyUrl": "https://agent-etc.ebaas.com/gateway/esign/callback",
    "redirectConfig": {
      "redirectUrl": "http://www.xx.cn/"
    }
  },
  "signers": [
    {
      "signConfig": {
        "signOrder": 1
      },
      "noticeConfig": {
        "noticeTypes": "1"
      },
      "signerType": 0,
      "psnSignerInfo": {
        "psnAccount": "${operatorMp}",
        "psnInfo": {
          "psnName": "${operatorName}"
        }
      },
      "signFields": [
        {
          "customBizNum": "firstSign",
          "fileId": "${fileId}",
          "normalSignFieldConfig": {
            "signFieldStyle": 1,
            "signFieldPosition": {
              "positionPage": "1",
              "positionX": 163,
              "positionY": 291
            }
          },
          "signDateConfig": {
            "showSignDate": 1
          }
        }
      ]
    },
    {
      "signConfig": {
        "signOrder": 2
      },
      "signerType": 1,
      "signFields": [
        {
          "customBizNum": "secondSign",
          "fileId": "${fileId}",
          "normalSignFieldConfig": {
            "autoSign": true,
            "signFieldStyle": 1,
            "assignedSealId": "e65fb735-014e-4019-b8a2-8725240eb2d4",
            "signFieldPosition": {
              "positionPage": "1",
              "positionX": 454,
              "positionY": 294
            }
          },
          "signDateConfig": {
            "showSignDate": 1
          }
        }
      ]
    }
  ]
}', 'test', 100000.00, 2000, 'PERSONAL', null);


INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',350 , '110100', '北京');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',300 , '510100', '成都');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',150 , '210200', '大连');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',200 , '441900', '东莞');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',250 , '440600', '佛山');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',200 , '350100', '福州');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',350 , '440100', '广州');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',150 , '230100', '哈尔滨');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',300 , '330100', '杭州');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',200 , '340100', '合肥');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',150 , '440700', '江门');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',150 , '530100', '昆明');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',150 , '360100', '南昌');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',250 , '320100', '南京');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',350 , '310100', '上海');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',350 , '440300', '深圳');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',150 , '210100', '沈阳');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',250 , '320500', '苏州');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',300 , '120100', '天津');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',150 , '650100', '乌鲁木齐');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',150 , '320200', '无锡');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',150 , '430100', '长沙');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CHANNEL_OPEN',250 , '500100', '重庆');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',6 , '110100', '北京');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',7 , '510100', '成都');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',3 , '210200', '大连');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',4 , '441900', '东莞');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',4 , '440600', '佛山');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',4 , '350100', '福州');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',10 , '440100', '广州');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',2 , '230100', '哈尔滨');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',6 , '330100', '杭州');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',4 , '340100', '合肥');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',2 , '440700', '江门');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',3 , '530100', '昆明');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',3 , '360100', '南昌');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',6 , '320100', '南京');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',9 , '310100', '上海');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',6 , '440300', '深圳');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',3 , '210100', '沈阳');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',4 , '320500', '苏州');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',6 , '120100', '天津');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',2 , '650100', '乌鲁木齐');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',3 , '320200', '无锡');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',3 , '430100', '长沙');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),100, 'SALE', 'XIANYU', 'CONSUME_CLUE',4 , '500100', '重庆');

INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',4.5  , '441900', '东莞');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',4.5  , '450600', '防城港');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',4.5  , '520100', '贵阳');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',4.5  , '450300', '桂林');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',4.5  , '460100', '海口');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',4.5  , '310100', '上海');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',4.5  , '350200', '厦门');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',4.8  , '350100', '福州');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',4.8  , '440300', '深圳');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',5    , '110100', '北京');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',5    , '510100', '成都');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',5    , '440100', '广州');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',5    , '330100', '杭州');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',5    , '340100', '合肥');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',5    , '441300', '惠州');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',5    , '530100', '昆明');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',5    , '330200', '宁波');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',5    , '320500', '苏州');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',5    , '120100', '天津');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',5    , '610100', '西安');
INSERT INTO tb_price (id,version, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id),101, 'RENT', 'XIANYU', 'CONSUME_CLUE',5    , '411500', '信阳');




insert into tb_template_price (template_id, price_id)
select 1,p.id from tb_price p
WHERE p.version=101
;

insert into tb_template_price (template_id, price_id)
select 4,p.id from tb_price p
WHERE p.version=101
;

# delete
# from tb_contract_price
# where 1=1;

insert tb_contract_price(contract_id,price_id)
select c.id,p.id
from tb_price p
left join tb_contract c on c.type='AGENT' and c.status='EFFECTIVE'
where p.version=101
;

# INSERT INTO tb_price (id, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id), 'SALE', 'XIANYU', 'CHANNEL_OPEN', , '', '');
# INSERT INTO tb_price (id, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id), 'SALE', 'XIANYU', 'CONSUME_CLUE'  ,, '', '');
# INSERT INTO tb_price (id, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id), 'RENT', 'XIANYU', 'CHANNEL_OPEN'       ,, '', '');
# INSERT INTO tb_price (id, business_code, channel_code, deal_type, agent_price, city_code, city_name) VALUES (NEXTVAL(seq_price_id), 'RENT', 'XIANYU', 'CONSUME_CLUE'  ,, '', '');


