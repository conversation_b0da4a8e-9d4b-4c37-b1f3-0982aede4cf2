INSERT INTO tb_template (id, create_time, logic_delete, update_time, version, avoid_period, contract_period, contract_type, doc_param, flow_param, name, target_amount, target_person, user_type, contract_end_time) VALUES (1, '2024-09-23 17:22:33.321381', false, '2024-09-27 18:29:58.735599', 31, 'P3M', 'P1Y', 'AGENT', '{"components": [{"componentKey": "userCertName", "componentValue": "${user.certName}"}, {"componentKey": "userCertName2", "componentValue": "${user.certName}"}, {"componentKey": "userCertNum", "componentValue": "${user.certNum}"}, {"componentKey": "userContactAddress", "componentValue": "${user.contactAddress}"}, {"componentKey": "userContactName", "componentValue": "${user.contactName}"}, {"componentKey": "userContactMobile", "componentValue": "${user.contactMobile}"}], "docTemplateId": "6af0722c37054ed1a0e9ac6a48adc7ad", "fileName": "乐居好房信息发布服务代理人协议"}', '{"docs": [{"fileId": "${docId}", "fileName": "${docName}"}], "signFlowConfig": {"autoFinish": true, "notifyUrl": "https://agent-test.ebaas.com/gateway/esign/callback", "redirectConfig": {"redirectUrl": "http://www.xx.cn/"}, "signFlowTitle": "乐居好房信息发布服务合作协议"}, "signers": [{"noticeConfig": {"noticeTypes": "1"}, "orgSignerInfo": {"orgInfo": {"orgIDCardNum": "${user.certNum}", "orgIDCardType": "CRED_ORG_USCC"}, "orgName": "${user.certName}", "transactorInfo": {"psnAccount": "${user.contactMobile}", "psnInfo": {"psnName": "${user.contactName}"}}}, "signConfig": {"signOrder": 1}, "signFields": [{"customBizNum": "userSign", "fileId": "${docId}", "normalSignFieldConfig": {"signFieldPosition": {"positionPage": "5", "positionX": 423, "positionY": 460}, "signFieldStyle": 1}, "signDateConfig": {"showSignDate": 1}}], "signerType": 1}, {"signConfig": {"signOrder": 2}, "signFields": [{"customBizNum": "platformSign", "fileId": "${docId}", "normalSignFieldConfig": {"assignedSealId": "e65fb735-014e-4019-b8a2-8725240eb2d4", "autoSign": true, "signFieldPosition": {"positionPage": "5", "positionX": 423, "positionY": 593}, "signFieldStyle": 1}, "signDateConfig": {"showSignDate": 1}}], "signerType": 1}]}', 'test', 100000.00, 2000, 'COMPANY', '2025-12-31 23:59:59.000000');
INSERT INTO tb_template (id, create_time, logic_delete, update_time, version, avoid_period, contract_period, contract_type, doc_param, flow_param, name, target_amount, target_person, user_type, contract_end_time) VALUES (2, '2024-09-23 18:39:43.497214', false, '2024-09-23 18:39:43.497214', 0, null, 'P100Y', 'PARTNER', null, null, 'test', 0.00, 0, null, null);
INSERT INTO tb_template (id, create_time, logic_delete, update_time, version, avoid_period, contract_period, contract_type, doc_param, flow_param, name, target_amount, target_person, user_type, contract_end_time) VALUES (3, '2024-09-23 18:41:33.958166', false, '2024-09-23 18:41:33.958166', 0, null, 'P100Y', 'BROKER', null, null, 'test', 0.00, 0, null, null);
INSERT INTO tb_template (id, create_time, logic_delete, update_time, version, avoid_period, contract_period, contract_type, doc_param, flow_param, name, target_amount, target_person, user_type, contract_end_time) VALUES (4, '2024-09-25 10:42:19.606767', false, '2024-09-27 15:30:42.452056', 18, 'P3M', 'P1Y', 'AGENT', '{"components": [{"componentKey": "userCertName", "componentValue": "${user.certName}"}, {"componentKey": "userCertName2", "componentValue": "${user.certName}"}, {"componentKey": "userCertNum", "componentValue": "${user.certNum}"}, {"componentKey": "userContactAddress", "componentValue": "${user.contactAddress}"}, {"componentKey": "userContactName", "componentValue": "${user.contactName}"}, {"componentKey": "userContactMobile", "componentValue": "${user.contactMobile}"}], "docTemplateId": "6af0722c37054ed1a0e9ac6a48adc7ad", "fileName": "乐居好房信息发布服务代理人协议"}', '{"docs": [{"fileId": "${docId}", "fileName": "${docName}"}], "signFlowConfig": {"autoFinish": true, "notifyUrl": "https://agent-test.ebaas.com/gateway/esign/callback", "redirectConfig": {"redirectUrl": "http://www.xx.cn/"}, "signFlowTitle": "乐居好房信息发布服务合作协议"}, "signers": [{"noticeConfig": {"noticeTypes": "1"}, "psnSignerInfo": {"psnAccount": "${user.contactMobile}", "psnInfo": {"psnName": "${user.contactName}"}}, "signConfig": {"signOrder": 1}, "signFields": [{"customBizNum": "userSign", "fileId": "${docId}", "normalSignFieldConfig": {"signFieldPosition": {"positionPage": "5", "positionX": 423, "positionY": 460}, "signFieldStyle": 1}, "signDateConfig": {"showSignDate": 1}}], "signerType": 0}, {"signConfig": {"signOrder": 2}, "signFields": [{"customBizNum": "platformSign", "fileId": "${docId}", "normalSignFieldConfig": {"assignedSealId": "e65fb735-014e-4019-b8a2-8725240eb2d4", "autoSign": true, "signFieldPosition": {"positionPage": "5", "positionX": 423, "positionY": 593}, "signFieldStyle": 1}, "signDateConfig": {"showSignDate": 1}}], "signerType": 1}]}', 'test', 100000.00, 2000, 'PERSONAL', '2025-12-31 23:59:59.000000');
