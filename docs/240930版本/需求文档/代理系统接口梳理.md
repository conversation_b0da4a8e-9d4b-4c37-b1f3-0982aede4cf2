- 同步用户信息: UserTest.editUser
    - 新建用户&手机号变更:用户ID,类型,手机号,用户名
    - 实名认证&变更:实名信息
    - 银行账号&变更:银行信息
- 创建代理商协议(发起电子签): ContractTest.testCreateAgent
    - 前置条件:完成用户信息同步(创建用户&实名认证&完善银行信息)
    - 应答信息:
        - status: 签约中
        - operatorMobile: 电子签发送手机号
- 查询合同: ContractTest.queryContract
    - 获取开通状态:
    - 查询默认邀请码:用于充值和下单
- 查询拥有的邀请码: InvitationTest.testQueryInvited
    - 判断公司还是个人: 判断user.id与当前operator.id是否相同
- 验证&更新验证码:ContractTest.testCheckInvitedCode
    - 编辑签约验证码:update=true
    - 充值&下单变更验证码:下单前做验证(update=false)
