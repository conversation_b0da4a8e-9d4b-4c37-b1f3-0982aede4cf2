SELECT sum(IF(detail_id IS NOT NULL, 1, 0)),
       count(1)
FROM tb_community;

UPDATE tb_community c
    LEFT JOIN (SELECT community_id,
                      id
               FROM tb_community_detail t1
               WHERE t1.community_name NOT LIKE '%失效%'
                 AND (
                   t1.verify = 1
                       OR t1.overlap_rate = (SELECT MAX(t2.overlap_rate)
                                             FROM tb_community_detail t2
                                             WHERE t2.community_id = t1.community_id)
                   )
               GROUP BY t1.community_id) t3 ON t3.community_id = c.id
SET c.detail_id = t3.id;

SELECT c.address,
       cd.address,
       c.`name`,
       cd.community_name,
       cd.verify,
       cd.overlap_rate,
       cd.id
FROM tb_community c
         LEFT JOIN tb_community_detail cd ON cd.id = c.detail_id
WHERE c.detail_id IS NOT NULL
  AND cd.verify = 0;

-- DELETE from tb_community_picture where type=208;

select *
from tb_community_picture
-- where type<>208
order by update_time desc
LIMIT 100;

UPDATE tb_community_picture
set community_id=null;

update tb_community_picture cp
    LEFT JOIN tb_community c on cp.community_detail_id = c.detail_id
set cp.community_id=c.id;


select *
from tb_community_layout
order by update_time desc
LIMIT 100;

UPDATE tb_community_layout
set community_id=null;

update tb_community_layout cp
    LEFT JOIN tb_community c on cp.community_detail_id = c.detail_id
set cp.community_id=c.id;



select *
from tb_community_layout
where community_detail_id = 455272;

SELECT community_id,
       id
FROM tb_community_detail t1
WHERE t1.verify = 1
   OR t1.overlap_rate = (SELECT MAX(t2.overlap_rate)
                         FROM tb_community_detail t2
                         WHERE t2.community_id = t1.community_id)
GROUP BY t1.community_id;

SELECT *
FROM (SELECT community_id,
             count(1) ct
      FROM tb_community_detail cd
      GROUP BY cd.community_id
      HAVING count(1) > 10) t1
ORDER BY ct DESC;

SELECT verify,
       overlap_rate,
       id
FROM tb_community_detail cd
WHERE cd.community_id = 207108
ORDER BY verify,
         overlap_rate DESC;


UPDATE tb_community_picture
SET type = CASE
               WHEN type = '1001' THEN 'DISTANT'
               WHEN type = '1002' THEN 'PASSAGEWAY'
               WHEN type = '1003' THEN 'BUILDING'
               WHEN type = '1004' THEN 'DISTRIBUTE'
               WHEN type = '1005' THEN 'ENTRY_DOOR'
               WHEN type = '1006' THEN 'PARK'
               WHEN type = '1007' THEN 'ROAD'
               WHEN type = '1008' THEN 'SCENERY'
               WHEN type = '1009' THEN 'FACILITY'
               WHEN type = '999' THEN 'OTHER'
               ELSE type
    END;

delete
from tb_community_picture
where type not in (
                   'DISTANT',
                   'PASSAGEWAY',
                   'BUILDING',
                   'DISTRIBUTE',
                   'ENTRY_DOOR',
                   'PARK',
                   'ROAD',
                   'SCENERY',
                   'FACILITY',
                   'OTHER'
    );