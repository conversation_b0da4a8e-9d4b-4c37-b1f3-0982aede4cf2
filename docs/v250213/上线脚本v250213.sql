# update tb_community
# set city_id = city_id/100+100
# where city_id in (110000, 120000, 310000, 500000);

alter table tb_broker
    change company company_name varchar(255) null comment '所属公司';

update tb_common_region
set type     = if(district_flag, 'DISTRICT', if(city_flag, 'CITY', if(province_flag, 'PROVINCE', null)))
  , city_id=if(district_flag, parent_id, null)
  , city_name=if(district_flag, parent_name, null)
;

delete
from tb_common_region
where id in (110000, 120000, 310000, 500000);

insert into tb_common_region(id,
                             code,
                             type,
                             name,
                             short_name,
                             alias,
                             pinyin_first,
                             pinyin_full,
                             pinyin_initials)
select round(id / 10000, 0) * 10000,
       round(id / 10000, 0) * 10000,
       'PROVINCE',
       name,
       short_name,
       alias,
       pinyin_first,
       pinyin_full,
       pinyin_initials
from tb_common_region
where id in (310100, 110100, 120100, 500100);

update tb_common_region
set parent_id = round(id / 10000, 0) * 10000
where id in (310100, 110100, 120100, 500100);

ALTER
SEQUENCE seq_region_id RESTART WITH 1000000;
# select NEXTVAL(seq_region_id) from dual;

# delete
# from tb_common_region
# where busi_flag = 1;

insert into tb_common_region(type,
                             id,
                             name,
                             short_name,
                             alias,
                             parent_id,
                             parent_name,
                             city_id,
                             city_name)
select 'BUSI',
       NEXTVAL(seq_region_id),
       busi_name,
       busi_name,
       busi_name,
       city.id,
       city.name,
       city.id,
       city.name
from tb_community comm
         left join tb_common_region city on comm.city_id = city.id
where comm.busi_name is not null && comm.busi_code is not null
group by comm.city_id, comm.busi_code, comm.busi_name
;

# update tb_common_region tr
#     left join tb_common_region pr on tr.parent_id = pr.id
# set tr.type='TOWN',
#     tr.city_id=if(pr.type = 'DISTRICT', pr.city_id, pr.id),
#     tr.city_name=if(pr.type = 'DISTRICT', pr.city_name, pr.name)
# where tr.town_flag = 1;

update tb_common_region
set code = id
where type in ('TOWN','BUSI');

# delete
# from tb_common_region
# where province_flag=0 and city_flag=0 and district_flag=0 and town_flag=0 and busi_flag=0;

select id,code,type from tb_common_region
# where code is null;
# where id <> cast(code as SIGNED);
# where type is null;
where type in ('DISTRICT','TOWN','BUSI') and (city_id is null or city_name is null);

alter table tb_common_region
    drop column city_flag;

alter table tb_common_region
    drop column district_flag;

alter table tb_common_region
    drop column province_flag;

alter table tb_common_region
    drop column alipay_code;

alter table tb_common_region
    drop column busi_flag;

alter table tb_common_region
    drop column town_flag;

alter table tb_community
    drop column busi_code;

alter table tb_community
    drop column town_code;

alter table tb_delegation
    drop column is_up;

alter table tb_delegation
    drop column rent_type;

alter table tb_delegation
    drop column verification_code;

alter table tb_delegation
    drop column verification_url;

alter table tb_delegation
    drop column channel_up;

alter table tb_common_media
    change target_id delegation_id bigint null comment '目标外键';

alter table tb_common_media
    drop column target_type;

rename table tb_common_media to tb_delegation_media;
rename table tb_common_region to tb_region;
rename table tb_common_dict to tb_delegation_dict;


ALTER TABLE `tb_delegation` DROP FOREIGN KEY `FKgndsykh1j2j035jmh1w6uwhw2`;
ALTER TABLE `tb_delegation` DROP FOREIGN KEY `FK6vubfqcpaakods2iv69c5xf7v`;
ALTER TABLE `tb_community` DROP FOREIGN KEY `FK57jw4ir1kqg9in8004ykbrklr`;
ALTER TABLE `tb_community` DROP FOREIGN KEY `FK8faxxu0xvmwf30wswmio8ro33`;
ALTER TABLE `tb_community` DROP FOREIGN KEY `FKl41pfuaxlcxrcfx70c6mtce6c`;
ALTER TABLE `tb_region` DROP FOREIGN KEY `FK7p2073gpoke0x9pkpxqwd8yyu`;
ALTER TABLE `tb_region` DROP FOREIGN KEY `FKj1l6kqpp1so0lr64kclr6u9pr`;
ALTER TABLE `tb_broker` DROP FOREIGN KEY `FKn9ywk7vx5jn6ppm28yrs704bt`;
ALTER TABLE `tb_community_tips` DROP FOREIGN KEY `FKlcb09rjwi93benaignvxpu2mn`;
ALTER TABLE `tb_consumer_login` DROP FOREIGN KEY `FK8jk5rtubd8awga25xxbd9vg4g`;
ALTER TABLE `tb_delegation_media` DROP FOREIGN KEY `FK667n8uhcdfj45padh987o1are`;

alter table tb_delegation_media
    drop column target_id;
alter table tb_delegation_media
    drop column subtype_title;


update tb_region set business_codes='SALE,RENT,NEW' where business_open=1;

