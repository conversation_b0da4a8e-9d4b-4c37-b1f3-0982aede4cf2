引入依赖:
```xml
<dependency>
    <groupId>com.ejuetc.consumer</groupId>
    <artifactId>consumer-api</artifactId>
    <version>0.0.9</version>
</dependency>
```

添加经纪人:
```java
BrokerAPI brokerAPI = getAPI(BrokerAPI.class, "http://10.123.159.50:8097");
ApiResponse<BrokerDTO> response = brokerAPI.edit(new EditBrokerPO()
    .setBrokerId(System.currentTimeMillis())
    .setName("张三_" + now())
    .setIdNum("310123456789012345")
    .setCityCode("310000")
    .setStoreName("上海大学店")
    .setCompanyName("上海添玑好房网络服务有限公司")
    .setCompanyLicenseNum("91310000676211967N")
    .setCompanyLicenseUrl("https://gimg2.baidu.com/image_search/src=https%3A%2F%2Fwww.generalwatertech.com%2Fuploadfiles%2F2020%2F01%2F20200109175314541.jpg%3FMS5qcGc%3D&refer=http%3A%2F%2Fwww.generalwatertech.com&app=2002&size=f10000,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=**********&t=0e2cf6f9df768dfc36a32ebbc2d2bccd")
    .setCompanyLegal("聂万海")
    .setPhone("13123456789")
    .setIcon("https://www.baidu.com")
    .setIntroduce("经纪人介绍")
    .setProfessionInformationCardUrl("https://imgwcs3.soufunimg.com/yun/2020_02/17/pic/ca66a828-7fbf-4d67-92af-8577aefb0100.jpg")
    .setProfessionInformationCardNumber("123456789012345678")
    .setCompanyId(System.currentTimeMillis())
    .setCityCode("310100")
);
```

获取房源委托:
```java
DelegationAPI delegationApi = getAPI(DelegationAPI.class, "http://10.123.159.50:8097");
ApiResponse<DelegationDTO> response = delegationApi.edit(new EditDelegationPO()
        
    //添加公司级委托(房源池)
    .setType(DelegationDTO.Type.COMPANY)
    .setCompanyId(1736993391015L)
        
    //添加经纪人级委托        
    //.setType(DelegationDTO.Type.BROKER)
    //.setBrokerId(1736933808770L)
    //.setParentOutId("ba74a877-6cf1-44e5-836e-880f9fbf5f48")
    //.setParentId(102L)
        
    //添加(经纪人)渠道委托        
    //.setType(DelegationDTO.Type.CHANNEL)
    //.setChannelCode(ChannelDTO.Code.XIANYU)
    //.setParentOutId("ba74a877-6cf1-44e5-836e-880f9fbf5f48")
    //.setParentId(50102L)

    .setSource("SaaS")
    .setOutId(UUID.randomUUID().toString())
    .setCommunityAddress("上海静安")
    .setCommunityName("慧芝湖")
    .setBusiCode(BusinessOpenDTO.Code.RENT)
    .setTitle("测试房源_租房_" + System.currentTimeMillis())
    .setKeywords(List.of("地铁房", "暖气", "图片房", "满二"))
    .setMedias(getMedias())
    .setRedoCode("REGAL")
    .setCurrentFloor(1)
    .setTotalFloor(10)
    .setTagElevator(true)
    .setCompletionTime(2022)
    .setPropertyTypeCode("RESIDENTIAL")
    .setElevatorCount(1)
    .setRoomPerFloor(4)
    .setParkingRatio("1:2")
    .setPropertyManagementCompany("物业公司")
    .setPropertyManagementFee(new BigDecimal("2.2"))
    .setPriceUnit(new BigDecimal("10000"))
    .setPriceTotal(new BigDecimal("1000000"))
    .setDeadline(now().plusDays(90))
    .setBuildName("10号楼")
    .setUnitName("1单元")
    .setFloorName("1楼")
    .setRoomName("101")
    .setOrientCode("SOUTH")
    .setRoomCount(2)
    .setHallCount(2)
    .setToiletCount(2)
    .setBuildingArea(new BigDecimal("100"))
    .setRentType(DelegationDTO.RentType.RENT_FULL)
    .setRoomTypeCode("MAIN_BEDROOM")
    .setShowAlipay(true)
);
```
