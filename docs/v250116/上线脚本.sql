update tb_common_region
set code = concat(substr(code, 1, 3), '100')
where code in ('110000', '120000', '310000', '500000');

update tb_community
set city_code=concat(substr(city_code, 1, 3), '100')
where city_code in ('110000', '120000', '310000', '500000');


SELECT CAST(concat(substr(id, 1, 3), '100') AS UNSIGNED) FROM tb_common_region
WHERE id IN (110000, 120000, 310000, 500000);

update tb_common_region
set id = CAST(concat(substr(id, 1, 3), '100') AS UNSIGNED)
where id in (110000, 120000, 310000, 500000);

update tb_community
set city_id=CAST(concat(substr(city_id, 1, 3), '100') AS UNSIGNED)
where city_id in (110000, 120000, 310000, 500000);

update tb_delegation
set city_id=CAST(concat(substr(city_id, 1, 3), '100') AS UNSIGNED)
where city_id in (110000, 120000, 310000, 500000);



