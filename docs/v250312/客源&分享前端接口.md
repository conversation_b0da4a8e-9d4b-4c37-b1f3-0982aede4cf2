## 一.客源接口

### 1,[客源列表](http://ejuetc-consumer.tst.ejucloud.cn/doc.html#/-v3-api-docs/WEB_%E6%B6%88%E8%B4%B9%E8%80%85/relationList)

### 2.[客源详情](http://ejuetc-consumer.tst.ejucloud.cn/doc.html#/-v3-api-docs/WEB_%E6%B6%88%E8%B4%B9%E8%80%85/relationDetail)

### 3.[编辑客源](http://ejuetc-consumer.tst.ejucloud.cn/doc.html#/-v3-api-docs/WEB_%E6%B6%88%E8%B4%B9%E8%80%85/relationEdit)

### 4.房源浏览记录
#### <待实现>

## 二.分享接口

### 1. [经纪人分享](http://ejuetc-consumer.tst.ejucloud.cn/doc.html#/-v3-api-docs/WEB_%E6%B6%88%E8%B4%B9%E8%80%85/shareByBroker)

### 2. [消费者分享](http://ejuetc-consumer.tst.ejucloud.cn/doc.html#/-v3-api-docs/WEB_%E6%B6%88%E8%B4%B9%E8%80%85/shareByConsumer)

### 3. [消费者登录](http://ejuetc-consumer.tst.ejucloud.cn/doc.html#/-v3-api-docs/WEB_消费者/login)

#### 新增参数

```json
{
"name": "shareCode",
"type": "string",
"description": "分享码"
}
``` 

### 4. 分享数据查询 :
#### <待实现>

