# 5.3 小区详情查询

## 请求头（http header）

- [参考报文请求头](..%2F1_%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84%2F1_%E8%AF%B7%E6%B1%82%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84.md)
- etc-api-code: consumer-community-queryDetail

## 请求体（http body）

### 字段

| 字段名     | 字段描述   | 说明 | 是否必须 | 类型及长度      | 示例               |
|---------|--------|----|------|------------|------------------|
| beikeId | 贝壳小区ID |    | true | number(20) | 5020069179760703 |

### 示例:

```json
{
  "beikeId": 5020069179760703
}
```

## 同步应答（http body）

### 字段

| 字段                          | 名称         | 类型              | 示例                   | 说明                                                                                   |
|-----------------------------|------------|-----------------|----------------------|--------------------------------------------------------------------------------------|
| status                      | 处理状态       | string          | SUCC_DONE            | - SUCC_DONE : 处理成功<br> - FAIL_PRM : 参数错误<br> - FAIL_BIZ : 业务异常<br> - FAIL_SYS : 系统异常 |
| message                     | 应答信息       | string          | 签名异常                 | 处理异常信息                                                                               |
| data                        | 处理结果数据     | json[]          |                      | 小区详情列表                                                                               |
| - id                        | 小区id       | number(20)      | 123456789            | 实现小区归一                                                                               |
| - name                      | 小区名        | string          | 慧芝湖花园                |                                                                                      |
| - beikeId                   | 贝壳小区id     | number(20)      | 123456789            | 实现小区归一                                                                               |
| - beikeName                 | 贝壳小区名      | string          | 慧芝湖花园                |                                                                                      |
| - address                   | 小区地址       | string          | 平型关路1083弄            |                                                                                      |
| - provinceName              | 省份名        | string          | 上海市                  |                                                                                      |
| - cityName                  | 城市名        | string          | 上海市                  |                                                                                      |
| - districtName              | 行政区名       | string          | 静安区                  |                                                                                      |
| - townName                  | 街道&镇名      | string          | 大宁路街道                |                                                                                      |
| - busiName                  | 商圈名        | string          | 凉城                   |                                                                                      |
| - formattedAddress          | 完整格式化地址    | string          | 上海市静安区大宁路街道慧芝湖花园     |                                                                                      |
| - typeName                  | 小区类型       | string          | 商务住宅;住宅区;住宅小区        |                                                                                      |
| - cityId                    | 城市id       | string          | 310100               |                                                                                      |
| - districtId                | 行政区id      | string          | 310106               |                                                                                      |
| - location                  | 坐标         | string          | 121.458773,31.281544 |                                                                                      |
| - latitude                  | 经度         | string          | 31.281544            |                                                                                      |
| - longitude                 | 纬度         | string          | 121.458773           |                                                                                      |
| - beikeIds                  | 贝壳小区id列表   | long[]          | \[1,2,3,4]           |                                                                                      |
| - detail                    | 小区详情       | json            |                      |                                                                                      |
| - - id                      | 详情主键       | number(20)      | 123456789            |                                                                                      |
| - - zipCd                   | 邮编         | string          | 200001               |                                                                                      |
| - - blockCd                 | 板块代码       | string          | 310106106            |                                                                                      |
| - - blockName               | 板块名称       | string          | 大宁                   |                                                                                      |
| - - proServiceAddr          | 物业服务中心地址   | string          | 紫园国际商务大厦4楼           | 福泰园一层,3号楼1单元负一楼,宝安区裕安路与107国道处,西溪山庄休闲中心13楼                                            |
| - - propertyOnTime          | 物业工作时间     | string          |                      |                                                                                      |
| - - intelGateFlag           | 是否安装智能道闸   | string          | true                 | true/false                                                                           |
| - - gateControlFlag         | 是否有门禁      | string          | true                 | true/false                                                                           |
| - - monitorFlag             | 是否有监控      | string          | true                 | true/false                                                                           |
| - - securityBoothNum        | 保安岗亭数      | string          |                      |                                                                                      |
| - - securityPersonNum       | 保安人数       | string          |                      |                                                                                      |
| - - securityAlldayFlag      | 是否24小时值守   | string          |                      |                                                                                      |
| - - securityPatrolFrequency | 保安巡察频率     | string          | 4.00                 |                                                                                      |
| - - policeNetworkingFlag    | 是否110联网    | string          |                      |                                                                                      |
| - - homeNameFlag            | 是否是总盘      | string          |                      |                                                                                      |
| - - propertyType            | 物业类型       | string          | 商业/商务公寓              | 商业/商务公寓,车库/普通住宅/工业厂房,普通住宅/商业/底商/商业办公类/商务公                                            |
| - - propertyYears           | 物业年限       | string          | 70,40/70             | 40/50,20/70,70/40,"70,40/70",40/50/70                                                |
| - - commBelong              | 交易权属       | string          | 商品房/使用权/拆迁安置房        | 商品房/使用权/拆迁安置房,商品房/已购公房/经济适用房/自建房                                                     |
| - - buildMaxYear            | 最大建筑年代     | string          | 2009                 |                                                                                      |
| - - buildMinYear            | 最小建筑年代     | string          | 2004                 |                                                                                      |
| - - buildNum                | 楼栋总数       | string          |                      |                                                                                      |
| - - houseNum                | 房屋总数       | string          |                      |                                                                                      |
| - - developerCorp           | 开发商        | string          | 中昊嘉信城市发展集团           |                                                                                      |
| - - brandCorp               | 品牌商        | string          | 成都城投置地（集团）有限公司       |                                                                                      |
| - - actArea                 | 占地面积       | string          |                      |                                                                                      |
| - - buildArea               | 建筑面积       | string          |                      |                                                                                      |
| - - buildingType            | 建筑类型       | string          | 板楼,塔板结合              | 合院别墅,塔楼、板楼、塔板结合、高层、超高层、小高层                                                           |
| - - houseType               | 房屋类型       | string          | 低层/小高层/高层            | 低层/多层/小高层/高层,低层/高层,小高层/高层/超高层,低层/多层,低层/多层/高层                                         |
| - - buildingCategory        | 建筑类别       | string          | 商住/独栋                | 商住/独栋,花园洋房/独栋,商住/叠拼,花园洋房/商住/独栋,花园洋房/叠拼                                               |
| - - propertyName            | 物业公司       | string          | 沈阳中环物业管理有限公司         | 沈阳中环物业管理有限公司,东莞市永泰物业有限公司		,京新物业管理有限公司                                                |
| - - propertyFee             | 物业费        | string          | 1.38-3元/月/㎡          | 1.38-3元/月/㎡,0.98-2.23元/月/㎡,3-5.11元/月/㎡                                               |
| - - propertyPhone           | 物业电话       | string          | 021-32300162         | 021-32300162,0571-88761000,020-85686293,010-66214377                                 |
| - - communityCloseFlag      | 小区是否封闭     | string          | true                 | false,true                                                                           |
| - - parkingRate             | 车位配比率(户:车) | string          | 1:1.55               | 1:1.55,1:6,1:0.186,1.00:1.01,1:4.92,1:2.80,1:4.59,1.00:0.20                          |
| - - upParkingNum            | 地上车位数      | number          | 1025                 | 1025,3000,588,88,332,404                                                             |
| - - downParkingNum          | 地下车位数      | string          |                      |                                                                                      |
| - - parkingNum              | 车位总数       | string          |                      |                                                                                      |
| - - personDivCarFlag        | 是否人车分流     | string          | true                 | ture/false                                                                           |
| - - parkingSaleFlag         | 是否出售产权车位   | string          | true                 | ture/false                                                                           |
| - - setParkingFee           | 固定停车费标准    | string          | 400/位                | 400/位,地下：400-500/位,地上250 地下250,18,"地上:70/位,地下:250/位",第一辆30 第二辆50                     |
| - -  tempParkingFee         | 临停停车费标准    | string          | 10元/小时               | 10元/小时,3元/天                                                                          |
| - - volumeRate              | 容积率        | string          | 4.42                 | 4.42,2.39,5.35,0.86,8.30,2.54,4.62                                                   |
| - - greenRate               | 绿化率        | string          | 0.70                 | 0.70,0.33,0.75,0.83,0.52,0.68                                                        |
| - - powerdDesc              | 供电描述       | string          | 民电，电费0.52元/度         | 一户一表，自行买电,民用电；用电量分三个档次、低于204度部分，,环网供电、月缴,3元/度                                        |
| - - waterDesc               | 供水描述       | string          | 居民用水，2.3元/立方米        | 小区供水,居民供水,商业,湖州自来水公司,高层住宅7层以上（含7层）无负压变频供水                                            |
| - -  gasDesc                | 供气描述       | string          | 2.38-3.3元/m³         | 3.03-3.5元/月/m³,2.48-2.65元/月/㎡,天然气入户,0.4-2.55元/月/m³                                   |
| - -  heatingDesc            | 供暖描述       | string          | 集中供暖26.7元/平方         | 无集中供暖,节能、环保、健康式采暖系统,自购供暖                                                             |
| - -  iconUrl                | 小区图标       | string          |                      |                                                                                      |
| - layoutsSummaryMap         | 小区户型汇总     | Map<'户型名',json> |                      |                                                                                      |
| - - <户型名>                   | 户型名        | string          | 3室2厅                 |                                                                                      |
| - - - roomCount             | 卧室数        | number          | 3                    |                                                                                      |
| - - - hallCount             | 客厅数        | number          | 2                    |                                                                                      |
| - - - setNum                | 套数         | number          | 12                   |                                                                                      |
| - - - areaMax               | 最大面积       | number          | 88.56                |                                                                                      |
| - - - areaMin               | 最小面积       | number          | 70.29                |                                                                                      |
| - - - picUrl                | 户型图        | string[]        | url                  |                                                                                      |
| - picturesMap               | 小区图片       | json[]          |                      |                                                                                      |
| - - <图片类型>                  | 图片类型       | string          | 景观带                  | 出入口,远景,道路,停车场,楼栋,入户门,景观带,配套,分布图,其他                                                   |
| - - <图片列表>                  | 图片类别       | string[]        | url                  |                                                                                      |

### 示例

```json
{
  "message": "成功",
  "status": "SUCC_DONE",
  "data": {
    "id": 1,
    "name": "慧芝湖花园",
    "address": "平型关路1083弄",
    "beikeId": 5020069179760703,
    "beikeName": "慧芝湖花园（三期）",
    "busiName": "凉城",
    "cityCode": "310100",
    "cityId": 310100,
    "cityName": "上海市",
    "districtId": 310106,
    "districtName": "静安区",
    "formattedAddress": "上海市静安区大宁路街道慧芝湖花园",
    "latitude": 31.281544,
    "location": "121.458773,31.281544",
    "longitude": 121.458773,
    "provinceName": "上海市",
    "townName": "大宁路街道",
    "typeName": "商务住宅;住宅区;住宅小区",
    "beikeIds": [
      5011000016384,
      5012970675942390,
      5012970675942395,
      5012970691145738,
      5016302021839830,
      5020069179760703
    ],
    "detail": {
      "id": 129712,
      "blockCd": "310106106",
      "blockName": "大宁",
      "buildMaxYear": "2009",
      "buildMinYear": "2004",
      "buildNum": 9,
      "buildingType": "板楼",
      "commBelong": "商品房/使用权",
      "createTime": "2024-12-31T15:00:49",
      "developerCorp": "嘉华(中国)投资有限公司",
      "downParkingNum": 0,
      "gasDesc": "3元/m³",
      "greenRate": 0.45,
      "heatingDesc": "自采暖",
      "houseNum": 3526,
      "parkingNum": 3494,
      "parkingRate": "1.00:0.70",
      "powerdDesc": "民电",
      "propertyFee": "2.7元/月/㎡",
      "propertyName": "龙湖物业",
      "propertyPhone": "021-66525123",
      "propertyType": "住宅",
      "propertyYears": "50/70",
      "setParkingFee": "300",
      "upParkingNum": 0,
      "updateTime": "2025-01-09T15:11:45",
      "version": 2,
      "volumeRate": 2.50,
      "waterDesc": "民水"
    },
    "layoutsSummaryMap": {
      "1室2厅": {
        "areaMax": 88.56,
        "areaMin": 70.29,
        "hallCount": 2,
        "name": "1室2厅",
        "picUrl": [
          "https://oss-consumer.ebaas.com/community_layout/129712/5f9ce615-2fd3-4d01-b95a-1be41ddcb495.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/d697791a-32be-461f-97bf-807266a1b262.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/ab966172-90cb-4116-b5f5-7af48e78fdc1.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/09513af2-c135-45ea-b8d4-144c78d94c9e.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/cf69a04e-9234-4675-8434-87f8dde4fa5f.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/e2dfc091-472f-464c-84be-3075377c4a33.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/37779854-64b7-42e6-bcd7-458656857ecf.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/d2276afc-e980-468c-bdd8-e3af192b18dd.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/a33c37f1-25c1-4e45-bdce-790ca28dd702.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/22630147-9fef-4447-87a8-411177125b75.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/55743f0d-c688-4400-8797-32363b386391.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/5eb5260e-c2a5-4808-b7d0-13695a0e774e.jpg"
        ],
        "roomCount": 1,
        "setNum": 12
      },
      "1室1厅": {
        "areaMax": 79.00,
        "areaMin": 72.44,
        "balconyCount": 2,
        "hallCount": 1,
        "kitchenCount": 1,
        "name": "1室1厅",
        "picUrl": [
          "https://oss-consumer.ebaas.com/community_layout/129712/c5abe500-df5f-43ae-9810-9a99cbf3931c.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/90f2a195-1269-48a8-bdb7-3276dae69864.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/994500f1-63b5-495c-9cdd-acca6fb8eff4.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/2bedf1cb-7343-4a68-bb71-4f66c0a5fb72.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/87e03e8a-9a87-41f4-b848-82626865b3ba.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/16021c5f-9c3b-4487-a118-be073e4fc6ec.jpg"
        ],
        "roomCount": 1,
        "setNum": 6,
        "toiletCount": 1
      },
      "2室1厅": {
        "areaMax": 107.00,
        "areaMin": 73.04,
        "balconyCount": 1,
        "hallCount": 1,
        "kitchenCount": 1,
        "name": "2室1厅",
        "picUrl": [
          "https://oss-consumer.ebaas.com/community_layout/129712/d33c326b-dbbb-45b4-852c-43c97c6b26c0.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/c8845078-8090-47ee-8c6d-28c748e289c3.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/baec009d-03fa-47df-8b06-f3c00871c0c9.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/d022237d-525d-4bfe-8e8e-a989c5edb8f8.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/31900555-ea7f-4632-a826-be9c3b950e60.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/704b445c-54a3-4bdf-8a89-e26d968f2d6b.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/54a82bf9-c9b8-47c7-aa78-4f894631dac9.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/b6f77457-ba09-443a-97a0-7a4dc63292e0.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/86227575-e2a6-4a7b-b2b0-5edb6f09aac1.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/54bc33a0-21f4-44fc-b8b3-7ccd77d9220f.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/a4e35d74-ea48-4c10-a61e-ddb897fac06c.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/93d825f5-b603-4f2d-9bb2-ad84a6a9b5c8.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/1688059e-43f9-4b36-bb4d-97ccb459b0f9.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/6188fcaa-165f-4252-af0d-2337b5e676cb.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/ba8df067-ecab-4879-95be-1454375f57c9.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/4aa5dc32-b24d-4307-8fe5-e59c5f251d05.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/c17813b5-4d95-4887-a0fe-53bf0169e52b.jpg"
        ],
        "roomCount": 2,
        "setNum": 34,
        "toiletCount": 1
      },
      "2室2厅": {
        "areaMax": 107.69,
        "areaMin": 72.44,
        "hallCount": 2,
        "name": "2室2厅",
        "picUrl": [
          "https://oss-consumer.ebaas.com/community_layout/129712/5c25520e-a9ba-4fea-ba50-f114b0045eec.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/f8a34698-460e-4081-be76-ac4f6358783f.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/d4cfc4ae-8577-4ded-b89e-d4395c19bdff.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/de847ef6-1f9e-45d9-a926-2993d69c231b.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/d46e126c-d2fc-4c77-98d7-78011227e0a8.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/1a83bf40-7ebb-4175-905f-e05ab13cc08d.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/a128c2d5-af73-42af-a4ed-962ea5a575dd.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/17a654c7-516a-45f7-ba77-b7f6d7635a59.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/68d77614-ca26-4246-b02b-afd4d3856ba6.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/5de50209-8735-4378-8a2f-1fdd4d1664af.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/116bc488-be3c-4869-bde9-6a3d92a818dc.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/7ea5d175-4464-49a2-95d8-d81c71490b4f.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/b90b8f74-2fcc-472c-95a1-cf1a3b2d3fd4.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/f6343115-21bc-4860-a9fb-2a56bb8e9b8b.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/9f4b5bf1-2897-4fc2-a0c0-1671664132ad.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/4c184efa-17c4-41c5-95b1-f850a5c84b24.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/224c4898-d821-4a39-a0b6-ec37f974bf57.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/c007286a-ee45-49b8-bec5-c55b067ff2f1.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/e428e6d2-0f55-46dd-b11a-f5d2992a8b12.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/5bb9938d-20d6-42dd-8626-535f98142a92.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/9686fde7-ec68-4967-b3ed-13035342c4db.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/fec5dc11-fbfb-48ae-bc7c-84c2739a5a4d.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/7e85149e-ae3e-43fa-ad7e-e66891b96feb.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/b37361d4-e2e2-45c0-abb7-53aaec0cea64.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/cdc71dbf-85f2-436f-8d24-aa2039c46c91.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/29a1b504-85f4-4816-8d96-e1c45e4ef62f.jpg"
        ],
        "roomCount": 2,
        "setNum": 52
      },
      "3室1厅": {
        "areaMax": 154.10,
        "areaMin": 80.98,
        "balconyCount": 2,
        "hallCount": 1,
        "kitchenCount": 1,
        "name": "3室1厅",
        "picUrl": [
          "https://oss-consumer.ebaas.com/community_layout/129712/575d21f1-a006-4a94-a7c6-580e44526012.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/a50a3d97-1d9a-4d75-b723-66b868508064.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/0399cf1d-a125-48c7-9786-09cc0b001644.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/78ca22c9-c7e0-458a-ad76-a26709cc5279.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/9ea813cb-cb29-489e-b706-9ea009769041.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/0cb94bb2-f993-42cc-835b-eaaf34a80b98.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/f4710f84-e12b-4f0e-a7dc-47964d08a5c2.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/d4dccfb1-4a01-4d40-b049-078cd495872e.jpg"
        ],
        "roomCount": 3,
        "setNum": 24,
        "toiletCount": 2
      },
      "3室2厅": {
        "areaMax": 109.77,
        "areaMin": 88.35,
        "balconyCount": 1,
        "hallCount": 2,
        "kitchenCount": 1,
        "name": "3室2厅",
        "picUrl": [
          "https://oss-consumer.ebaas.com/community_layout/129712/8fa57921-6ae9-40ec-b908-1354ec8c96a1.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/9be33f94-6fc7-4e25-938d-f48ceabede2f.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/35bd1042-5590-40e4-be80-121200c606fd.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/75b1a0bc-0002-42e2-8f74-4444388048e2.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/274d65d5-c83c-4c18-940b-3aa8274a889a.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/08041c30-6243-4a2a-a074-edc7a491bd83.jpg"
        ],
        "roomCount": 3,
        "setNum": 18,
        "toiletCount": 1
      },
      "4室1厅": {
        "areaMax": 193.24,
        "areaMin": 193.24,
        "balconyCount": 1,
        "hallCount": 1,
        "kitchenCount": 1,
        "name": "4室1厅",
        "picUrl": [
          "https://oss-consumer.ebaas.com/community_layout/129712/63882253-ae12-4104-bcbc-9ec2b2568366.jpg"
        ],
        "roomCount": 4,
        "setNum": 4,
        "toiletCount": 3
      },
      "4室2厅": {
        "areaMax": 173.82,
        "areaMin": 95.16,
        "hallCount": 2,
        "name": "4室2厅",
        "picUrl": [
          "https://oss-consumer.ebaas.com/community_layout/129712/69713ad8-471c-446b-82f0-d23a70dfb323.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/24d32dab-003c-4c45-873b-82c1753ff122.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/ef87c00b-dbb8-422f-ad49-dca8bc7b43eb.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/6c658490-a3c6-4998-9384-d5bb0e8f5192.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/4dd843f6-7dfe-4d75-8fd1-9c87461fe2dd.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/fddab51e-fdb1-42f8-9707-3bfab6830d8e.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/4ac8187e-a9fc-480a-998c-1f5fee6af129.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/c64aee10-36b7-441f-8fe9-9b658f6b411a.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/b07ed0cd-fc36-40df-ad78-143ccc8684a9.jpg",
          "https://oss-consumer.ebaas.com/community_layout/129712/106eef48-0888-4f07-b308-895e92ad2e86.jpg"
        ],
        "roomCount": 4,
        "setNum": 40
      },
      "4室3厅": {
        "areaMax": 184.21,
        "areaMin": 184.21,
        "balconyCount": 1,
        "hallCount": 3,
        "kitchenCount": 1,
        "name": "4室3厅",
        "picUrl": [
          "https://oss-consumer.ebaas.com/community_layout/129712/8e3ff767-b10e-441b-acd5-3cbd71781875.jpg"
        ],
        "roomCount": 4,
        "setNum": 4,
        "toiletCount": 3
      }
    },
    "picturesMap": {
      "景观带": [
        "https://oss-consumer.ebaas.com/community_picture/129712/9a6a9e4c-910a-4c59-a23d-c7139c65d977.jpg"
      ],
      "楼栋": [
        "https://oss-consumer.ebaas.com/community_picture/129712/8b1ebf63-1bd2-43cd-bef4-19be36028123.jpg",
        "https://oss-consumer.ebaas.com/community_picture/129712/84d3ed2d-cd25-4234-b216-712b8a55d9e9.jpg",
        "https://oss-consumer.ebaas.com/community_picture/129712/61b50115-7f66-407e-84cc-4fe411328ba4.jpg",
        "https://oss-consumer.ebaas.com/community_picture/129712/41998c0d-0c8b-4166-b9ac-b88e5fabbec1.jpg"
      ],
      "远景": [
        "https://oss-consumer.ebaas.com/community_picture/129712/669a8c30-dd3e-47ec-8d84-8eb1270af886.jpg",
        "https://oss-consumer.ebaas.com/community_picture/129712/6e8cdf8e-0f8d-4ff3-ac90-5e2b43cd6993.jpg",
        "https://oss-consumer.ebaas.com/community_picture/129712/7568bea0-8562-46e7-918c-4030112f554b.jpg"
      ],
      "出入口": [
        "https://oss-consumer.ebaas.com/community_picture/129712/4e5ccd03-e53a-4947-8fa5-92377fb667de.jpg",
        "https://oss-consumer.ebaas.com/community_picture/129712/bf36c1dc-1d96-4b92-8e0a-9557faa8256a.jpg",
        "https://oss-consumer.ebaas.com/community_picture/129712/ffa6345b-9491-44d5-b89f-85030a77db01.jpg"
      ],
      "入户门": [
        "https://oss-consumer.ebaas.com/community_picture/129712/2b86547c-0a1e-4966-8420-b282d98c6c23.jpg",
        "https://oss-consumer.ebaas.com/community_picture/129712/5002fc3d-6ac5-4159-a7ee-6462ab03ae0b.jpg",
        "https://oss-consumer.ebaas.com/community_picture/129712/21dcdab4-096a-4017-bdda-e70909762b09.jpg"
      ],
      "分布图    ": [
        "https://oss-consumer.ebaas.com/community_picture/129712/83c455ea-11dc-47d1-8202-5221a4406aa1.jpg"
      ],
      "其他": [
        "https://oss-consumer.ebaas.com/community_picture/129712/9374425c-dde1-4af3-8dcb-7fbaa8036bed.jpg"
      ],
      "道路": [
        "https://oss-consumer.ebaas.com/community_picture/129712/e8a6de00-afa1-4ee0-b58c-a2e42f7f0b09.jpg",
        "https://oss-consumer.ebaas.com/community_picture/129712/e2644c9f-e70a-4cd0-a07b-cba60cc878b6.jpg",
        "https://oss-consumer.ebaas.com/community_picture/129712/737a59b1-2db1-4297-8f58-e9573233aeee.jpg",
        "https://oss-consumer.ebaas.com/community_picture/129712/ff213483-ee1e-4e92-9c24-2c12820d4bb6.jpg",
        "https://oss-consumer.ebaas.com/community_picture/129712/1fe2ba85-8cf6-4db8-8516-609d0c0014ff.jpg"
      ],
      "停车场": [
        "https://oss-consumer.ebaas.com/community_picture/129712/6ffd978c-36a0-47db-895a-dd56938174f7.jpg",
        "https://oss-consumer.ebaas.com/community_picture/129712/fcd4718b-9600-4ecd-9b7c-298e472fe9d7.jpg"
      ]
    }
  }
}
```