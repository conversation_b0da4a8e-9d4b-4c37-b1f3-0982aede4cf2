# 5.1 查询小区贝壳

## 请求头（http header）

- [参考报文请求头](..%2F1_%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84%2F1_%E8%AF%B7%E6%B1%82%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84.md)
- etc-api-code: consumer-community-queryBeikeCommunity

## 请求体（http body）

### 字段

| 字段名      | 字段描述 | 说明    | 是否必须 | 类型及长度       | 示例     |
|----------|------|-------|------|-------------|--------|
| cityId   | 城市编码 | 城市国标码 | true | number      | 310100 |
| keyword  | 小区名  | 最少2个字 | true | string(127) | 慧芝湖    |
| pageSize | 单页行数 | 小于100 | true | number      | 10     |
| pageNum  | 页码   | 从1开始  | true | number      | 1      |

### 示例:

```json
{
  "cityId": 310100,
  "keyword": "慧芝湖",
  "pageSize": 5,
  "pageNum": 1
}
```

## 同步应答（http body）

### 字段

| 字段             | 名称     | 类型         | 示例         | 说明                                                                                   |
|----------------|--------|------------|------------|--------------------------------------------------------------------------------------|
| status         | 处理状态   | string     | SUCC_DONE  | - SUCC_DONE : 处理成功<br> - FAIL_PRM : 参数错误<br> - FAIL_BIZ : 业务异常<br> - FAIL_SYS : 系统异常 |
| message        | 应答信息   | string     | 签名异常       | 处理异常信息                                                                               |
| data           | 处理结果数据 | json[]     |            | 小区详情列表                                                                               |
| - id           | 贝壳小区id | number(20) | 123456789  | 实现小区归一                                                                               |
| - name         | 贝壳小区名  | string     | 慧芝湖花园      |                                                                                      |
| - cityId       | 城市id   | string     | 310100     |                                                                                      |
| - cityName     | 城市名    | string     | 上海市        |                                                                                      |
| - districtName | 行政区名   | string     | 静安区        |                                                                                      |
| - lat          | 经度     | string     | 31.281544  |                                                                                      |
| - lng          | 纬度     | string     | 121.458773 |                                                                                      |
| page           | 分页信息   | json       |            |                                                                                      |
| - pageNum      | 页码     | number     | 1          |                                                                                      |
| - pageSize     | 单页行数   | number     | 5          |                                                                                      |
| - totalPage    | 总页数    | number     | 1          |                                                                                      |
| - totalRow     | 总行数    | number     | 1          |                                                                                      |

### 示例

```json
{
  "status": "SUCC_DONE",
  "message": "成功",
  "data": [
    {
      "id": 5011000016384,
      "name": "慧芝湖花园（一二期）",
      "cityId": 310100,
      "cityName": "上海",
      "districtName": "静安",
      "lat": 31.288130,
      "lng": 121.465689
    },
    {
      "id": 5020069179760703,
      "name": "慧芝湖花园（三期）",
      "cityId": 310100,
      "cityName": "上海",
      "districtName": "静安",
      "lat": 31.288366,
      "lng": 121.463710
    }
  ],
  "page": {
    "pageNum": 1,
    "pageSize": 5,
    "totalPage": 1,
    "totalRow": 2
  }
}
```