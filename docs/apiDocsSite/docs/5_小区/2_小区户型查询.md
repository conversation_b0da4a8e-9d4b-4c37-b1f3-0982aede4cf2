# 5.2 小区户型类别查询

## 请求头（http header）

- [参考报文请求头](..%2F1_%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84%2F1_%E8%AF%B7%E6%B1%82%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84.md)
- etc-api-code: consumer-community-queryLayoutGroup

## 请求体（http body）

### 字段

| 字段名     | 字段描述   | 说明 | 是否必须 | 类型及长度      | 示例               |
|---------|--------|----|------|------------|------------------|
| beikeId | 贝壳小区ID |    | true | number(20) | 5020069179760703 |

### 示例:

```json
{
  "beikeId": 5020069179760703
}
```

## 同步应答（http body）

### 字段

| 字段      | 名称    | 类型                   | 示例               | 说明                                                                                   |
|---------|-------|----------------------|------------------|--------------------------------------------------------------------------------------|
| status  | 处理状态  | string               | SUCC_DONE        | - SUCC_DONE : 处理成功<br> - FAIL_PRM : 参数错误<br> - FAIL_BIZ : 业务异常<br> - FAIL_SYS : 系统异常 |
| message | 应答信息  | string               | 签名异常             | 处理异常信息                                                                               |
| data    | 户型名数组 | map<number,string[]> | 小区户型映射           |                                                                                      |
| - <房间数> | 房间数   | number               | 1                |                                                                                      |
| - 户型名数组 | 户型名   | string               | \["1室1厅","1室2厅"] |                                                                                      |

### 示例

```json
{
  "message": "成功",
  "status": "SUCC_DONE",
  "data": {
    1: [
      "1室1厅",
      "1室2厅"
    ],
    2: [
      "2室1厅",
      "2室2厅"
    ],
    3: [
      "3室1厅",
      "3室2厅"
    ],
    4: [
      "4室1厅",
      "4室2厅",
      "4室3厅"
    ]
  }
}
```