# 4.2 新增更新租房
## 请求头（http header）

- [参考报文请求头](..%2F1_%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84%2F1_%E8%AF%B7%E6%B1%82%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84.md)
- 新增更新租房接口
- etc-api-code: rentApi

## 请求体（http body）

| 字段参数                   | 字段名称       | 是否必填 | 字段格式        | 备注说明                                                                                                                                             | 示例                  |
|------------------------|------------|------|-------------|--------------------------------------------------------------------------------------------------------------------------------------------------|---------------------|
| roomParam              | 房源对象       | 是    |             |                                                                                                                                                  |                     |
| estateId               | 小区ID       | 是    | Long(20)    |                                                                                                                                                  | 9095370697957469952 |
| buildingName           | 楼栋名称       | 是    | string(20)  | 不支持修改                                                                                                                                            | 1栋                  |
| cellName               | 单元名称       | 是    | string(20)  | 不支持修改，如果没有单元传“无单元”                                                                                                                               | 2单元                 |
| roomName               | 房号         | 是    | string(20)  | 不支持修改                                                                                                                                            | 302                 |
| propertyManagementType | 物业类型       | 是    | int         | 不支持修改，1:住宅,2:商住,3:别墅,4:商铺,5:写字楼,6:酒店式公寓,7:厂房,8:仓库,9:车位                                                                                           | 1                   |
| roomCount              | 室          | 是    | int         | 范围1-20                                                                                                                                           | 3                   |
| hallCount              | 厅          | 是    | int         | 范围0-10                                                                                                                                           | 2                   |
| toiletCount            | 卫          | 是    | int         | 范围0-10                                                                                                                                           | 1                   |
| buildingArea           | 建筑面积       | 是    | BigDecimal  | 支持2位小数                                                                                                                                           | 65.89               |
| orientation            | 朝向         | 是    | int         | 1:东,2:南,3:西,4:北,5:南北,6:东西,7:东南,8:东北,9:西南,10:西北,11:东西北,12:东西南,13:南北东,14:南北西,15:东南西北                                                               | 1                   |
| floorTotal             | 总楼层        | 是    | int         |                                                                                                                                                  | 18                  |
| floor                  | 所在楼层       | 是    | int         |                                                                                                                                                  | 10                  |
| roomPerFloor           | 每层户数       | 是    | int         |                                                                                                                                                  | 4                   |
| isLeft                 | 是否有电梯      | 否    | int         | 1:有 0:无                                                                                                                                          | 0                   |
| completionTime         | 建成年代       | 否    | int         | 1937-2023                                                                                                                                        | 2005                |
| propertyRightDate      | 产权年限       | 否    | int         | 1-70年、2-40年、3-50年                                                                                                                                | 1                   |
| propertyType           | 产权类型       | 否    | int         | 1:商品房,2:房改房,3:已购公房,4:经济适用房,5:回迁安置房,6:军产房,7:校产房,8:使用权房,9:集体房,10:其他,11:公产房,12:企业产房                                                                 | 1                   |
| buildingType           | 建筑形态       | 否    | int         | 1:多层,2:小高层,3:高层,4:独栋,5:联排,6:双拼,7:叠拼,8:洋房,9:平房,10:复式,11:其他                                                                                        | 2                   |
| structure              | 房屋结构       | 否    | int         | 1:板楼,2:塔楼,3:板塔结合,4:砖混,5:其他                                                                                                                       | 1                   |
| stairPerFloor          | 梯数         | 否    | int         | 1-10                                                                                                                                             | 2                   |
| innerArea              | 套内面积       | 否    | BigDecimal  | 支持2位小数                                                                                                                                           | 34.68               |
| delegationParam        | 委托对象       | 是    |             |                                                                                                                                                  |                     |
| delegationId           | 房源委托id     | 否    | Long(20)    | 更新场合必填                                                                                                                                           | 9095370980284460803 |
| decorate               | 装修情况       | 是    | int         | 1:毛坯,2:简装,3:中装,4:精装,5:豪装                                                                                                                         | 1                   |
| priceTotal             | 租价(单位:元/月) | 是    | BigDecimal  |                                                                                                                                                  | 3400                |
| share                  | 盘别         | 是    | int         | 1:私盘 2:公盘                                                                                                                                        | 1                   |
| srcHouseId             | 第三方房源id    | 是    | string(100) |                                                                                                                                                  |                     |
| employeePhone          | 房源归属经纪人手机号 | 是    | string(11)  | 11位，saas创建的员工手机号                                                                                                                                 | 13912345678         |
| rentType               | 出租方式       | 是    | int         | 1：整租 2：合租                                                                                                                                        | 1                   |
| balconyNum             | 阳台数        | 否    | int         | 范围0-5                                                                                                                                            | 1                   |
| kitchenNum             | 厨房数        | 否    | int         | 范围0-5                                                                                                                                            | 1                   |
| premisesPermitDate     | 房本年限       | 否    | int         | 1-不满二、2-满二、3-满五                                                                                                                                  | 2                   |
| level                  | 房屋等级       | 否    | Long        | 1-A级、2-B级、3-C级、4-D级                                                                                                                              | 1                   |
| isUnique               | 是否唯一       | 否    | int         | 0-否 1-是                                                                                                                                          | 1                   |
| propertyStatus         | 房屋现状       | 否    | int         | 1-空闲、2-自住、3-出租、4-其他                                                                                                                              | 1                   |
| seeHousePoint          | 看房时间       | 否    | int         | 1-随时可看、2-电话预约、3-暂不可看                                                                                                                             | 1                   |
| creatDate              | 挂牌委托时间     | 否    | Date        |                                                                                                                                                  |                     |
| title                  | 房源标题       | 否    | string      |                                                                                                                                                  |                     |
| description            | 房源描述       | 否    | string      |                                                                                                                                                  |                     |
| viewImage              | 房源实勘图      | 否    | List        | 请查看【图片对象】                                                                                                                                        |                     |
| checkInDate            | 可入住时间      | 否    | Date        |                                                                                                                                                  |                     |
| depositTypes           | 押金方式       | 否    | int         | 99-未知，1-无押金，2-押金为一个月租金，3-押金为两个月租金，4-押金为三个月租金，5-自定义押金                                                                                             |                     |
| paymentTypes           | 付款方式       | 否    | int         | 99-未知，1-月付，2-季付，3-半年付，4-年付，5-双月付                                                                                                                 |                     |
| facilities             | 房屋设施       | 否    | String      | 多选，英文逗号间隔；(1-床, 2-衣柜, 3-智能门锁, 4-书桌, 5-暖气, 6-天然气, 7-WIFI,8-电视机, 9-冰箱, 10-洗衣机, 11-空调, 12-热水器, 13-微波炉, 14-抽油烟机, 15-燃气, 16-电磁炉, 17-沙发, 18-餐桌, 19-橱柜) | 1,3                 |
| lookTime               | 可看房时间      | 否    | int         | 1-随时可看,2-下班后可看,3-提前预约,4-周末可看                                                                                                                     |                     |
| tenantDemand           | 租客要求       | 否    | String      | 多选，英文逗号间隔； (0-无要求,1-单身,2-仅限女生,3-仅限男生,4-家庭优先,5-爱干净,6-不吸烟,7-无宠物)                                                                                   | 1,3                 |

## 图片对象

| 字段参数    | 字段名称  | 是否必填 | 字段格式    | 备注说明       | 示例 |
|---------|-------|------|---------|------------|----|
| url     | 图片url | 是    | string  | 外网可访问的图片地址 |    |
| isCover | 是否封面图 | 是    | Boolean |            |    |


### 示例

```json
{
  "roomParam": {
    "estateId": 9095358172557766400,
    "buildingName": "2栋",
    "cellName": "1单元",
    "roomName": "0203",
    "propertyManagementType": 1,
    "roomCount": 2,
    "hallCount": 2,
    "toiletCount": 1,
    "buildingArea": 58.56,
    "orientation": 1,
    "floorTotal": 6,
    "floor": 2,
    "roomPerFloor": 5,
    "isLeft": 0,
    "completionTime": 1996,
    "propertyRightDate": 1,
    "propertyType": 1,
    "buildingType": 1,
    "structure": 2,
    "innerArea": 48.34
  },
  "delegationParam": {
    "rentType": 1,
    "decorate": 4,
    "priceTotal": 865,
    "description": "描述信息",
    "depositTypes": 1,
    "paymentTypes": 1,
    "facilities": "1,2",
    "lookTime": "2",
    "tenantDemand": "1,2",
    "share": 1,
    "srcHouseId": "abc100000002",
    "employeePhone": "13140075222",
    "premisesPermitDate": 1,
    "level": 2,
    "isUnique": 0,
    "propertyStatus": 1,
    "seeHousePoint": 1,
    "viewImage": [
      {
        "url": "https://img2.baidu.com/it/u=1565964969,2683838366&fm=253&fmt=auto&app=138&f=JPEG?w=1067&h=800"
      }
    ]
  }
}
```

## 同步应答（http body）

| 字段      | 名称     | 类型     | 示例                    | 说明              |
|---------|--------|--------|-----------------------|-----------------|
| status  | 处理状态   | string | SUCC_DONE             | [应答状态表](#应答状态表) |
| message | 应答信息   | string | 签名异常                  | 处理异常信息          |
| data    | 处理结果数据 | json   | {"k1":"v2","k2":"v2"} | 各个业务接口有不同应答数据内容 |

| 字段名          | 备注说明             | 示例                  |
|--------------|------------------|---------------------|
| delegationNo | 房源编号(Saas系统搜索使用) | FZ00000001          |
| delegationId | 房源委托id(更新场合必填)   | 9095370980284460803 |

```json
{
  "status": "SUCC_DONE",
  "message": "成功",
  "data": {
    "delegationNo": "FZ00000001",
    "delegationId": 9095370980284460803
  }
}
```