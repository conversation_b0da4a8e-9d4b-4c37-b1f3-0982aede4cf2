# 4.3 新增更新租房房间

## 请求头（http header）

- [参考报文请求头](..%2F1_%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84%2F1_%E8%AF%B7%E6%B1%82%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84.md)
- 新增更新租房房间接口(合租场合使用)
- etc-api-code: rentRoomApi

## 请求体（http body）

| 字段参数                | 字段名称         | 是否必填 | 字段格式       | 备注说明                                                                                                                                             | 示例                  |
|---------------------|--------------|------|------------|--------------------------------------------------------------------------------------------------------------------------------------------------|---------------------|
| delegationRoomParam | 租房房间对象(合租用)  | 是    |            |                                                                                                                                                  |                     |
| delegationId        | 房源委托id       | 是    | Long(20)   |                                                                                                                                                  | 9095370980284460803 |
| roomName            | 房间名称         | 是    | string     |                                                                                                                                                  |                     |
| roomType            | 房间类型         | 是    | int        | 99-未知，1-主卧，2-次卧，3-隔断                                                                                                                             | 1                   |
| towards             | 房间朝向         | 是    | int        | 1-东,2-南,3-西,4-北,5-东南,6-东北,7-西南,8-西北,9-南北,10-东西                                                                                                   | 1                   |
| roomArea            | 房间出租面积       | 是    | BigDecimal |                                                                                                                                                  |                     |
| roomPrice           | 房间租金(单位:元/月) | 是    | BigDecimal |                                                                                                                                                  |                     |
| srcHouseId          | 第三方房源id      | 是    | string     |                                                                                                                                                  |                     |
| employeePhone       | 房源归属经纪人手机号   | 是    | string     |                                                                                                                                                  |                     |
| delegationRoomId    | 房间id         | 否    | Long       | 更新场合必填                                                                                                                                           |                     |
| description         | 房源描述         | 否    | string     |                                                                                                                                                  |                     |
| roomImage           | 房间图          | 否    | List       | 请查看【图片对象】                                                                                                                                        |                     |
| checkInDate         | 可入住时间        | 否    | Date       |                                                                                                                                                  |                     |
| depositTypes        | 押金方式         | 否    | int        | 99-未知，1-无押金，2-押金为一个月租金，3-押金为两个月租金，4-押金为三个月租金，5-自定义押金                                                                                             |                     |
| paymentTypes        | 付款方式         | 否    | int        | 99-未知，1-月付，2-季付，3-半年付，4-年付，5-双月付                                                                                                                 |                     |
| facilities          | 房屋设施         | 否    | String     | 多选，英文逗号间隔；(1-床, 2-衣柜, 3-智能门锁, 4-书桌, 5-暖气, 6-天然气, 7-WIFI,8-电视机, 9-冰箱, 10-洗衣机, 11-空调, 12-热水器, 13-微波炉, 14-抽油烟机, 15-燃气, 16-电磁炉, 17-沙发, 18-餐桌, 19-橱柜) | 1,3                 |
| lookTime            | 可看房时间        | 否    | int        | 1-随时可看,2-下班后可看,3-提前预约,4-周末可看                                                                                                                     |                     |
| tenantDemand        | 租客要求         | 否    | String     | 多选，英文逗号间隔； (0-无要求,1-单身,2-仅限女生,3-仅限男生,4-家庭优先,5-爱干净,6-不吸烟,7-无宠物)                                                                                   | 1,3                 |
| timeLimitMin        | 最短租期         | 否    | int        | 1-1个月，2-2个月，3-3个月，4-4个月，5-5个月，6-6个月，7-7个月，8-8个月，9-9个月，10-10个月，11-11个月，12-1年，24-2年，36-3年， 99-未知                                                   |                     |
| timeLimitMax        | 最长租期         | 否    | int        | 1-1个月，2-2个月，3-3个月，4-4个月，5-5个月，6-6个月，7-7个月，8-8个月，9-9个月，10-10个月，11-11个月，12-1年，24-2年，36-3年， 999-不限，99-未知                                            |                     |
| windowTypes         | 窗类型          | 否    | int        | 1-无窗,2-内窗,3-外窗,4-其它                                                                                                                              |                     |
| isPartition         | 隔断类型         | 否    | int        | 0-非隔断间，1-隔断间                                                                                                                                     |                     |
| isSeparateToilet    | 是否有独立卫生间     | 否    | int        | 0-无，1-有                                                                                                                                          |                     |
| isSeparateBalcony   | 是否有独立阳台      | 否    | int        | 0-无，1-有                                                                                                                                          |                     |
| creatDate           | 挂牌委托时间       | 否    | Date       |                                                                                                                                                  |                     |

## 图片对象

| 字段参数    | 字段名称  | 是否必填 | 字段格式    | 备注说明       | 示例 |
|---------|-------|------|---------|------------|----|
| url     | 图片url | 是    | string  | 外网可访问的图片地址 |    |
| isCover | 是否封面图 | 是    | Boolean |            |    |


### 示例

```json
{
  "delegationRoomParam": {
    "delegationId": 9095359387228204803,
    "roomName": "清风",
    "roomType": 1,
    "towards": 1,
    "roomArea": 15,
    "roomPrice": 400,
    "rentType": 2,
    "decorate": 4,
    "priceTotal": 888,
    "description": "描述",
    "depositTypes": 1,
    "paymentTypes": 1,
    "facilities": "1,3",
    "lookTime": "2",
    "tenantDemand": "1,3,4",
    "isPartition": 1,
    "share": 1,
    "srcHouseId": "abc100000002",
    "employeePhone": "13140075222",
    "premisesPermitDate": 1,
    "level": 2,
    "isUnique": 0,
    "propertyStatus": 1,
    "seeHousePoint": 1,
    "roomImage": [
      {
        "url": "https://img2.baidu.com/it/u=1565964969,2683838366&fm=253&fmt=auto&app=138&f=JPEG?w=1067&h=800"
      }
    ]
  }
}
```

## 同步应答（http body）

| 字段      | 名称     | 类型     | 示例                    | 说明              |
|---------|--------|--------|-----------------------|-----------------|
| status  | 处理状态   | string | SUCC_DONE             | [应答状态表](#应答状态表) |
| message | 应答信息   | string | 签名异常                  | 处理异常信息          |
| data    | 处理结果数据 | json   | {"k1":"v2","k2":"v2"} | 各个业务接口有不同应答数据内容 |

| 字段名              | 备注说明             | 示例                  |
|------------------|------------------|---------------------|
| delegationNo     | 房源编号(Saas系统搜索使用) | FZ00000002          |
| delegationId     | 房源委托id           | 9095370980284460803 |
| delegationRoomId | 房间id(更新场合必填)     | 9095359471248502528 |

```json
{
  "status": "SUCC_DONE",
  "message": "成功",
  "data": {
    "delegationId": 9095359387228204803,
    "delegationRoomId": 9095359471248502528,
    "delegationNo": "FZ00000002"
  }
}
```