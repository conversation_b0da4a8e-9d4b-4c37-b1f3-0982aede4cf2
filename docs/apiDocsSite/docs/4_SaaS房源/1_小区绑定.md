# 4.1 小区绑定

## 请求头（http header）

- [参考报文请求头](..%2F1_%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84%2F1_%E8%AF%B7%E6%B1%82%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84.md)
- 新增或修改小区接口
- etc-api-code: estateApi

## 请求体（http body）

| 字段参数                     | 字段名称   | 是否必填 | 字段格式       | 备注说明              | 示例                  |
|--------------------------|--------|------|------------|-------------------|---------------------|
| estateId                 | 小区ID   | 否    | Long(20)   | 小区ID 全局唯一 更新场合必填  | 9095370697957469952 |
| estateName               | 小区名称   | 是    | string(20) |                   | 慧芝湖花园               |
| estateAlias              | 小区别名   | 否    | string(20) |                   | "新江湾城"              |
| districtId               | 区域id   | 是    | Long(20)   | 请查看【通过城市获取区域列表接口】 | 静安                  |
| areaId                   | 板块id   | 是    | Long(20)   | 请查看【通过区域获取板块列表接口】 | 大宁                  |
| longitude                | 高德经度   | 否    | BigDecimal |                   | 121.458773          |
| latitude                 | 高德纬度   | 否    | BigDecimal |                   | 31.281544           |
| estateExecutionAddress   | 小区行政地址 | 否    | string(50) |                   | 静安区平型关路1083弄        |
| estateCertificateAddress | 小区产权地址 | 否    | string(50) |                   | 静安区平型关路1083弄        |


### 示例

```json
{
  "estateName": "上明新苑",
  "districtId": "410102",
  "areaId": "100031298"
}
```

## 同步应答（http body）

| 字段      | 名称     | 类型     | 示例                    | 说明              |
|---------|--------|--------|-----------------------|-----------------|
| status  | 处理状态   | string | SUCC_DONE             | [应答状态表](#应答状态表) |
| message | 应答信息   | string | 签名异常                  | 处理异常信息          |
| data    | 处理结果数据 | json   | {"k1":"v2","k2":"v2"} | 各个业务接口有不同应答数据内容 |

| 字段名          | 备注说明 | 示例                  |
|--------------|------|---------------------|
| districtId   | 区域id | 410102              |
| areaId       | 板块id | 100031298           |
| districtName | 区域名  | 中原区                 |
| estateId     | 小区id | 9095370697957469952 |
| estateName   | 小区名  | 上明新苑                |
| merchantId   | 公司id | 9095201533893570305 |
| cityId       | 城市id | 410100              |
| provinceName | 省份   | 郑州市                 |

```json
{
  "status": "SUCC_DONE",
  "message": "成功",
  "data": {
    "districtId": "410102",
    "areaId": "100031298",
    "districtName": "中原区",
    "estateId": "9095370697957469952",
    "estateName": "上明新苑",
    "merchantId": "9095201533893570305",
    "cityId": "410100",
    "provinceName": "郑州市"
  }
}
```