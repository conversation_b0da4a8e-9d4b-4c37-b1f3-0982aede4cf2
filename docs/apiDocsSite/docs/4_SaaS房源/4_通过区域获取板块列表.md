# 4.4 通过区域获取板块列表
## 请求头（http header）

- [参考报文请求头](..%2F1_%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84%2F1_%E8%AF%B7%E6%B1%82%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84.md)
- 通过区域获取板块列表接口
- etc-api-code: areaApi

## 请求体（http body）

| 字段参数                  | 字段名称 | 是否必填 | 字段格式     | 备注说明                          | 示例           |
|-----------------------|------|------|----------|-------------------------------|--------------|
| districtId              | 区域id | 是    | Long(20) |                       |      310101        |



### 示例

```json
{
  "districtId":310101
}
```

## 同步应答（http body）

| 字段      | 名称     | 类型     | 示例                    | 说明              |
|---------|--------|--------|-----------------------|-----------------|
| status  | 处理状态   | string | SUCC_DONE             | [应答状态表](#应答状态表) |
| message | 应答信息   | string | 签名异常                  | 处理异常信息          |
| data    | 处理结果数据 | json   | {"k1":"v2","k2":"v2"} | 各个业务接口有不同应答数据内容 |

|  字段名  | 备注说明 | 示例           |
|------|------|--------------|
| areaId | 板块id |      100020142 |
| districtId | 区域id |      310101 |
| areaName | 板块名  |      董家渡 |


```json
{
  "status": "SUCC_DONE",
  "message": "成功",
  "data": [
        {
          "areaId": "100020142",
          "districtId": "310101",
          "areaName": "董家渡"
        },
        {
          "areaId": "100020143",
          "districtId": "310101",
          "areaName": "黄浦滨江"
        },
        {
          "areaId": "100020144",
          "districtId": "310101",
          "areaName": "老西门"
        },
        {
          "areaId": "100020145",
          "districtId": "310101",
          "areaName": "南京东路"
        },
        {
          "areaId": "100020146",
          "districtId": "310101",
          "areaName": "蓬莱公园"
        },
        {
          "areaId": "100020147",
          "districtId": "310101",
          "areaName": "人民广场"
        },
        {
          "areaId": "100020148",
          "districtId": "310101",
          "areaName": "豫园"
        }
    ]
}
```