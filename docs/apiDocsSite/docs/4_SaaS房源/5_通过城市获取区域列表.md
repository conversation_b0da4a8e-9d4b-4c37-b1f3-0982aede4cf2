# 4.5 通过城市获取区域列表
## 请求头（http header）

- [参考报文请求头](..%2F1_%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84%2F1_%E8%AF%B7%E6%B1%82%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84.md)
- 通过城市获取区域列表接口
- etc-api-code: districtApi

## 请求体（http body）

| 字段参数                  | 字段名称   | 是否必填 | 字段格式        | 备注说明                          | 示例           |
|-----------------------|--------|------|-------------|-------------------------------|--------------|
| cityName              | 城市名    | 是    | string(20)  |                       |      上海        |


### 示例

```json
{
  "cityName":"上海"
}
```

## 同步应答（http body）

| 字段      | 名称     | 类型     | 示例                    | 说明              |
|---------|--------|--------|-----------------------|-----------------|
| status  | 处理状态   | string | SUCC_DONE             | [应答状态表](#应答状态表) |
| message | 应答信息   | string | 签名异常                  | 处理异常信息          |
| data    | 处理结果数据 | json   | {"k1":"v2","k2":"v2"} | 各个业务接口有不同应答数据内容 |

|  字段名  | 备注说明 | 示例           |
|------|------|--------------|
| districtId | 区域id |      310101 |
| districtName | 区域名  |      黄浦区 |
| cityId | 城市id |      310100 |

### 示例

```json
{
  "status": "SUCC_DONE",
  "message": "成功",
  "data": [
        {
            "districtId": "310101",
            "districtName": "黄浦区",
            "cityId": "310100"
        },
        {
            "districtId": "310104",
            "districtName": "徐汇区",
            "cityId": "310100"
        },
        {
            "districtId": "310105",
            "districtName": "长宁区",
            "cityId": "310100"
        }
    ]
}
```