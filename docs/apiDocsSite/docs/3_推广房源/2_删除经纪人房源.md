# 3.2 删除经纪人房源

## 请求头（http header）

- [参考报文请求头](..%2F1_%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84%2F1_%E8%AF%B7%E6%B1%82%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84.md)
- etc-api-code: deleteBrokerDelegates

## 请求体（http body）

### 字段

| 字段名            | 字段描述       | 说明             | 是否必须  | 类型及长度       | 示例                  |
|----------------|------------|----------------|-------|-------------|---------------------|
| brokerId       | 经纪人编号      | 创建经纪人时生成的经纪人编号 | true  | number(20)  | 12345678987654321   |
| sourceId       | 来源端房源唯一标识  | 删除指定房源         | false | string(127) | 9096420778400836609 |
| parentSourceId | 来源端父房源唯一标识 | 删除指定父房源下所有房源   | false | string(127) | 9096420778400836609 |
| reason         | 删除原因       |                | true  | string(63)  | BROKER              |

### 示例:

```json
{
  "brokerId": 9095985342271461320,
  "sourceId": "5aded06f-996e-43c9-b3cd-c64e624bf4b0",
  "reason": "测试"
}

```

## 同步应答（http body）

### 字段

| 字段      | 名称   | 类型     | 示例        | 说明                                                                                   |
|---------|------|--------|-----------|--------------------------------------------------------------------------------------|
| status  | 处理状态 | string | SUCC_DONE | - SUCC_DONE : 处理成功<br> - FAIL_PRM : 参数错误<br> - FAIL_BIZ : 业务异常<br> - FAIL_SYS : 系统异常 |
| message | 应答信息 | string | 签名异常      | 处理异常信息                                                                               |
|

### 示例

```json
{
  "status": "SUCC_DONE",
  "message": "成功"
}
```

## 异步通知（http body）

> 无异步通知
