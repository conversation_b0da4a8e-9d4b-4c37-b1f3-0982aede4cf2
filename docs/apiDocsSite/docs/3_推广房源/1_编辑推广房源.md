# 3.1 编辑推广房源

## 请求头（http header）

- [参考报文请求头](..%2F1_%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84%2F1_%E8%AF%B7%E6%B1%82%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84.md)
- etc-api-code: editDelegation

## 请求体（http body）

### 字段

| 字段名                       | 字段描述       | 说明                                                                                                                                                              | 是否必须     | 类型及长度              | 示例                                     |
|---------------------------|------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------|----------|--------------------|----------------------------------------|
|                           |            | -----**_元信息_** -----                                                                                                                                            |          |                    |                                        |
| sourceId                  | 来源端房源唯一标识  | 作为房源状态变更通知的唯一标识                                                                                                                                                 | true     | string(127)        | 9096420778400836609                    |
| parentSourceId            | 来源端父房源唯一标识 | 关联父房源唯一标识                                                                                                                                                       | false    | string(127)        | 9096420778400836609                    |
| brokerId                  | 经纪人编号      | 创建经纪人时生成的经纪人编号                                                                                                                                                  | true     | number(20)         | 12345678987654321                      |
| level                     | 房源层级       | - COMPANY:公司级<br>- BROKER:经纪人级<br>- CHANNEL:渠道级                                                                                                                 | true     | string(63)         | BROKER                                 |
| type                      | 委托类型       | - SALE:二手房<br>- RENT:租房<br>- NEW:新房                                                                                                                             | true     | string(63)         | SALE                                   |
| subType                   | 交易子类型      | - SALE_FULL:二手整售<br>- RENT_FULL:整租<br>- RENT_SHARE:合租<br>- NEW_NORMAL:标准新房                                                                                      | true     | string(63)         | RENT_FULL                              |
| channelCodes              | 上架渠道       | - XIANYU:闲鱼<br>- ALIPAY:支付宝<br>- PRIVATE:个人店                                                                                                                    | true     | array(string)      | \["XIANYU","ALIPAY","PRIVATE"\]        |
| channelUp                 | 渠道上下架      | - true:上架<br>- false:下架<br>- null:只编辑不上下架                                                                                                                       | false    | boolean            | true                                   |
|                           |            | -----**_挂牌信息_** -----                                                                                                                                           |          |                    |                                        |
| title                     | 标题         |                                                                                                                                                                 | true     | string(100)        | 牛成村精装带阳台一房一可以宠物停车方便                    |
| description               | 描述         |                                                                                                                                                                 | true     | string(300)        | 牛成村第二工业区房源，拎包即住，温馨如家                   |
| priceTotal                | 总价或租金      | - 新房&二手:万元<br>- 租房:元                                                                                                                                            | true     | number(10,2)       | 1900                                   |
| priceUnit                 | 单价或押金      | 单位:元                                                                                                                                                            | true     | number(10,2)       | 50000                                  |
| medias                    | 多媒体资料      | 媒体信息                                                                                                                                                            | true     | array(object)      |                                        |
| &nbsp;\|- type            | 类型         | - IMAGE:图片<br>- VIDEO:视频<br>- AUDIO:音频                                                                                                                          | true     | string(63)         | IMAGE                                  |
| &nbsp;\|- subtype         | 子类型        | - 实景图<br>- 户型图<br>- 视频<br>- 小区图                                                                                                                                 | 租房必填     | string(63)         | 实景图                                    |
| &nbsp;\|- url             | 地址         |                                                                                                                                                                 | true     | string(63)         | https://fyoss.fangyou.com/7d8ee08.jpg  |
| &nbsp;\|- cover           | 封面         | 视频时需要                                                                                                                                                           | false    | string(63)         | https://fyoss.fangyou.com/97d8ee08.jpg |
| &nbsp;\|- name            | 名称         |                                                                                                                                                                 | false    | string(63)         | 客厅                                     |
| labels                    | 关键字        | 可选值                                                                                                                                                             | false    | array(string)      | \["拎包入住","拎包即住","南北通透"\]               |
| brokerService             | 中介服务       | - 中介费特惠<br>- 接送服务<br>- 十年房产经验<br>- 到店送礼<br>- 成交返红包好礼                                                                                                            | false    | array(string)      | \["中介费特惠","接送服务"\]                     |
| boutique                  | 是否精品       | 在个人店里置顶                                                                                                                                                         | false    | boolean            | true                                   |
| listDate                  | 挂牌日期       |                                                                                                                                                                 | 新房&二手房必填 | string(yyyy-MM-dd) | 2025-04-23                             |                                        |
| lookType                  | 看房时间类型     | - 随时可看<br>- 暂不可看<br>- 提前预约<br>- 周末可看                                                                                                                            | false    | string(63)         | 随时可看                                   |
| checkinDate               | 入住日期       | 空表示随时入住                                                                                                                                                         | false    | string(yyyy-MM-dd) | 2025-04-23                             |
| deadline                  | 委托到期时间     |                                                                                                                                                                 | false    | string(yyyy-MM-dd) | 2025-07-23                             |
|                           |            | -----**_小区信息_** -----                                                                                                                                           |          |                    |                                        |
| communityAddress          | 小区地址       | 省市区名称即可                                                                                                                                                         | true     | string(255)        | 广东省深圳市南山区                              |
| communityName             | 小区名        |                                                                                                                                                                 | true     | string(127)        | 水湘人家                                   |
| propertyType              | 物业类型       | - 住宅 - 商住<br>- 别墅 - 商铺<br>- 写字楼<br>- 酒店式公寓<br>- 厂房 - 仓库<br>- 车位                                                                                                 | 新房&二手房必填 | string(63)         | 住宅                                     |
| propertyManagementCompany | 物业管理公司     |                                                                                                                                                                 | false    | string(127)        | 郑州凯旋物业管理有限公司                           |
| propertyManagementFee     | 物业管理费用     | 单位:元/㎡·月                                                                                                                                                        | false    | number(10,2)       | 2.8                                    |
| parkingRatio              | 车位配比       |                                                                                                                                                                 | false    | string(63)         | 1:1.2                                  |
| around                    | 周边配套       | - 餐饮 - 公交<br>- 学校 - 公园广场<br>- 超市 - 健身会馆<br>- 地铁房 - 菜场<br>- 医院 - 购物<br>- 便利店 - 可短租                                                                               | false    | array              | \["公交","学校","医院","购物","餐饮"\]           |
|                           |            | -----**_建筑信息_** -----                                                                                                                                           |          |                    |                                        |
| houseStructure            | 房屋结构       | - 独栋 - 联排<br>- 复式 - 平层<br>- 跃层 - 错层<br>- loft - 其他                                                                                                              | false    | string(63)         | 平层                                     |
| buildingCategory          | 建筑类别       | - 其他 - 低层<br>- 多层 - 小高层<br>- 高层 - 超高层                                                                                                                           | false    | string(63)         | 多层                                     |
| buildingType              | 建筑类型       | - 板楼 - 塔楼<br>- 塔板结合 - 叠拼<br>- 独栋 - 联排<br>- 双拼 - 平房<br>- 其他                                                                                                      | false    | string(63)         | 板楼                                     |
| completionTime            | 建成年代       | - 1937~2025                                                                                                                                                     | 新房&二手房必填 | string(63)         | 2020                                   |
| totalFloor                | 总楼层        |                                                                                                                                                                 | true     | integer(10)        | 10                                     |
| currentFloor              | 当前楼层       |                                                                                                                                                                 | true     | integer(10)        | 5                                      |
| roomPerFloor              | 每层户数       |                                                                                                                                                                 | false    | integer(10)        | 5                                      |
| tagElevator               | 是否有电梯      |                                                                                                                                                                 | 新房&二手房必填 | boolean            | true                                   |
| elevatorCount             | 电梯数量       |                                                                                                                                                                 | false    | integer(10)        | 3                                      |
|                           |            | -----**_房屋信息_** -----                                                                                                                                           |          |                    |                                        |
| buildName                 | 楼栋名        |                                                                                                                                                                 | 租房必填     | string(63)         | 1号楼                                    |
| unitName                  | 单元名        |                                                                                                                                                                 | 租房必填     | string(63)         | 5单元                                    |
| roomNum                   | 房号         |                                                                                                                                                                 | 租房必填     | string(63)         | 201                                    |
| buildingArea              | 建筑面积       |                                                                                                                                                                 | true     | number(10,2)       | 100                                    |
| useArea                   | 使用面积       |                                                                                                                                                                 | 合租必填     | number(10,2)       | 80                                     |
| efficiencyRate            | 得房率        |                                                                                                                                                                 | false    | number(10,2)       | 0.8                                    |
| orient                    | 朝向         | - 朝东 - 朝西<br> - 朝南 - 朝北<br> - 东南 - 东北<br>- 西南 - 西北<br> - 南北 - 东西                                                                                                | 新房&二手房必填 | string(63)         | 朝东                                     | string(63)         | 朝南                                     |
| roomCount                 | 房间数        |                                                                                                                                                                 | true     | integer(10)        | 3                                      |
| hallCount                 | 客厅数        |                                                                                                                                                                 | true     | integer(10)        | 2                                      |
| toiletCount               | 卫生间数       |                                                                                                                                                                 | true     | integer(10)        | 2                                      |
| redo                      | 装修情况编码     | - 毛坯 - 普装<br>- 精装 - 豪华                                                                                                                                          | true     | string(63)         | 精装                                     |
| parking                   | 是否有车位      |                                                                                                                                                                 | false    | boolean            | true                                   |
| saleReason                | 出售原因       |                                                                                                                                                                 | false    | string(63)         |                                        |
| houseSituation            | 房屋现状       | - 空房 - 自住<br>- 租客住 - 其他                                                                                                                                         | false    | string(63)         | 空房                                     |
| houseYears                | 房屋年限       | - 满五 - 满二<br>- 不满二                                                                                                                                              | false    | string(63)         | 满五                                     |
| soleHouse                 | 唯一住房       |                                                                                                                                                                 | false    | boolean            | true                                   |
|                           |            | -----**_权证信息_** -----                                                                                                                                           |          |                    |                                        |
| housePlanPurpose          | 房屋规划用途     | - 住宅 - 公寓<br>- 酒店 - 综合<br>- 其他                                                                                                                                  | false    | string(63)         | 住宅                                     |
| houseType                 | 产权类型       | - 商品房<br> - 经济适用房<br>- 回迁房<br> - 共有产权房<br>- 其他<br> - 公房                                                                                                         | 新房&二手房必填 | string(63)         | 商品房                                    |
| houseCertType             | 产权证类型      | - 不动产权证<br> - 房产证<br>- 网备合同<br> - 商品房备案号<br>- 其他权属证                                                                                                             | 徐州租房必填   | string(63)         | 不动产权证                                  |
| houseCertNO               | 产权证证号      |                                                                                                                                                                 | 徐州租房必填   | string(63)         | 2021101301F91308                       |
| houseCertAddress          | 房屋产证地址     |                                                                                                                                                                 | false    | string(63)         | 苏州市相城区                                 |
| propertyYears             | 产权年限       | - 10- 40<br>- 50 - 70<br>- 永久                                                                                                                                   | 新房&二手房必填 | string(63)         | 70                                     |
| propertyOwnership         | 产权所属       | - 共有产权<br>- 单独产权                                                                                                                                                | false    | string(63)         | 单独产权                                   |
| houseCertVerify           | 是否房产证验证    |                                                                                                                                                                 | 新房&二手房必填 | boolean            | true                                   |
| ownerName                 | 业主姓名       |                                                                                                                                                                 | 徐州租房必填   | string(63)         | 张三                                     |
| ownerCertNO               | 业主证件号      |                                                                                                                                                                 | 徐州租房必填   | string(63)         | 123456789123456789                     |
| bailorName                | 委托人姓名      |                                                                                                                                                                 | false    | string(63)         | 李四                                     |
| bailorCertType            | 委托人证件类型    | - 身份证                                                                                                                                                           | false    | string(63)         | 身份证                                    |
| bailorCertNO              | 委托人证件号码    |                                                                                                                                                                 | false    | string(63)         | 123456789123456789                     |
| bailorNames               | 委托人姓名集合    |                                                                                                                                                                 | false    | string(63)         | \["张三","李四"\]                          |
|                           |            | -----**_政府核验_** -----                                                                                                                                           |          |                    |                                        |
| govVerifyCode             | 政府核验码      | 二手房必填城市:<br>- 上海 - 杭州<br>- 宁波 - 厦门<br>- 武汉 - 深圳<br>- 成都                                                                                                         | false    | string(63)         | 1234567890                             |
| govVerifyUrl              | 政府核验码图片地址  |                                                                                                                                                                 | false    | string(63)         |                                        |
| govContractCode           | 政府委托协议编码   |                                                                                                                                                                 | false    | string(63)         |                                        |
| govPromoCode              | 政府房源推广码    |                                                                                                                                                                 | false    | string(63)         |                                        |
| houseBusiNO               | 房屋业务件号     | 政府核验相关                                                                                                                                                          | false    | string(63)         | 2023-GCHT-001                          |
|                           |            | -----**_租赁特有_** -----                                                                                                                                           |          |                    |                                        |
| payMonths                 | 付款月数       | 单位:月                                                                                                                                                            | 租房必填     | integer(10)        | 3                                      |
| depositMonths             | 押金月数       | 单位:月                                                                                                                                                            | 租房必填     | integer(10)        | 1                                      |
| roomType                  | 房间类型编码     | - 主卧 - 次卧<br>- 隔断 - 未知                                                                                                                                          | 合租必填     | string(63)         | 主卧                                     |
| roomName                  | 房间名        | - 主卧 - 次卧<br>- 隔断 - 未知                                                                                                                                          | 合租必填     | string(63)         | 201室                                   |
| equipments                | 配套设施       | - 空调 - 冰箱<br>- 洗衣机 - 燃气灶<br>- 热水器 - WIFI<br>- 电视机 - 电磁炉<br>- 暖气 - 独立卫生间<br> - 大阳台 - 微波炉<br>- 衣柜 - 床<br> - 智能门锁< - 书桌<br>- 天然气 - 抽油烟机<br>- 燃气 - 沙发<br> - 餐桌 - 橱柜 | false    | array(string)      | \["热水器","燃气灶","空调","冰箱","衣柜"\]         |

### 房源属性可选值

| 属性     | 场景  | 标签                                                                                                 |
|--------|-----|----------------------------------------------------------------------------------------------------|
| labels | 二手房 | - 满五 - 满二 - 不满二 - 近地铁 - 随时可看 - 新上 - 降价好房 - 满五唯一 - 唯一 - 七日热门 - 明厨明卫 - 带车位 - 南北通透 - 急售               |
| labels | 租房  | - 拎包即住 - 南北通透 - 家电齐全 - 交通便利 - 高速网络 - 允许宠物 - 精装修 - 带车位 - 近地铁 - 租金优惠 - 安静舒适 - 灵活租期 - 可短租 - 急租 - 拎包入住 |
| labels | 新房  | - 绿化率高 - 交通便利 - 车位充足 - 人车分流 - 品牌房企 - 置换改善 - 养老舒适 - 低密居所 - 精装交付 - 配套成熟                              |

### 示例:

```json
{
  "brokerId": 9095985342271461320,
  "sourceId": "%s",
  "buildingArea": 222,
  "type": "RENT",
  "useArea": 222,
  "roomNum": "010A",
  "freshCompanyDelegation": false,
  "communityName": "讴象社区菊园别墅二区",
  "roomCount": 1,
  "unitName": "无单元",
  "orient": "朝东",
  "level": "BROKER",
  "medias": [
    {
      "subtype": "实景图",
      "type": "IMAGE",
      "url": "https://fyoss-test.fangyou.com/250423163ec6487e0a3477bfb5f3209f010587e8b9c1e29e.jpg"
    }
  ],
  "sourceType": "SAAS",
  "toiletCount": 0,
  "totalFloor": 8,
  "hallCount": 0,
  "subType": "RENT_FULL",
  "currentFloor": 1,
  "description": "52525252525",
  "redo": "精装",
  "title": "别墅测试啊 1室 3000.00",
  "around": [
    "餐饮",
    "公交",
    "超市",
    "菜场"
  ],
  "communityAddress": "上海市嘉定区",
  "propertyType": "别墅",
  "priceTotal": 3000,
  "channelUp": true,
  "channelCode": "XIANYU",
  "buildName": "1幢",
  "equipments": [
    "衣柜",
    "热水器",
    "燃气",
    "冰箱",
    "空调"
  ],
  "payMonths": 2,
  "depositMonths": 1,
  "channelCodes": [
    "XIANYU",
    "ALIPAY"
  ]
}
```

## 同步应答（http body）

### 字段

| 字段                                 | 名称        | 类型          | 示例                    | 说明                                                                                                             |
|------------------------------------|-----------|-------------|-----------------------|----------------------------------------------------------------------------------------------------------------|
| status                             | 处理状态      | string      | SUCC_DONE             | - SUCC_DONE : 处理成功<br> - FAIL_PRM : 参数错误<br> - FAIL_BIZ : 业务异常<br> - FAIL_SYS : 系统异常                           |
| message                            | 应答信息      | string      | 签名异常                  | 处理异常信息                                                                                                         |
| data                               | 处理结果数据    | json        | {"k1":"v2","k2":"v2"} | 各个业务接口有不同应答数据内容                                                                                                |
| &nbsp;\|- sourceId                 | 请求端房源唯一标识 | string(127) | 9094407201969186566   | 原请求里的sourceId                                                                                                  |
| &nbsp;\|- channelDelegationMap     | 渠道房源映射    | json        |                       | 各渠道房源上架状态及错误信息                                                                                                 |
| &nbsp;\| &nbsp;\|-`<key>`          | 渠道编码      | string      |                       | - XIANYU:闲鱼<br>- ALIPAY:支付宝<br>- PRIVATE:个人店                                                                   |
| &nbsp;\| &nbsp;\| &nbsp;\|- status | 上架状态      | string      |                       | - UP_SUCC   : 上架成功<br> - UP_FAIL  : 上架失败 <br> - UP_ING : 上架中    <br> - DOWN_SUCC : 下架成功<br> - DOWN_ING  : 下架失败 |
| &nbsp;\| &nbsp;\| &nbsp;\|- remark | 错误信息      | string      | 账户绑定状态异常              |                                                                                                                |

### 示例

```json
{
  "status": "SUCC_DONE",
  "message": "成功",
  "data": {
    "sourceId": "9094407201969186566",
    "channelDelegationMap": {
      "ALIPAY": {
        "status": "UP_FAIL",
        "remark": "渠道经纪人[55,619]可上架房源数[200]已满,不能发布新房源!"
      },
      "XIANYU": {
        "status": "UP_SUCC",
        "remark": ""
      },
      "PRIVATE": {
        "status": "UP_SUCC",
        "remark": ""
      }
    }
  }
}
```

## 异步通知（http body）

> 异步通知的请求体和同步应答一致
