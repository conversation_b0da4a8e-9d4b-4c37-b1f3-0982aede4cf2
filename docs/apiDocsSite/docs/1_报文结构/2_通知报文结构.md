# 1.2 通知报文结构

## 通知配置

- 通知地址:生成秘钥时提供的通知地址
- 签名秘钥:与请求报文使用相同秘钥
- [签名算法:与请求报文使用相同算法](..%2F1_%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84%2F1_%E8%AF%B7%E6%B1%82%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84.md)

## 通知头（http header）

### 字段

| 字段            | 名称   | 格式                          | 必填 | 示例                                           | 说明                                                                   |
|---------------|------|-----------------------------|----|----------------------------------------------|----------------------------------------------------------------------|
| etc-key-code  | 秘钥号  | string:UUID                 | 是  | aef5fedee20f40d7b02a3d53e9b82f55             | 用于确认秘钥(及签名算法)                                                        |
| etc-api-code  | 接口代码 | string                      | 是  | pushCommnuity                                | 接口代码，见业务接口协议                                                         |
| etc-notify-id | 通知号  | string                      | 是  | 任意128位以内字符串                                  | 唯一约束(key-code+api-code+request-id)                                   |
| etc-time      | 时间戳  | string: yyyy-MM-dd HH:mm:ss | 是  | 2024-01-01 00:00:00                          | 不能偏离当前时间正负2分钟，防止请求重放                                                 |
| etc-sign      | 签名   | string:BASE64               | 是  | Utbbz3Me+xTX67rV/qrgGH2wtNFNJq+y3Eg9VjovKuM= | [签名算法](1_%E8%AF%B7%E6%B1%82%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84.md#%E7%AD%BE%E5%90%8D%E7%AE%97%E6%B3%95) |

## 通知体（http body）

### 字段

| 字段      | 名称     | 类型     | 示例                    | 说明                                                                                   |
|---------|--------|--------|-----------------------|--------------------------------------------------------------------------------------|
| status  | 处理状态   | string | SUCC_DONE             | - SUCC_DONE : 处理成功<br> - FAIL_PRM : 参数错误<br> - FAIL_BIZ : 业务异常<br> - FAIL_SYS : 系统异常 |
| message | 应答信息   | string | 签名异常                  | 处理异常信息                                                                               |
| data    | 处理结果数据 | json   | {"k1":"v2","k2":"v2"} | 各个业务接口有不同应答数据内容                                                                      |

### 示例

```json
{
  "status": "SUCC_DONE",
  "message": "成功",
  "data": {
    "responseKey2": "value2",
    "responseKey1": "value1"
  }
}
```

## 应答结果（http body）

- 处理正常:RECEIVE_NOTIFY_SUCCESS
- 处理异常:<相关错误信息>
