# 1.1 请求报文结构

## 请求地址

- 测试环境: https://saasapi-test.ebaas.com/gateway/invoke
- 生产环境: https://saasapi.ebaas.com/gateway/invoke

## 请求头（http header）

### 字段

| 字段             | 名称     | 格式                          | 必填 | 示例                                           | 说明                                 |
|----------------|--------|-----------------------------|----|----------------------------------------------|------------------------------------|
| etc-key-code   | 秘钥号    | string:UUID                 | 是  | 8f0372d5cb11442bb2952f86703dbea9             | 用于确认秘钥(及签名算法)                      |
| etc-api-code   | 接口代码   | string                      | 是  | mockApi                                      | 接口代码，见业务接口协议                       |
| etc-request-id | 商户侧请求号 | string                      | 是  | 任意128位以内字符串                                  | 唯一约束(key-code+api-code+request-id) |
| etc-time       | 时间戳    | string: yyyy-MM-dd HH:mm:ss | 是  | 2024-01-01 00:00:00                          | 不能偏离当前时间正负2分钟，防止请求重放               |
| etc-sign       | 签名     | string:BASE64               | 是  | krSHzxBrLOPWGvw3nMsDCUghlYnMIP5qrYL8jIP85cE= | [签名算法](#签名算法)                      |

### 签名算法

> 1. hander参数排序:
     - 对除etc-sign外所有http header参数，根据参数名称的ASCII码表的顺序排序;
> 2. 原文拼接:
     - 拼接顺序: `<headers>`(依次键值拼接) + `<body>` + `<key>`
> 3. 计算摘要:
     - 对拼装字符串计算签名: 取得utf-8字节码 -> SHA256摘要 -> base64编码。

### 签名示例

> 1. 原始数据:
     - headers:{etc-b=2, etc-a=1}
     - body:{} 
     - key:TN1WU0ggSqBBUPp7ZlhO6rVH8MoUQW/WFCp7LUyp0Gs=
> 2. 对headers排序:
     - {etc-a=1, etc-b=2}
> 3. 报文拼接:
     - etc-a1etc-b2{}TN1WU0ggSqBBUPp7ZlhO6rVH8MoUQW/WFCp7LUyp0Gs=
> 4. 签名结果:
     - krSHzxBrLOPWGvw3nMsDCUghlYnMIP5qrYL8jIP85cE=

## 请求体（http body）

### 格式

- 由业务接口定义的JSON字符串

### 示例

```json
{
  "requestKey1": "value1",
  "requestKey2": "value2"
}
```

## 应答结果（http body）

### 字段

| 字段      | 名称     | 类型     | 示例                    | 说明                                                                                   |
|---------|--------|--------|-----------------------|--------------------------------------------------------------------------------------|
| status  | 处理状态   | string | SUCC_DONE             | - SUCC_DONE : 处理成功<br> - FAIL_PRM : 参数错误<br> - FAIL_BIZ : 业务异常<br> - FAIL_SYS : 系统异常 |
| message | 应答信息   | string | 签名异常                  | 处理异常信息                                                                               |
| data    | 处理结果数据 | json   | {"k1":"v2","k2":"v2"} | 各个业务接口有不同应答数据内容                                                                      |

### 示例

```json
{
  "status": "SUCC_DONE",
  "message": "成功",
  "data": {
    "responseKey2": "value2",
    "responseKey1": "value1"
  }
}
```

