# 2.3 账号解绑

## 请求头（http header）

- [参考报文请求头](..%2F1_%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84%2F1_%E8%AF%B7%E6%B1%82%E6%8A%A5%E6%96%87%E7%BB%93%E6%9E%84.md)
- 解绑闲鱼账号接口
- etc-api-code: brokerUnbind

## 请求体（http body）

| 字段参数         | 字段名称 | 是否必填 | 字段格式     | 备注说明           | 示例                  |
|--------------|------|------|----------|----------------|---------------------|
| userId       | 用户ID | 是    | Long(20) |                | 9095370697957469952 |
| businessType | 业务类型 | 是    | int      | 业务类型 1 二手 2 租赁 | 1                   |


### 示例

```json
{
  "userId": "9095370697957469952",
  "businessType": "1"
}
```

## 同步应答（http body）

| 字段      | 名称     | 类型     | 示例                    | 说明              |
|---------|--------|--------|-----------------------|-----------------|
| status  | 处理状态   | string | SUCC_DONE             | - SUCC_DONE : 处理成功<br> - FAIL_PRM : 参数错误<br> - FAIL_BIZ : 业务异常<br> - FAIL_SYS : 系统异常 |
| message | 应答信息   | string | 签名异常                  | 处理异常信息          |
| data    | 处理结果数据 | json   |  | 解绑应答 |
| -status | 状态 | string   | UNBIND_EXECUTORY | 绑定状态 绑定中-BIND_ING,绑定成功-BIND_SUCC,绑定失败-BIND_FAIL,解绑成功-UNBIND_SUCC,解绑待执行-UNBIND_EXECUTORY,解绑失败-UNBIND_FAIL |

```json
{
  "status": "SUCC_DONE",
  "message": "成功",
  "data": {
    "status": "UNBIND_EXECUTORY"
  }
}
```

> 异步通知的请求体和同步应答一致