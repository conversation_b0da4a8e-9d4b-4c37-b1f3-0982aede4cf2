[toc]

### 1.小区POI

- 行数: 345748
- 更新: 2025-03-31 19:22:09
- 表结构

```sql
create table tb_community
(
    id                bigint                                   not null primary key,
    create_time       datetime(6) default CURRENT_TIMESTAMP(6) not null comment '创建时间',
    logic_delete      bit         default b'0'                 not null comment '是否逻辑删除:1是0否',
    update_time       datetime(6) default CURRENT_TIMESTAMP(6) not null comment '更新时间',
    version           bigint      default 0                    not null comment '版本号',
    address           varchar(255)                             null comment '简化小区地址',
    around_request    text                                     null comment '街道查询请求',
    around_response   json                                     null comment '街道查询应答',
    around_time       datetime(6)                              null comment '周边查询时间',
    busi_code         varchar(63)                              null comment '商圈编码',
    busi_name         varchar(63)                              null comment '商圈名称',
    city_code         varchar(127)                             null comment '城市编码',
    city_name         varchar(127)                             null comment '城市',
    district_name     varchar(127)                             null comment '行政区名称',
    formatted_address varchar(255)                             null comment '格式化小区地址',
    latitude          decimal(10, 6)                           null comment '纬度',
    location          varchar(255)                             null comment '(高德)经度',
    longitude         decimal(10, 6)                           null comment '纬度',
    name              varchar(255)                             null comment '小区名称',
    poi_id            varchar(63)                              null comment '高德POI ID',
    province_name     varchar(127)                             null comment '省份',
    town_code         varchar(63)                              null comment '街道编码',
    town_name         varchar(63)                              null comment '街道名称',
    town_request      text                                     null comment '街道查询请求',
    town_response     json                                     null comment '街道查询应答',
    type_code         varchar(63)                              null comment 'POI类型编码',
    type_name         varchar(255)                             null comment 'POI类型',
    city_id           bigint                                   null comment '城市ID',
    district_id       bigint                                   null comment '行政区ID',
    province_id       bigint                                   null comment '省份ID',
    detail_id         bigint                                   null comment '小区详情ID',
    community_bind_id bigint                                   null comment '小区绑定ID'
)
    comment '小区表' charset = utf8mb4;
```

### 2.小区详情

- 行数: 478388
- 更新: 2024-12-17 14:12:25
- 表结构

```sql
create table tb_community_detail
(
    id                        bigint                                   not null primary key,
    create_time               datetime(6) default CURRENT_TIMESTAMP(6) not null comment '创建时间',
    logic_delete              bit         default b'0'                 not null comment '是否逻辑删除:1是0否',
    update_time               datetime(6) default CURRENT_TIMESTAMP(6) not null comment '更新时间',
    version                   bigint      default 0                    not null comment '版本号',
    act_area                  decimal(20, 2)                           null comment '占地面积',
    address                   varchar(255)                             null comment '小区地址',
    brand_corp                varchar(255)                             null comment '品牌商',
    build_area                decimal(20, 2)                           null comment '建筑面积',
    build_max_year            varchar(255)                             null comment '建筑年代最大',
    build_min_year            varchar(255)                             null comment '建成年代最小',
    build_num                 int                                      null comment '楼栋总数',
    building_category         varchar(255)                             null comment '建筑类别',
    building_type             varchar(255)                             null comment '建筑类型',
    comm_belong               varchar(255)                             null comment '交易权属',
    community_close_flag      bit                                      null comment '小区是否封闭',
    community_name            varchar(255)                             null comment '小区名称',
    developer_corp            varchar(255)                             null comment '开发公司',
    district_full_name        varchar(255)                             null comment '完整行政区名',
    down_parking_num          int                                      null comment '地下车位数',
    gas_desc                  varchar(255)                             null comment '供气描述',
    green_rate                decimal(20, 2)                           null comment '绿化率',
    heating_desc              varchar(255)                             null comment '供暖情况',
    house_num                 int                                      null comment '房屋总数',
    house_type                varchar(255)                             null comment '房屋类型',
    parking_num               int                                      null comment '车位总数',
    parking_rate              varchar(255)                             null comment '车位配比率(户:车)',
    parking_sale_flag         bit                                      null comment '是否出售产权车位',
    person_div_car_flag       bit                                      null comment '是否人车分流',
    pic_url                   varchar(255)                             null comment '小区缩略图地址',
    powerd_desc               varchar(255)                             null comment '供电描述',
    property_fee              varchar(255)                             null comment '物业费',
    property_name             varchar(255)                             null comment '物业公司',
    property_phone            varchar(255)                             null comment '物业电话',
    property_type             varchar(255)                             null comment '物业类型',
    property_years            varchar(255)                             null comment '产权年限',
    set_parking_fee           varchar(255)                             null comment '固定停车费标准',
    src_community_id          bigint                                   null comment '原数据小区ID',
    temp_parking_fee          varchar(255)                             null comment '临时停车费标准',
    up_parking_num            int                                      null comment '地上车位数',
    volume_rate               decimal(20, 2)                           null comment '容积率',
    water_desc                varchar(255)                             null comment '供水描述',
    community_id              bigint                                   null comment '小区ID',
    community_bind_id         bigint                                   null comment '小区绑定ID',
    block_cd                  varchar(10)                              null comment '板块代码',
    block_name                varchar(20)                              null comment '板块名称',
    city_cd                   varchar(10)                              null comment '关联城市代码',
    city_name                 varchar(20)                              null comment '关联城市名称',
    community_no              varchar(20)                              null comment '小区编号',
    district_cd               varchar(10)                              null comment '关联区域代码',
    district_name             varchar(20)                              null comment '关联区域名称',
    gate_control_flag         bit                                      null comment '是否有门禁',
    home_name                 varchar(30)                              null comment '大盘名称',
    home_name_flag            bit                                      null comment '是否是总盘',
    intel_gate_flag           bit                                      null comment '是否安装智能道闸',
    monitor_flag              bit                                      null comment '是否有监控',
    pic_count                 int         default 0                    null comment '图片数量',
    pic_urls                  longtext                                 null comment '小区缩略图地址',
    police_networking_flag    bit                                      null comment '是否110联网',
    pro_service_addr          varchar(100)                             null comment '物业服务中心地址',
    property_on_time          varchar(20)                              null comment '物业工作时间',
    province_cd               varchar(10)                              null comment '关联省份代码',
    province_name             varchar(20)                              null comment '关联省份名称',
    security_allday_flag      bit                                      null comment '是否24小时值守',
    security_booth_num        int(4)                                   null comment '保安岗亭数',
    security_patrol_frequency varchar(63)                              null comment '保安巡逻频次,小时/次',
    security_person_num       int(4)                                   null comment '保安人员数',
    zip_cd                    varchar(20)                              null comment '邮编',
    icon_url                  varchar(255)                             null comment '小区图标地址',
    memo                      text                                     null comment '备注',
    verify                    tinyint(1)  default 0                    null comment '绑定小区验证通过',
    overlap_rate              decimal(20, 2)                           null comment '小区名称&地址与绑定小区名称地址文字重叠度'
)
    comment '小区详情' charset = utf8mb4;
```

### 3.小区户型

- 行数: 4295401
- 更新: 2023-08-29 16:27:25
- 问题解答
    + 看着是到户型上的，是否到房间上？
        * 未到房间上
- 表结构

```sql
create table tb_community_layout
(
    id                  bigint                                   not null primary key,
    create_time         datetime(6) default CURRENT_TIMESTAMP(6) not null comment '创建时间',
    logic_delete        bit         default b'0'                 not null comment '是否逻辑删除:1是0否',
    update_time         datetime(6) default CURRENT_TIMESTAMP(6) not null comment '更新时间',
    version             bigint      default 0                    not null comment '版本号',
    area                decimal(10, 2)                           null comment '面积',
    area_act            decimal(10, 2)                           null comment '套内面积',
    area_max            decimal(10, 2)                           null comment '最大面积',
    area_min            decimal(10, 2)                           null comment '最小面积',
    balcony_count       int(10)     default 0                    null comment '阳台数',
    hall_count          int(10)     default 0                    null comment '客厅数',
    kitchen_count       int(10)     default 0                    null comment '厨房数',
    main_layout_flag    bit                                      null comment '主力户型',
    name                varchar(255)                             null comment '户型名称',
    orientation         varchar(255)                             null comment '房间朝向',
    pic_url             varchar(255)                             null comment '户型图',
    room_count          int(10)     default 0                    null comment '房间数',
    src_community_id    bigint                                   null comment '原关联小区ID',
    src_layout_id       bigint                                   null comment '原户型ID',
    toilet_count        int(10)     default 0                    null comment '卫生间数',
    community_id        bigint                                   null comment '小区ID',
    community_detail_id bigint                                   null comment '小区详情ID',
    set_num             int(10)     default 0                    null comment '总套数',
    pic_url2            varchar(255)                             null comment '户型图'
)
    comment '小区户型' charset = utf8mb4;
```

### 4.小区图片表

- 行数: 1536145
- 更新: 2024-01-23 14:14:54
- 表结构

```sql
create table tb_community_picture
(
    id                  bigint                                   not null
        primary key,
    create_time         datetime(6) default CURRENT_TIMESTAMP(6) not null comment '创建时间',
    logic_delete        bit         default b'0'                 not null comment '是否逻辑删除:1是0否',
    update_time         datetime(6) default CURRENT_TIMESTAMP(6) not null comment '更新时间',
    version             bigint      default 0                    not null comment '版本号',
    type                varchar(20)                              null comment '来源类型',
    url2                varchar(511)                             null comment '图片地址',
    community_id        bigint                                   null comment '小区ID',
    community_detail_id bigint                                   null comment '小区详情ID',
    url                 varchar(511)                             null comment '图片地址',
    url_oss             varchar(511)                             null comment '图片地址'
)
    comment '小区图片表' charset = utf8mb4;
```

### 5.楼栋信息

- 行数: 3995146
- 更新: 2024-12-03 10:58:11
- 表结构

```sql
CREATE TABLE `building`
(
    `building_id`          bigint(11) NOT NULL COMMENT '楼栋ID',
    `building_no`          varchar(10)    DEFAULT NULL COMMENT '楼栋编号',
    `community_no`         varchar(20)    DEFAULT NULL COMMENT '小区编号',
    `source_building_id`   varchar(64)    DEFAULT NULL COMMENT '源楼栋号',
    `community_id`         bigint(11)     DEFAULT NULL COMMENT '关联小区ID',
    `building_name`        varchar(416)   DEFAULT NULL,
    `zhuang_name`          varchar(20)    DEFAULT NULL COMMENT '楼栋名称',
    `building_alias`       varchar(50)    DEFAULT NULL COMMENT '单元别名',
    `zhuang_alias`         varchar(50)    DEFAULT NULL COMMENT '楼栋别名',
    `source_building_addr` varchar(50)    DEFAULT NULL COMMENT '单元地址',
    `building_area`        decimal(11, 4) DEFAULT NULL COMMENT '面积',
    `province_cd`          varchar(10)    DEFAULT NULL COMMENT '省份代码',
    `city_cd`              varchar(10)    DEFAULT NULL COMMENT '城市代码',
    `district_cd`          varchar(10)    DEFAULT NULL COMMENT '区域代码',
    `road`                 varchar(20)    DEFAULT NULL COMMENT '路',
    `vill`                 varchar(20)    DEFAULT NULL COMMENT '弄',
    `vill_tail`            varchar(255)   DEFAULT NULL,
    `number`               varchar(10)    DEFAULT NULL COMMENT '号',
    `zip_cd`               varchar(20)    DEFAULT NULL COMMENT '邮编',
    `block_cd`             varchar(10)    DEFAULT NULL COMMENT '板块代码',
    `baidu_lng`            varchar(512)   DEFAULT NULL,
    `baidu_lat`            varchar(512)   DEFAULT NULL,
    `zhuang_baidu_lat`     decimal(10, 7) DEFAULT NULL COMMENT '楼栋百度中心纬度坐标',
    `zhuang_baidu_lng`     decimal(10, 7) DEFAULT NULL COMMENT '楼栋百度中心经度坐标',
    `gaode_lng`            varchar(512)   DEFAULT NULL,
    `gaode_lat`            varchar(512)   DEFAULT NULL,
    `zhuang_gaode_lat`     decimal(10, 7) DEFAULT NULL COMMENT '楼栋高德中心纬度坐标',
    `zhuang_gaode_lng`     decimal(10, 7) DEFAULT NULL,
    `property_years`       int(11)        DEFAULT NULL COMMENT '产权年限代码',
    `set_num`              varchar(512)   DEFAULT NULL,
    `floor_num`            int(11)        DEFAULT NULL COMMENT '实际层高',
    `start_floor`          int(11)        DEFAULT NULL COMMENT '起始楼层',
    `highest_floor`        int(11)        DEFAULT NULL COMMENT '最高楼层',
    `building_number`      varchar(10)    DEFAULT NULL COMMENT '单元号',
    `zhuang_number`        varchar(10)    DEFAULT NULL COMMENT '楼栋号',
    `elevator_num`         varchar(512)   DEFAULT NULL,
    `house_elevator_rate`  varchar(352)   DEFAULT NULL,
    `property_type`        varchar(20)    DEFAULT NULL COMMENT '物业类型',
    `build_type`           varchar(20)    DEFAULT NULL COMMENT '建筑类型',
    `build_year`           varchar(20)    DEFAULT NULL COMMENT '建筑年代',
    `property_fee`         decimal(10, 4) DEFAULT NULL COMMENT '物业费',
    `gate_control_ind`     tinyint(2)     DEFAULT NULL COMMENT '是否有门禁',
    `towards`              varchar(20)    DEFAULT NULL COMMENT '朝向',
    `gate_distance`        decimal(10, 4) DEFAULT NULL COMMENT '距离小区出口',
    `building_score`       int(4)         DEFAULT NULL COMMENT '单元置信分',
    `building_source`      varchar(20)    DEFAULT NULL COMMENT '单元来源',
    `main_source`          varchar(20)    DEFAULT NULL,
    `del_ind`              tinyint(2)     DEFAULT '0' COMMENT '删除标识',
    `create_user`          varchar(32)    DEFAULT NULL COMMENT '创建人员(工号)',
    `create_time`          timestamp      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user`          varchar(32)    DEFAULT NULL COMMENT '更新人员',
    `update_time`          timestamp      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `distance`             decimal(10, 2) DEFAULT NULL COMMENT '与小区中心坐标点的距离'
) COMMENT ='楼栋信息表';
```

### 6.单元信息

- 行数: 7906204
- 更新: 2024-12-03 10:58:11
- 表结构

```sql
CREATE TABLE `unit`
(
    `unit_id`             bigint(11) NOT NULL COMMENT '单元ID',
    `unit_no`             varchar(20)    DEFAULT NULL COMMENT '单元资产编号',
    `source_unit_id`      varchar(64)    DEFAULT NULL COMMENT '源单元号',
    `building_id`         bigint(11) NOT NULL COMMENT '楼栋ID',
    `community_id`        bigint(11)     DEFAULT NULL,
    `building_no`         bigint(20) NOT NULL COMMENT '楼栋资产编号',
    `unit_name`           varchar(416)   DEFAULT NULL,
    `unit_alias`          varchar(50)    DEFAULT NULL COMMENT '单元别名',
    `unit_area`           decimal(20, 4) DEFAULT NULL COMMENT '面积',
    `province_cd`         varchar(10)    DEFAULT NULL COMMENT '省份代码',
    `city_cd`             varchar(10)    DEFAULT NULL COMMENT '城市代码',
    `district_cd`         varchar(10)    DEFAULT NULL COMMENT '区域代码',
    `road`                varchar(100)   DEFAULT NULL COMMENT '路',
    `road_num`            varchar(100)   DEFAULT NULL COMMENT '路号',
    `zip_cd`              varchar(20)    DEFAULT NULL COMMENT '邮编',
    `block_cd`            varchar(20)    DEFAULT NULL COMMENT '板块代码',
    `baidu_lng`           varchar(512)   DEFAULT NULL,
    `baidu_lat`           varchar(512)   DEFAULT NULL,
    `gaode_lng`           varchar(512)   DEFAULT NULL,
    `gaode_lat`           varchar(512)   DEFAULT NULL,
    `property_years`      int(11)        DEFAULT NULL COMMENT '产权年限',
    `set_num`             int(11)        DEFAULT NULL COMMENT '总套数',
    `floor_num`           int(11)        DEFAULT NULL COMMENT '实际层高',
    `start_floor`         int(11)        DEFAULT NULL COMMENT '起始楼层',
    `highest_floor`       int(11)        DEFAULT NULL COMMENT '最高楼层',
    `unit_number`         varchar(20)    DEFAULT NULL COMMENT '楼栋号',
    `elevator_num`        int(11)        DEFAULT NULL COMMENT '电梯数',
    `house_elevator_rate` varchar(20)    DEFAULT NULL COMMENT '梯户比',
    `property_type`       varchar(20)    DEFAULT NULL COMMENT '物业类型',
    `build_type`          varchar(20)    DEFAULT NULL COMMENT '建筑类型',
    `build_year`          varchar(20)    DEFAULT NULL COMMENT '建筑年代',
    `property_fee`        decimal(10, 4) DEFAULT NULL COMMENT '物业费',
    `gate_control_ind`    tinyint(2)     DEFAULT NULL COMMENT '是否有门禁',
    `towards`             varchar(20)    DEFAULT NULL COMMENT '朝向',
    `gate_distance`       decimal(11, 4) DEFAULT NULL COMMENT '距离小区出口',
    `unit_score`          int(11)        DEFAULT NULL COMMENT '单元置信分',
    `unit_source`         varchar(20)    DEFAULT NULL COMMENT '单元来源',
    `del_ind`             tinyint(2)     DEFAULT '0' COMMENT '删除标识',
    `create_user`         varchar(16)    DEFAULT NULL COMMENT '创建人员(工号)',
    `create_time`         timestamp      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user`         varchar(16)    DEFAULT NULL COMMENT '更新人员',
    `update_time`         timestamp      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT ='单元信息表';

```

### 7.室号信息

- 行数: 161706200
- 更新: 2024-12-03 10:58:11
- 表结构

```sql
CREATE TABLE `room`
(
    `id`              bigint(11)  NOT NULL AUTO_INCREMENT,
    `unit_id`         bigint(11)  NOT NULL COMMENT '单元ID',
    `building_id`     bigint(11)  NOT NULL COMMENT '关联楼栋ID',
    `community_id`    bigint(11)  NOT NULL COMMENT '关联小区id',
    `city_cd`         varchar(10) NOT NULL,
    `room_no`         varchar(512)   DEFAULT NULL,
    `room_num`        int(11)        DEFAULT NULL COMMENT '几室',
    `hall_num`        int(11)        DEFAULT NULL COMMENT '几厅',
    `kitchen_num`     int(11)        DEFAULT NULL COMMENT '几厨',
    `bath_num`        int(11)        DEFAULT NULL COMMENT '几卫',
    `balcony_num`     int(11)        DEFAULT NULL COMMENT '几阳',
    `towards`         varchar(20)    DEFAULT NULL COMMENT '朝向',
    `nom_floor`       int(11)        DEFAULT NULL COMMENT '名义层',
    `act_floor`       int(11)        DEFAULT NULL COMMENT '实际层',
    `build_area`      decimal(11, 4) DEFAULT NULL COMMENT '建筑面积',
    `act_area`        decimal(11, 4) DEFAULT NULL COMMENT '使用面积',
    `share_area`      decimal(11, 4) DEFAULT NULL COMMENT '公摊面积',
    `undergroud_area` decimal(11, 4) DEFAULT NULL COMMENT '地下面积',
    `get_house_rate`  decimal(11, 4) DEFAULT NULL COMMENT '得房率',
    `house_layout_id` varchar(20)    DEFAULT NULL COMMENT '户型ID',
    `property_type`   varchar(20)    DEFAULT NULL COMMENT '物业类型',
    `spec_fun_ind`    tinyint(2)     DEFAULT NULL COMMENT '是否具有特殊职能',
    `room_source`     varchar(20)    DEFAULT NULL COMMENT '房间来源',
    `del_ind`         tinyint(2)     DEFAULT '0' COMMENT '删除标识',
    `create_user`     varchar(16)    DEFAULT NULL COMMENT '创建人员(工号/中文名)',
    `create_time`     timestamp      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user`     varchar(16)    DEFAULT NULL COMMENT '更新人员',
    `update_time`     timestamp      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);


```

### 8.楼盘信息

- 行数: 176968 (地址去重:164616)
- 更新: 2024-01-24 11:34:40
- 表结构

```sql
CREATE TABLE `xf_base_info`
(
    `id`                               bigint(11)          NOT NULL AUTO_INCREMENT,
    `source`                           int(11)             NOT NULL COMMENT '来源1-EBBAS 2-开发商 3-ETC',
    `type`                             int(11)             NOT NULL COMMENT '类型1-新房楼盘 2-小区',
    `e_code`                           varchar(32)         NOT NULL COMMENT '楼盘E码',
    `outer_id`                         varchar(32)         NOT NULL COMMENT '外部楼盘ID',
    `highlights`                       varchar(128)        NOT NULL COMMENT '楼盘亮点',
    `tag_codes`                        varchar(300)        NOT NULL COMMENT '楼盘标签',
    `total_price`                      varchar(40)                  DEFAULT NULL COMMENT '楼盘总价',
    `avg_price`                        varchar(40)                  DEFAULT NULL COMMENT '楼盘均价',
    `total_price_unit`                 varchar(20)                  DEFAULT NULL COMMENT '总价单位',
    `avg_price_unit`                   varchar(20)                  DEFAULT NULL COMMENT '场均单位',
    `show_price`                       varchar(40)                  DEFAULT NULL COMMENT '楼盘展示价（元）',
    `estate_types`                     varchar(32)         NOT NULL COMMENT '物业类型(1-住宅 2-商业)',
    `property_rights_years_codes`      varchar(20)                  DEFAULT NULL COMMENT '产权年限码',
    `building_area`                    varchar(40)                  DEFAULT NULL COMMENT '建筑面积(平米)',
    `decoration_standard_codes`        varchar(20)         NOT NULL COMMENT '装修标准(1-毛坯 2-简装 3-精装）',
    `project_detail_address`           varchar(100)        NOT NULL COMMENT '楼盘详细地址',
    `map_location_detail_address`      varchar(255)                 DEFAULT NULL COMMENT '地图定位-详细地址',
    `map_location_longitude`           varchar(64)         NOT NULL COMMENT '地图定位-经度',
    `map_location_latitude`            varchar(64)         NOT NULL COMMENT '地图定位-纬度',
    `developer_full_name`              varchar(150)        NOT NULL COMMENT '开发商全称',
    `brand_name`                       varchar(32)                  DEFAULT NULL COMMENT '品牌商名称',
    `developer_sales_office_address`   varchar(300)                 DEFAULT NULL COMMENT '售楼处地址',
    `developer_opening_time`           varchar(10)                  DEFAULT NULL COMMENT '开盘时间(yyyy-MM-dd)',
    `developer_due_time`               varchar(10)                  DEFAULT NULL COMMENT '交付时间(yyyy-MM-dd)',
    `community_area`                   varchar(40)                  DEFAULT NULL COMMENT '楼盘概况-占地面积(平米)',
    `community_volume_rate`            varchar(20)                  DEFAULT NULL COMMENT '楼盘概况-容积率（单位：%）(例：20)',
    `community_greening_rate`          varchar(20)                  DEFAULT NULL COMMENT '楼盘概况-绿化率（单位：%）(例：30)',
    `community_planning_building`      int(11)                      DEFAULT NULL COMMENT '楼盘概况-规划楼栋',
    `community_planning_households`    bigint(20)                   DEFAULT NULL COMMENT '楼盘概况-规划户数',
    `community_estate_company`         varchar(100)                 DEFAULT NULL COMMENT '楼盘概况-物业公司',
    `community_estate_expenses`        varchar(300)                 DEFAULT NULL COMMENT '楼盘概况-物业费用(元/m²/月)',
    `community_heating_type_desc`      varchar(300)                 DEFAULT NULL COMMENT '供暖方式',
    `community_water_type_desc`        varchar(300)                 DEFAULT NULL COMMENT '供水方式',
    `community_power_type_desc`        varchar(64)                  DEFAULT NULL COMMENT '供电方式',
    `overall_introduction`             text                         DEFAULT NULL COMMENT '楼盘整体介绍',
    `submit_reason`                    varchar(1024)                DEFAULT NULL COMMENT '此次修改的原因',
    `country_name`                     varchar(64)         NOT NULL COMMENT '楼盘所属国家名字',
    `prov`                             varchar(64)         NOT NULL COMMENT '楼盘所属省名字',
    `city`                             varchar(64)         NOT NULL COMMENT '楼盘所属市名字',
    `area`                             varchar(64)         NOT NULL COMMENT '楼盘所属区名字',
    `street_name`                      varchar(64)                  DEFAULT NULL COMMENT '楼盘所属街道名字',
    `project_name`                     varchar(50)         NOT NULL COMMENT '楼盘名称 不能超过18个字',
    `telephone`                        varchar(50)                  DEFAULT NULL COMMENT '联系人手机',
    `phone_no`                         varchar(50)                  DEFAULT NULL COMMENT '售楼处座机',
    `main_phone`                       varchar(30)                  DEFAULT NULL COMMENT '售楼处400主机电话',
    `sub_phone`                        varchar(30)                  DEFAULT NULL COMMENT '售楼处400分机电话',
    `country_id`                       bigint(20) unsigned NOT NULL DEFAULT '1' COMMENT '楼盘所属国家Id',
    `prov_id`                          bigint(20) unsigned NOT NULL COMMENT '楼盘所属省编码',
    `city_id`                          bigint(20) unsigned NOT NULL COMMENT '楼盘所属市编码',
    `area_id`                          bigint(20) unsigned NOT NULL COMMENT '楼盘所属区编码',
    `street_id`                        bigint(20) unsigned          DEFAULT NULL COMMENT '楼盘所属街道编码',
    `buisness_id`                      varchar(32)                  DEFAULT NULL COMMENT '商圈ID',
    `module_id`                        varchar(32)                  DEFAULT NULL COMMENT '板块ID',
    `parking_radio`                    varchar(100)                 DEFAULT NULL COMMENT '车位比例(1:1.5)',
    `alias_names`                      varchar(500)                 DEFAULT NULL COMMENT '楼盘别名',
    `loop_line`                        varchar(64)                  DEFAULT NULL COMMENT '环线',
    `building_types`                   varchar(128)        NOT NULL COMMENT '建筑物类型（低层、小高层）',
    `building_categorys`               varchar(128)                 DEFAULT NULL COMMENT '建筑类别（板楼、塔楼、板塔结合）',
    `house_types`                      varchar(128)        NOT NULL COMMENT '房屋类型（花园洋房，商住，叠拼，独栋）',
    `project_sales_status`             int(11)             NOT NULL COMMENT '楼盘销售状态',
    `traffic_description`              text                         DEFAULT NULL COMMENT '交通状况描述',
    `floor_description`                varchar(64)                  DEFAULT NULL COMMENT '楼层分布描述',
    `peripheral_business`              text                         DEFAULT NULL COMMENT '周边商业',
    `surrounding_landscape`            text                         DEFAULT NULL COMMENT '周边景观',
    `surrounding_parks`                text                         DEFAULT NULL COMMENT '周边公园',
    `surrounding_hospitals`            text                         DEFAULT NULL COMMENT '周边医院',
    `surrounding_schools`              text                         DEFAULT NULL COMMENT '周边学校',
    `surrounding_traffic`              text                         DEFAULT NULL COMMENT '周边交通',
    `project_feature_description`      varchar(64)                  DEFAULT NULL COMMENT '项目特色描述',
    `project_schedule_description`     varchar(64)                  DEFAULT NULL COMMENT '工程进度描述',
    `sales_progress_description`       varchar(64)                  DEFAULT NULL COMMENT '销售进度描述',
    `start_time`                       varchar(10)                  DEFAULT NULL COMMENT '开工时间(yyyy-MM-dd)',
    `completion_time`                  varchar(10)                  DEFAULT NULL COMMENT '竣工时间(yyyy-MM-dd)',
    `house_acquisition_rate`           varchar(16)                  DEFAULT NULL COMMENT '得房率(例：20)（单位：%）',
    `investors`                        varchar(128)                 DEFAULT NULL COMMENT '投资商',
    `payment_method`                   varchar(50)                  DEFAULT NULL COMMENT '付款方式',
    `down_payment_ratio`               varchar(16)                  DEFAULT NULL COMMENT '首付比例(例：20)（单位：%）',
    `parking_fee`                      varchar(200)                 DEFAULT NULL COMMENT '车位费(单位：元/月)',
    `parking_number`                   int(11)                      DEFAULT NULL COMMENT '车位数',
    `overground_parking_number`        int(11)                      DEFAULT NULL COMMENT '地上车位数',
    `underground_parking_number`       int(11)                      DEFAULT NULL COMMENT '地下车位数',
    `garage_configuration_description` varchar(150)                 DEFAULT NULL COMMENT '车库配置描述',
    `gas_supply_description`           varchar(64)                  DEFAULT NULL COMMENT '供气描述',
    `main_house_type_description`      varchar(150)                 DEFAULT NULL COMMENT '主力户型描述',
    `diversion_people_vehicles`        varchar(10)                  DEFAULT NULL COMMENT '是否人车分流',
    `phone_area_code`                  varchar(16)                  DEFAULT NULL COMMENT '电话区号',
    `real_estate_certificate`          varchar(64)                  DEFAULT NULL COMMENT '不动产权证',
    `stages`                           varchar(16)                  DEFAULT NULL COMMENT '分期',
    `fencing`                          text                         DEFAULT NULL COMMENT '围栏数据',
    `architecture_age`                 varchar(16)                  DEFAULT NULL COMMENT '建筑年代',
    `pinyin`                           varchar(50)                  DEFAULT NULL COMMENT '楼盘拼音首字母',
    `full_pinyin`                      varchar(150)                 DEFAULT NULL COMMENT '楼盘名拼音',
    `direct_sale`                      varchar(50)                  DEFAULT NULL COMMENT '营销方式',
    `pre_sale_permit`                  varchar(128)                 DEFAULT NULL COMMENT '预售证',
    `surrounding_banks`                varchar(255)                 DEFAULT NULL COMMENT '周边配套',
    `internal_matching`                text                         DEFAULT NULL COMMENT '楼盘内部配套',
    `surrounding_restaurant`           varchar(128)                 DEFAULT NULL COMMENT '周边餐饮',
    `other_facilities`                 varchar(64)                  DEFAULT NULL COMMENT '其他配套',
    `record_name`                      varchar(64)                  DEFAULT NULL COMMENT '备案名',
    `layouts`                          varchar(128)                 DEFAULT NULL COMMENT '户型',
    `pictures`                         varchar(255)                 DEFAULT NULL COMMENT '图片',
    `create_time`                      timestamp           NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                      timestamp           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_test`                          tinyint(1)                   DEFAULT '0' COMMENT '是否测试数据',
    `is_not_push`                      tinyint(1)                   DEFAULT '0' COMMENT '是否不推送 ：推送 0  不推1',
    `parking_number_desc`              varchar(200)                 DEFAULT NULL COMMENT '车位数描述',
    `city_cn`                          varchar(100)                 DEFAULT NULL,
    `district_cn`                      varchar(100)                 DEFAULT NULL,
    `unified_city_cn`                  varchar(100)                 DEFAULT NULL,
    `unified_district_cn`              varchar(100)                 DEFAULT NULL,
    `leju_update_time`                 timestamp           NULL     DEFAULT NULL COMMENT '乐居更新时间',
    `leju_status`                      varchar(3)                   DEFAULT NULL,
    `leju_sale_status`                 varchar(20)                  DEFAULT NULL,
    `province_cn`                      varchar(100)                 DEFAULT NULL,
    `unified_province_cn`              varchar(100)                 DEFAULT NULL,
    `clean_complete_date`              timestamp           NULL     DEFAULT NULL COMMENT '清洗完成日期',
    `temp_city_id`                     bigint(20)                   DEFAULT NULL COMMENT '临时城市id',
    `temp_area_id`                     bigint(20)                   DEFAULT NULL COMMENT '临时区域id',
    `temp_function_id`                 bigint(20)                   DEFAULT NULL COMMENT '临时功能区id',
    `temp_module_id`                   bigint(20)                   DEFAULT NULL COMMENT '临时板块id',
    `function_id_leju`                 bigint(20)                   DEFAULT NULL COMMENT '没有清洗前的功能区id',
    `function_id`                      bigint(20)                   DEFAULT NULL COMMENT '功能区id',
    `function_name`                    varchar(100)                 DEFAULT NULL COMMENT '功能区名称',
    `function_name_leju`               varchar(100)                 DEFAULT NULL COMMENT '没有清洗前的功能区名称'
) COMMENT ='楼盘信息';

```

### 9. 新房楼盘预售证

- 行数: 182236 (许可证号去重:165057)
- 更新: 2024-01-24 13:08:45
- 表结构

```sql
CREATE TABLE `xf_pre_sale_info`
(
    `id`                      bigint(11)  NOT NULL AUTO_INCREMENT,
    `outer_permit_id`         varchar(64) NOT NULL COMMENT '外部预售证id',
    `outer_id`                varchar(64) NOT NULL COMMENT '外部楼盘ID',
    `pre_sale_license_number` varchar(500)         DEFAULT NULL COMMENT '预售许可证编号',
    `certification_date`      varchar(32)          DEFAULT NULL COMMENT '发证日期',
    `permitted_sales_area`    varchar(128)         DEFAULT NULL COMMENT '准许销售面积',
    `land_housing_use`        varchar(100)         DEFAULT NULL,
    `pre_sale_position`       varchar(500)         DEFAULT NULL COMMENT '预售部位',
    `service_life`            varchar(32)          DEFAULT NULL COMMENT '使用年限',
    `sales_sets`              int(11)              DEFAULT NULL COMMENT '销售套数',
    `sales_area`              int(11)              DEFAULT NULL COMMENT '销售面积',
    `proposed_sale_price`     varchar(64)          DEFAULT NULL COMMENT '住宅拟售价格',
    `registration_start_time` varchar(32)          DEFAULT NULL COMMENT '登记开始时间',
    `registration_end_time`   varchar(32)          DEFAULT NULL COMMENT '登记结束时间',
    `average_price`           varchar(64)          DEFAULT NULL COMMENT '均价',
    `unit_price_range`        varchar(32)          DEFAULT NULL COMMENT '单价区间',
    `total_price_range`       varchar(32)          DEFAULT NULL COMMENT '总价区间',
    `publicity_time`          varchar(32)          DEFAULT NULL COMMENT '公示时间',
    `pre_sale_build`          varchar(500)         DEFAULT NULL COMMENT '预售楼栋id',
    `create_time`             timestamp            DEFAULT NULL COMMENT '创建时间',
    `update_time`             timestamp            DEFAULT NULL COMMENT '更新时间',
    `is_test`                 tinyint(1)           DEFAULT '0' COMMENT '是否测试0-非测试 1-测试',
    `is_not_push`             tinyint(1)           DEFAULT '0' COMMENT '是否不推送 ：推送 0  不推1',
    `is_del`                  tinyint(4)  NOT NULL DEFAULT '0' COMMENT '删除标志0,1'
) COMMENT ='预售证信息';

```

### 10. 新房楼盘图片

- 行数: 6666663
- 更新: 2024-01-24 15:06:07
- 表结构

```sql
CREATE TABLE `xf_picture_info`
(
    `id`               bigint(11)   NOT NULL AUTO_INCREMENT,
    `outer_id`         varchar(32)  NOT NULL COMMENT '外部楼盘ID',
    `outer_picture_id` varchar(32)  NOT NULL COMMENT '外部图片ID',
    `outer_layout_id`  varchar(32)           DEFAULT NULL COMMENT '外部户型ID',
    `order_tag`        int(11)               DEFAULT NULL COMMENT '图片顺序',
    `category`         int(11)      NOT NULL COMMENT '图片类型',
    `pic_data`         varchar(256)          DEFAULT NULL COMMENT '图片URL全路径 首店天猫店',
    `pic_data_second`  varchar(255)          DEFAULT NULL COMMENT '图片URL全路径 次店乐居店',
    `source_pic_data`  varchar(255) NOT NULL COMMENT '源图片url全路径',
    `description`      varchar(2500)         DEFAULT NULL COMMENT '图片说明',
    `create_time`      timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted`       tinyint(1)            DEFAULT '0' COMMENT '是否逻辑删 是否删除 1 是 0 否',
    `is_test`          tinyint(1)            DEFAULT '0' COMMENT '是否测试数据',
    `is_not_push`      tinyint(1)            DEFAULT '0' COMMENT '是否不推送 ：推送 0  不推1',
    `is_upload`        tinyint(1)            DEFAULT '0' COMMENT '图片是否上传过(1:是，0:否)',
    `pre_pic_md5`      varchar(50)           DEFAULT NULL COMMENT '上一次更新的图片url的MD5',
    `is_focus`         int(1)                DEFAULT '0' COMMENT '是否是焦点图 1:是,0:否',
    `is_cover`         int(1)                DEFAULT '0' COMMENT '是否是封面图 1:是,0:否'
) COMMENT ='楼盘图片信息';

```

### 11. 上架房源

- 房源总数(渠道去重): 433149

|   渠道    |  房源数量   |   二手房   |   租房   |  新房  |
|:-------:|:-------:|:-------:|:------:|:----:|
|   闲鱼	   | 125571  | 90899	  | 34170  | 	510 |
|  支付宝	   | 109912  |  82167  | 	27749 |  	0  |
| 微信(个人店) | 	328068 | 264188	 | 63879  |  	0  |

- 表结构

```sql
CREATE TABLE `tb_delegation`
(
    `level`                       enum ('COMPANY') NOT NULL  DEFAULT 'CHANNEL' COMMENT '类型',
    `id`                          bigint(20)       NOT NULL,
    `create_time`                 datetime(6)      NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `logic_delete`                bit(1)           NOT NULL  DEFAULT b'0' COMMENT '是否逻辑删除:1是0否',
    `update_time`                 datetime(6)      NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) COMMENT '更新时间',
    `version`                     bigint(20)       NOT NULL  DEFAULT '0' COMMENT '版本号',
    `broker_name`                 varchar(255)               DEFAULT NULL COMMENT '经纪人名',
    `channel_code`                enum ('ALIPAY')            DEFAULT NULL COMMENT '渠道编码',
    `code`                        varchar(63)                DEFAULT NULL COMMENT '委托编码',
    `company_id`                  bigint(20)                 DEFAULT NULL COMMENT '所属公司userId',
    `delete_flag`                 tinyint(1)                 DEFAULT '0' COMMENT '删除(隐藏)标志位(用于)',
    `description`                 varchar(1024)              DEFAULT NULL COMMENT '描述',
    `around`                      varchar(255)               DEFAULT NULL COMMENT '周边配套',
    `bailor_cert_no`              varchar(255)               DEFAULT NULL COMMENT '委托人证件号码',
    `bailor_cert_type`            varchar(63)                DEFAULT NULL COMMENT '委托人证件类型',
    `bailor_name`                 varchar(255)               DEFAULT NULL COMMENT '委托人姓名',
    `bailor_names`                varchar(255)               DEFAULT NULL COMMENT '委托人姓名集合',
    `boutique`                    bit(1)                     DEFAULT b'0' COMMENT '是否精品房源',
    `broker_services`             varchar(255)               DEFAULT NULL COMMENT '中介服务',
    `build_name`                  varchar(255)               DEFAULT NULL COMMENT '楼栋名',
    `building_area`               decimal(10, 2)             DEFAULT NULL COMMENT '建筑面积',
    `building_category`           varchar(63)                DEFAULT NULL COMMENT '建筑类别',
    `building_type`               varchar(63)                DEFAULT NULL COMMENT '建筑类型',
    `checkin_date`                date                       DEFAULT NULL COMMENT '入住日期(空表示随时入住)',
    `city_name`                   varchar(255)               DEFAULT NULL COMMENT '城市名',
    `community_name`              varchar(255)               DEFAULT NULL COMMENT '小区名',
    `completion_time`             varchar(63)                DEFAULT NULL COMMENT '建成年代',
    `current_floor`               int(10)                    DEFAULT NULL COMMENT '当前楼层',
    `deadline`                    datetime(6)                DEFAULT NULL COMMENT '委托到期时间',
    `deposit_months`              int(10)                    DEFAULT NULL COMMENT '押金月数',
    `district_name`               varchar(255)               DEFAULT NULL COMMENT '行政区名称',
    `efficiency_rate`             decimal(20, 2)             DEFAULT NULL COMMENT '得房率',
    `elevator_count`              int(10)                    DEFAULT NULL COMMENT '电梯数量',
    `equipments`                  varchar(255)               DEFAULT NULL COMMENT '配套设施',
    `floor_category`              varchar(63)                DEFAULT NULL COMMENT '楼层类别',
    `gov_contract_code`           varchar(64)                DEFAULT NULL COMMENT '政府委托协议编码',
    `gov_promo_code`              varchar(64)                DEFAULT NULL COMMENT '政府房源推广码',
    `gov_verify_code`             varchar(64)                DEFAULT NULL COMMENT '政府核验码',
    `gov_verify_url`              varchar(1024)              DEFAULT NULL COMMENT '政府核验码图片地址',
    `hall_count`                  int(10)                    DEFAULT '0' COMMENT '客厅数',
    `house_busi_no`               varchar(255)               DEFAULT NULL COMMENT '房屋业务件号(政府核验相关)',
    `house_cert_address`          varchar(255)               DEFAULT NULL COMMENT '产证地址',
    `house_cert_no`               varchar(255)               DEFAULT NULL COMMENT '产证号',
    `house_cert_type`             varchar(63)                DEFAULT NULL COMMENT '产证类型',
    `house_cert_verify`           tinyint(1)                 DEFAULT NULL COMMENT '是否房产证验证',
    `house_plan_purpose`          varchar(255)               DEFAULT NULL COMMENT '房屋规划用途',
    `house_situation`             varchar(63)                DEFAULT NULL COMMENT '房屋现状',
    `house_structure`             varchar(63)                DEFAULT NULL COMMENT '房屋结构',
    `house_type`                  varchar(63)                DEFAULT NULL COMMENT '产权类型',
    `house_years`                 varchar(63)                DEFAULT NULL COMMENT '房屋年限',
    `list_date`                   date                       DEFAULT NULL COMMENT '上架时间',
    `look_type`                   varchar(63)                DEFAULT NULL COMMENT '看房时间类型',
    `orient`                      varchar(63)                DEFAULT NULL COMMENT '朝向',
    `owner_cert_no`               varchar(63)                DEFAULT NULL COMMENT '业主证件号',
    `owner_name`                  varchar(255)               DEFAULT NULL COMMENT '业主姓名',
    `parking`                     bit(1)                     DEFAULT b'0' COMMENT '是否有车位',
    `parking_ratio`               varchar(32)                DEFAULT NULL COMMENT '车位配比',
    `pay_months`                  int(10)                    DEFAULT NULL COMMENT '付款月数',
    `property_management_company` varchar(63)                DEFAULT NULL COMMENT '物业管理公司',
    `property_management_fee`     decimal(20, 2)             DEFAULT NULL COMMENT '物业管理费用',
    `property_ownership`          varchar(63)                DEFAULT NULL COMMENT '产权所属',
    `property_type`               varchar(63)                DEFAULT NULL COMMENT '物业类型',
    `property_years`              varchar(63)                DEFAULT NULL COMMENT '产权年限',
    `redo`                        varchar(63)                DEFAULT NULL COMMENT '装修情况',
    `room_count`                  int(10)                    DEFAULT '0' COMMENT '房间数',
    `room_name`                   varchar(255)               DEFAULT NULL COMMENT '房间名(用于合租指定房间名)',
    `room_num`                    varchar(255)               DEFAULT NULL COMMENT '房号',
    `room_per_floor`              int(10)                    DEFAULT NULL COMMENT '每层户数',
    `room_type`                   varchar(63)                DEFAULT NULL COMMENT '房间类型',
    `sale_reason`                 varchar(255)               DEFAULT NULL COMMENT '出售原因',
    `sole_house`                  bit(1)                     DEFAULT b'0' COMMENT '是否唯一住房',
    `sub_type`                    enum ('NEW','RENT','SALE') DEFAULT NULL COMMENT '交易子类型',
    `tag_elevator`                tinyint(1)                 DEFAULT NULL COMMENT '是否有电梯',
    `toilet_count`                int(10)                    DEFAULT '0' COMMENT '卫生间数',
    `total_floor`                 int(10)                    DEFAULT NULL COMMENT '总楼层',
    `type`                        enum ('NEW','RENT','SALE') DEFAULT NULL COMMENT '交易类型',
    `unit_name`                   varchar(255)               DEFAULT NULL COMMENT '单元名',
    `use_area`                    decimal(10, 2)             DEFAULT NULL COMMENT '使用面积',
    `labels`                      varchar(511)               DEFAULT NULL COMMENT '标签',
    `medias`                      json                       DEFAULT NULL COMMENT '媒体',
    `memo`                        text                       DEFAULT NULL COMMENT '备注(内部异常)',
    `parent_source_id`            varchar(127)               DEFAULT NULL COMMENT '外部父ID',
    `price_total`                 decimal(20, 2)             DEFAULT NULL COMMENT '总价|租金',
    `price_unit`                  decimal(20, 2)             DEFAULT NULL COMMENT '单价|押金',
    `remark`                      text                       DEFAULT NULL COMMENT '备注(对外展示)',
    `source_id`                   varchar(127)               DEFAULT NULL COMMENT '外部ID',
    `source_type`                 varchar(64)                DEFAULT NULL COMMENT '来源(SaaS/QiaoFang/MingCe/...)',
    `status`                      enum ()                    DEFAULT NULL COMMENT '状态',
    `title`                       varchar(255)               DEFAULT NULL COMMENT '标题',
    `broker_id`                   bigint(20)                 DEFAULT NULL COMMENT '经纪人ID',
    `broker_delegation_id`        bigint(20)                 DEFAULT NULL COMMENT '关联经纪房源ID',
    `company_delegation_id`       bigint(20)                 DEFAULT NULL COMMENT '关联公司房源ID',
    `city_id`                     bigint(20)                 DEFAULT NULL COMMENT '城市ID',
    `community_id`                bigint(20)                 DEFAULT NULL COMMENT '小区ID',
    `community_bind_id`           bigint(20)                 DEFAULT NULL COMMENT '小区绑定ID',
    `district_id`                 bigint(20)                 DEFAULT NULL COMMENT '行政区ID',
    `po`                          json                       DEFAULT NULL COMMENT '编辑请求信息',
    `history_up`                  tinyint(1)                 DEFAULT NULL COMMENT '请求渠道状态',
    `edit_po`                     json                       DEFAULT NULL COMMENT '编辑请求信息',
    `up_po`                       json                       DEFAULT NULL COMMENT '上架请求信息',
    `busi_name`                   varchar(127)               DEFAULT NULL COMMENT '商圈名称',
    `community_address`           varchar(255)               DEFAULT NULL COMMENT '简化小区地址',
    `town_name`                   varchar(127)               DEFAULT NULL COMMENT '城镇/街道名称',
    `manual_push`                 tinyint(1)                 DEFAULT NULL COMMENT '手工推送标(1:上架,0:下架)'
) COMMENT ='(房源)委托';


```