[toc]

### 1.小区详情:小区各项指标信息

```sql
select t1.*
from (select c.city_name                                                                    as 城市
           , sum(if(c.`community_name` is not null and c.community_name is not null, 1, 0)) as 小区名
           , sum(if(c.`address` is not null and c.address is not null, 1, 0))               as 小区地址
           , sum(if(tc.location is not null and tc.location is not null, 1, 0))             as 经纬度
           , sum(if(tc.district_name is not null and tc.district_name is not null, 1, 0))   as 行政区
           , sum(if(c.`block_name` is not null and c.block_name is not null, 1, 0))         as 板块名
           , sum(if(c.`build_area` is not null and c.build_area <> 0, 1, 0))                as 建筑面积
           , sum(if(c.`house_num` is not null and c.house_num <> 0, 1, 0))                  as 房屋总数
           , sum(if(c.`parking_num` is not null and c.parking_num <> 0, 1, 0))              as 车位总数
           , sum(if(c.`green_rate` is not null and c.green_rate <> 0, 1, 0))                as 绿化率
           , sum(if(c.`build_max_year` is not null and c.build_max_year <> '', 1, 0))       as 建筑年代
           , sum(if(c.`building_type` is not null and c.building_type <> '', 1, 0))         as 建筑类型
           , sum(if(c.`property_type` is not null and c.property_type <> '', 1, 0))         as 物业类型
           , sum(if(c.`volume_rate` is not null and c.volume_rate <> 0, 1, 0))              as 容积率
           , sum(if(c.`developer_corp` is not null and c.build_area <> 0, 1, 0))            as 开发商
           , sum(if(c.`pic_url` is not null and c.pic_url <> '', 1, 0))                     as 小区图片
      from tb_community_detail c
               left join tb_community tc on tc.id = c.community_id
      where c.city_name in (
                            '上海',
                            '成都',
                            '北京',
                            '重庆',
                            '广州',
                            '武汉',
                            '深圳',
                            '天津',
                            '杭州',
                            '郑州',
                            '苏州',
                            '西安',
                            '南京',
                            '佛山',
                            '青岛',
                            '济南',
                            '昆明',
                            '长沙',
                            '宁波',
                            '南昌'
          )
      GROUP BY c.city_name) t1
where 小区名 > 500
order by 小区名 desc;
```

### 2.小区户型

```sql
select t1.*
from (select cd.city_name                                                          as 城市
           , count(1)                                                              as 户型数
           , sum(if(c.`name` is not null and c.name <> '', 1, 0))                  as 户型名
           , sum(if(c.`set_num` is not null and c.set_num <> 0, 1, 0))             as 总套数_推断主力户型
           , sum(if(c.`area` is not null and c.area <> 0, 1, 0))                   as 面积_推断同户型最大最小面积
           , sum(if(c.`pic_url` is not null and c.pic_url <> '', 1, 0))          as 户型图
      from tb_community_layout c
               join tb_community_detail cd on cd.id = c.community_detail_id
      where cd.city_name in (
                             '上海',
                             '成都',
                             '北京',
                             '重庆',
                             '广州',
                             '武汉',
                             '深圳',
                             '天津',
                             '杭州',
                             '郑州',
                             '苏州',
                             '西安',
                             '南京',
                             '佛山',
                             '青岛',
                             '济南',
                             '昆明',
                             '长沙',
                             '宁波',
                             '南昌'
          )
      GROUP BY cd.city_name) t1
where 户型数 > 500
order by 户型数 desc
```