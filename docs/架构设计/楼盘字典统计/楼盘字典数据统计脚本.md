[toc]

### 1.小区表

```sql
select *
from (select c.city_name                                    as 城市
           , count(1)                                       as 小区数
           , sum(if(c.`address` is not null, 1, 0))         as 有地址
           , sum(if(c.`location` is not null, 1, 0))        as 有坐标
           , sum(if(c.`district_name` is not null, 1, 0))   as 有行政区
           , sum(if(c.`town_name` is not null, 1, 0))       as 有街道
           , sum(if(c.`busi_name` is not null, 1, 0))       as 有商圈
           , sum(if(c.`around_response` is not null, 1, 0)) as 有周边
      from tb_community c
      GROUP BY c.city_name) t1
where 小区数 > 500
order by 小区数 desc;
```

### 2.小区详情:小区各项指标信息

```sql
select t1.*
from (select c.city_name                                                                    as 城市
           , count(1)                                                                       as 总数
           , sum(if(c.`green_rate` is not null and c.green_rate <> 0, 1, 0))                as 绿化率
           , sum(if(c.`house_num` is not null and c.house_num <> 0, 1, 0))                  as 房屋总数
           , sum(if(c.`parking_num` is not null and c.parking_num <> 0, 1, 0))              as 车位总数
           , sum(if(c.`volume_rate` is not null and c.volume_rate <> 0, 1, 0))              as 容积率
           , sum(if(c.`build_max_year` is not null and c.build_max_year <> '', 1, 0))       as 建筑年代最大
           , sum(if(c.`build_min_year` is not null and c.build_min_year <> '', 1, 0))       as 建成年代最小
           , sum(if(c.`building_type` is not null and c.building_type <> '', 1, 0))         as 建筑类型
           , sum(if(c.`build_num` is not null and c.build_num <> 0, 1, 0))                  as 楼栋总数
           , sum(if(c.`developer_corp` is not null and c.developer_corp <> '', 1, 0))       as 交易权属
           , sum(if(c.`gas_desc` is not null and c.gas_desc <> '', 1, 0))                   as 供气描述
           , sum(if(c.`heating_desc` is not null and c.heating_desc <> '', 1, 0))           as 供暖情况
           , sum(if(c.`parking_rate` is not null and c.parking_rate <> '', 1, 0))           as 车位配比率
           , sum(if(c.`pic_url` is not null and c.pic_url <> '', 1, 0))                     as 小区缩略图地址
           , sum(if(c.`powerd_desc` is not null and c.powerd_desc <> '', 1, 0))             as 供电描述
           , sum(if(c.`property_fee` is not null and c.property_fee <> '', 1, 0))           as 物业费
           , sum(if(c.`property_name` is not null and c.property_name <> '', 1, 0))         as 物业公司
           , sum(if(c.`property_phone` is not null and c.property_phone <> '', 1, 0))       as 物业电话
           , sum(if(c.`property_type` is not null and c.property_type <> '', 1, 0))         as 物业类型
           , sum(if(c.`property_years` is not null and c.property_years <> '', 1, 0))       as 产权年限
           , sum(if(c.`set_parking_fee` is not null and c.set_parking_fee <> '', 1, 0))     as 固定停车费标准
           , sum(if(c.`water_desc` is not null and c.water_desc <> '', 1, 0))               as 供水描述
           , sum(if(c.`down_parking_num` is not null and c.down_parking_num <> 0, 1, 0))    as 地下车位数
           , sum(if(c.`parking_sale_flag` is not null, 1, 0))                               as 是否出售产权车位
           , sum(if(c.`person_div_car_flag` is not null, 1, 0))                             as 是否人车分流
           , sum(if(c.`building_category` is not null and c.building_category <> '', 1, 0)) as 建筑类别
           , sum(if(c.`house_type` is not null and c.house_type <> '', 1, 0))               as 房屋类型
           , sum(if(c.`act_area` is not null and c.act_area <> 0, 1, 0))                    as 占地面积
           , sum(if(c.`build_area` is not null and c.build_area <> 0, 1, 0))                as 建筑面积
      from tb_community_detail c
      GROUP BY c.city_name) t1
where 总数 > 500
order by 总数 desc
```

### 3.小区户型

```sql
select t1.*
from (select cd.city_name                                                          as 城市
           , count(1)                                                              as 总数
           , sum(if(c.`area` is not null and c.area <> 0, 1, 0))                   as 面积
           , sum(if(c.`pic_url2` is not null and c.pic_url2 <> '', 1, 0))          as 户型图
           , sum(if(c.`balcony_count` is not null and c.balcony_count <> 0, 1, 0)) as 阳台数
           , sum(if(c.`hall_count` is not null and c.hall_count <> 0, 1, 0))       as 客厅数
           , sum(if(c.`kitchen_count` is not null and c.kitchen_count <> 0, 1, 0)) as 厨房数
           , sum(if(c.`room_count` is not null and c.room_count <> 0, 1, 0))       as 房间数
           , sum(if(c.`toilet_count` is not null and c.toilet_count <> 0, 1, 0))   as 卫生间数
           , sum(if(c.`set_num` is not null and c.set_num <> 0, 1, 0))             as 总套数
           , sum(if(c.`name` is not null and c.name <> '', 1, 0))                  as 户型名
      from tb_community_layout c
               join tb_community_detail cd on cd.id = c.community_detail_id
      GROUP BY cd.city_name) t1
where 总数 > 500
order by 总数 desc
```

### 4.小区图片表

```sql
select t1.*
from (select cd.city_name                          as 城市
           , count(distinct c.community_detail_id) as 小区数
           , count(1)                              as 图片数
           , sum(if(c.type = 'PASSAGEWAY', 1, 0))  as 出入口
           , sum(if(c.type = 'DISTANT', 1, 0))     as 远景
           , sum(if(c.type = 'ROAD', 1, 0))        as 道路
           , sum(if(c.type = 'PARK', 1, 0))        as 远景图
           , sum(if(c.type = 'BUILDING', 1, 0))    as 楼栋
           , sum(if(c.type = 'ENTRY_DOOR', 1, 0))  as 入户门
           , sum(if(c.type = 'SCENERY', 1, 0))     as 景观带
           , sum(if(c.type = 'FACILITY', 1, 0))    as 配套
           , sum(if(c.type = 'DISTRIBUTE', 1, 0))  as 分布图
           , sum(if(c.type = 'OTHER', 1, 0))       as 其他
      from tb_community_picture c
               join tb_community_detail cd on cd.id = c.community_detail_id
      GROUP BY cd.city_name) t1
where 图片数 > 500
order by 图片数 desc
```

### 5.楼栋信息

```sql
SELECT *
from (SELECT c.city_name                                                                        as 城市,
             count(DISTINCT c.id)                                                               as 小区数,
             count(1)                                                                           as 楼栋数,
             sum(if(b.source_building_addr is not null and b.source_building_addr <> '', 1, 0)) as 单元地址,
             sum(if(b.baidu_lng is not null and b.baidu_lng <> '', 1, 0))                       as 百度经度,
             sum(if(b.baidu_lat is not null and b.baidu_lat <> '', 1, 0))                       as 百度纬度,
             sum(if(b.gaode_lng is not null and b.gaode_lng <> '', 1, 0))                       as 高德经度,
             sum(if(b.gaode_lat is not null and b.gaode_lat <> '', 1, 0))                       as 高德纬度,
             sum(if(b.set_num is not null and b.set_num <> '', 1, 0))                           as 总套数,
             sum(if(b.floor_num is not null and b.floor_num <> 0, 1, 0))                        as 实际层高,
             sum(if(b.start_floor is not null and b.start_floor <> 0, 1, 0))                    as 起始楼层,
             sum(if(b.highest_floor is not null and b.highest_floor <> 0, 1, 0))                as 最高楼层,
             sum(if(b.elevator_num is not null and b.elevator_num <> '', 1, 0))                 as 电梯数量,
             sum(if(b.house_elevator_rate is not null and b.house_elevator_rate <> '', 1, 0))   as 梯户比,
             sum(if(b.distance is not null and b.distance <> 0, 1, 0))                          as 距离小区中心
      from building b
               left JOIN community c on c.community_id = b.community_id
      group by c.city_name) t1
order by 楼栋数 desc
```

### 6.单元信息

```sql
SELECT *
from (SELECT c.city_name                                                         as 城市,
             count(DISTINCT c.id)                                                as 小区数,
             count(1)                                                            as 单元数,
             sum(if(b.unit_name is not null and b.unit_name <> '', 1, 0))        as 单元地址,
             sum(if(b.set_num is not null and b.set_num <> '', 1, 0))            as 总套数,
             sum(if(b.floor_num is not null and b.floor_num <> 0, 1, 0))         as 实际层高,
             sum(if(b.start_floor is not null and b.start_floor <> 0, 1, 0))     as 起始楼层,
             sum(if(b.highest_floor is not null and b.highest_floor <> 0, 1, 0)) as 最高楼层
      from unit b
               left JOIN community c on c.community_id = b.community_id
      group by c.city_name) t1
order by 单元数 desc
```

### 7.房屋信息

```sql
SELECT *
from (SELECT c.city_name                                                 as 城市,
             count(DISTINCT c.id)                                        as 小区数,
             count(1)                                                    as 房屋数,
             sum(if(b.room_no is not null and b.room_no <> '', 1, 0))    as 房号,
             sum(if(b.nom_floor is not null and b.nom_floor <> 0, 1, 0)) as 名义楼层,
             sum(if(b.act_floor is not null and b.act_floor <> 0, 1, 0)) as 实际楼层
      from room b
               left JOIN community c on c.community_id = b.community_id
      group by c.city_name) t1
order by 房屋数 desc
```

### 8.楼盘信息

```sql
select *
from (select xbi.city                                                                                           as 城市
           , count(1)                                                                                              楼盘数
           , sum(if(highlights is not null and highlights <> '', 1, 0))                                         as 楼盘亮点
           , sum(if(tag_codes is not null and tag_codes <> '', 1, 0))                                           as 楼盘标签
           , sum(if(avg_price is not null and avg_price <> '', 1, 0))                                           as 楼盘均价
           , sum(if(show_price is not null and show_price <> '', 1, 0))                                         as 楼盘展示价
           , sum(if(estate_types is not null and estate_types <> '', 1, 0))                                     as 物业类型
           , sum(if(building_area is not null and building_area <> '', 1, 0))                                   as 建筑面积
           , sum(if(project_detail_address is not null and project_detail_address <> '', 1, 0))                 as 楼盘详细地址
           , sum(if(map_location_longitude is not null and map_location_longitude <> '', 1, 0))                 as 地图定位
           , sum(if(developer_full_name is not null and developer_full_name <> '', 1, 0))                       as 开发商全称
           , sum(if(developer_sales_office_address is not null and developer_sales_office_address <> '', 1, 0)) as 售楼处地址
           , sum(if(developer_opening_time is not null and developer_opening_time <> '', 1, 0))                 as 开盘时间
           , sum(if(developer_due_time is not null and developer_due_time <> '', 1, 0))                         as 交付时间
           , sum(if(community_area is not null and community_area <> '', 1, 0))                                 as 占地面积
           , sum(if(community_volume_rate is not null and community_volume_rate <> '', 1, 0))                   as 容积率
           , sum(if(community_greening_rate is not null and community_greening_rate <> '', 1, 0))               as 绿化率
           , sum(if(community_planning_building is not null and community_planning_building <> '', 1, 0))       as 规划楼栋
           , sum(if(community_planning_households is not null and community_planning_households <> '', 1, 0))   as 规划户数
           , sum(if(community_estate_company is not null and community_estate_company <> '', 1, 0))             as 物业公司
           , sum(if(community_estate_expenses is not null and community_estate_expenses <> '', 1, 0))           as 物业费用
           , sum(if(community_heating_type_desc is not null and community_heating_type_desc <> '', 1, 0))       as 供暖方式
           , sum(if(community_water_type_desc is not null and community_water_type_desc <> '', 1, 0))           as 供水方式
           , sum(if(community_power_type_desc is not null and community_power_type_desc <> '', 1, 0))           as 供电方式
           , sum(if(overall_introduction is not null and overall_introduction <> '', 1, 0))                     as 楼盘介绍
           , sum(if(phone_no is not null and phone_no <> '', 1, 0))                                             as 售楼处座机
           , sum(if(parking_radio is not null and parking_radio <> '', 1, 0))                                   as 车位比例
           , sum(if(alias_names is not null and alias_names <> '', 1, 0))                                       as 楼盘别名
           , sum(if(building_types is not null and building_types <> '', 1, 0))                                 as 建筑类型
           , sum(if(house_types is not null and house_types <> '', 1, 0))                                       as 房屋类型
           , sum(if(traffic_description is not null and traffic_description <> '', 1, 0))                       as 交通状况
           , sum(if(peripheral_business is not null and peripheral_business <> '', 1, 0))                       as 周边商业
           , sum(if(surrounding_landscape is not null and surrounding_landscape <> '', 1, 0))                   as 周边景观
           , sum(if(surrounding_parks is not null and surrounding_parks <> '', 1, 0))                           as 周边公园
           , sum(if(surrounding_hospitals is not null and surrounding_hospitals <> '', 1, 0))                   as 周边医院
           , sum(if(surrounding_schools is not null and surrounding_schools <> '', 1, 0))                       as 周边学校
           , sum(if(surrounding_traffic is not null and surrounding_traffic <> '', 1, 0))                       as 周边交通
           , sum(if(start_time is not null and start_time <> '', 1, 0))                                         as 开工时间
           , sum(if(completion_time is not null and completion_time <> '', 1, 0))                               as 竣工时间
           , sum(if(house_acquisition_rate is not null and house_acquisition_rate <> '', 1, 0))                 as 得房率
           , sum(if(parking_number is not null and parking_number <> 0, 1, 0))                                  as 车位数
           , sum(if(parking_fee is not null and parking_fee <> '待定', 1, 0))                                   as 车位费
           , sum(if(main_house_type_description is not null and main_house_type_description <> '', 1, 0))       as 主力户型
           , sum(if(diversion_people_vehicles is not null, 1, 0))                                               as 人车分流
      from xf_base_info xbi
      where city is not null
        and city <> ''
      GROUP BY xbi.city) t1
order by 楼盘数 desc
```

### 9. 新房楼盘预售证

```sql
select *
from (select bi.city                                                                              as 城市
           , count(1)                                                                                预售证数
           , sum(if(pre_sale_license_number is not null and pre_sale_license_number <> '', 1, 0)) as 许可证号
           , sum(if(certification_date is not null and certification_date <> '', 1, 0))           as 发证日期
           , sum(if(permitted_sales_area is not null and permitted_sales_area <> '', 1, 0))       as 准许销售面积
           , sum(if(pre_sale_position is not null and pre_sale_position <> '', 1, 0))             as 预售部位
      from xf_pre_sale_info si
               join xf_base_info bi on bi.outer_id = si.outer_id
      where bi.city is not null
        and bi.city <> ''
      GROUP BY bi.city) t1
order by 预售证数 desc
;
```

### 10. 新房楼盘图片

```sql
select *
from (select bi.city as                  城市
           , count(DISTINCT si.outer_id) 楼盘数
           , count(1)                    图片数
      from xf_picture_info si
               join xf_base_info bi on bi.outer_id = si.outer_id
      where bi.city is not null
        and bi.city <> ''
      GROUP BY bi.city) t1
order by 楼盘数 desc
;
```

### 11. 挂牌房源
```sql
select *
from (select d.city_name                             as 城市
           , count(DISTINCT d.broker_id)             as 经纪人数
           , sum(if(channel_code = 'XIANYU', 1, 0))  as 闲鱼
           , sum(if(channel_code = 'ALIPAY', 1, 0))  as 支付宝
           , sum(if(channel_code = 'PRIVATE', 1, 0)) as 微信个人店
           , count(1)                                as 合计
      from tb_delegation d
      where d.channel_code is not null
        and d.`status` = 'UP_SUCC'
        and d.logic_delete = 0
      GROUP BY d.city_name) t1
order by 闲鱼 desc;
```