-- 新增普代城市价格配置 --
START TRANSACTION;

insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), ***********,'XIANYU', 'SALE', 'CHANNEL_OPEN', '西安', '610100', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), ***********,'XIANYU', 'SALE', 'CLUE_CONSUME', '西安', '610100', 9, 7, 1, 7, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), ***********,'ALIPAY', 'SALE', 'CLUE_CONSUME', '西安', '610100', 9, 7, 1, 7, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), ***********,'XIANYU', 'RENT', 'CHANNEL_OPEN', '西安', '610100', 200, 100, 1, 100, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), ***********,'XIANYU', 'RENT', 'CLUE_CONSUME', '西安', '610100', 4.5, 3.5, 1, 3.5, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), ***********,'ALIPAY', 'RENT', 'CLUE_CONSUME', '西安', '610100', 4.5, 3.5, 1, 3.5, 1, 1);

INSERT into tb_template_price(price_id,template_id)
select p.id,1 from tb_price p where p.version=***********;
INSERT into tb_template_price(price_id,template_id)
select p.id,4 from tb_price p where p.version=***********;

INSERT into tb_contract_price(contract_id,price_id)
select c.id,p.id from tb_contract c
                          LEFT JOIN tb_price p on p.version=***********
where c.`status`='EFFECTIVE'
  and sub_type='AGENT_NORMAL'
  and c.template_id in (1,4)
;

#commit ;



-- 导入交易成本价和导入时间 --
update tb_deal d
set d.cost_price=0,
    d.cost_import_time='2025-04-09',
    d.version=d.version + 1,
    d.update_time=current_timestamp
where order_num in (
                    'SaaS_969702',
                    'SaaS_967515',
                    'SaaS_967579',
                    'SaaS_1116704',
                    'SaaS_1109945',
                    'SaaS_300419',
                    'SaaS_22963',
                    'SaaS_17901',
                    'SaaS_14983',
                    'SaaS_21336',
                    'SaaS_21384'
    );

-- 重新生成结算单 -------------------------------------------------------------------------
-- START TRANSACTION;
UPDATE tb_clearing c
    join tb_statement s on s.id=c.statement_id
set c.statement_id=null,c.update_time=CURRENT_TIMESTAMP,c.version=c.version+1
where s.statement_month='202502' and s.logic_delete=0
;
commit;

UPDATE tb_statement s
set s.logic_delete=1,update_time=CURRENT_TIMESTAMP,version=version+1
where s.statement_month='202502' and s.logic_delete=0
;

SELECT * from tb_statement s
where s.statement_month='202502' and s.logic_delete=0
;

-- ----------------- 变更代理商所属合伙人 ----------------------------------------------------------------------
select c.invited_code,c.invited_id,c.invited_user_id,c.invited_contract_id, c.* from tb_user u
                                                                                         LEFT JOIN tb_contract c on c.user_id=u.id and c.type='AGENT'
where u.contact_mobile='13548074545'
;

select i.`code`,i.id inv_id,i.user_id user_id,c.id contract_id from tb_invitation i
                                                                        LEFT JOIN tb_contract c on c.user_id=i.user_id and c.type='PARTNER'
where i.`code`='505620'
-- where i.`code`='H2VPCJ'
;

select d.pay_time,d.`status`,d.partner_contract_id,d.`status`,d.* from tb_deal d
where d.agent_contract_id=100905 and d.version between 250508001 and 250508999
order by d.pay_time desc
-- select * from tb_contract c where c.user_id=9095376821982095616;


-- 本人辜波(代理手机号13548109843、代理身份证号513822198901217110)申请解除当下代理合伙人关系，转到505620名下做代理
-- 本人吴彦冰(手机号13548074545 513723199108062315)申请解除当下代理合伙人关系，转到505620名下做代理