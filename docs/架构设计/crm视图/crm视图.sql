create OR replace view agent_clearing_deal AS
SELECT ac.id                                                                                   AS clearing_id,                   -- 清分记录ID
       d.id                                                                                    AS deal_id,                       -- 交易明细ID
       'ORIG'                                                                                  AS category,                      -- 类别: ORIG-原始数据,AGENT_DIFF-代理调差, PARTNER_DIFF-合伙调差
       ac.create_time                                                                          AS create_time,                   --  创建时间
       ac.update_time                                                                          AS update_time,                   --  更新时间
       d.channel_code                                                                          AS channel_code,                  -- 渠道类型/线索来源
       CASE d.channel_code
           WHEN 'XIANYU' THEN '闲鱼'
           WHEN 'ALIPAY' THEN '支付宝'
           WHEN 'MEITUAN' THEN '美团'
           WHEN 'GAODE' THEN '高德'
           WHEN 'TIANMAO' THEN '天猫'
           ELSE d.channel_code END                                                             AS channel_code_name,             -- 渠道类型名称/线索来源名称
       IF(d.enjoy = 1, 'ENJOY', CONCAT(d.type, ''))                                            AS deal_type,                     -- 交易类型
       IF(d.enjoy = 1, '畅享包', CASE d.type
                                     WHEN 'CHANNEL_OPEN' THEN '渠道开通'
                                     WHEN 'CLUE_BUY' THEN '线索购买'
                                     WHEN 'CLUE_CONSUME' THEN '线索消耗'
                                     ELSE d.type END)                                          AS deal_type_name,                -- 交易类型名称
       IF(d.agent_employee_flag = 1, NULL, d.agent_sub_type_name)                              AS agent_sub_type_name,           -- 代理商类型
       d.city_name                                                                             AS city_name,                     --  代理城市名称&客户所在城市
       IF(d.agent_employee_flag = 1, NULL, d.agent_user_type_name)                             AS agent_user_type_name,          --  代理商类型
       IF(d.agent_employee_flag = 1, NULL, d.agent_user_id)                                    AS agent_user_id,                 --  代理商公司/个人编号
       IF(d.agent_employee_flag = 1, NULL, d.agent_user_name)                                  AS agent_user_name,               --  代理商公司/个人名称&返佣对象一
       case d.business_code
           when 'RENT' then '租房'
           when 'SALE' then '二手房'
           when 'NEW' then '新房'
           else d.business_code end                                                            AS business_code_name,            --  产品类型
       d.pay_amount                                                                            AS pay_amount,                    --  售价&产品总额
       IF(d.agent_employee_flag = 1, NULL, d.agent_price)                                      AS agent_price,                   --  线索费结算价
       IF(d.agent_employee_flag = 1, NULL, d.agent_discount)                                   AS agent_discount,                -- 结算价折扣率
       IF(d.agent_employee_flag = 1, NULL, d.agent_discount_price)                             AS agent_discount_price,          -- 线索费结算价（折后）
       d.cost_price                                                                            AS cost_price,                    -- 内部成本价
       d.cost_import_time                                                                      AS cost_import_time,              -- 成本导入时间
       IF(d.agent_employee_flag = 1 OR d.partner_employee_flag = 1, NULL, d.partner_user_name) AS partner_user_name,             -- 返佣对象二&归属合伙人名称
       IF(d.agent_employee_flag = 1 OR d.partner_employee_flag = 1, NULL, d.partner_comm_rate) AS partner_comm_rate,             -- 返佣对象二比例
       IF(d.agent_employee_flag = 1 OR d.partner_employee_flag = 1, NULL, d.partner_user_id)   AS partner_user_id,               -- 归属合伙人ID
       d.crm_order_no                                                                          AS crm_order_no,                  -- 产品订单ID
       IF(d.type = 'CLUE_CONSUME', d.order_num, NULL)                                          AS order_num,                     -- 线索ID
       d.children_amount                                                                       AS children_amount,               -- 已用金额
       d.children_quantity                                                                     AS clue_quantity,                 -- 产品线索数
       d.pay_time                                                                              AS pay_time,                      -- 订单支付日期&交付完成日期&成销日期
       IF(d.agent_employee_flag = 1, NULL, ac.report_pre_tax)                                  AS agent_report_pre_tax,          -- 应计返佣一税前
       IF(d.agent_employee_flag = 1, NULL, ac.report_after_tax)                                AS agent_report_after_tax,        -- 应计返佣一税后
       IF(d.agent_employee_flag = 1 OR d.partner_employee_flag = 1, NULL, pc.report_pre_tax)   AS partner_report_pre_tax,        -- 应计返佣二税前
       IF(d.agent_employee_flag = 1 OR d.partner_employee_flag = 1, NULL, pc.report_after_tax) AS partner_report_after_tax,      -- 应计返佣二税后
       IF(d.agent_employee_flag = 1, NULL, ac.fact_pre_tax)                                    AS agent_fact_pre_tax,            -- 实计返佣一税前
       IF(d.agent_employee_flag = 1, NULL, ac.fact_after_tax)                                  AS agent_fact_after_tax,          -- 实计返佣一税后
       IF(d.agent_employee_flag = 1, NULL, ac.fact_time)                                       AS agent_fact_time,               -- 实计返佣一时间
       IF(d.agent_employee_flag = 1, NULL, ac.effect_time)                                     AS agent_effect_time,             -- 成销返佣一时间
       IF(d.agent_employee_flag = 1 OR d.partner_employee_flag = 1, NULL, pc.fact_pre_tax)     AS partner_fact_pre_tax,          -- 实计返佣二税前
       IF(d.agent_employee_flag = 1 OR d.partner_employee_flag = 1, NULL, pc.fact_after_tax)   AS partner_fact_after_tax,        -- 实计返佣二税后
       IF(d.agent_employee_flag = 1 OR d.partner_employee_flag = 1, NULL, pc.fact_time)        AS partner_fact_time,             -- 实计返佣二时间
       IF(d.agent_employee_flag = 1 OR d.partner_employee_flag = 1, NULL, pc.effect_time)      AS partner_effect_time,           -- 成销返佣二时间
       d.broker_user_id                                                                        AS broker_user_id,                -- 客户编号
       d.broker_user_name                                                                      AS broker_user_name,              -- 客户名称
       d.broker_contract_sub_types_name                                                        AS broker_contract_sub_types_name,-- 客户身份
       d.standard_price                                                                        AS standard_price,                -- 平台定价
       IF(d.tm_forward = 1 OR d.agent_employee_flag = 1, NULL, ac.excess_amount)               AS excess_amount,                 -- 超出售价金额
       IF(d.tm_forward = 1 OR d.agent_employee_flag = 1, NULL, ac.excess_comm_ratio)           AS agent_excess_comm_ratio,       -- 超出账号费返佣比例
       IF(d.tm_forward = 1 OR d.agent_employee_flag = 1, NULL, ac.excess_comm)                 AS agent_excess_comm,             -- 超出账号费返佣金额
       IF(d.agent_employee_flag = 1, NULL, ac.comm)                                            AS agent_comm,                    -- 代理商总返佣金额
       d.tm_forward                                                                            AS tm_forward,                    -- 是否天猫结转
       d.tm_bail                                                                               AS tm_bail,                       -- 天猫保证金
       d.tm_new_trade                                                                          AS tm_new_trade,                  -- 天猫新交易
       d.tm_service_fee                                                                        AS tm_service_fee,                -- 天猫服务费
       d.employee_name                                                                         AS employee_name,                 -- 业务员名称
       d.up_delegate_flag                                                                      AS up_delegate_flag,              -- 是否上翻房源
       d.up_delegate_time                                                                      AS up_delegate_time,              -- 上翻房源时间
       d.employee_no                                                                           AS employee_no,                   -- 业务员工号
       ac.crm_split                                                                            AS agent_crm_split,               -- 返佣对象一:CRM是否逐月拆分
       pc.crm_split                                                                            AS partner_crm_split,             -- 返佣对象二:CRM是否逐月拆分
       d.tm_income_contract_no                                                                 AS tm_income_contract_no,         -- 天猫结转收入合同号
       NULL                                                                                    AS agent_invoice_check_time,      -- 返佣对象一:发票审核时间
       NULL                                                                                    AS partner_invoice_check_time,    -- 返佣对象二:发票审核时间
       IF(d.type = 'CLUE_CONSUME', pd.pay_audit_time, d.pay_audit_time)                        AS pay_audit_time,                -- 支付审核时间
       d.category                                                                              AS deal_category                  -- 交易类别
FROM tb_deal AS d
         LEFT JOIN tb_clearing AS ac ON ac.id = d.agent_clearing_id
         LEFT JOIN tb_clearing AS pc ON pc.id = d.partner_clearing_id
         LEFT JOIN tb_deal AS pd ON pd.id = d.parent_id
WHERE d.status = 'CLEARED'
  AND d.test_flag = 0
  AND d.logic_delete = 0
  AND d.category <> 'EXPIRE'
UNION ALL
SELECT c.id                                                                                                                      AS clearing_id,                    -- 清分记录ID
       d.id                                                                                                                      AS deal_id,                        -- 交易明细ID
       CONCAT(c.type, '_DIFF')                                                                                                   AS category,                       -- 类别: ORIG-原始数据,AGENT_DIFF-代理调差, PARTNER_DIFF-合伙调差
       c.create_time                                                                                                             AS create_time,                    --  创建时间
       c.update_time                                                                                                             AS update_time,                    --  更新时间
       d.channel_code                                                                                                            AS channel_code,                   -- 渠道类型
       CASE d.channel_code
           WHEN 'XIANYU' THEN '闲鱼'
           WHEN 'ALIPAY' THEN '支付宝'
           WHEN 'MEITUAN' THEN '美团'
           WHEN 'GAODE' THEN '高德'
           WHEN 'TIANMAO' THEN '天猫'
           ELSE d.channel_code END                                                                                               AS channel_code_name,              -- 渠道类型名称
       IF(d.enjoy = 1, 'ENJOY', CONCAT(d.type, ''))                                                                              AS deal_type,                      -- 交易类型
       IF(d.enjoy = 1, '畅享包', CASE d.type
                                     WHEN 'CHANNEL_OPEN' THEN '渠道开通'
                                     WHEN 'CLUE_BUY' THEN '线索购买'
                                     WHEN 'CLUE_CONSUME' THEN '线索消耗'
                                     ELSE d.type END)                                                                            AS deal_type_name,                 -- 交易类型名称
       IF(d.agent_employee_flag = 1, NULL, d.agent_sub_type_name)                                                                AS agent_sub_type_name,            -- 代理商类型
       d.city_name                                                                                                               AS city_name,                      --  代理城市名称&客户所在城市
       IF(d.agent_employee_flag = 1, NULL, d.agent_user_type_name)                                                               AS agent_user_type_name,           --  代理商类型
       IF(d.agent_employee_flag = 1, NULL, d.agent_user_id)                                                                      AS agent_user_id,                  --  代理商公司/个人编号
       IF(d.agent_employee_flag = 1, NULL, d.agent_user_name)                                                                    AS agent_user_name,                --  代理商公司/个人名称&返佣对象一
       case d.business_code
           when 'RENT' then '租房'
           when 'SALE' then '二手房'
           when 'NEW' then '新房'
           else d.business_code end                                                                                              AS business_code_name,             --  产品类型
       d.pay_amount                                                                                                              AS pay_amount,                     --  售价&产品总额
       IF(d.agent_employee_flag = 1, NULL, d.agent_price)                                                                        AS agent_price,                    --  线索费结算价
       IF(d.agent_employee_flag = 1, NULL, d.agent_discount)                                                                     AS agent_discount,                 -- 结算价折扣率
       IF(d.agent_employee_flag = 1, NULL, d.agent_discount_price)                                                               AS agent_discount_price,           -- 线索费结算价（折后）
       d.cost_price                                                                                                              AS cost_price,                     -- 内部成本价
       d.cost_import_time                                                                                                        AS cost_import_time,               -- 成本导入时间
       IF(d.agent_employee_flag = 1 OR d.partner_employee_flag = 1, NULL, d.partner_user_name)                                   AS partner_user_name,              -- 返佣对象二&归属合伙人名称
       IF(d.agent_employee_flag = 1 OR d.partner_employee_flag = 1, NULL, d.partner_comm_rate)                                   AS partner_comm_rate,              -- 返佣对象二比例
       IF(d.agent_employee_flag = 1 OR d.partner_employee_flag = 1, NULL, d.partner_user_id)                                     AS partner_user_id,                -- 归属合伙人ID
       d.crm_order_no                                                                                                            AS crm_order_no,                   -- 产品订单ID
       IF(d.type = 'CLUE_CONSUME', d.order_num, NULL)                                                                            AS order_num,                      -- 线索ID
       d.children_amount                                                                                                         AS children_amount,                -- 已用金额
       d.children_quantity                                                                                                       AS clue_quantity,                  -- 产品线索数
       d.pay_time                                                                                                                AS pay_time,                       -- 订单支付日期&交付完成日期&成销日期
       IF(c.type = 'AGENT', IF(d.agent_employee_flag = 1, NULL, c.report_pre_tax_fact), NULL)                                    AS agent_report_pre_tax,           -- 应计返佣一税前
       IF(c.type = 'AGENT', IF(d.agent_employee_flag = 1, NULL, c.report_after_tax_fact), NULL)                                  AS agent_report_after_tax,         -- 应计返佣一税后
       IF(c.type = 'PARTNER', IF(d.agent_employee_flag = 1 OR d.partner_employee_flag = 1, NULL, c.report_pre_tax_fact), NULL)   AS partner_report_pre_tax,         -- 应计返佣二税前
       IF(c.type = 'PARTNER', IF(d.agent_employee_flag = 1 OR d.partner_employee_flag = 1, NULL, c.report_after_tax_fact), NULL) AS partner_report_after_tax,       -- 应计返佣二税后
       IF(c.type = 'AGENT', IF(d.agent_employee_flag = 1, NULL, c.fact_pre_tax), NULL)                                           AS agent_fact_pre_tax,             -- 实计返佣一税前
       IF(c.type = 'AGENT', IF(d.agent_employee_flag = 1, NULL, c.fact_after_tax), NULL)                                         AS agent_fact_after_tax,           -- 实计返佣一税后
       IF(c.type = 'AGENT', IF(d.agent_employee_flag = 1, NULL, c.fact_time), NULL)                                              AS agent_fact_time,                -- 实计返佣一时间
       IF(c.type = 'AGENT', IF(d.agent_employee_flag = 1, NULL, c.effect_time), NULL)                                            AS agent_effect_time,              -- 成销返佣一时间
       IF(c.type = 'PARTNER', IF(d.agent_employee_flag = 1 OR d.partner_employee_flag = 1, NULL, c.fact_pre_tax), NULL)          AS partner_fact_pre_tax,           -- 实计返佣二税前
       IF(c.type = 'PARTNER', IF(d.agent_employee_flag = 1 OR d.partner_employee_flag = 1, NULL, c.fact_after_tax), NULL)        AS partner_fact_after_tax,         -- 实计返佣二税后
       IF(c.type = 'PARTNER', IF(d.agent_employee_flag = 1 OR d.partner_employee_flag = 1, NULL, c.fact_time), NULL)             AS partner_fact_time,              -- 实计返佣二时间
       IF(c.type = 'PARTNER', IF(d.agent_employee_flag = 1 OR d.partner_employee_flag = 1, NULL, c.effect_time), NULL)           AS partner_effect_time,            -- 成销返佣二时间
       d.broker_user_id                                                                                                          AS broker_user_id,                 -- 客户编号
       d.broker_user_name                                                                                                        AS broker_user_name,               -- 客户名称
       d.broker_contract_sub_types_name                                                                                          AS broker_contract_sub_types_name, -- 客户身份
       d.standard_price                                                                                                          AS standard_price,                 -- 平台定价
       IF(c.type = 'AGENT', IF(d.tm_forward = 1 OR d.agent_employee_flag = 1, NULL, c.excess_amount), NULL)                      AS excess_amount,                  -- 超出售价金额
       IF(c.type = 'AGENT', IF(d.tm_forward = 1 OR d.agent_employee_flag = 1, NULL, c.excess_comm_ratio), NULL)                  AS agent_excess_comm_ratio,        -- 超出账号费返佣比例
       IF(c.type = 'AGENT', IF(d.tm_forward = 1 OR d.agent_employee_flag = 1, NULL, c.excess_comm), NULL)                        AS agent_excess_comm,              -- 超出账号费返佣金额
       NULL                                                                                                                      AS agent_comm,                     -- 代理商总返佣金额
       d.tm_forward                                                                                                              AS tm_forward,                     -- 是否天猫结转
       d.tm_bail                                                                                                                 AS tm_bail,                        -- 天猫保证金
       d.tm_new_trade                                                                                                            AS tm_new_trade,                   -- 天猫新交易
       d.tm_service_fee                                                                                                          AS tm_service_fee,                 -- 天猫服务费
       d.employee_name                                                                                                           AS employee_name,                  -- 业务员名称
       d.up_delegate_flag                                                                                                        AS up_delegate_flag,               -- 是否上翻房源
       d.up_delegate_time                                                                                                        AS up_delegate_time,               -- 上翻房源时间
       d.employee_no                                                                                                             AS employee_no,                    -- 业务员工号,
       IF(c.type = 'AGENT', c.crm_split, NULL)                                                                                   AS agent_crm_split,                -- 返佣对象一:CRM是否逐月拆分
       IF(c.type = 'PARTNER', c.crm_split, NULL)                                                                                 AS partner_crm_split,              -- 返佣对象二:CRM是否逐月拆分
       d.tm_income_contract_no                                                                                                   AS tm_income_contract_no,          -- 天猫结转收入合同号
       IF(c.type = 'AGENT', s.invoice_check_time, NULL)                                                                          AS agent_invoice_check_time,       -- 返佣对象一:发票审核时间
       IF(c.type = 'PARTNER', s.invoice_check_time, NULL)                                                                        AS partner_invoice_check_time,     -- 返佣对象二:发票审核时间
       IF(d.type = 'CLUE_CONSUME', pd.pay_audit_time, d.pay_audit_time)                                                          AS pay_audit_time,                 -- 支付审核时间
       d.category                                                                                                                AS deal_category                   -- 交易类别
FROM tb_clearing AS c
         LEFT JOIN tb_deal AS d ON d.id = c.deal_id
         LEFT JOIN tb_deal AS pd ON pd.id = d.parent_id
         LEFT JOIN tb_statement AS s ON s.id = c.statement_id
WHERE c.flag_diff = 1
  AND c.logic_delete = 0
  AND d.test_flag = 0
  AND d.category <> 'EXPIRE'
  AND s.pay_time IS NOT NULL