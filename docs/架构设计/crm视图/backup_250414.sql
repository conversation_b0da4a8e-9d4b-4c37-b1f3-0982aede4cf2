CREATE ALGORITHM = UNDEFINED DEFINER =`ejuetc_crm_writer`@`%` SQL SECURITY DEFINER VIEW `agent_clearing_deal`
            (`clearing_id`, `deal_id`, `category`, `create_time`, `update_time`, `channel_code`, `channel_code_name`, `deal_type`, `deal_type_name`, `agent_sub_type_name`, `city_name`, `agent_user_type_name`, `agent_user_id`, `agent_user_name`, `business_code_name`, `pay_amount`, `agent_price`,
             `agent_discount`, `agent_discount_price`, `cost_price`, `cost_import_time`, `partner_user_name`, `partner_comm_rate`, `partner_user_id`, `crm_order_no`, `order_num`, `children_amount`, `clue_quantity`, `pay_time`, `agent_report_pre_tax`, `agent_report_after_tax`,
             `partner_report_pre_tax`, `partner_report_after_tax`, `agent_fact_pre_tax`, `agent_fact_after_tax`, `agent_fact_time`, `agent_effect_time`, `partner_fact_pre_tax`, `partner_fact_after_tax`, `partner_fact_time`, `partner_effect_time`, `broker_user_id`, `broker_user_name`,
             `broker_contract_sub_types_name`, `standard_price`, `excess_amount`, `agent_excess_comm_ratio`, `agent_excess_comm`, `agent_comm`, `tm_forward`, `tm_bail`, `tm_new_trade`, `tm_service_fee`, `employee_name`, `up_delegate_flag`, `up_delegate_time`, `employee_no`, `agent_crm_split`,
             `partner_crm_split`, `tm_income_contract_no`, `agent_invoice_check_time`, `partner_invoice_check_time`, `pay_audit_time`, `deal_category`)
AS
SELECT `ac`.`id`                                                                                                                                                                                                                                                                AS `clearing_id`,
       `d`.`id`                                                                                                                                                                                                                                                                 AS `deal_id`,
       _UTF8MB4'ORIG'                                                                                                                                                                                                                                                           AS `category`,
       `ac`.`create_time`                                                                                                                                                                                                                                                       AS `create_time`,
       `ac`.`update_time`                                                                                                                                                                                                                                                       AS `update_time`,
       `d`.`channel_code`                                                                                                                                                                                                                                                       AS `channel_code`,
       CASE `d`.`channel_code` WHEN _UTF8MB4'XIANYU' THEN _UTF8MB4'闲鱼' WHEN _UTF8MB4'ALIPAY' THEN _UTF8MB4'支付宝' WHEN _UTF8MB4'MEITUAN' THEN _UTF8MB4'美团' WHEN _UTF8MB4'GAODE' THEN _UTF8MB4'高德' WHEN _UTF8MB4'TIANMAO' THEN _UTF8MB4'天猫' ELSE `d`.`channel_code` END AS `channel_code_name`,
       IF(`d`.`enjoy` = 1, _UTF8MB4'ENJOY', CONCAT(`d`.`type`, _UTF8MB4''))                                                                                                                                                                                                     AS `deal_type`,
       IF(`d`.`enjoy` = 1, _UTF8MB4'畅享包', CASE `d`.`type` WHEN _UTF8MB4'CHANNEL_OPEN' THEN _UTF8MB4'渠道开通' WHEN _UTF8MB4'CLUE_BUY' THEN _UTF8MB4'线索购买' WHEN _UTF8MB4'CLUE_CONSUME' THEN _UTF8MB4'线索消耗' ELSE `d`.`type` END)                                       AS `deal_type_name`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `d`.`agent_sub_type_name`)                                                                                                                                                                                                       AS `agent_sub_type_name`,
       `d`.`city_name`                                                                                                                                                                                                                                                          AS `city_name`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `d`.`agent_user_type_name`)                                                                                                                                                                                                      AS `agent_user_type_name`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `d`.`agent_user_id`)                                                                                                                                                                                                             AS `agent_user_id`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `d`.`agent_user_name`)                                                                                                                                                                                                           AS `agent_user_name`,
       CASE `d`.`business_code` WHEN _UTF8MB4'RENT' THEN _UTF8MB4'租房' WHEN _UTF8MB4'SALE' THEN _UTF8MB4'二手房' WHEN _UTF8MB4'NEW' THEN _UTF8MB4'新房' ELSE `d`.`business_code` END                                                                                           AS `business_code_name`,
       `d`.`pay_amount`                                                                                                                                                                                                                                                         AS `pay_amount`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `d`.`agent_price`)                                                                                                                                                                                                               AS `agent_price`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `d`.`agent_discount`)                                                                                                                                                                                                            AS `agent_discount`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `d`.`agent_discount_price`)                                                                                                                                                                                                      AS `agent_discount_price`,
       `d`.`cost_price`                                                                                                                                                                                                                                                         AS `cost_price`,
       `d`.`cost_import_time`                                                                                                                                                                                                                                                   AS `cost_import_time`,
       IF(`d`.`agent_employee_flag` = 1 OR `d`.`partner_employee_flag` = 1, NULL, `d`.`partner_user_name`)                                                                                                                                                                      AS `partner_user_name`,
       IF(`d`.`agent_employee_flag` = 1 OR `d`.`partner_employee_flag` = 1, NULL, `d`.`partner_comm_rate`)                                                                                                                                                                      AS `partner_comm_rate`,
       IF(`d`.`agent_employee_flag` = 1 OR `d`.`partner_employee_flag` = 1, NULL, `d`.`partner_user_id`)                                                                                                                                                                        AS `partner_user_id`,
       `d`.`crm_order_no`                                                                                                                                                                                                                                                       AS `crm_order_no`,
       IF(`d`.`type` = _UTF8MB4'CLUE_CONSUME', `d`.`order_num`, NULL)                                                                                                                                                                                                           AS `order_num`,
       `d`.`children_amount`                                                                                                                                                                                                                                                    AS `children_amount`,
       `d`.`children_quantity`                                                                                                                                                                                                                                                  AS `clue_quantity`,
       `d`.`pay_time`                                                                                                                                                                                                                                                           AS `pay_time`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `ac`.`report_pre_tax`)                                                                                                                                                                                                           AS `agent_report_pre_tax`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `ac`.`report_after_tax`)                                                                                                                                                                                                         AS `agent_report_after_tax`,
       IF(`d`.`agent_employee_flag` = 1 OR `d`.`partner_employee_flag` = 1, NULL, `pc`.`report_pre_tax`)                                                                                                                                                                        AS `partner_report_pre_tax`,
       IF(`d`.`agent_employee_flag` = 1 OR `d`.`partner_employee_flag` = 1, NULL, `pc`.`report_after_tax`)                                                                                                                                                                      AS `partner_report_after_tax`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `ac`.`fact_pre_tax`)                                                                                                                                                                                                             AS `agent_fact_pre_tax`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `ac`.`fact_after_tax`)                                                                                                                                                                                                           AS `agent_fact_after_tax`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `ac`.`fact_time`)                                                                                                                                                                                                                AS `agent_fact_time`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `ac`.`effect_time`)                                                                                                                                                                                                              AS `agent_effect_time`,
       IF(`d`.`agent_employee_flag` = 1 OR `d`.`partner_employee_flag` = 1, NULL, `pc`.`fact_pre_tax`)                                                                                                                                                                          AS `partner_fact_pre_tax`,
       IF(`d`.`agent_employee_flag` = 1 OR `d`.`partner_employee_flag` = 1, NULL, `pc`.`fact_after_tax`)                                                                                                                                                                        AS `partner_fact_after_tax`,
       IF(`d`.`agent_employee_flag` = 1 OR `d`.`partner_employee_flag` = 1, NULL, `pc`.`fact_time`)                                                                                                                                                                             AS `partner_fact_time`,
       IF(`d`.`agent_employee_flag` = 1 OR `d`.`partner_employee_flag` = 1, NULL, `pc`.`effect_time`)                                                                                                                                                                           AS `partner_effect_time`,
       `d`.`broker_user_id`                                                                                                                                                                                                                                                     AS `broker_user_id`,
       `d`.`broker_user_name`                                                                                                                                                                                                                                                   AS `broker_user_name`,
       `d`.`broker_contract_sub_types_name`                                                                                                                                                                                                                                     AS `broker_contract_sub_types_name`,
       `d`.`standard_price`                                                                                                                                                                                                                                                     AS `standard_price`,
       IF(`d`.`tm_forward` = 1 OR `d`.`agent_employee_flag` = 1, NULL, `ac`.`excess_amount`)                                                                                                                                                                                    AS `excess_amount`,
       IF(`d`.`tm_forward` = 1 OR `d`.`agent_employee_flag` = 1, NULL, `ac`.`excess_comm_ratio`)                                                                                                                                                                                AS `agent_excess_comm_ratio`,
       IF(`d`.`tm_forward` = 1 OR `d`.`agent_employee_flag` = 1, NULL, `ac`.`excess_comm`)                                                                                                                                                                                      AS `agent_excess_comm`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `ac`.`comm`)                                                                                                                                                                                                                     AS `agent_comm`,
       `d`.`tm_forward`                                                                                                                                                                                                                                                         AS `tm_forward`,
       `d`.`tm_bail`                                                                                                                                                                                                                                                            AS `tm_bail`,
       `d`.`tm_new_trade`                                                                                                                                                                                                                                                       AS `tm_new_trade`,
       `d`.`tm_service_fee`                                                                                                                                                                                                                                                     AS `tm_service_fee`,
       `d`.`employee_name`                                                                                                                                                                                                                                                      AS `employee_name`,
       `d`.`up_delegate_flag`                                                                                                                                                                                                                                                   AS `up_delegate_flag`,
       `d`.`up_delegate_time`                                                                                                                                                                                                                                                   AS `up_delegate_time`,
       `d`.`employee_no`                                                                                                                                                                                                                                                        AS `employee_no`,
       `ac`.`crm_split`                                                                                                                                                                                                                                                         AS `agent_crm_split`,
       `pc`.`crm_split`                                                                                                                                                                                                                                                         AS `partner_crm_split`,
       `d`.`tm_income_contract_no`                                                                                                                                                                                                                                              AS `tm_income_contract_no`,
       NULL                                                                                                                                                                                                                                                                     AS `agent_invoice_check_time`,
       NULL                                                                                                                                                                                                                                                                     AS `partner_invoice_check_time`,
       IF(`d`.`type` = _UTF8MB4'CLUE_CONSUME', `pd`.`pay_audit_time`, `d`.`pay_audit_time`)                                                                                                                                                                                     AS `pay_audit_time`,
       `d`.`category`                                                                                                                                                                                                                                                           AS `deal_category`
FROM ((`prod_agent`.`tb_deal` AS `d` LEFT JOIN `prod_agent`.`tb_clearing` AS `ac` ON `ac`.`id` = `d`.`agent_clearing_id`) LEFT JOIN `prod_agent`.`tb_clearing` AS `pc` ON `pc`.`id` = `d`.`partner_clearing_id`)
         LEFT JOIN `prod_agent`.`tb_deal` AS `pd` ON `pd`.`id` = `d`.`parent_id`
WHERE `d`.`status` = _UTF8MB4'CLEARED'
  AND `d`.`test_flag` = 0
  AND `d`.`logic_delete` = 0
UNION ALL
SELECT `c`.`id`                                                                                                                                                                                                                                                                 AS `clearing_id`,
       `d`.`id`                                                                                                                                                                                                                                                                 AS `deal_id`,
       CONCAT(`c`.`type`, _UTF8MB4'_DIFF')                                                                                                                                                                                                                                      AS `category`,
       `c`.`create_time`                                                                                                                                                                                                                                                        AS `create_time`,
       `c`.`update_time`                                                                                                                                                                                                                                                        AS `update_time`,
       `d`.`channel_code`                                                                                                                                                                                                                                                       AS `channel_code`,
       CASE `d`.`channel_code` WHEN _UTF8MB4'XIANYU' THEN _UTF8MB4'闲鱼' WHEN _UTF8MB4'ALIPAY' THEN _UTF8MB4'支付宝' WHEN _UTF8MB4'MEITUAN' THEN _UTF8MB4'美团' WHEN _UTF8MB4'GAODE' THEN _UTF8MB4'高德' WHEN _UTF8MB4'TIANMAO' THEN _UTF8MB4'天猫' ELSE `d`.`channel_code` END AS `channel_code_name`,
       IF(`d`.`enjoy` = 1, _UTF8MB4'ENJOY', CONCAT(`d`.`type`, _UTF8MB4''))                                                                                                                                                                                                     AS `deal_type`,
       IF(`d`.`enjoy` = 1, _UTF8MB4'畅享包', CASE `d`.`type` WHEN _UTF8MB4'CHANNEL_OPEN' THEN _UTF8MB4'渠道开通' WHEN _UTF8MB4'CLUE_BUY' THEN _UTF8MB4'线索购买' WHEN _UTF8MB4'CLUE_CONSUME' THEN _UTF8MB4'线索消耗' ELSE `d`.`type` END)                                       AS `deal_type_name`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `d`.`agent_sub_type_name`)                                                                                                                                                                                                       AS `agent_sub_type_name`,
       `d`.`city_name`                                                                                                                                                                                                                                                          AS `city_name`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `d`.`agent_user_type_name`)                                                                                                                                                                                                      AS `agent_user_type_name`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `d`.`agent_user_id`)                                                                                                                                                                                                             AS `agent_user_id`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `d`.`agent_user_name`)                                                                                                                                                                                                           AS `agent_user_name`,
       CASE `d`.`business_code` WHEN _UTF8MB4'RENT' THEN _UTF8MB4'租房' WHEN _UTF8MB4'SALE' THEN _UTF8MB4'二手房' WHEN _UTF8MB4'NEW' THEN _UTF8MB4'新房' ELSE `d`.`business_code` END                                                                                           AS `business_code_name`,
       `d`.`pay_amount`                                                                                                                                                                                                                                                         AS `pay_amount`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `d`.`agent_price`)                                                                                                                                                                                                               AS `agent_price`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `d`.`agent_discount`)                                                                                                                                                                                                            AS `agent_discount`,
       IF(`d`.`agent_employee_flag` = 1, NULL, `d`.`agent_discount_price`)                                                                                                                                                                                                      AS `agent_discount_price`,
       `d`.`cost_price`                                                                                                                                                                                                                                                         AS `cost_price`,
       `d`.`cost_import_time`                                                                                                                                                                                                                                                   AS `cost_import_time`,
       IF(`d`.`agent_employee_flag` = 1 OR `d`.`partner_employee_flag` = 1, NULL, `d`.`partner_user_name`)                                                                                                                                                                      AS `partner_user_name`,
       IF(`d`.`agent_employee_flag` = 1 OR `d`.`partner_employee_flag` = 1, NULL, `d`.`partner_comm_rate`)                                                                                                                                                                      AS `partner_comm_rate`,
       IF(`d`.`agent_employee_flag` = 1 OR `d`.`partner_employee_flag` = 1, NULL, `d`.`partner_user_id`)                                                                                                                                                                        AS `partner_user_id`,
       `d`.`crm_order_no`                                                                                                                                                                                                                                                       AS `crm_order_no`,
       IF(`d`.`type` = _UTF8MB4'CLUE_CONSUME', `d`.`order_num`, NULL)                                                                                                                                                                                                           AS `order_num`,
       `d`.`children_amount`                                                                                                                                                                                                                                                    AS `children_amount`,
       `d`.`children_quantity`                                                                                                                                                                                                                                                  AS `clue_quantity`,
       `d`.`pay_time`                                                                                                                                                                                                                                                           AS `pay_time`,
       IF(`c`.`type` = _UTF8MB4'AGENT', IF(`d`.`agent_employee_flag` = 1, NULL, `c`.`report_pre_tax_fact`), NULL)                                                                                                                                                               AS `agent_report_pre_tax`,
       IF(`c`.`type` = _UTF8MB4'AGENT', IF(`d`.`agent_employee_flag` = 1, NULL, `c`.`report_after_tax_fact`), NULL)                                                                                                                                                             AS `agent_report_after_tax`,
       IF(`c`.`type` = _UTF8MB4'PARTNER', IF(`d`.`agent_employee_flag` = 1 OR `d`.`partner_employee_flag` = 1, NULL, `c`.`report_pre_tax_fact`), NULL)                                                                                                                          AS `partner_report_pre_tax`,
       IF(`c`.`type` = _UTF8MB4'PARTNER', IF(`d`.`agent_employee_flag` = 1 OR `d`.`partner_employee_flag` = 1, NULL, `c`.`report_after_tax_fact`), NULL)                                                                                                                        AS `partner_report_after_tax`,
       IF(`c`.`type` = _UTF8MB4'AGENT', IF(`d`.`agent_employee_flag` = 1, NULL, `c`.`fact_pre_tax`), NULL)                                                                                                                                                                      AS `agent_fact_pre_tax`,
       IF(`c`.`type` = _UTF8MB4'AGENT', IF(`d`.`agent_employee_flag` = 1, NULL, `c`.`fact_after_tax`), NULL)                                                                                                                                                                    AS `agent_fact_after_tax`,
       IF(`c`.`type` = _UTF8MB4'AGENT', IF(`d`.`agent_employee_flag` = 1, NULL, `c`.`fact_time`), NULL)                                                                                                                                                                         AS `agent_fact_time`,
       IF(`c`.`type` = _UTF8MB4'AGENT', IF(`d`.`agent_employee_flag` = 1, NULL, `c`.`effect_time`), NULL)                                                                                                                                                                       AS `agent_effect_time`,
       IF(`c`.`type` = _UTF8MB4'PARTNER', IF(`d`.`agent_employee_flag` = 1 OR `d`.`partner_employee_flag` = 1, NULL, `c`.`fact_pre_tax`), NULL)                                                                                                                                 AS `partner_fact_pre_tax`,
       IF(`c`.`type` = _UTF8MB4'PARTNER', IF(`d`.`agent_employee_flag` = 1 OR `d`.`partner_employee_flag` = 1, NULL, `c`.`fact_after_tax`), NULL)                                                                                                                               AS `partner_fact_after_tax`,
       IF(`c`.`type` = _UTF8MB4'PARTNER', IF(`d`.`agent_employee_flag` = 1 OR `d`.`partner_employee_flag` = 1, NULL, `c`.`fact_time`), NULL)                                                                                                                                    AS `partner_fact_time`,
       IF(`c`.`type` = _UTF8MB4'PARTNER', IF(`d`.`agent_employee_flag` = 1 OR `d`.`partner_employee_flag` = 1, NULL, `c`.`effect_time`), NULL)                                                                                                                                  AS `partner_effect_time`,
       `d`.`broker_user_id`                                                                                                                                                                                                                                                     AS `broker_user_id`,
       `d`.`broker_user_name`                                                                                                                                                                                                                                                   AS `broker_user_name`,
       `d`.`broker_contract_sub_types_name`                                                                                                                                                                                                                                     AS `broker_contract_sub_types_name`,
       `d`.`standard_price`                                                                                                                                                                                                                                                     AS `standard_price`,
       IF(`c`.`type` = _UTF8MB4'AGENT', IF(`d`.`tm_forward` = 1 OR `d`.`agent_employee_flag` = 1, NULL, `c`.`excess_amount`), NULL)                                                                                                                                             AS `excess_amount`,
       IF(`c`.`type` = _UTF8MB4'AGENT', IF(`d`.`tm_forward` = 1 OR `d`.`agent_employee_flag` = 1, NULL, `c`.`excess_comm_ratio`), NULL)                                                                                                                                         AS `agent_excess_comm_ratio`,
       IF(`c`.`type` = _UTF8MB4'AGENT', IF(`d`.`tm_forward` = 1 OR `d`.`agent_employee_flag` = 1, NULL, `c`.`excess_comm`), NULL)                                                                                                                                               AS `agent_excess_comm`,
       NULL                                                                                                                                                                                                                                                                     AS `agent_comm`,
       `d`.`tm_forward`                                                                                                                                                                                                                                                         AS `tm_forward`,
       `d`.`tm_bail`                                                                                                                                                                                                                                                            AS `tm_bail`,
       `d`.`tm_new_trade`                                                                                                                                                                                                                                                       AS `tm_new_trade`,
       `d`.`tm_service_fee`                                                                                                                                                                                                                                                     AS `tm_service_fee`,
       `d`.`employee_name`                                                                                                                                                                                                                                                      AS `employee_name`,
       `d`.`up_delegate_flag`                                                                                                                                                                                                                                                   AS `up_delegate_flag`,
       `d`.`up_delegate_time`                                                                                                                                                                                                                                                   AS `up_delegate_time`,
       `d`.`employee_no`                                                                                                                                                                                                                                                        AS `employee_no`,
       IF(`c`.`type` = _UTF8MB4'AGENT', `c`.`crm_split`, NULL)                                                                                                                                                                                                                  AS `agent_crm_split`,
       IF(`c`.`type` = _UTF8MB4'PARTNER', `c`.`crm_split`, NULL)                                                                                                                                                                                                                AS `partner_crm_split`,
       `d`.`tm_income_contract_no`                                                                                                                                                                                                                                              AS `tm_income_contract_no`,
       IF(`c`.`type` = _UTF8MB4'AGENT', `s`.`invoice_check_time`, NULL)                                                                                                                                                                                                         AS `agent_invoice_check_time`,
       IF(`c`.`type` = _UTF8MB4'PARTNER', `s`.`invoice_check_time`, NULL)                                                                                                                                                                                                       AS `partner_invoice_check_time`,
       IF(`d`.`type` = _UTF8MB4'CLUE_CONSUME', `pd`.`pay_audit_time`, `d`.`pay_audit_time`)                                                                                                                                                                                     AS `pay_audit_time`,
       `d`.`category`                                                                                                                                                                                                                                                           AS `deal_category`
FROM ((`prod_agent`.`tb_clearing` AS `c` LEFT JOIN `prod_agent`.`tb_deal` AS `d` ON `d`.`id` = `c`.`deal_id`) LEFT JOIN `prod_agent`.`tb_deal` AS `pd` ON `pd`.`id` = `d`.`parent_id`)
         LEFT JOIN `prod_agent`.`tb_statement` AS `s` ON `s`.`id` = `c`.`statement_id`
WHERE `c`.`flag_diff` = 1
  AND `c`.`logic_delete` = 0
  AND `d`.`test_flag` = 0
  AND `s`.`pay_time` IS NOT NULL