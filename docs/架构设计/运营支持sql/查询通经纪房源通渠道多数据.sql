UPDATE tb_delegation d set d.logic_delete=1
-- select d.update_time, d.`status`,d.remark,d.source_id from tb_delegation d
where version BETWEEN 25030802000 and 25030802999
-- d.manual_push is not null and d.`level`='CHANNEL' and d.logic_delete=0
;

START TRANSACTION;
commit;

-- select * from tb_delegation
-- update tb_delegation md set md.version=25030802000 -- ,md.manual_push=0
-- where id in (
SELECT
    t2.broker_delegation_id,t2.channel_code,d1.id,d1.source_id,d1.parent_source_id,d1. STATUS,d2.id,d2.source_id,d2.source_type,d2. STATUS,if(d1.`status`='UP_SUCC' and d2.`status`='UP_SUCC',1,if(d1.`status`='UP_SUCC',2,if(d2.`status`='UP_SUCC',3,4)))
-- d2.id
FROM
    (
        SELECT DISTINCT
            d.broker_delegation_id,
            d.channel_code -- ,d.source_id,d.`status`,d.*
        FROM
            tb_delegation d
                JOIN (
                SELECT
                    d.broker_delegation_id,
                    d.channel_code,
                    count(1)
                FROM
                    tb_delegation d
                WHERE
                    d.`level` = 'CHANNEL'
                  AND d.logic_delete = 0
                GROUP BY
                    d.broker_delegation_id,
                    d.channel_code
                HAVING
                    count(1) > 1
            ) t1 ON d.broker_delegation_id = t1.broker_delegation_id
                AND d.channel_code = t1.channel_code -- order by d.broker_delegation_id,d.channel_code,d.source_id
    ) t2
        LEFT JOIN tb_delegation d1 ON t2.broker_delegation_id = d1.broker_delegation_id
        AND t2.channel_code = d1.channel_code
        AND d1.source_id IS NOT NULL and d1.logic_delete=0
        LEFT JOIN tb_delegation d2 ON t2.broker_delegation_id = d2.broker_delegation_id
        AND t2.channel_code = d2.channel_code
        AND d2.source_id IS NULL and d2.logic_delete=0
-- and d1.`status`<>d2.`status`
where d1.id is not null and d2.id is not null
-- and d1.id=1402610
  and if(d1.`status`='UP_SUCC' and d2.`status`='UP_SUCC',1,if(d1.`status`='UP_SUCC',2,if(d2.`status`='UP_SUCC',3,4)))=3
    )
;

select d.parent_source_id,d.channel_code,d.source_id,d.`status` from tb_delegation d where d.broker_delegation_id=1273653 and d.channel_code='XIANYU'
;