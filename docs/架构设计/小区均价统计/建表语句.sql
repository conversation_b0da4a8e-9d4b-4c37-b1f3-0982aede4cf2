drop table if exists st_delegation_price_unit;

create table if not exists st_delegation_price_unit
(
    month                varchar(7)     not null comment '统计月份',
    community_id         int(20)        null comment '小区ID',
    id                   varchar(63)    not null comment '主键(月份_小区号)' primary key,
    fk_before_before     varchar(63)    null comment '上上个月的外键',
    fk_before            varchar(63)    null comment '上个月的外键',
    fk_next              varchar(63)    null comment '下个月的外键',
    count_0_months       int            null comment '本月房源数量',
    avg_0_months         decimal        null comment '本月均价',
    count_6_months       int            null comment '近6个月房源数量',
    avg_6_months         decimal        null comment '近6个月均价',
    count_12_months      int            null comment '近12个月房源数量',
    avg_12_months        decimal        null comment '近12个月均价',
    avg_community_amend  decimal        null comment '修正后小区均价',
    count_1_room         int            null comment '本月一居室房源数量',
    avg_1_room_orig      decimal        null comment '本月一居室均价',
    count_2_room         int            null comment '本月二居室房源数量',
    avg_2_room_orig      decimal        null comment '本月二居室均价',
    count_3_room         int            null comment '本月三居室房源数量',
    avg_3_room_orig      decimal        null comment '本月三居室均价',
    count_4_room         int            null comment '本月四居室房源数量',
    avg_4_room_orig      decimal        null comment '本月四居室均价',
    avg_1_room_amend     decimal        null comment '修正后一居室均价',
    avg_2_room_amend     decimal        null comment '修正后二居室均价',
    avg_3_room_amend     decimal        null comment '修正后三居室均价',
    avg_4_room_amend     decimal        null comment '修正后四居室均价',
    city_min_price_orig  decimal(10, 2) null comment '城市最低价',
    city_max_price_orig  decimal(10, 2) null comment '城市最高价',
    city_avg_price       decimal        null comment '城市均价',
    city_min_price_amend decimal        null comment '修正后城市最低价',
    city_max_price_amend decimal        null comment '修正后城市最高价',
    city_coeff_unit      decimal        null comment '城市价格系数单位',
    coeff_value          decimal(10, 2) null comment '价格系数值',
    avg_community_coeff  decimal        null comment '小区均价*系数',
    avg_1_room_coeff     decimal        null comment '一居室均价*系数',
    avg_2_room_coeff     decimal        null comment '两居室均价*系数',
    avg_3_room_coeff     decimal        null comment '三居室均价*系数',
    avg_4_room_coeff     decimal        null comment '四居室均价*系数'
)
    comment '小区均价统计表';

