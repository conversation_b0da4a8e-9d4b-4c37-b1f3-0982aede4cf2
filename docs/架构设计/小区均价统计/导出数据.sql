SELECT d.month                                                                                                                                                            as '统计月份',
       d.community_id                                                                                                                                                     as '小区ID',
       c.city_name                                                                                                                                                        as '城市',
       c.district_name                                                                                                                                                    as '行政区',
       c.town_name                                                                                                                                                        as '镇/街道',
       c.address                                                                                                                                                          as '小区地址',
       c.name                                                                                                                                                             as '小区名称',
       d.count_0_months                                                                                                                                                   as '本月房源数量',
       d.avg_0_months                                                                                                                                                     as '本月均价',
       d.count_6_months                                                                                                                                                   as '近6个月房源数量',
       d.avg_6_months                                                                                                                                                     as '近6个月均价',
       d.count_12_months                                                                                                                                                  as '近12个月房源数量',
       d.avg_12_months                                                                                                                                                    as '近12个月均价',
       d.avg_community_amend                                                                                                                                              as '修正后小区均价',
       d.count_1_room                                                                                                                                                     as '本月一居室房源数量',
       d.avg_1_room_orig                                                                                                                                                  as '本月一居室均价',
       d.count_2_room                                                                                                                                                     as '本月二居室房源数量',
       d.avg_2_room_orig                                                                                                                                                  as '本月二居室均价',
       d.count_3_room                                                                                                                                                     as '本月三居室房源数量',
       d.avg_3_room_orig                                                                                                                                                  as '本月三居室均价',
       d.count_4_room                                                                                                                                                     as '本月四居室房源数量',
       d.avg_4_room_orig                                                                                                                                                  as '本月四居室均价',
       d.avg_1_room_amend                                                                                                                                                 as '修正后一居室均价',
       d.avg_2_room_amend                                                                                                                                                 as '修正后二居室均价',
       d.avg_3_room_amend                                                                                                                                                 as '修正后三居室均价',
       d.avg_4_room_amend                                                                                                                                                 as '修正后四居室均价',
       d.city_min_price_orig                                                                                                                                              as '城市最低价',
       d.city_max_price_orig                                                                                                                                              as '城市最高价',
       d.city_avg_price                                                                                                                                                   as '城市均价',
       d.city_min_price_amend                                                                                                                                             as '修正后城市最低价',
       d.city_max_price_amend                                                                                                                                             as '修正后城市最高价',
       d.city_coeff_unit                                                                                                                                                  as '城市价格系数单位',
       d.coeff_value                                                                                                                                                      as '价格系数值',
       d.avg_community_coeff                                                                                                                                              as '小区均价诊断一',
       d.avg_1_room_coeff                                                                                                                                                 as '一居室均价诊断一',
       d.avg_2_room_coeff                                                                                                                                                 as '两居室均价诊断一',
       d.avg_3_room_coeff                                                                                                                                                 as '三居室均价诊断一',
       d.avg_4_room_coeff                                                                                                                                                 as '四居室均价诊断一',
       dbb.id is not null                                                                                                                                                 as '上上月数据是否存在',
       db.id is not null                                                                                                                                                  as '上月数据是否存在',
       dn.id is not null                                                                                                                                                  as '下月数据是否存在',
       if(db.id is not null && dbb.id is not null and dn.id is not null, round((db.avg_community_coeff + dbb.avg_community_coeff + dn.avg_community_coeff) / 3, 0), null) as '小区均价诊断二',
       if(db.id is not null && dbb.id is not null and dn.id is not null, round((db.avg_1_room_coeff + dbb.avg_1_room_coeff + dn.avg_1_room_coeff) / 3, 0), null)          as '一居室均价诊断二',
       if(db.id is not null && dbb.id is not null and dn.id is not null, round((db.avg_2_room_coeff + dbb.avg_2_room_coeff + dn.avg_2_room_coeff) / 3, 0), null)          as '两居室均价诊断二',
       if(db.id is not null && dbb.id is not null and dn.id is not null, round((db.avg_3_room_coeff + dbb.avg_3_room_coeff + dn.avg_3_room_coeff) / 3, 0), null)          as '三居室均价诊断二',
       if(db.id is not null && dbb.id is not null and dn.id is not null, round((db.avg_4_room_coeff + dbb.avg_4_room_coeff + dn.avg_4_room_coeff) / 3, 0), null)          as '四居室均价诊断二'
FROM st_delegation_price_unit d
         left join tb_community c on d.community_id = c.id
         left join st_delegation_price_unit db on db.id = d.fk_before
         left join st_delegation_price_unit dbb on dbb.id = d.fk_before_before
         left join st_delegation_price_unit dn on dn.id = d.fk_next
where d.`month` in ('2024-10', '2024-11', '2024-12')
# where d.`month` in ('2025-01', '2025-02', '2025-03')
ORDER BY d.`month`,
         c.city_name,
         c.district_name,
         c.town_name,
         c.address,
         c.name;