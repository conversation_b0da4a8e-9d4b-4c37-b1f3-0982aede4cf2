# set @month = '2024-10';
# set @month = '2024-11';
# set @month = '2024-12';
# set @month = '2025-01';
# set @month = '2025-02';
set @month = '2025-03';
set @month_last_time := CONCAT(LAST_DAY(concat(@month, '-01')), ' 23:59:59');

-- 删除历史数据
delete
from st_delegation_price_unit
where month = @month;

-- 小区均价统计
insert into st_delegation_price_unit(id,
                                     fk_before,
                                     fk_before_before,
                                     fk_next,
                                     month,
                                     community_id,
                                     count_0_months,
                                     avg_0_months,
                                     count_6_months,
                                     avg_6_months,
                                     count_12_months,
                                     avg_12_months,
                                     count_1_room,
                                     avg_1_room_orig,
                                     count_2_room,
                                     avg_2_room_orig,
                                     count_3_room,
                                     avg_3_room_orig,
                                     count_4_room,
                                     avg_4_room_orig,
                                     city_avg_price,
                                     city_min_price_orig,
                                     city_max_price_orig,
                                     city_min_price_amend,
                                     city_max_price_amend,
                                     city_coeff_unit,
                                     avg_community_amend,
                                     coeff_value,
                                     avg_1_room_amend,
                                     avg_2_room_amend,
                                     avg_3_room_amend,
                                     avg_4_room_amend,
                                     avg_community_coeff,
                                     avg_1_room_coeff,
                                     avg_2_room_coeff,
                                     avg_3_room_coeff,
                                     avg_4_room_coeff)
select t3.*,
       round(avg_community_amend * coeff_value, 0) as avg_community_coeff,
       round(avg_1_room_amend * coeff_value, 0)    as avg_1_room_coeff,
       round(avg_2_room_amend * coeff_value, 0)    as avg_2_room_coeff,
       round(avg_3_room_amend * coeff_value, 0)    as avg_3_room_coeff,
       round(avg_4_room_amend * coeff_value, 0)    as avg_4_room_coeff
from (select t2.*,
             0.92 - round((avg_community_amend - city_min_price_amend) / city_coeff_unit * 0.01, 2) as coeff_value,
             if(avg_1_room_this_months is not null, avg_1_room_this_months, avg_community_amend)    as avg_1_room_amend,
             if(avg_2_room_this_months is not null, avg_2_room_this_months, avg_community_amend)    as avg_2_room_amend,
             if(avg_3_room_this_months is not null, avg_3_room_this_months, avg_community_amend)    as avg_3_room_amend,
             if(avg_4_room_this_months is not null, avg_4_room_this_months, avg_community_amend)    as avg_4_room_amend
      from (select t1.*,
                   if(avg_0_months is not null, avg_0_months, if(avg_6_months is not null, avg_6_months, if(avg_6_months is not null, avg_6_months, avg_12_months))) avg_community_amend
            from (select concat(@month, '_', d.community_id)                                                                        as id,
                         concat(DATE_FORMAT(DATE_SUB(concat(@month, '-01'), INTERVAL 1 MONTH), '%Y-%m'), '_', d.community_id)       as fk_before,
                         concat(DATE_FORMAT(DATE_SUB(concat(@month, '-01'), INTERVAL 2 MONTH), '%Y-%m'), '_', d.community_id)       as fk_before_before,
                         concat(DATE_FORMAT(DATE_ADD(concat(@month, '-01'), INTERVAL 1 MONTH), '%Y-%m'), '_', d.community_id)       as fk_next,
                         @month                                                                                                     as month,
                         d.community_id,
                         count(case when date_format(d.list_date, '%Y-%m') = @month then price_unit end)                            as count_0_months,
                         round(avg(case when date_format(d.list_date, '%Y-%m') = @month then price_unit end), 0)                    as avg_0_months,
                         count(case when d.list_date > DATE_SUB(@month_last_time, INTERVAL 6 MONTH) then price_unit end)            as count_6_months,
                         round(avg(case when d.list_date > DATE_SUB(@month_last_time, INTERVAL 6 MONTH) then price_unit end), 0)    as avg_6_months,
                         count(price_unit)                                                                                          as count_12_months,
                         round(avg(price_unit), 0)                                                                                  as avg_12_months,
                         count(case when date_format(d.list_date, '%Y-%m') = @month and room_count = 1 then price_unit end)         as count_1_room_this_months,
                         round(avg(case when date_format(d.list_date, '%Y-%m') = @month and room_count = 1 then price_unit end), 0) as avg_1_room_this_months,
                         count(case when date_format(d.list_date, '%Y-%m') = @month and room_count = 2 then price_unit end)         as count_2_room_this_months,
                         round(avg(case when date_format(d.list_date, '%Y-%m') = @month and room_count = 2 then price_unit end), 0) as avg_2_room_this_months,
                         count(case when date_format(d.list_date, '%Y-%m') = @month and room_count = 3 then price_unit end)         as count_3_room_this_months,
                         round(avg(case when date_format(d.list_date, '%Y-%m') = @month and room_count = 3 then price_unit end), 0) as avg_3_room_this_months,
                         count(case when date_format(d.list_date, '%Y-%m') = @month and room_count = 4 then price_unit end)         as count_4_room_this_months,
                         round(avg(case when date_format(d.list_date, '%Y-%m') = @month and room_count = 4 then price_unit end), 0) as avg_4_room_this_months,
                         city_avg_price_orig,
                         city_min_price_orig,
                         city_max_price_orig,
                         city_min_price_amend,
                         city_max_price_amend,
                         round((city_max_price_amend - city_min_price_amend) / 4, 0)                                                as city_coeff_unit
                  from tb_delegation d
                           left join (select d.city_name,
                                             min(price_unit)           as                                                              city_min_price_orig,
                                             max(price_unit)           as                                                              city_max_price_orig,
                                             round(avg(price_unit), 0) as                                                              city_avg_price_orig,
                                             round(if(min(price_unit) < avg(price_unit) / 2, avg(price_unit) / 2, min(price_unit)), 0) city_min_price_amend,
                                             round(if(max(price_unit) > avg(price_unit) * 5, avg(price_unit) * 5, max(price_unit)), 0) city_max_price_amend
                                      from tb_delegation d
                                      where d.channel_code = 'XIANYU'
                                        and d.community_id is not null
                                      group by d.city_name) city_stat
                                     on city_stat.city_name = d.city_name
                  where d.channel_code = 'XIANYU'
                    and d.community_id is not null
                    and d.price_unit between city_min_price_amend
                      and city_max_price_amend
                    and d.list_date between DATE_SUB(@month_last_time
                      , INTERVAL 12 MONTH)
                      and @month_last_time
                  group by d.community_id) t1) t2) t3
;

select *
from (SELECT dpu.`month`,
             count(1)
      FROM st_delegation_price_unit dpu
      GROUP BY dpu.`month`) t1
order by month
;
