SELECT community_id                                    as id,
       community_id                                    as src_community_id,
       concat(province_name, city_name, district_name) as district_full_name,
       community_addr                                  as address,
       community_name                                  as community_name,
       property_type                                   as property_type,
       property_years                                  as property_years,
       comm_belong                                     as comm_belong,
       build_max_year                                  as build_max_year,
       build_min_year                                  as build_min_year,
       building_num                                    as build_num,
       house_num                                       as house_num,
       IF(
               LOCATE('5piT5bGF==', developer_corp) > 0,
               AES_DECRYPT(UNHEX(SUBSTRING_INDEX(REPLACE(developer_corp, '5piT5bGF==', ''), '==', - 1)), 'ZWp1ZGF0YS1lcnNm'),
               developer_corp
       )                                               as developer_corp,
       brand_corp                                      as brand_corp,
       act_area                                        as act_area,
       build_area                                      as build_area,
       building_type                                   as building_type,
       house_type                                      as house_type,
       building_category                               as building_category,
       IF(
               LOCATE('5piT5bGF==', property_name) > 0,
               AES_DECRYPT(UNHEX(SUBSTRING_INDEX(REPLACE(property_name, '5piT5bGF==', ''), '==', - 1)), 'ZWp1ZGF0YS1lcnNm'),
               property_name
       )                                               as property_name,
       IF(
               LOCATE('5piT5bGF==', property_fee) > 0,
               AES_DECRYPT(UNHEX(SUBSTRING_INDEX(REPLACE(property_fee, '5piT5bGF==', ''), '==', - 1)), 'ZWp1ZGF0YS1lcnNm'),
               property_fee
       )                                               as property_fee,
       pro_phone                                       as property_phone,
       community_close_ind                             as community_close_flag,
       parking_rate                                    as parking_rate,
       up_parking_num                                  as up_parking_num,
       down_parking_num                                as down_parking_num,
       parking_num                                     as parking_num,
       person_div_car_ind                              as person_div_car_flag,
       parking_sale_ind                                as parking_sale_flag,
       set_parking_fee                                 as set_parking_fee,
       temp_parking_fee                                as temp_parking_fee,
       volume_rate                                     as volume_rate,
       green_rate                                      as green_rate,
       powerd_desc                                     as powerd_desc,
       water_desc                                      as water_desc,
       gas_desc                                        as gas_desc,
       heating_desc                                    as heating_desc,
       community_pic                                   as icon_url,
       district_cd,
       district_name,
       city_cd,
       city_name,
       province_cd,
       province_name,
       community_no,
       home_name,
       zip_cd,
       block_cd,
       block_name,
       pro_service_addr,
       pro_on_time                                     as property_on_time,
       intel_gate_ind                                  as intel_gate_flag,
       gate_control_ind                                as gate_control_flag,
       monitor_ind                                     as monitor_flag,
       security_booth_num,
       security_person_num,
       security_allday_ind                             as security_allday_flag,
       security_patrol_frequency,
       police_networking_ind                           as police_networking_flag,
       home_name_ind                                   as home_name_flag
FROM house_asset.community t
WHERE volume_rate is not null
  and parking_rate is not null
limit 500;

SELECT house_pic_id                      as id,
       house_pic_id                      as src_layout_id,
       if(length(area) = 0, null, IF(
               LOCATE('5piT5bGF==', area) > 0,
               AES_DECRYPT(UNHEX(SUBSTRING_INDEX(REPLACE(area, '5piT5bGF==', ''), '==', - 1)), 'ZWp1ZGF0YS1lcnNm'),
               area
                                  ))     as area,
       if(length(act_area) = 0, null, IF(
               LOCATE('5piT5bGF==', act_area) > 0,
               AES_DECRYPT(UNHEX(SUBSTRING_INDEX(REPLACE(act_area, '5piT5bGF==', ''), '==', - 1)), 'ZWp1ZGF0YS1lcnNm'),
               act_area
                                      )) as area_act,
       if(length(max_area) = 0, null, IF(
               LOCATE('5piT5bGF==', max_area) > 0,
               AES_DECRYPT(UNHEX(SUBSTRING_INDEX(REPLACE(max_area, '5piT5bGF==', ''), '==', - 1)), 'ZWp1ZGF0YS1lcnNm'),
               max_area
                                      )) as area_max,
       if(length(min_area) = 0, null, IF(
               LOCATE('5piT5bGF==', min_area) > 0,
               AES_DECRYPT(UNHEX(SUBSTRING_INDEX(REPLACE(min_area, '5piT5bGF==', ''), '==', - 1)), 'ZWp1ZGF0YS1lcnNm'),
               min_area
                                      )) as area_min,
       balcony_num                       as balcony_count,
       hall_num                          as hall_count,
       kitchen_num                       as kitchen_count,
       main_layout_ind                   as main_layout_flag,
       house_layout_name                 as name,
       room_orientation                  as orientation,
       layout_pic_path_fang_v2           as pic_url,
       room_num                          as room_count,
       community_id                      as src_community_id,
       community_id                      as community_detail_id,
       bath_num                          as toilet_count,
       set_num
FROM house_asset.house_layout_std t
where house_layout_name is not null
  and bath_num is not null
limit 500;


select id                                     as src_id,
       'community_picture.community_pic_path' as src_type,
       community_id                           as community_detail_id,
       community_pic_path                     as url
from community_picture cp;


select id                                     as src_id,
       'community_picture_gxd.community_pic_path' as src_type,
       community_id                           as community_detail_id,
       community_pic_path                     as url
from community_picture_gxd cp;
select sum(c)
from (select count(1) c
      from community_picture
      union
      select count(1) c
      from community_picture_gxd) tc
;

select id                                            as src_id,
       'community_picture_survey.community_pic_path' as src_type,
       community_id                                  as community_detail_id,
       community_pic_path                            as url
from community_picture_survey cp;

# select id                                               as src_id,
#        'community_picture_survey.community_pic_ess_url' as src_type,
#        community_id                                     as community_detail_id,
#        community_pic_ess_url                            as url
# from community_picture_survey cp;
#
# select id                                                as src_id,
#        'community_picture_survey.community_pic_proc_url' as src_type,
#        community_id                                      as community_detail_id,
#        community_pic_proc_url                            as url
# from community_picture_survey cp;
