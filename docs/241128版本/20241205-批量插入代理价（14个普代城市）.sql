insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CHANNEL_OPEN', '南宁', '450100', 400, 200, 1, 200, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CHANNEL_OPEN', '武汉', '420100', 500, 250, 1, 250, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CHANNEL_OPEN', '温州', '330300', 400, 200, 1, 200, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CHANNEL_OPEN', '宁波', '330200', 400, 200, 1, 200, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CHANNEL_OPEN', '徐州', '320300', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CHANNEL_OPEN', '太原', '140100', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CHANNEL_OPEN', '石家庄', '130100', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CHANNEL_OPEN', '济南', '370100', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CHANNEL_OPEN', '中山', '442000', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CHANNEL_OPEN', '保定', '130600', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CHANNEL_OPEN', '柳州', '450200', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CHANNEL_OPEN', '潍坊', '370700', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CHANNEL_OPEN', '肇庆', '441200', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CHANNEL_OPEN', '承德', '130800', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CLUE_CONSUME', '南宁', '450100', 6, 4, 1, 4, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CLUE_CONSUME', '武汉', '420100', 6, 4, 1, 4, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CLUE_CONSUME', '温州', '330300', 6, 4, 1, 4, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CLUE_CONSUME', '宁波', '330200', 6, 4, 1, 4, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CLUE_CONSUME', '徐州', '320300', 4, 3, 1, 3, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CLUE_CONSUME', '太原', '140100', 4, 3, 1, 3, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CLUE_CONSUME', '石家庄', '130100', 4, 3, 1, 3, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CLUE_CONSUME', '济南', '370100', 4, 3, 1, 3, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CLUE_CONSUME', '中山', '442000', 3, 2, 1, 2, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CLUE_CONSUME', '保定', '130600', 3, 2, 1, 2, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CLUE_CONSUME', '柳州', '450200', 3, 2, 1, 2, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CLUE_CONSUME', '潍坊', '370700', 3, 2, 1, 2, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CLUE_CONSUME', '肇庆', '441200', 3, 2, 1, 2, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'SALE', 'CLUE_CONSUME', '承德', '130800', 3, 2, 1, 2, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CHANNEL_OPEN', '南宁', '450100', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CHANNEL_OPEN', '武汉', '420100', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CHANNEL_OPEN', '温州', '330300', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CHANNEL_OPEN', '宁波', '330200', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CHANNEL_OPEN', '徐州', '320300', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CHANNEL_OPEN', '太原', '140100', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CHANNEL_OPEN', '石家庄', '130100', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CHANNEL_OPEN', '济南', '370100', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CHANNEL_OPEN', '中山', '442000', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CHANNEL_OPEN', '保定', '130600', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CHANNEL_OPEN', '柳州', '450200', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CHANNEL_OPEN', '潍坊', '370700', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CHANNEL_OPEN', '肇庆', '441200', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CHANNEL_OPEN', '承德', '130800', 300, 150, 1, 150, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CLUE_CONSUME', '南宁', '450100', 4.5, 3.5, 1, 3.5, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CLUE_CONSUME', '武汉', '420100', 4.5, 3.5, 1, 3.5, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CLUE_CONSUME', '温州', '330300', 4.5, 3.5, 1, 3.5, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CLUE_CONSUME', '宁波', '330200', 4.5, 3.5, 1, 3.5, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CLUE_CONSUME', '徐州', '320300', 4.5, 3.5, 1, 3.5, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CLUE_CONSUME', '太原', '140100', 4.5, 3.5, 1, 3.5, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CLUE_CONSUME', '石家庄', '130100', 4.5, 3.5, 1, 3.5, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CLUE_CONSUME', '济南', '370100', 4.5, 3.5, 1, 3.5, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CLUE_CONSUME', '中山', '442000', 4.5, 3.5, 1, 3.5, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CLUE_CONSUME', '保定', '130600', 4.5, 3.5, 1, 3.5, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CLUE_CONSUME', '柳州', '450200', 4.5, 3.5, 1, 3.5, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CLUE_CONSUME', '潍坊', '370700', 4.5, 3.5, 1, 3.5, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CLUE_CONSUME', '肇庆', '441200', 4.5, 3.5, 1, 3.5, 1, 1);
insert into tb_price (id, version,channel_code, business_code, deal_type, city_name, city_code, standard_price, agent_price, agent_discount, agent_discount_price, normal_comm_rate, excess_comm_ratio) VALUES(NEXTVAL(seq_price_id), 20241205,'XIANYU', 'RENT', 'CLUE_CONSUME', '承德', '130800', 4.5, 3.5, 1, 3.5, 1, 1);




insert into tb_template_price (template_id, price_id)
select 1,p.id from tb_price p
WHERE p.version=20241205
;

insert into tb_template_price (template_id, price_id)
select 4,p.id from tb_price p
WHERE p.version=20241205
;

insert tb_contract_price(contract_id,price_id)
select c.id,p.id
from tb_price p
left join tb_contract c on c.sub_type='AGENT_NORMAL'
where p.version=20241205
;