#ejuetc网关配置排除[/consumer/api/*]访问权限
UPDATE tb_gateway_api t SET t.url = 'http://ejuetc-consumer.tst.ejucloud.cn/consumer/gateway/delegation/deleteBrokerDelegates' WHERE t.id = 11;
UPDATE tb_gateway_api t SET t.url = 'http://ejuetc-consumer.tst.ejucloud.cn/consumer/gateway/delegation/edit' WHERE t.id = 10;


insert into tb_delegation_dict(category, name, sort) values('LABELS_SALE','学区房',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_SALE','黄金地段',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_SALE','商圈配套成熟',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_SALE','景观房',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_SALE','采光充足',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_SALE','拎包入住',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_SALE','稀缺房源',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_SALE','看房有礼',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_SALE','精装修',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_SALE','地暖/中央空调',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_SALE','送家具家电',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_SALE','复式带阁楼',0);

insert into tb_delegation_dict(category, name, sort) values('LABELS_RENT','黄金地段',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_RENT','商圈配套成熟',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_RENT','近医院/学校',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_RENT','采光充足',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_RENT','随时看房',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_RENT','支持转租',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_RENT','新房出租',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_RENT','灵活付款',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_RENT','业主直租',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_RENT','长租折扣',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_RENT','免物业费',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_RENT','看房有礼',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_RENT','智能门锁',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_RENT','小区健身房',0);
insert into tb_delegation_dict(category, name, sort) values('LABELS_RENT','智能家电',0);


create index idx_community_detail_communityId
    on tb_community_detail (community_id);
create index idx_community_layout_communityId
    on tb_community_layout (community_id);
create index idx_community_picture_communityId
    on tb_community_picture (community_id);

update tb_community_layout
set logic_delete = 1, version = 250612000, update_time = current_timestamp
where hall_count is null or room_count is null;


create index idx_community_detailId
    on tb_community (detail_id);

create index idx_community_layout_communityDetailId
    on tb_community_layout (community_detail_id);


update tb_community_layout l
set l.community_id =  null
where community_id is not null
;

update tb_region
set pinyin_first = 'c'
where name like '长%';


update tb_community_layout l
    join tb_community c on c.detail_id=l.community_detail_id
set l.community_id =  c.id
where c.city_id=310100 and c.detail_id is not null and l.community_id is null
limit 25000
;

alter table tb_community_layout
    drop column pic_url;

alter table tb_community_layout
    change pic_url2 pic_url varchar(255) null comment '户型图';
update tb_region
set feature_codes='HOUSE_VALUE_ASSESSMENT'
where type='CITY' and short_name in (
                                     '上海',
                                     '成都',
                                     '北京',
                                     '重庆',
                                     '广州',
                                     '武汉',
                                     '深圳',
                                     '天津',
                                     '杭州',
                                     '郑州',
                                     '苏州',
                                     '西安',
                                     '南京',
                                     '佛山',
                                     '青岛',
                                     '济南',
                                     '昆明',
                                     '长沙',
                                     '宁波',
                                     '南昌'
    );

UPDATE tb_community c
SET beike_ids = (
    SELECT GROUP_CONCAT(bk.project_id ORDER BY bk.project_id)
    FROM tb_community_beike_sh bk
    WHERE bk.community_id=c.id
);

alter table tb_community_beike
    add match_type varchar(127) null comment '匹配类型';

ALTER TABLE tb_community_bind ADD COLUMN city_name varchar(127) NULL COMMENT '城市';
ALTER TABLE tb_community_bind ADD COLUMN district_name varchar(127) NULL COMMENT '行政区名称';

create index tb_community_city_name_index
    on tb_community (city_name);

create index tb_community_bind_city_name_index
    on tb_community_bind (city_name);
create index tb_community_beike_city_id_name_index
    on tb_community_beike (city_id, name);



update tb_community_beike bk
    join tb_region r on r.type='CITY' and r.short_name=bk.city
set bk.city_id = r.id
where bk.city_id is null
;

update tb_community_beike bk
    join tb_community c on bk.community_id = c.id
set bk.detail_id  = c.detail_id
where bk.community_id is not null and bk.city_id=310100;

alter table tb_community_beike
    change city city_name varchar(127) null comment '城市名(简称)';

drop index tb_community_beike_city_index on tb_community_beike;

create index tb_community_beike_city_name_index
    on tb_community_beike (city_name);

alter table tb_community_beike
    change district district_name varchar(127) null comment '行政区名';

update tb_delegation_dict
set sort = 3000-sort
where category='COMPLETION_TIME';