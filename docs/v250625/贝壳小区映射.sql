update tb_community_bind cb
    join tb_community c on cb.community_id = c.id
set cb.city_name = c.city_name,cb.district_name = c.district_name
where c.city_name='南昌市'
;

-- update tb_community_beike
-- set community_id = null,match_type=null
-- where city='南昌' and (community_id is not null or match_type is not null);
--
-- select sum(if(bk.community_id is not null,1,0)),sum(if(bk.community_id is null,1,0)),count(1)
-- from tb_community_beike bk
-- where bk.city='南昌';
--

update tb_community_beike bk
    join tb_community c on (bk.district is null or c.district_name like concat(bk.district,'%')) and c.name=bk.name
set bk.community_id  = c.id,bk.match_type='1_name_equal_communityName'
where c.city_name='南昌市' and bk.city='南昌'
;

update tb_community_beike bk
    join tb_community c on (bk.district is null or c.district_name like concat(bk.district,'%')) and c.address=bk.name
set bk.community_id  = c.id,bk.match_type='2_name_equal_communityAddress'
where c.city_name='南昌市' and bk.city='南昌'
  and bk.community_id is null
;


update tb_community_beike bk
    join tb_community_bind cb on (bk.district is null or cb.district_name like concat(bk.district,'%')) and cb.name=bk.name
set bk.community_bind_id=cb.id,bk.community_id = cb.community_id,bk.match_type='3_name_equal_bindName'
where cb.city_name='南昌市' and bk.city='南昌'
  and bk.community_id is null
;

update tb_community_beike bk
    join tb_community_bind cb on (bk.district is null or cb.district_name like concat(bk.district,'%')) and cb.address=bk.name
set bk.community_bind_id=cb.id,bk.community_id = cb.community_id,bk.match_type='4_name_equal_bindAddress'
where cb.city_name='南昌市' and bk.city='南昌'
  and bk.community_id is null
;

update tb_community_beike bk
    join tb_community c on c.name like concat('%',bk.name,'%') or c.address like concat('%',bk.name,'%')
set bk.community_id  = c.id,bk.match_type='5_name_like_communityNameOrAddress'
where c.city_name='南昌市' and bk.city='南昌'
  and bk.community_id is null
;


update tb_community_beike bk
    join tb_community_bind cb on cb.name like concat('%',bk.name,'%') or cb.address like concat('%',bk.name,'%')
set bk.community_bind_id=cb.id,bk.community_id = cb.community_id,bk.match_type='6_name_like_bindNameOrAddress'
where cb.city_name='南昌市' and bk.city='南昌'
  and bk.community_id is null
;

select match_type,count(1) from tb_community_beike
where city='南昌'
group by match_type
order by match_type
;

update tb_community_beike bk
    join tb_community_detail dt on dt.community_name=bk.`name` and dt.district_name=bk.district and bk.city_name=dt.city_name
set bk.community_detail_id=dt.id,bk.community_id=dt.community_id,bk.match_type='equal_detail_name&district'
;