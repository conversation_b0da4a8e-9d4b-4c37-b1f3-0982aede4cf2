create view agent_report_fee as
SELECT
    r.id,-- 报表ID
    CASE
        c.sub_type
        WHEN 'PARTNER' THEN
            '合伙'
        WHEN 'AGENT_NORMAL' THEN
            '普通代理'
        WHEN 'AGENT_XIANYU_EXCLUSIVE' THEN
            '闲鱼独家代理'
        WHEN 'AGENT_LEJU_EXCLUSIVE' THEN
            '乐居独家代理'
        WHEN 'BROKER' THEN
            '经纪' ELSE '未知'
        END AS agent_sub_type_name,-- 代理类型
    REPLACE(c.city_name, '市', '') AS city_name,--  代理城市名称
    CASE
        c.user_type
        WHEN 'PERSONAL' THEN
            '个人'
        WHEN 'COMPANY' THEN
            '公司' ELSE '未知'
        END AS agent_user_type_name,-- 代理商类型
    c.user_id agent_user_id,--  代理商公司/个人编号
    CASE
        c.user_type
        WHEN 'PERSONAL' THEN
            u.personal_name
        WHEN 'COMPANY' THEN
            u.company_name ELSE '未知'
        END AS agent_user_name,-- 代理商公司/个人名称
    r.product_price,-- 产品价格
    r.check_subject_name,-- 核算主体名称
    r.check_subject_code,-- 核算主体编码
    r.tm_service_fee,-- 天猫服务费转
    r.tm_bail,-- 天猫保证金
    r.tm_new_trade,-- 新交
    c.contract_no,-- 进驻合同编号
    r.termination_contract_no,-- 终止合同编号
    r.refund_expense_order_no,-- 退费支出单号
    c.contract_begin_time,-- 合作开始日期
    c.contract_end_time,-- 合作到期日期
    r.oa_approved_time,-- 进驻协议 OA 审核通过日期
    r.termination_contract_effective_time,-- 终止合同生效日期
    r.receivable_amount,-- 应收金额
    r.received_amount,-- 到账金额
    r.received_time,-- 到账日期
    r.refund_no,-- 退款单号
    r.current_income,-- 本次收入
    r.current_fair_income,-- 本次公允收入
    r.stat_year_month,-- 统计年月
    r.write_off_time,-- 成销日期
    r.fair_pre_accrued_income_pre_tax,-- 公允前应计收入税前
    r.fair_pre_accrued_income_after_tax,-- 公允前应计收入税后
    r.fair_pre_accrued_rebate_pre_tax,-- 公允前应计返佣税前
    r.fair_pre_accrued_rebate_after_tax,-- 公允前应计返佣税后
    r.fair_pre_accrued_net_income_pre_tax,-- 公允前应计净收税前
    r.fair_pre_accrued_net_income_after_tax,-- 公允前应计净收税后
    r.fair_pre_actual_income_pre_tax,-- 公允前实际收入税前
    r.fair_pre_actual_income_after_tax,-- 公允前实际收入税后
    r.fair_pre_actual_rebate_pre_tax,-- 公允前实际返佣税前
    r.fair_pre_actual_rebate_after_tax,-- 公允前实际返佣税后
    r.fair_pre_actual_net_income_pre_tax,-- 公允前实际净收税前
    r.fair_pre_actual_net_income_after_tax,-- 公允前实际净收税后
    r.fair_after_accrued_income_pre_tax,-- 公允后应计收入税前
    r.fair_after_accrued_income_after_tax,-- 公允后应计收入税后
    r.fair_after_accrued_rebate_pre_tax,-- 公允后应计返佣税前
    r.fair_after_accrued_rebate_after_tax,-- 公允后应计返佣税后
    r.fair_after_accrued_net_income_pre_tax,-- 公允后应计净收税前
    r.fair_after_accrued_net_income_after_tax,-- 公允后应计净收税后
    r.fair_after_actual_income_pre_tax,-- 公允后实际收入税前
    r.fair_after_actual_income_after_tax,-- 公允后实际收入税后
    r.fair_after_actual_rebate_pre_tax,-- 公允后实际返佣税前
    r.fair_after_actual_rebate_after_tax,-- 公允后实际返佣税后
    r.fair_after_actual_net_income_pre_tax,-- 公允后实际净收税前
    r.fair_after_actual_net_income_after_tax,-- 公允后实际净收税后
    r.achievement_user_name,-- 业绩人姓名
    r.achievement_user_no -- 业绩人工号

FROM
    tb_report_fee r
        LEFT JOIN tb_contract c ON r.contract_id = c.id
        LEFT JOIN tb_user u ON c.user_id = u.id
WHERE
    r.logic_delete = 0;