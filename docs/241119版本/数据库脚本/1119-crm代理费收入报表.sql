CREATE TABLE `tb_report_fee` (
                                 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                 `create_time` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
                                 `logic_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否逻辑删除:1是0否',
                                 `update_time` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '更新时间',
                                 `version` bigint(20) NOT NULL DEFAULT '0' COMMENT '版本号',
                                 `contract_id` bigint(20) DEFAULT NULL COMMENT '合同ID',
                                 `product_price` decimal(20,2) DEFAULT NULL COMMENT '产品价格',
                                 `check_subject_name` varchar(64) DEFAULT NULL COMMENT '核算主体名称',
                                 `check_subject_code` varchar(64) DEFAULT NULL COMMENT '核算主体编码',
                                 `tm_service_fee` decimal(20,2) DEFAULT NULL COMMENT '天猫服务费转',
                                 `tm_bail` decimal(20,2) DEFAULT NULL COMMENT '天猫保证金',
                                 `tm_new_trade` decimal(20,2) DEFAULT NULL COMMENT '新交',
                                 `termination_contract_no` varchar(64) DEFAULT NULL COMMENT '终止合同编号',
                                 `refund_expense_order_no` varchar(64) DEFAULT NULL COMMENT '退费支出单号',
                                 `oa_approved_time` datetime DEFAULT NULL COMMENT '进驻协议OA审核通过日期',
                                 `termination_contract_effective_time` datetime DEFAULT NULL COMMENT '终止合同生效日期',
                                 `receivable_amount` decimal(20,2) DEFAULT NULL COMMENT '应收金额',
                                 `received_amount` decimal(20,2) DEFAULT NULL COMMENT '到账金额',
                                 `received_time` datetime DEFAULT NULL COMMENT '到账日期',
                                 `refund_no` varchar(255) DEFAULT NULL COMMENT '退款单号',
                                 `current_income` decimal(20,2) DEFAULT NULL COMMENT '本次收入',
                                 `current_fair_income` decimal(20,2) DEFAULT NULL COMMENT '本次公允收入',
                                 `stat_year_month` varchar(64) DEFAULT NULL COMMENT '统计年月',
                                 `write_off_time` datetime DEFAULT NULL COMMENT '成销日期',
                                 `fair_pre_accrued_income_pre_tax` decimal(20,2) DEFAULT NULL COMMENT '公允前应计收入税前',
                                 `fair_pre_accrued_income_after_tax` decimal(20,2) DEFAULT NULL COMMENT '公允前应计收入税后',
                                 `fair_pre_accrued_rebate_pre_tax` decimal(20,2) DEFAULT NULL COMMENT '公允前应计返佣税前',
                                 `fair_pre_accrued_rebate_after_tax` decimal(20,2) DEFAULT NULL COMMENT '公允前应计返佣税后',
                                 `fair_pre_accrued_net_income_pre_tax` decimal(20,2) DEFAULT NULL COMMENT '公允前应计净收税前',
                                 `fair_pre_accrued_net_income_after_tax` decimal(20,2) DEFAULT NULL COMMENT '公允前应计净收税后',
                                 `fair_pre_actual_income_pre_tax` decimal(20,2) DEFAULT NULL COMMENT '公允前实际收入税前',
                                 `fair_pre_actual_income_after_tax` decimal(20,2) DEFAULT NULL COMMENT '公允前实际收入税后',
                                 `fair_pre_actual_rebate_pre_tax` decimal(20,2) DEFAULT NULL COMMENT '公允前实际返佣税前',
                                 `fair_pre_actual_rebate_after_tax` decimal(20,2) DEFAULT NULL COMMENT '公允前实际返佣税后',
                                 `fair_pre_actual_net_income_pre_tax` decimal(20,2) DEFAULT NULL COMMENT '公允前实际净收税前',
                                 `fair_pre_actual_net_income_after_tax` decimal(20,2) DEFAULT NULL COMMENT '公允前实际净收税后',
                                 `fair_after_accrued_income_pre_tax` decimal(20,2) DEFAULT NULL COMMENT '公允后应计收入税前',
                                 `fair_after_accrued_income_after_tax` decimal(20,2) DEFAULT NULL COMMENT '公允后应计收入税后',
                                 `fair_after_accrued_rebate_pre_tax` decimal(20,2) DEFAULT NULL COMMENT '公允后应计返佣税前',
                                 `fair_after_accrued_rebate_after_tax` decimal(20,2) DEFAULT NULL COMMENT '公允后应计返佣税后',
                                 `fair_after_accrued_net_income_pre_tax` decimal(20,2) DEFAULT NULL COMMENT '公允后应计净收税前',
                                 `fair_after_accrued_net_income_after_tax` decimal(20,2) DEFAULT NULL COMMENT '公允后应计净收税后',
                                 `fair_after_actual_income_pre_tax` decimal(20,2) DEFAULT NULL COMMENT '公允后实际收入税前',
                                 `fair_after_actual_income_after_tax` decimal(20,2) DEFAULT NULL COMMENT '公允后实际收入税后',
                                 `fair_after_actual_rebate_pre_tax` decimal(20,2) DEFAULT NULL COMMENT '公允后实际返佣税前',
                                 `fair_after_actual_rebate_after_tax` decimal(20,2) DEFAULT NULL COMMENT '公允后实际返佣税后',
                                 `fair_after_actual_net_income_pre_tax` decimal(20,2) DEFAULT NULL COMMENT '公允后实际净收税前',
                                 `fair_after_actual_net_income_after_tax` decimal(20,2) DEFAULT NULL COMMENT '公允后实际净收税后',
                                 `achievement_user_name` varchar(64) DEFAULT NULL COMMENT '业绩人姓名',
                                 `achievement_user_no` varchar(64) DEFAULT NULL COMMENT '业绩人工号',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='ETC代理费收入报表';