package com.ejuetc.agent.domain.crm;

import com.alibaba.fastjson.JSON;
import com.ejuetc.agent.domain.account.BankAccount;
import com.ejuetc.agent.domain.contract.AgentContract;
import com.ejuetc.agent.domain.contract.Contract;
import com.ejuetc.agent.domain.contract.ContractRpt;
import com.ejuetc.agent.domain.contract.PartnerContract;
import com.ejuetc.agent.dto.ContractDTO;
import com.ejuetc.agent.dto.CrmInstructDTO;
import com.ejuetc.agent.dto.UserDTO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.collection.CollectionUtils;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.utils.DateTimeUtils;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@Getter
@Entity
@Slf4j
@DiscriminatorValue("CONTRACT")
@SubtypeCode(parent = CrmInstruct.class, code = "CONTRACT", name = "合同")
@NoArgsConstructor
public class CrmInstruct4Contract extends CrmInstruct {

    public CrmInstruct4Contract(Contract contract) {
        super(contract);
    }

    @Override
    protected void doExec() {
        Map<String, Object> body = new HashMap<>();
        Contract contract = this.getContract();
        body.put("saasID", contract.getUser().getId().toString());
        body.put("dlrContractNo", contract.getContractNo());
        body.put("mainCity", contract.getCityName());

        PartnerContract partnerContract = null;
        if (contract instanceof PartnerContract pc) {
            partnerContract = pc;
        } else {
            partnerContract = getBean(ContractRpt.class).findByIdAndType(contract.getInvitedContract().getId(), ContractDTO.Type.PARTNER);
        }
        if (partnerContract == null || StringUtils.isBlank(partnerContract.getKhCode()))
            throw new CodingException("根据名称未找到对应的考核主体，代理合同id：" + contract.getId());
        body.put("khCode", partnerContract.getKhCode());
        body.put("khName", partnerContract.getKhName());
        body.put("costCode", partnerContract.getCostCode());
        body.put("costName", partnerContract.getCostName());

        body.put("customerType", contract.getUserType() == UserDTO.Type.COMPANY ? "企业" : "个人");
        body.put("partnerName", contract.getInvitedUser() == null ? "-" : contract.getInvitedUser().getCertName());
        body.put("partnerID", contract.getInvitedUser() == null ? "-" : contract.getInvitedUser().getId().toString());
        if (contract.getUserType() == UserDTO.Type.COMPANY) {
            body.put("companyName", contract.getUser().getCertName());
            body.put("abbName", contract.getUser().getStoreName());
            body.put("businessLicenseNo", contract.getUser().getCompanyNum());
            body.put("legalPerson", contract.getUser().getCompanyLegal());
            body.put("regAddress", contract.getUser().getCompanyAddress());

            body.put("accountName",contract.getUser().getAccounts().get(0).getAccountName());
            BankAccount bankAccount = (BankAccount) contract.getUser().getAccounts().get(0);
            body.put("accountProvinceName", bankAccount.getBankProvince());
            body.put("accountCityName", bankAccount.getBankCity());
            body.put("bankName", bankAccount.getBankAddress());
            body.put("accountNo", bankAccount.getAccountNO());
        } else {
            body.put("brokerName", contract.getUser().getCertName());
            body.put("brokerTel", contract.getUser().getContactMobile());
            body.put("brokerIdCard", contract.getUser().getPersonalNum());
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        body.put("cooBegin", formatter.format(contract.getContractIndate().getBeginTime()));
        body.put("cooEnd", formatter.format(contract.getContractIndate().getEndTime()));
        body.put("contactPerson", contract.getUser().getContactName());
        body.put("contactTel", contract.getUser().getContactMobile());
        body.put("suppFlag", contract.getSuppFlag() ? "1" : "0");
        if (contract.getSuppFlag()) {
            body.put("oaNo", contract.getContractNo());
        }
        if (!CollectionUtils.isEmpty(contract.getCompleteFiles())) {
            body.put("contractFileList", List.of(Map.of("fileName", "agentContract_" + contract.getId(), "fileUrl", contract.getCompleteFiles().get(0))));
        }
        send("/ljuApi/etcFlowDlr/getDlrContract", body);
    }
}
