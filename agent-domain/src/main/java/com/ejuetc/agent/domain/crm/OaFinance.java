package com.ejuetc.agent.domain.crm;

import com.ejuetc.agent.api.contract.CrmResultPo;
import com.ejuetc.agent.api.statement.StatementPartnerAmountRO;
import com.ejuetc.agent.domain.clearing.ClearingRpt;
import com.ejuetc.agent.domain.statement.Statement;
import com.ejuetc.agent.domain.statement.StatementAccount;
import com.ejuetc.agent.dto.ContractDTO;
import com.ejuetc.agent.dto.OaFinanceDTO;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.usertype.MemoUT;
import com.ejuetc.commons.base.valueobj.Memo;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.OptimisticLock;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.alibaba.fastjson.JSON.parseObject;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@Getter
@Entity
@Slf4j
@NoArgsConstructor
@Comment("OA财务")
@Table(name = "tb_oa_finance")
@Where(clause = "logic_delete = 0")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", columnDefinition = "enum('CASH_OUT', 'CHARGE_OFF') COMMENT '订单类型'")
public abstract class OaFinance extends BaseEntity<OaFinance> {

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, insertable = false, updatable = false)
    private OaFinanceDTO.Type type;

    @Id
    @GeneratedValue(generator = "finance_id")
    @SequenceGenerator(name = "finance_id", sequenceName = "seq_finance_id")
    private Long id;

    @OptimisticLock(excluded = true)
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "oaFinance")
    protected List<Statement> statements = new ArrayList<>();

    @OptimisticLock(excluded = true)
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "oaFinance")
    protected List<CrmInstruct> crmInstruct = new ArrayList<>();

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "crm_instruct_id", columnDefinition = "bigint(20) COMMENT 'CRM交互号'")
    protected CrmInstruct lastCrmInstruct;

    @Column(name = "oa_num", columnDefinition = "varchar(255) COMMENT 'OA编号'")
    protected String oaNum;

    @Enumerated(EnumType.STRING)
    @Column(name = "oa_status", nullable = false)
    protected ContractDTO.OAStatus oaStatus = ContractDTO.OAStatus.INIT;

    @Type(MemoUT.class)
    @Column(name = "oa_memo", columnDefinition = "text COMMENT 'oa备注信息'")
    protected Memo oaMemo = new Memo();

    @Column(name = "oa_audit_time", columnDefinition = "datetime COMMENT 'OA审核时间'")
    protected LocalDateTime oaAuditTime;

    @Column(name = "invoice_pre_tax", columnDefinition = "decimal(20,2) default 0 COMMENT '发票确认税前金额'")
    protected BigDecimal invoicePreTax;

    @Column(name = "invoice_after_tax", columnDefinition = "decimal(20,2) default 0 COMMENT '发票确认税后金额'")
    protected BigDecimal invoiceAfterTax;

    @Column(name = "apply_name", columnDefinition = "varchar(50) COMMENT '申请人'")
    protected String applyName;

    @Column(name = "apply_time", columnDefinition = "datetime COMMENT '申请时间'")
    protected LocalDateTime applyTime;

    @Column(name = "remark", columnDefinition = "varchar(255) COMMENT '备注'")
    protected String remark;

    public abstract void sendCrm(String applyName);

    public void contractCrmResult(CrmResultPo po) {
        if (StringUtils.isNotBlank(po.getRejectReason())) {
            this.oaMemo.addMemo(po.getRejectReason());
        }
        this.oaNum = po.getOaNum();
        this.oaStatus = po.getSuccess() ? ContractDTO.OAStatus.SUCCESS : ContractDTO.OAStatus.FAIL;
        this.oaAuditTime = po.getAuditTime();
    }

    public boolean isSucc() {
        return this.oaStatus == ContractDTO.OAStatus.IN_HAND || this.oaStatus == ContractDTO.OAStatus.SUCCESS;
    }

    public List<StatementPartnerAmountRO> getKhInfo() {
        List<Object[]> resultList = getBean(ClearingRpt.class).selPartnerAmount(this.getStatements().stream().map(Statement::getId).collect(Collectors.toList()));
        return resultList.stream().map(t -> {
            return new StatementPartnerAmountRO((String) t[0], (String) t[1], (String) t[2], (String) t[3], (BigDecimal) t[4], (BigDecimal) t[5], (Long) t[6], (BigDecimal) t[7], (BigDecimal) t[8]);
        }).collect(Collectors.toList());
    }
}
