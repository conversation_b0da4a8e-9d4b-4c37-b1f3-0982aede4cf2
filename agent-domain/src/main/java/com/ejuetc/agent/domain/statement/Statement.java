package com.ejuetc.agent.domain.statement;

import static com.ejuetc.agent.dto.ContractDTO.OAStatus.SUCCESS;
import static com.ejuetc.agent.dto.DealDTO.Type.CHANNEL_OPEN;
import static com.ejuetc.agent.dto.StatementDTO.Action.AGREE;
import static com.ejuetc.agent.dto.StatementDTO.Action.REJECT;
import static com.ejuetc.agent.dto.StatementDTO.Action.STOP;
import static com.ejuetc.agent.dto.StatementDTO.Role.*;
import static com.ejuetc.agent.dto.StatementDTO.Status.CHECK_REJECT;
import static com.ejuetc.agent.dto.StatementDTO.Status.CHECK_WAIT;
import static com.ejuetc.agent.dto.StatementDTO.Status.INVOICE_REJECT;
import static com.ejuetc.agent.dto.StatementDTO.Status.INVOICE_WAIT_CHECK;
import static com.ejuetc.agent.dto.StatementDTO.Status.INVOICE_WAIT_UP;
import static com.ejuetc.agent.dto.StatementDTO.Status.LAUNCH_WAIT;
import static com.ejuetc.agent.dto.StatementDTO.Status.PAY_DONE;
import static com.ejuetc.agent.dto.StatementDTO.Status.PAY_FAIL;
import static com.ejuetc.agent.dto.StatementDTO.Status.PAY_ING;
import static com.ejuetc.agent.dto.StatementDTO.Status.SIGN_ING;
import static com.ejuetc.agent.dto.StatementDTO.Status.SIGN_WAIT;
import static com.ejuetc.agent.dto.StatementDTO.Status.STOP_DONE;
import static com.ejuetc.agent.dto.StatementDTO.Status.STOP_WAIT;
import static com.ejuetc.agent.dto.TemplateDTO.Type.STATEMENT;
import static com.ejuetc.agent.dto.UserDTO.Type.PERSONAL;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.StringUtils.notBlank;
import static com.ejuetc.commons.base.utils.StringUtils.splitString;
import static java.math.BigDecimal.ZERO;
import static java.time.LocalDateTime.now;
import static java.util.Comparator.comparing;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import org.hibernate.annotations.Comment;
import org.hibernate.annotations.OptimisticLock;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import com.alibaba.fastjson.JSONObject;
import com.ejuetc.agent.api.statement.FlowActionPO;
import com.ejuetc.agent.domain.clearing.Clearing;
import com.ejuetc.agent.domain.clearing.ClearingRpt;
import com.ejuetc.agent.domain.clearing.ClearingStatement;
import com.ejuetc.agent.domain.crm.OaFinance;
import com.ejuetc.agent.domain.crm.OaFinance4CashOut;
import com.ejuetc.agent.domain.esign.ESign;
import com.ejuetc.agent.domain.esign.ESignTarget;
import com.ejuetc.agent.domain.esign.Template;
import com.ejuetc.agent.domain.esign.TemplateRpt;
import com.ejuetc.agent.domain.withdraw.Withdraw;
import com.ejuetc.agent.dto.ClearingDTO;
import com.ejuetc.agent.dto.DealDTO;
import com.ejuetc.agent.dto.StatementDTO;
import com.ejuetc.agent.dto.WithdrawDTO;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.usertype.MemoUT;
import com.ejuetc.commons.base.valueobj.Memo;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderColumn;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@Entity
@NoArgsConstructor
@Comment("结算单")
@Table(name = "tb_statement")
@Where(clause = "logic_delete = 0")
public class Statement extends ClearingStatement<Statement> implements ESignTarget {

    @Id
    @GeneratedValue(generator = "statement_id")
    @SequenceGenerator(name = "statement_id", sequenceName = "seq_statement_id")
    private Long id;

    @Column(name = "statement_no", columnDefinition = "varchar(64) COMMENT '结算单号'", nullable = false)
    private String statementNO;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    protected StatementDTO.Status status = CHECK_WAIT;

    @Column(name = "statement_month", columnDefinition = "varchar(6) COMMENT '结算月份'", nullable = false)
    private String statementMonth;

    @Column(name = "clearing_item_count", columnDefinition = "int(11) default 0 COMMENT '清分数量'", nullable = false)
    private Integer clearingItemCount;

    @Setter
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "oa_finance_id", columnDefinition = "bigint(20) default null  COMMENT '所属oa动作'")
    protected OaFinance oaFinance;

    @Column(name = "statement_amount", columnDefinition = "decimal(20,2) default 0 COMMENT '结算金额'")
    protected BigDecimal statementAmount = ZERO;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "esign_id", columnDefinition = "bigint(20) COMMENT '关联电子签Id'")
    protected ESign eSign;

    @Setter
    @Column(name = "invoice_url", columnDefinition = "varchar(511) COMMENT '发票URL'")
    private String invoiceUrl;

    @Column(name = "invoice_time", columnDefinition = "datetime COMMENT '发票上传时间'")
    private LocalDateTime invoiceTime;

    @Column(name = "pay_time", columnDefinition = "datetime COMMENT '打款时间'")
    private LocalDateTime payTime;

    @Column(name = "sign_time", columnDefinition = "datetime COMMENT '签约时间'")
    private LocalDateTime signTime;

    @OptimisticLock(excluded = true)
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "statement")
    private List<Clearing> clearings = new ArrayList<>();


    @OptimisticLock(excluded = true)
    @OrderColumn(name = "sort")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "statement")
    private List<StatementAccount> statementAccounts = new ArrayList<>();

    @OptimisticLock(excluded = true)
    @OrderColumn(name = "sort")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "statement")
    private List<StatementLog> statementLogs = new ArrayList<>();

    @Type(MemoUT.class)
    @Column(name = "memo", columnDefinition = "text COMMENT '备注信息'")
    private Memo memo = new Memo();

    @Column(name = "statement_batch", columnDefinition = "varchar(255) COMMENT '结算批次'")
    private String statementBatch;

    @Column(name = "remark", columnDefinition = "varchar(511) COMMENT '操作描述'")
    private String remark;

    @Setter
    @Column(name = "invoice_confirm_url", columnDefinition = "varchar(511) COMMENT '发票确认URL'")
    private String invoiceConfirmUrl;

    @Setter
    @Column(name = "invoice_pre_tax", columnDefinition = "decimal(20,2) default 0 COMMENT '发票确认税前金额'")
    private BigDecimal invoicePreTax;

    @Setter
    @Column(name = "invoice_after_tax", columnDefinition = "decimal(20,2) default 0 COMMENT '发票确认税后金额'")
    private BigDecimal invoiceAfterTax;

    @Column(name = "invoice_check_time", columnDefinition = "datetime COMMENT '发票确认时间'")
    private LocalDateTime invoiceCheckTime;

    @Column(name = "invoice_batch_no", columnDefinition = "varchar(255) COMMENT '发票批次号'")
    private String invoiceBatchNO;

    @Column(name = "account_batch_no", columnDefinition = "varchar(255) COMMENT '账号批次号'")
    private String accountBatchNo;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "statement_invoice_id", columnDefinition = "bigint(20) COMMENT '结算单发票'")
    private StatementInvoice invoice;

    @Transient
    @Getter(lazy = true)
    private final Template template = getBean(TemplateRpt.class).findByUserTypeAndType(user.getType(), STATEMENT).orElseThrow(() -> new CodingException("未找到[%s][%s]对应模板", user.getType(), STATEMENT));

    static {
        StatementFlow.register(CHECK_WAIT, OPERATOR, AGREE, LAUNCH_WAIT);
        StatementFlow.register(CHECK_WAIT, OPERATOR, REJECT, CHECK_REJECT);

        StatementFlow.register(LAUNCH_WAIT, OPERATOR, AGREE, SIGN_WAIT);
        StatementFlow.register(LAUNCH_WAIT, OPERATOR, REJECT, CHECK_WAIT);

        StatementFlow.register(SIGN_WAIT, USER, AGREE, SIGN_ING, Statement::launchEsign);
        StatementFlow.register(SIGN_WAIT, OPERATOR, REJECT, CHECK_WAIT);

        StatementFlow.register(SIGN_ING, SYSTEM, AGREE, null, Statement::signSucc);
        StatementFlow.register(SIGN_ING, SYSTEM, REJECT, SIGN_WAIT, Statement::cancelEsign);
        StatementFlow.register(SIGN_ING, USER, REJECT, SIGN_WAIT, Statement::cancelEsign);
        StatementFlow.register(SIGN_ING, OPERATOR, REJECT, CHECK_WAIT, Statement::cancelEsign);

        StatementFlow.register(INVOICE_WAIT_UP, USER, AGREE, INVOICE_WAIT_CHECK, Statement::uploadInvoice);
        StatementFlow.register(INVOICE_WAIT_UP, USER, REJECT, SIGN_WAIT, Statement::cancelEsign);
        StatementFlow.register(INVOICE_WAIT_UP, OPERATOR, STOP, STOP_WAIT, Statement::cancelEsign);

        StatementFlow.register(INVOICE_WAIT_CHECK, OPERATOR, AGREE, PAY_ING, Statement::launchPay);
        StatementFlow.register(INVOICE_WAIT_CHECK, OPERATOR, REJECT, INVOICE_REJECT, Statement::invoiceCheckParamSave);
        StatementFlow.register(INVOICE_WAIT_CHECK, OPERATOR, STOP, STOP_WAIT, Statement::cancelEsign);

        StatementFlow.register(INVOICE_REJECT, USER, AGREE, INVOICE_WAIT_CHECK, Statement::uploadInvoice);
        StatementFlow.register(INVOICE_REJECT, USER, REJECT, SIGN_WAIT, Statement::cancelEsign);

        StatementFlow.register(PAY_ING, OPERATOR, AGREE, PAY_DONE, Statement::refreshInvoice);
        StatementFlow.register(PAY_ING, OPERATOR, REJECT, PAY_FAIL, Statement::cancelEsign);
        StatementFlow.register(PAY_ING, OPERATOR, STOP, STOP_WAIT, Statement::cancelEsign);

        StatementFlow.register(PAY_ING, USER, REJECT, SIGN_WAIT, Statement::cancelEsign);

        StatementFlow.register(PAY_FAIL, USER, AGREE, SIGN_WAIT);
        StatementFlow.register(PAY_FAIL, USER, REJECT, PAY_ING);

        StatementFlow.register(STOP_WAIT, SYSTEM, AGREE, STOP_DONE);
        StatementFlow.register(STOP_WAIT, SYSTEM, REJECT, null, Statement::userRejectStop);
    }

    private StatementDTO.Status signSucc() {
        return switch (user.getType()) {
            case COMPANY -> INVOICE_WAIT_UP;
            case PERSONAL -> PAY_ING;
        };
    }

    public List<StatementAccount> getStatementAccountsByAccountBatchNO() {
        return statementAccounts.stream().filter(e -> e.getAccountBatchNO().equals(accountBatchNo)).toList();
    }

    public void refreshPayResult(LocalDateTime payTime) {
        log.info("refreshPayResult:statementAccounts={},payTime={}", statementAccounts, payTime);
        if (getStatementAccountsByAccountBatchNO().stream().allMatch(StatementAccount::paySucc)) {
            flowAction(OPERATOR, AGREE, "支付成功");
            this.payTime = payTime;
        } else {
            flowAction(OPERATOR, REJECT, "支付失败|部分失败");
        }
    }

    public void flowAction(FlowActionPO po) {
        StatementFlow flow = StatementFlow.of(status, po.getRole(), po.getAction());
        StatementDTO.Status newStatus = flow.getCallback().exec(this, po);
        if (this.status == newStatus) return;
        this.status = newStatus != null ? newStatus : flow.getToStatus();
        this.remark = po.getRemark();
        this.statementLogs.add(new StatementLog(this, flow, po, this.status));
    }

    private void launchEsign(FlowActionPO po) {
        if (notBlank(po.getParam())) resetSignParam(po.getParam());

        statementAccounts.forEach(StatementAccount::logicDelete);//删除之前的结算账号
        String batchNO = UUID.randomUUID().toString().replaceAll("-", "");
        getFactClearings().stream().sorted(comparing(Clearing::statementAccountSuccFirst)).forEach(c -> c.allotAccount(batchNO));
        getBean(EntityManager.class).flush();
        getBean(StatementAccountRpt.class).batchInsert(batchNO);
        int updateCount = getBean(ClearingRpt.class).updateStatementAccountId(batchNO);
        int insertCount = getBean(ClearingRpt.class).insertStatementAccountClearing(batchNO);
        if (updateCount != insertCount) {
            throw new CodingException("结算单[%s]清分记录更新[%s]与插入[%s]数量不一致", id, updateCount, insertCount);
        }
        accountBatchNo = batchNO;
        statementAccounts = getBean(StatementAccountRpt.class).findByStatement(this)
                .stream().filter(e -> e.getAccountBatchNO().equals(accountBatchNo)).toList();

        for (StatementAccount statementAccount : statementAccounts) {
            if (statementAccounts.get(statementAccounts.size() - 1).equals(statementAccount)) {
                statementAccount.calcTaxValue4Last(this, statementAccounts);
            } else {
                statementAccount.calcTaxValue();
            }
        }

        eSign = new ESign(getUser(), getTemplate(), this);
        eSign.exec();
    }

    private void resetSignParam(String param) {
        if (user.getType() == PERSONAL) {//个人用户
            user.sortAccount(splitString(param, ",", Long.class));
        } else {//公司用户
            BigDecimal newTaxRate = new BigDecimal(param);
            user.setTaxRate(newTaxRate);
            if (taxRate.compareTo(newTaxRate) != 0) {
                taxRate = newTaxRate;
                getOrigClearings().forEach(c -> c.adjustedTaxRate(newTaxRate));
                recalculateSum();
                getBean(ClearingRpt.class).findUserTaxDiff(user, taxRate).forEach(c -> c.exec(taxRate));//公司调整税率时,对尚未生成结算单的清分记录进行重新计算
            }
        }
    }

    private void recalculateSum() {
        excessComm = ZERO;
        normalComm = ZERO;
        comm = ZERO;
        commPreTax = ZERO;
        reportPreTax = ZERO;
        disburseFee = ZERO;
        reportAfterTax = ZERO;
        statementAmount = ZERO;

        for (Clearing factClearing : getFactClearings()) {
            excessComm = excessComm.add(factClearing.getExcessComm());
            normalComm = normalComm.add(factClearing.getNormalComm());
            comm = comm.add(factClearing.getComm());
            commPreTax = commPreTax.add(factClearing.getCommPreTax());
            reportPreTax = reportPreTax.add(factClearing.getReportPreTax());
            disburseFee = disburseFee.add(factClearing.getDisburseFee());
            reportAfterTax = reportAfterTax.add(factClearing.getReportAfterTax());
            statementAmount = statementAmount.add(factClearing.getStatementAmount());
        }
    }

    // 结算单个人版-返费金额-单元格Key
    private static final List<String> STATEMENT_PERSONAL_RECORD_COLUMN_LIST = Arrays.asList("column1", "column2", "column3", "column4", "column5", "column6", "column7", "column8", "column9", "column10", "column11");
    // 结算单个人版-返费金额-表头
    private static final List<String> STATEMENT_PERSONAL_RECORD_TITLE_LIST = Arrays.asList("序号", "合作姓名", "类型", "甲方签约主体", "发放个人姓名", "身份证号码", "联系电话", "银行帐号", "总金额", "服务费", "实发金额");
    // 结算单个人版-业绩明细-单元格Key
    private static final List<String> STATEMENT_PERSONAL_DETAIL_COLUMN_LIST = Arrays.asList("column1", "column2", "column3", "column4", "column5", "column6", "column7", "column8", "column9", "column10", "column11", "column12", "column13", "column14", "column15", "column16");
    // 结算单个人版-业绩明细-表头
    private static final List<String> STATEMENT_PERSONAL_DETAIL_TITLE_LIST = Arrays.asList("合作对象", "类型", "代理身份", "订单号", "城市", "采买人", "使用人", "项目", "消耗数量", "销售金额", "结算金额", "返佣金额（未扣除服务费）", "销售人", "销售人推荐码", "普通代理人", "普通代理推荐码");

    // 结算单企业版-返费金额-单元格Key
    private static final List<String> STATEMENT_COMPANY_RECORD_COLUMN_LIST = Arrays.asList("column1", "column2", "column3", "column4", "column5", "column6", "column7", "column8", "column9", "column10", "column11");
    // 结算单企业版-返费金额-表头
    private static final List<String> STATEMENT_COMPANY_RECORD_TITLE_LIST = Arrays.asList("序号", "合作姓名", "类型", "甲方签约主体", "发放公司姓名", "营业执照编码", "公司联系电话", "对公账号", "返费金额", "开票税率", "实际返费金额（需开票）");
    // 结算单企业版-业绩明细-单元格Key
    private static final List<String> STATEMENT_COMPANY_DETAIL_COLUMN_LIST = Arrays.asList("column1", "column2", "column3", "column4", "column5", "column6", "column7", "column8", "column9", "column10", "column11", "column12", "column13", "column14");
    // 结算单企业版-业绩明细-表头
    private static final List<String> STATEMENT_COMPANY_DETAIL_TITLE_LIST = Arrays.asList("代理身份", "采买订单号", "城市", "采买人", "使用人", "项目", "消耗数量", "销售金额", "结算金额", "返佣金额（未扣除税差）", "销售人", "销售人推荐码", "普通代理人", "普通代理推荐码");

    public static Map<String, Object> buildRowData4Statement(List<String> columnList, List<String> itemList) {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> rowData = new HashMap<>();
        for (int i = 0; i < columnList.size(); i++) {
            rowData.put(columnList.get(i), itemList.get(i));
        }
        result.put("row", rowData);
        return result;
    }

    public String buildTableData(List<String> columnList, List<String> titleList, List<List<String>> dataList, List<String> summationList) {
        List<Map<String, Object>> tableData = new ArrayList<>();
        tableData.add(buildRowData4Statement(columnList, titleList));
        int rowIndex = 1;
        for (List<String> itemList : dataList) {
            Map<String, Object> itemData = buildRowData4Statement(columnList, itemList);
            if (rowIndex >= 3) itemData.put("insertRow", "true");
            tableData.add(itemData);
            rowIndex++;
        }
        if (summationList != null && summationList.size() > 0) {
            Map<String, Object> summationData = buildRowData4Statement(columnList, summationList);
            if (rowIndex >= 3) summationData.put("insertRow", "true");
            tableData.add(summationData);
        }

        ObjectMapper objectMapper = new ObjectMapper();
        String json;
        try {
            json = objectMapper.writeValueAsString(tableData);
            return json.replace("\"", "\\\"");
        } catch (JsonProcessingException e) {
            throw new CodingException("电子签[%s]数据异常!", e.getMessage());
        }
    }

    public String getStatementMonthStr() {
        return this.statementMonth.substring(0, 4) + "年" + Integer.valueOf(this.statementMonth.substring(4)) + "月";
    }

    public String getTaxRateStr() {
        BigDecimal value = (this.taxRate != null) ? this.taxRate.multiply(new BigDecimal("100")) : BigDecimal.ZERO;
        return value.toString() + "%";
    }

    public String getPersonalRecord() {
        List<String> columnList = STATEMENT_PERSONAL_RECORD_COLUMN_LIST;
        List<String> titleList = STATEMENT_PERSONAL_RECORD_TITLE_LIST;
        List<List<String>> dataList = new ArrayList<>();
        for (int i = 0; i < getStatementAccountsByAccountBatchNO().size(); i++) {
            StatementAccount statementAccount = getStatementAccountsByAccountBatchNO().get(i);
            List<String> data = new ArrayList<>();
            data.add(String.valueOf(i + 1));//序号
            data.add(this.user.getCertName());//合作姓名
            data.add("个人");//类型,默认个人
            data.add("上海添玑");//甲方签约主体,默认上海添玑
            data.add(statementAccount.getAccount().getAccountName());//发放个人姓名
            data.add(statementAccount.getAccount().getCardId());//身份证号码
            data.add(statementAccount.getUser().getContactMobile());//联系电话
            data.add(statementAccount.getAccount().getAccountNO());//银行帐号
            data.add(new DecimalFormat("#,##0.00").format(statementAccount.getComm()));//总金额
            data.add(new DecimalFormat("#,##0.00").format(statementAccount.getDisburseFee()));//服务费
            data.add(new DecimalFormat("#,##0.00").format(statementAccount.getCommPreTax()));//实发金额
            dataList.add(data);
        }
        List<String> summationList = new ArrayList<>();
        summationList.add("合计");
        summationList.add("");
        summationList.add("");
        summationList.add("");
        summationList.add("");
        summationList.add("");
        summationList.add("");
        summationList.add("");
        summationList.add(new DecimalFormat("#,##0.00").format(this.comm));//总金额
        summationList.add(new DecimalFormat("#,##0.00").format(this.disburseFee));//服务费（xx%）
        summationList.add(new DecimalFormat("#,##0.00").format(this.commPreTax));//实发金额
        return buildTableData(columnList, titleList, dataList, summationList);
    }

    public String formattBigDecimal(BigDecimal bigDecimal) {
        DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
        return decimalFormat.format(bigDecimal);
    }

    public String getPersonalDetail() {
        List<String> columnList = STATEMENT_PERSONAL_DETAIL_COLUMN_LIST;
        List<String> titleList = STATEMENT_PERSONAL_DETAIL_TITLE_LIST;
        Map<String, Integer> clueConsumeNumMap = new HashMap<>();
        Map<String, BigDecimal> factAmountMap = new HashMap<>();
        Map<String, BigDecimal> statementAmountMap = new HashMap<>();
        Map<String, BigDecimal> commMap = new HashMap<>();
        Map<String, LocalDateTime> payTimeMap = new HashMap<>();
        Map<String, List<String>> dataMap = new HashMap<>();
        List<Clearing> factClearings = this.getFactClearings();
        for (int i = 0; i < factClearings.size(); i++) {
            Clearing clearing = factClearings.get(i);
            if (clearing.getFlagFact() == null || !clearing.getFlagFact()) continue;
            String subType = (ClearingDTO.Type.PARTNER == clearing.getType()) ? "合伙人" : clearing.getDeal().getAgentSubTypeName();
            String key = CHANNEL_OPEN == clearing.getDeal().getType() ? clearing.getDeal().getCrmOrderNO() + clearing.getType()
                    : clearing.getDeal().getBrokerUser().getId() + "_" + clearing.getDeal().getBrokerOperatorId() + "_" + clearing.getDeal().getAgentCode() + "_" + subType + "_" + clearing.getDeal().getBusinessCode() + "_" + clearing.getDeal().getChannelCode();
            Integer clueConsumeNum = clueConsumeNumMap.get(key) != null ? clueConsumeNumMap.get(key) + 1 : 1;
            clueConsumeNumMap.put(key, clueConsumeNum);
            BigDecimal factAmount = factAmountMap.get(key) != null ? factAmountMap.get(key).add(clearing.getDeal().getPayAmount()) : clearing.getDeal().getPayAmount();
            factAmountMap.put(key, factAmount);
            BigDecimal statementAmount = statementAmountMap.get(key) != null ? statementAmountMap.get(key).add(clearing.getStatementAmount()) : clearing.getStatementAmount();
            statementAmountMap.put(key, statementAmount);
            BigDecimal comm = commMap.get(key) != null ? commMap.get(key).add(clearing.getComm()) : clearing.getComm();
            commMap.put(key, comm);
            LocalDateTime payTime = (DealDTO.Type.CHANNEL_OPEN == clearing.getDeal().getType()) ?
                    clearing.getDeal().getUpDelegateTime() : clearing.getDeal().getPayTime();
            if (payTimeMap.get(key) != null && payTimeMap.get(key).isAfter(payTime)) payTime = payTimeMap.get(key);
            payTimeMap.put(key, payTime);
            if (DealDTO.Type.CLUE_CONSUME == clearing.getDeal().getType() && dataMap.get(key) != null) {
                List<String> data = dataMap.get(key);
                data.set(8, String.valueOf(clueConsumeNum));
                data.set(9, new DecimalFormat("#,##0.00").format(factAmount));
                data.set(10, (ClearingDTO.Type.AGENT == clearing.getType()) ? new DecimalFormat("#,##0.00").format(factAmount.subtract(comm)) : "");
                data.set(11, new DecimalFormat("#,##0.00").format(comm));
                //data.set(12, payTime != null ? payTime.format(ofPattern("yyyy-MM-dd")) : "");
                continue;
            }
            List<String> data = new ArrayList<>();
            data.add(clearing.getUser().getCertName());//合作对象
            data.add("个人");//类型,默认个人
            data.add(subType);//代理身份,合伙人/普通代理
            data.add(clearing.getDeal().getType() == DealDTO.Type.CHANNEL_OPEN ? clearing.getDeal().getCrmOrderNO() : "");//采买订单号
            data.add(clearing.getDeal().getCityName());//采买城市
            data.add(clearing.getDeal().getBrokerUserName());//采买人姓名/企业
            data.add(clearing.getDeal().getBrokerOperatorName());//使用人姓名
            String goodsName = "";
//          if (clearing.getDeal() instanceof ChannelOpenDeal c) goodsName = c.getGoodsName();
//          if (clearing.getDeal() instanceof ClueConsumeDeal c) goodsName = c.getGoodsName();
            if (CHANNEL_OPEN == clearing.getDeal().getType()) {
                goodsName = clearing.getDeal().getChannelCode().getTitle() + clearing.getDeal().getBusinessCode().getTitle() + "账号费";
            } else if (DealDTO.Type.CLUE_CONSUME == clearing.getDeal().getType()) {
                goodsName = clearing.getDeal().getChannelCode().getTitle() + clearing.getDeal().getBusinessCode().getTitle() + "互动用户服务费";
            }
            data.add(goodsName);//项目
            data.add(String.valueOf(clueConsumeNum));//销售数量/消耗条数
            data.add(new DecimalFormat("#,##0.00").format(factAmount));//销售金额
            data.add((ClearingDTO.Type.AGENT == clearing.getType()) ? new DecimalFormat("#,##0.00").format(factAmount.subtract(comm)) : "");//结算金额
            data.add(new DecimalFormat("#,##0.00").format(comm));//返佣金额
            //data.add(payTime != null ? payTime.format(ofPattern("yyyy-MM-dd")) : "");//确认可返日期,账号费可返时间 /线索费是按当月消耗最后一条时间显示
            String cName = (ClearingDTO.Type.AGENT == clearing.getType()) ? clearing.getDeal().getAgentCurrentOperatorName()
                    : (ClearingDTO.Type.PARTNER == clearing.getType()) ? clearing.getDeal().getPartnerCurrentOperatorName()
                    : "";
            data.add(cName);//销售人姓名,闲鱼独家下的-销售人姓名,etc独家下的-销售人姓名,合伙人下的-普通代理人姓名,普通代理：普通代理人姓名
            String cCode = (ClearingDTO.Type.AGENT == clearing.getType()) ? clearing.getDeal().getAgentCode()
                    : (ClearingDTO.Type.PARTNER == clearing.getType()) ? clearing.getDeal().getPartnerCode()
                    : "";
            data.add(cCode);//销售人推荐码
            data.add((ClearingDTO.Type.PARTNER == clearing.getType()) ? clearing.getDeal().getAgentUserName() : "");//普通代理人名称,合伙人下的普通代理人名称，其余身份时该字段不展示
            data.add((ClearingDTO.Type.PARTNER == clearing.getType()) ? clearing.getDeal().getAgentCode() : "");//普通代理的编码,合伙人下的普通代理人名称，其余身份时该字段不展示
            dataMap.put(key, data);
        }
        List<List<String>> dataList = new ArrayList<>(dataMap.values());

//        List<String> summationList = new ArrayList<>();
//        summationList.add("汇总");
//        summationList.add("");
//        summationList.add("");
//        summationList.add("");
//        summationList.add("");
//        summationList.add("");
//        summationList.add("");
//        summationList.add("");
//        summationList.add("");
//        summationList.add("");
//        summationList.add("");
//        summationList.add("");
//        //summationList.add("");
//        summationList.add("");
//        summationList.add("");
//        summationList.add("");
//        summationList.add("");
        return buildTableData(columnList, titleList, dataList, null);
    }

    public String getCompanyRecord() {
        List<String> columnList = STATEMENT_COMPANY_RECORD_COLUMN_LIST;
        List<String> titleList = STATEMENT_COMPANY_RECORD_TITLE_LIST;
        List<List<String>> dataList = new ArrayList<>();
        for (int i = 0; i < this.statementAccounts.size(); i++) {
            StatementAccount statementAccount = this.statementAccounts.get(i);
            List<String> data = new ArrayList<>();
            data.add(String.valueOf(i + 1));//序号
            data.add(this.user.getCertName());//合作姓名
            data.add("企业");//类型,默认企业
            data.add("上海添玑");//甲方签约主体,默认上海添玑
            data.add(statementAccount.getAccount().getAccountName());//发放公司姓名
            data.add(statementAccount.getUser().getCompanyNum());//营业执照编码
            data.add(statementAccount.getUser().getContactMobile());//公司联系电话
            data.add(statementAccount.getAccount().getAccountNO());//对公账号
            data.add(new DecimalFormat("#,##0.00").format(statementAccount.getComm()));//返费金额
            data.add(getTaxRateStr());//开票税率
            data.add(new DecimalFormat("#,##0.00").format(statementAccount.getCommPreTax()));//实际返费金额（需开票）
            dataList.add(data);
        }
        List<String> summationList = new ArrayList<>();
        summationList.add("合计");
        summationList.add("");
        summationList.add("");
        summationList.add("");
        summationList.add("");
        summationList.add("");
        summationList.add("");
        summationList.add("");
        summationList.add(new DecimalFormat("#,##0.00").format(this.comm));//返费金额
        summationList.add(getTaxRateStr());//开票税率）
        summationList.add(new DecimalFormat("#,##0.00").format(this.commPreTax));//实际返费金额（需开票）
        return buildTableData(columnList, titleList, dataList, summationList);
    }

    public String getCompanyDetail() {
        List<String> columnList = STATEMENT_COMPANY_DETAIL_COLUMN_LIST;
        List<String> titleList = STATEMENT_COMPANY_DETAIL_TITLE_LIST;
        Map<String, Integer> clueConsumeNumMap = new HashMap<>();
        Map<String, BigDecimal> factAmountMap = new HashMap<>();
        Map<String, BigDecimal> statementAmountMap = new HashMap<>();
        Map<String, BigDecimal> commMap = new HashMap<>();
        Map<String, LocalDateTime> payTimeMap = new HashMap<>();
        Map<String, List<String>> dataMap = new HashMap<>();
        List<Clearing> factClearings = this.getFactClearings();
        for (int i = 0; i < factClearings.size(); i++) {
            Clearing clearing = factClearings.get(i);
            if (clearing.getFlagFact() == null || !clearing.getFlagFact()) continue;
            String subType = (ClearingDTO.Type.PARTNER == clearing.getType()) ? "合伙人" : clearing.getDeal().getAgentSubTypeName();
            String key = CHANNEL_OPEN == clearing.getDeal().getType() ? clearing.getDeal().getCrmOrderNO() + clearing.getType()
                    : clearing.getDeal().getBrokerUser().getId() + "_" + clearing.getDeal().getBrokerOperatorId() + "_" + clearing.getDeal().getAgentCode() + "_" + subType + "_" + clearing.getDeal().getBusinessCode() + "_" + clearing.getDeal().getChannelCode();
            Integer clueConsumeNum = clueConsumeNumMap.get(key) != null ? clueConsumeNumMap.get(key) + 1 : 1;
            clueConsumeNumMap.put(key, clueConsumeNum);
            BigDecimal factAmount = factAmountMap.get(key) != null ? factAmountMap.get(key).add(clearing.getDeal().getPayAmount()) : clearing.getDeal().getPayAmount();
            factAmountMap.put(key, factAmount);
            BigDecimal statementAmount = statementAmountMap.get(key) != null ? statementAmountMap.get(key).add(clearing.getStatementAmount()) : clearing.getStatementAmount();
            statementAmountMap.put(key, statementAmount);
            BigDecimal comm = commMap.get(key) != null ? commMap.get(key).add(clearing.getComm()) : clearing.getComm();
            commMap.put(key, comm);
            LocalDateTime payTime = (DealDTO.Type.CHANNEL_OPEN == clearing.getDeal().getType()) ?
                    clearing.getDeal().getUpDelegateTime() : clearing.getDeal().getPayTime();
            if (payTimeMap.get(key) != null && payTimeMap.get(key).isAfter(payTime)) payTime = payTimeMap.get(key);
            payTimeMap.put(key, payTime);
            if (DealDTO.Type.CLUE_CONSUME == clearing.getDeal().getType() && dataMap.get(key) != null) {
                List<String> data = dataMap.get(key);
                data.set(6, String.valueOf(clueConsumeNum));
                data.set(7, new DecimalFormat("#,##0.00").format(factAmount));
                data.set(8, (ClearingDTO.Type.AGENT == clearing.getType()) ? new DecimalFormat("#,##0.00").format(factAmount.subtract(comm)) : "");
                data.set(9, new DecimalFormat("#,##0.00").format(comm));
                //data.set(10, payTime != null ? payTime.format(ofPattern("yyyy-MM-dd")) : "");
                continue;
            }
            List<String> data = new ArrayList<>();
            data.add(subType);//代理身份,合伙人/普通代理
            data.add(clearing.getDeal().getType() == DealDTO.Type.CHANNEL_OPEN ? clearing.getDeal().getCrmOrderNO() : "");//采买订单号
            data.add(clearing.getDeal().getCityName());//采买城市
            data.add(clearing.getDeal().getBrokerUserName());//采买人姓名/企业
            data.add(clearing.getDeal().getBrokerOperatorName());//使用人姓名
            String goodsName = "";
//            if (clearing.getDeal() instanceof ChannelOpenDeal c) goodsName = c.getGoodsName();
//            if (clearing.getDeal() instanceof ClueConsumeDeal c) goodsName = c.getGoodsName();
            if (CHANNEL_OPEN == clearing.getDeal().getType()) {
                goodsName = clearing.getDeal().getChannelCode().getTitle() + clearing.getDeal().getBusinessCode().getTitle() + "账号费";
            } else if (DealDTO.Type.CLUE_CONSUME == clearing.getDeal().getType()) {
                goodsName = clearing.getDeal().getChannelCode().getTitle() + clearing.getDeal().getBusinessCode().getTitle() + "互动用户服务费";
            }
            data.add(goodsName);//项目
            data.add(String.valueOf(clueConsumeNum));//销售数量/消耗条数
            data.add(new DecimalFormat("#,##0.00").format(factAmount));//销售金额
            data.add((ClearingDTO.Type.AGENT == clearing.getType()) ? new DecimalFormat("#,##0.00").format(factAmount.subtract(comm)) : "");//结算金额
            data.add(new DecimalFormat("#,##0.00").format(comm));//返佣金额
            //data.add(payTime != null ? payTime.format(ofPattern("yyyy-MM-dd")) : "");//确认可返日期,账号费可返时间 /线索费是按当月消耗最后一条时间显示
            String cName = (ClearingDTO.Type.AGENT == clearing.getType()) ? clearing.getDeal().getAgentCurrentOperatorName()
                    : (ClearingDTO.Type.PARTNER == clearing.getType()) ? clearing.getDeal().getPartnerCurrentOperatorName()
                    : "";
            data.add(cName);//销售人姓名,闲鱼独家下的-销售人姓名,etc独家下的-销售人姓名,合伙人下的-普通代理人姓名,普通代理：普通代理人姓名
            String cCode = (ClearingDTO.Type.AGENT == clearing.getType()) ? clearing.getDeal().getAgentCode()
                    : (ClearingDTO.Type.PARTNER == clearing.getType()) ? clearing.getDeal().getPartnerCode()
                    : "";
            data.add(cCode);//销售人推荐码
            data.add((ClearingDTO.Type.PARTNER == clearing.getType()) ? clearing.getDeal().getAgentUserName() : "");//普通代理人名称,合伙人下的普通代理人名称，其余身份时该字段不展示
            data.add((ClearingDTO.Type.PARTNER == clearing.getType()) ? clearing.getDeal().getAgentCode() : "");//普通代理的编码,合伙人下的普通代理人名称，其余身份时该字段不展示
            dataMap.put(key, data);
        }
        List<List<String>> dataList = dataMap.values().stream().collect(Collectors.toList());

//        List<String> summationList = new ArrayList<>();
//        summationList.add("汇总");
//        summationList.add("");
//        summationList.add("");
//        summationList.add("");
//        summationList.add("");
//        summationList.add("");
//        summationList.add("");
//        summationList.add("");
//        summationList.add("");
//        summationList.add("");
//        //summationList.add("");
//        summationList.add("");
//        summationList.add("");
//        summationList.add("");
//        summationList.add("");
        return buildTableData(columnList, titleList, dataList, null);
    }

    public List<Clearing> getFactClearings() {
        return clearings.stream().filter(c -> c.getFlagFact() && !c.getLogicDelete()).toList();
    }

    public List<Clearing> getOrigClearings() {
        return clearings.stream().filter(Clearing::getFlagOrig).toList();
    }


    private void launchPay(FlowActionPO po) {
        invoiceCheckParamSave(po);

        log.debug("结算单[{}]发起打款", statementNO);
        Withdraw withdraw = new Withdraw(this.getStatementAccounts(), WithdrawDTO.Type.COMPANY);
        getBean(EntityManager.class).persist(withdraw);
        //企业请款
        this.oaFinance = new OaFinance4CashOut(this);
        oaFinance.sendCrm(null);
    }

    private void invoiceCheckParamSave(FlowActionPO po) {
        JSONObject jsonObject = JSONObject.parseObject(po.getParam());
        invoiceConfirmUrl = jsonObject.getString("invoiceConfirmUrl");
        invoicePreTax = jsonObject.getBigDecimal("invoicePreTax");
        invoiceAfterTax = jsonObject.getBigDecimal("invoiceAfterTax");
        invoiceCheckTime = now();
        remark = po.getRemark();
    }

    public Clearing newClearing4Invoice(BigDecimal diffPreAmount, BigDecimal diffAfterTax) {
        Clearing target = getOrigClearings().stream().filter(c -> c.getDeal().getPartnerUser().getId().equals(9095376821982095616L)).findFirst()//优先取交易所属合伙人为"添玑"的清分记录进行发票调差
                .orElse(getOrigClearings().stream().filter(c -> c.getDealType() == CHANNEL_OPEN).findFirst().orElse(clearings.get(0)));
        target.new4Invoice(diffPreAmount, diffAfterTax, taxRate);
        return target.getInvoiceDiff();
    }

    private void cancelEsign(FlowActionPO po) {
        if (eSign != null) {
            eSign.cancel(po.getRole(), po.getRemark());
        }
    }

    private void uploadInvoice(FlowActionPO po) {
        invoiceUrl = po.getParam();
        invoiceTime = now();
    }

    private StatementDTO.Status userRejectStop() {
        return statementLogs.get(statementLogs.size() - 1).getFlow().getFromStatus();
    }

    @Override
    public void signResult(Boolean signResult, String description, List<String> completeFiles) {
        if (signResult) {
            flowAction(SYSTEM, AGREE, description);
            signTime = now();
        } else {
            flowAction(SYSTEM, REJECT, description);
            memo.addMemo(description);
        }
    }

    private void flowAction(StatementDTO.Role role, StatementDTO.Action action, String description) {
        flowAction(new FlowActionPO()
                .setStatementId(id)
                .setRole(role)
                .setAction(action)
                .setOperatorId(null)
                .setOperatorName(null)
                .setParam(description)
                .setRemark(!AGREE.equals(action) ? description : "")
        );
    }

    public void changeChild(Clearing orig, Clearing fact) {
        clearings.remove(orig);
        clearings.add(fact);
    }

    public void addChild(Clearing clearing) {
        clearings.add(clearing);
    }

    public void setInvoiceBatchNO(String invoiceBatchNO) {
        if (notBlank(this.invoiceBatchNO)) return;

        this.invoiceBatchNO = invoiceBatchNO;
        this.invoice = StatementInvoice.of(invoiceBatchNO);
        this.invoice.addStatement(this);
    }

    public void refreshInvoice() {
        invoice.exec();
    }

    public boolean notOaSucc() {
        return oaFinance == null || oaFinance.getOaStatus() != SUCCESS;
    }

    public boolean notOaFinance() {
        return oaFinance == null || oaFinance.getId() == null || oaFinance.getId() == -1;
    }

    public boolean hasChannelOpen() {
        return clearings.stream().anyMatch(c -> c.getDealType() == CHANNEL_OPEN);
    }
}
