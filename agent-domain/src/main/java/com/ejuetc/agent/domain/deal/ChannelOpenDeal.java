package com.ejuetc.agent.domain.deal;

import com.ejuetc.agent.api.deal.CreateChannelOpenDealPO;
import com.ejuetc.agent.domain.contract.Contract;
import com.ejuetc.agent.dto.DealDTO;
import com.ejuetc.commons.base.clazz.SubtypeCode;

import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@Slf4j
@Getter
@Entity
@DiscriminatorValue("CHANNEL_OPEN")
@SubtypeCode(parent = Contract.class, code = "CHANNEL_OPEN", name = "渠道开通")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class ChannelOpenDeal extends Deal {

    @Column(name = "parent_order_id", columnDefinition = "bigint(20) COMMENT '父订单外键'")
    private Long parentOrderId;

    @Column(name = "goods_id", columnDefinition = "bigint(20) COMMENT '商品外键'")
    private Long goodsId;

    @Column(name = "goods_name", columnDefinition = "varchar(255) COMMENT '商品名称'")
    private String goodsName;

    @Column(name = "order_num", columnDefinition = "varchar(255) COMMENT 'MALL订单号'")
    private String orderNum;

    @Column(name = "parent_order_num", columnDefinition = "varchar(255) COMMENT 'MALL父订单号'")
    private String parentOrderNum;

    @Column(name = "order_id", columnDefinition = "bigint(20) COMMENT '子订单外键 （mall模块子订单id）'")
    private Long orderId;

    public ChannelOpenDeal(CreateChannelOpenDealPO po) {
        super(po);
        this.goodsId = po.getGoodsId();
        this.goodsName = po.getCategory() == DealDTO.Category.COMPANY_ENJOY
                ? "公司无限包"
                : getChannelCodeName() + getBusinessCodeName() + "端口账号费";
        this.parentOrderId = po.getParentOrderId();
        this.orderNum = po.getOrderNum();
        this.parentOrderNum = po.getParentOrderNum();
        this.orderId = po.getOrderId();
        this.enjoy = po.isEnjoy();
    }

}
