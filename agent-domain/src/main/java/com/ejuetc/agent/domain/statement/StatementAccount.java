package com.ejuetc.agent.domain.statement;

import com.ejuetc.agent.domain.account.Account;
import com.ejuetc.agent.domain.clearing.Clearing;
import com.ejuetc.agent.domain.clearing.ClearingStatement;
import com.ejuetc.agent.domain.crm.OaFinance;
import com.ejuetc.agent.domain.withdraw.Withdraw;
import com.ejuetc.agent.dto.AccountDTO;
import com.ejuetc.agent.dto.WithdrawDTO;
import com.ejuetc.commons.base.clazz.ClassUtils;
import com.ejuetc.commons.base.usertype.MemoUT;
import com.ejuetc.commons.base.valueobj.Memo;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.ejuetc.agent.dto.UserDTO.Type.COMPANY;
import static com.ejuetc.agent.dto.WithdrawDTO.Status.*;
import static java.math.BigDecimal.ZERO;
import static lombok.AccessLevel.PROTECTED;

@Slf4j
@Getter
@Entity
@NoArgsConstructor
@Comment("结算账号汇总")
@Table(name = "tb_statement_account")
@Where(clause = "logic_delete = 0")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", columnDefinition = "enum('BANK_ACCOUNT','YZH_ACCOUNT') COMMENT '结算账户类型'")
public abstract class StatementAccount extends ClearingStatement<StatementAccount> {

    @Id
    @GeneratedValue(generator = "statement_account_id")
    @SequenceGenerator(name = "statement_account_id", sequenceName = "seq_statement_account_id")
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", insertable = false, updatable = false)
    private AccountDTO.Type type;

    public AccountDTO.Type getType() {
        if (type == null)
            type = AccountDTO.Type.valueOf(ClassUtils.getClassAnnotation(this.getClass(), DiscriminatorValue.class).value());
        return type;
    }

    @Setter
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, columnDefinition = "enum('INIT','PAYING','SUCCESS','FAIL') default 'INIT' COMMENT '结算单账号状态'")
    protected WithdrawDTO.Status status = INIT;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "account_id", columnDefinition = "bigint(20) COMMENT '所属账号ID'", nullable = false)
    private Account account;

    @Setter(PROTECTED)
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "statement_id", columnDefinition = "bigint(20) COMMENT '所属结算单'", nullable = false)
    protected Statement statement;

    @Setter
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "withdraw_id", columnDefinition = "bigint(20) COMMENT '所属出款单'")
    protected Withdraw withdraw;

    @Column(name = "clearing_item_count", columnDefinition = "int(11) default 0 COMMENT '清分数量'", nullable = false)
    private Integer clearingItemCount;

    @Column(name = "statement_amount", columnDefinition = "decimal(20,2) default 0 COMMENT '结算金额'")
    protected BigDecimal statementAmount = ZERO;

    @Setter
    @Column(name = "pay_time", columnDefinition = "datetime COMMENT '打款时间'")
    private LocalDateTime payTime;

    @ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinTable(name = "tb_statement_account_clearing",
            joinColumns = @JoinColumn(name = "statement_account_id", columnDefinition = "bigint(20) COMMENT '结算账号ID'"),
            inverseJoinColumns = @JoinColumn(name = "clearing_id", columnDefinition = "bigint(20) COMMENT '清分记录ID'")
    )
    @Comment("结算账号与清分记录多对多关系")
    private List<Clearing> clearings = new ArrayList<>();

    @Type(MemoUT.class)
    @Column(name = "memo", columnDefinition = "text COMMENT '备注信息'")
    private Memo memo = new Memo();

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "oa_finance_id", columnDefinition = "bigint(20) COMMENT 'oa财务请款/核销id'")
    protected OaFinance oaFinance;

    @Column(name = "account_batch_no", columnDefinition = "varchar(63) COMMENT '账号批次号'")
    private String accountBatchNO;

    public abstract void launchPay();

    public boolean paySucc() {
        return payTime != null;
    }

    public void setWithdraw(Withdraw wd, String invoiceBatchNO) {
        if (status != INIT && status != FAIL)
            throw new IllegalStateException("结算账号已经被使用");

        withdraw = wd;
        status = PAYING;
        statement.setInvoiceBatchNO(invoiceBatchNO);
    }

    public boolean isSucc() {
        return status == SUCCESS;
    }

    public boolean isPaying() {
        return status == PAYING;
    }

    public boolean isYzhPaySucc(String orderStatus) {
        return "已付款".equals(orderStatus) || "已支付".equals(orderStatus);
    }

    public void updateStatusAndPayTime(String orderStatus, LocalDateTime payTime) {
        if (isYzhPaySucc(orderStatus)) {
            this.status = WithdrawDTO.Status.SUCCESS;
            this.payTime = payTime;
        } else {
            this.status = WithdrawDTO.Status.FAIL;
        }

    }

    public void updateClearingFactFields() {
        log.info("更新结算账号[id:{}][status:{}][clearings.size:{}]清分记录实际字段", id, status, clearings.size());
        if (getStatus() != SUCCESS) return;
        getClearings().forEach(Clearing::updateFactFields);
    }
}


