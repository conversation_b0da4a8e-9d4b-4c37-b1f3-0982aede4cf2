package com.ejuetc.agent.domain.statement;

import com.ejuetc.agent.domain.contract.Contract;
import com.ejuetc.agent.domain.crm.OaFinance4CashOut;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.alibaba.fastjson.JSON.parseObject;

@Slf4j
@Entity
@Getter
@DiscriminatorValue("BANK_ACCOUNT")
@SubtypeCode(parent = Contract.class, code = "BANK_ACCOUNT", name = "银行账户")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class StatementAccount4Bank extends StatementAccount {

    @Override
    public void launchPay() {

    }

}