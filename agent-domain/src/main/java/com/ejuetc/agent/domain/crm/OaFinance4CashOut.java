package com.ejuetc.agent.domain.crm;

import com.alibaba.fastjson.JSONObject;
import com.ejuetc.agent.api.contract.CrmResultPo;
import com.ejuetc.agent.domain.statement.Statement;
import com.ejuetc.agent.domain.statement.StatementAccount;
import com.ejuetc.agent.dto.ContractDTO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.exception.CodingException;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.alibaba.fastjson.JSON.parseObject;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@Slf4j
@Entity
@Getter
@DiscriminatorValue("CASH_OUT")
@SubtypeCode(parent = OaFinance.class, code = "CASH_OUT", name = "请款")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class OaFinance4CashOut extends OaFinance{

    @Column(name = "real_after_amount", columnDefinition = "decimal(20,2) default 0 COMMENT 'crm实际税后金额'")
    private BigDecimal realAfterAmount = BigDecimal.ZERO;

    public OaFinance4CashOut(Statement statement) {
        this.statements.add(statement);
        this.invoiceAfterTax = statement.getInvoiceAfterTax();
        this.invoicePreTax = statement.getInvoicePreTax();
    }

    @Override
    public void sendCrm(String applyName) {
        if (this.oaStatus == ContractDTO.OAStatus.IN_HAND || this.oaStatus == ContractDTO.OAStatus.SUCCESS)
            return;
        this.lastCrmInstruct = new CrmInstruct4CashOut(this);
        this.crmInstruct.add(lastCrmInstruct);
        this.lastCrmInstruct.exec();
        this.oaStatus = switch (lastCrmInstruct.getStatus()) {
            case SUCC -> ContractDTO.OAStatus.IN_HAND;
            default -> ContractDTO.OAStatus.FAIL;
        };
        if (oaStatus == ContractDTO.OAStatus.IN_HAND) {
            JSONObject jsonObject = parseObject(this.lastCrmInstruct.getResultData());
            this.oaNum = jsonObject.getString("oaNo");
        }
    }

    public void crmPayResult(CrmResultPo po) {
        if (!po.getSuccess())
            return;
        this.realAfterAmount = po.getRealAmount();
        getBean(EntityManager.class).persist(this);
        List<Statement> statements = this.getStatements();
        List<StatementAccount> statementAccounts = statements.stream().flatMap(statement -> statement.getStatementAccounts().stream()).collect(Collectors.toList());
        statementAccounts.forEach(t -> t.updateStatusAndPayTime("已支付", po.getAuditTime()));
        statements.forEach(t -> t.refreshPayResult(po.getAuditTime()));
    }
}
