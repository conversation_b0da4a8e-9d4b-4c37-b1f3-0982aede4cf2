package com.ejuetc.agent.domain.statement;


import com.ejuetc.agent.domain.clearing.Clearing;
import com.ejuetc.agent.domain.crm.OaFinance;
import com.ejuetc.agent.dto.StatementDTO;
import com.ejuetc.agent.dto.UserDTO;
import com.ejuetc.commons.base.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.OptimisticLock;
import org.hibernate.annotations.Where;

import java.math.BigDecimal;
import java.util.*;

import static java.math.BigDecimal.ZERO;

@Slf4j
@Getter
@Entity
@NoArgsConstructor
@Comment("结算单发票")
@Table(name = "tb_statement_invoice")
@Where(clause = "logic_delete = 0")
public class StatementInvoice extends BaseEntity<StatementInvoice> {

    @Id
    @GeneratedValue(generator = "statement_invoice_id")
    @SequenceGenerator(name = "statement_invoice_id", sequenceName = "seq_statement_invoice_id")
    private Long id;

    @Column(name = "invoice_batch_no", columnDefinition = "varchar(255) COMMENT '发票批次号'")
    private String batchNO;

    @Column(name = "statement_pre_tax", columnDefinition = "decimal(20,2) COMMENT '结算单税前金额'")
    private BigDecimal statementPreTax;

    @Column(name = "statement_after_tax", columnDefinition = "decimal(20,2) COMMENT '结算单税后金额'")
    private BigDecimal statementAfterTax;

    @Column(name = "fact_pre_tax", columnDefinition = "decimal(20,2) COMMENT '实际税前金额'")
    private BigDecimal factPreTax;

    @Column(name = "fact_after_tax", columnDefinition = "decimal(20,2) COMMENT '实际税后金额'")
    private BigDecimal factAfterTax;

    @Column(name = "diff_pre_tax", columnDefinition = "decimal(20,2) COMMENT '税前差异金额'")
    private BigDecimal diffPreTax;

    @Column(name = "diff_after_tax", columnDefinition = "decimal(20,2) COMMENT '税后差异金额'")
    private BigDecimal diffAfterTax;

    @OptimisticLock(excluded = true)
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "invoice")
    protected List<Statement> statements = new ArrayList<>();

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "diff_statement_id", columnDefinition = "bigint(20) COMMENT '差异结算单'")
    private Statement diffStatement;

    private static final Map<String, StatementInvoice> cache = new HashMap<>();

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "diff_clearing_id", columnDefinition = "bigint(20) COMMENT '调差清分记录'")
    private Clearing diffClearing;

    public StatementInvoice(String no) {
        this.batchNO = no;
    }

    public static StatementInvoice of(String batchNO) {
        return cache.compute(batchNO, (k, v) -> v == null ? new StatementInvoice(k) : v);
    }

    public void addStatement(Statement statement) {
        if (!statements.contains(statement)) statements.add(statement);
    }

    public void exec() {
        if (statements.stream().anyMatch(t -> t.getStatus() != StatementDTO.Status.PAY_DONE)
            && statements.stream().anyMatch(Statement::notOaFinance)) return;

        factPreTax = statements.stream().map(Statement::getOaFinance).distinct().map(OaFinance::getInvoicePreTax).reduce(BigDecimal::add).orElse(ZERO);
        statementPreTax = statements.stream().map(Statement::getReportPreTax).reduce(BigDecimal::add).orElse(ZERO);
        diffPreTax = factPreTax.subtract(statementPreTax);

        factAfterTax = statements.stream().map(Statement::getOaFinance).distinct().map(OaFinance::getInvoiceAfterTax).reduce(BigDecimal::add).orElse(ZERO);
        statementAfterTax = statements.stream().map(Statement::getReportAfterTax).reduce(BigDecimal::add).orElse(ZERO);
        diffAfterTax = factAfterTax.subtract(statementAfterTax);

        if (diffPreTax.compareTo(ZERO) != 0 || diffAfterTax.compareTo(ZERO) != 0) {
            diffStatement = statements.stream().filter(Statement::hasChannelOpen).max(Comparator.comparing(Statement::getCreateTime)).orElse(statements.get(0));
            diffClearing = diffStatement.newClearing4Invoice(diffPreTax, diffAfterTax);
        }

        statements.stream()
                .flatMap(s -> s.getStatementAccounts().stream())
                .forEach(StatementAccount::updateClearingFactFields);
    }
}
