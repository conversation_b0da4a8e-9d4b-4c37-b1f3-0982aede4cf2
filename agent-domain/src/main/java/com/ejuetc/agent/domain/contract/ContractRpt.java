package com.ejuetc.agent.domain.contract;

import com.ejuetc.agent.dto.ContractDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.List;

public interface ContractRpt extends JpaRepositoryImplementation<Contract, Long> {

    @Query("""
            from Contract  c where
            c.type = :type and
            c.user.id = :userId and
            c.status = :status and
            current_timestamp between c.contractIndate.beginTime and c.contractIndate.endTime
            """)
    List<Contract> findContract(Long userId, ContractDTO.Status status, ContractDTO.Type type);

    Contract findByContractNo(String contractNo);

    @Query("""
            SELECT DISTINCT c
            FROM BrokerContract c
            LEFT JOIN FETCH c.user u
            LEFT JOIN c.inviteds ic
            LEFT JOIN c.invited i
            WHERE
                c.status = 'EFFECTIVE'
                AND (:userId IS NULL OR ic.user.id = :userId OR i.user.id = :userId)
                AND (:operatorId IS NULL OR ic.operator.id = :operatorId OR i.operator.id = :operatorId)
                AND (:brokerName IS NULL OR u.companyName LIKE CONCAT('%', :brokerName, '%') OR u.personalName LIKE CONCAT('%', :brokerName, '%'))
            ORDER BY c.createTime DESC
            """)
    Page<BrokerContract> findInvitedBrokers(Long userId, Long operatorId, String brokerName, Pageable pageable);

    @Query(value = """
            select distinct i.code
             from tb_price p
                      left join tb_contract_price cp on cp.price_id = p.id
                      left join tb_contract c on c.id = cp.contract_id
                      left join tb_invitation i on i.operator_id = c.user_id and i.user_id=c.user_id
             where p.city_code = ?1
               and c.sub_type in ('AGENT_XIANYU_EXCLUSIVE','AGENT_LEJU_EXCLUSIVE')
               and c.status = 'EFFECTIVE'
               and current_timestamp between c.contract_begin_time and c.contract_end_time
            """, nativeQuery = true)
    List<String> findInvitationCodeByExclusive(String cityCode);

    PartnerContract findByIdAndType(Long id, ContractDTO.Type type);
}
