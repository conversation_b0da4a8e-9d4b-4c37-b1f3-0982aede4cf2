package com.ejuetc.agent.domain.esign;

import static com.ejuetc.agent.dto.ESignDTO.Status.CANCEL;
import static com.ejuetc.agent.dto.ESignDTO.Status.COMPLE;
import static com.ejuetc.agent.dto.ESignDTO.Status.INIT;
import static com.ejuetc.agent.dto.ESignDTO.Status.INVALID;
import static com.ejuetc.agent.dto.ESignDTO.Status.INVALID_ING;
import static com.ejuetc.agent.dto.ESignDTO.Status.REJECT;
import static com.ejuetc.agent.dto.ESignDTO.Status.SIGN_ING;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.spring.SpringUtil.getProperty;
import static com.ejuetc.commons.base.utils.ThreadUtils.invokeAll;
import static java.time.LocalDateTime.now;
import static java.time.format.DateTimeFormatter.ofPattern;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

import org.hibernate.annotations.Comment;
import org.hibernate.annotations.CompositeType;
import org.hibernate.annotations.Parameter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import com.alibaba.fastjson.JSONObject;
import com.ejuetc.agent.domain.statement.Statement;
import com.ejuetc.agent.domain.user.User;
import com.ejuetc.agent.dto.ESignDTO;
import com.ejuetc.agent.dto.StatementDTO;
import com.ejuetc.agent.integration.esign.ESignComponent;
import com.ejuetc.commons.base.component.OssComponent;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.usertype.EntityUT;
import com.ejuetc.commons.base.usertype.ListUT;

import jakarta.persistence.AttributeOverride;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Getter
@Slf4j
@Entity
@NoArgsConstructor
@Comment("电子签")
@Table(name = "tb_esign") //,uniqueConstraints = @UniqueConstraint(columnNames = {"target_type", "target_id"})
@Where(clause = "logic_delete = 0")
public class ESign extends BaseEntity<ESign> implements Serializable {

    @Id
    @GeneratedValue(generator = "esign_id")
    @SequenceGenerator(name = "esign_id", sequenceName = "seq_esign_id")
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    protected ESignDTO.Status status = INIT;

    @Embedded
    @CompositeType(EntityUT.class)
    @AttributeOverride(name = "type", column = @Column(name = "target_type", columnDefinition = "varchar(127) COMMENT '目标类型'"))
    @AttributeOverride(name = "id", column = @Column(name = "target_id", columnDefinition = "bigint(20) COMMENT '目标外键'"))
    private BaseEntity target;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", columnDefinition = "bigint(20) COMMENT '用户外键'")
    protected User user;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "template_id", columnDefinition = "bigint(20) COMMENT '合同模板Id'")
    protected Template template;

    @Column(name = "doc_param", columnDefinition = "json COMMENT '生成文件请求参数'")
    private String docParam;

    @Column(name = "doc_id", columnDefinition = "varchar(64) COMMENT '文件ID'")
    private String docId;

    @Column(name = "doc_name", columnDefinition = "varchar(64) COMMENT '文件名'")
    private String docName;

    @Column(name = "doc_url", columnDefinition = "varchar(512) COMMENT '文件下载地址'")
    private String docUrl;

    @Column(name = "flow_param", columnDefinition = "json COMMENT '生成文件请求参数'")
    private String flowParam;

    @Column(name = "flow_id", columnDefinition = "varchar(64) COMMENT '文件ID'")
    private String flowId;

    @Column(name = "psn_id", columnDefinition = "varchar(64) COMMENT '个人发起方账号ID|经办人账号ID'")
    private String psnId;

    @Column(name = "flow_url", columnDefinition = "varchar(512) COMMENT '文件下载地址'")
    private String flowUrl;

    @Column(name = "flow_short_url", columnDefinition = "varchar(512) COMMENT '文件下载地址(短链接)'")
    private String flowShortUrl;

    @Type(value = ListUT.class, parameters = @Parameter(name = "splitChar", value = "\n"))
    @Column(name = "complete_files", columnDefinition = "text COMMENT '下载完成的文件'")
    private List<String> completeFiles;

    @Column(name = "callback_result", columnDefinition = "varchar(63) COMMENT '签署结果'")
    private String callbackResult;

    @Column(name = "callback_desc", columnDefinition = "varchar(255) COMMENT '签署描述'")
    private String callbackDesc;

    @Column(name = "callback_body", columnDefinition = "json COMMENT '签署描述'")
    private String callbackBody;

    @Column(name = "remark", columnDefinition = "varchar(255) COMMENT '备注'")
    private String remark;

    @Column(name = "rescission_flow_param", columnDefinition = "json COMMENT '合同解约生成文件请求参数'")
    private String rescissionFlowParam;

    @Column(name = "rescission_flow_id", columnDefinition = "varchar(64) COMMENT '合同解约文件ID'")
    private String rescissionFlowId;

    @Column(name = "rescission_flow_url", columnDefinition = "varchar(512) COMMENT '合同解约文件下载地址'")
    private String rescissionFlowUrl;

    @Column(name = "rescission_flow_short_url", columnDefinition = "varchar(512) COMMENT '合同解约文件下载地址(短链接)'")
    private String rescissionFlowShortUrl;

    @Type(value = ListUT.class, parameters = @Parameter(name = "splitChar", value = "\n"))
    @Column(name = "rescission_complete_files", columnDefinition = "text COMMENT '合同解约下载完成的文件'")
    private List<String> rescissionCompleteFiles;

    public <Target extends BaseEntity & ESignTarget> ESign(User user, Template template, Target target) {
        this.user = user;
        this.template = template;
        this.target = target;
        getBean(EntityManager.class).persist(this);
    }

    @SneakyThrows
    public void exec() {
        if (status != INIT) throw new CodingException("电子签[%s]当前状态[%s]不能发起签约!", id, status);
        if (this.target instanceof Statement s) this.target = s;
        this.docParam = template.getDocParam().parse(this);
        JSONObject createDoc = call("/v3/files/create-by-doc-template", docParam);
        this.docId = createDoc.getString("fileId");
        for (int i = 0; i < 5; i++) {
            JSONObject queryData = call("/v3/files/" + docId);
            String status = queryData.getString("fileStatus");
            if ("2".equalsIgnoreCase(status) || "5".equalsIgnoreCase(status)) {
                this.docUrl = queryData.getString("fileDownloadUrl");
                this.docName = queryData.getString("fileName");
                break;
            }
            TimeUnit.SECONDS.sleep(2);
        }

        this.flowParam = template.getFlowParam().parse(this);
        JSONObject data = call("/v3/sign-flow/create-by-file", flowParam);
        this.flowId = data.getString("signFlowId");
        JSONObject queryFlowUrl = call(
                "/v3/sign-flow/" + flowId + "/sign-url",
                Map.of(
                        "clientType", "ALL",
                        "needLogin", false,
                        "urlType", 2,
                        "operator", Map.of(
                                "psnAccount", user.getContactMobile()
                        )
                )
        );
        this.flowShortUrl = queryFlowUrl.getString("shortUrl");
        this.flowUrl = queryFlowUrl.getString("url");

        JSONObject querySignDetail = call("/v3/sign-flow/" + flowId + "/detail");
        JSONObject signers = querySignDetail.getJSONArray("signers").getJSONObject(0);
        this.psnId = switch (signers.getIntValue("signerType")) {
            case 0 -> signers.getJSONObject("psnSigner").getString("psnId");
            case 1 -> signers.getJSONObject("orgSigner").getJSONObject("transactor").getString("psnId");
            default -> throw new CodingException("查询签约详情应答信息有误:\n%s", querySignDetail);
        };

        this.status = SIGN_ING;
    }

    public void callback(JSONObject bodyObj) {
        if (status == INVALID_ING) {
            if (bodyObj.getString("signFlowStatus").equals("2")) {
                return;
            }
            String callbackDesc = bodyObj.getString("statusDescription");
            ((ESignTarget) target).signResult(false, "解约流程-签约失败[%s]".formatted(callbackDesc), null);
            this.status = COMPLE;
            return;
        }
        if (status != SIGN_ING)
            throw new CodingException("电子签[%s]当前状态[%s]不能执行回调!", id, status);

        String signResult = bodyObj.getString("signFlowStatus");
        if (signResult.equals(this.callbackResult)) {
            log.info("重复回调，忽略:\n" + bodyObj);
            return;
        }

        this.callbackResult = signResult;
        this.callbackDesc = bodyObj.getString("statusDescription");
        this.callbackBody = bodyObj.toJSONString();
        boolean succ = signResult.equals("2");
        if (succ) {
            JSONObject data = call("/v3/sign-flow/" + flowId + "/file-download-url");
            List<Callable<String>> convertUrls = data.getJSONArray("files").stream().map(f -> (Callable<String>) () ->
                    getBean(OssComponent.class).urlConvert(
                            ((JSONObject) f).getString("downloadUrl"),
                            "contract/" + now().format(ofPattern("yyyyMMdd")) + "/" + UUID.randomUUID() + "/" + ((JSONObject) f).getString("fileName")
                    )
            ).toList();
            completeFiles = invokeAll(convertUrls);
        }
        this.status = succ ? COMPLE : REJECT;

        ((ESignTarget) target).signResult(succ, "签约[%s]".formatted(callbackDesc), completeFiles);

    }

    public void callback4Rescission(JSONObject bodyObj) {
        if (status != INVALID_ING)
            throw new CodingException("电子签[%s]当前状态[%s]不能执行回调!", id, status);

        String signResult = bodyObj.getString("action");
        if (signResult.equals(this.callbackResult)) {
            log.info("重复回调，忽略:\n" + bodyObj);
            return;
        }
        this.callbackResult = signResult;
        this.callbackDesc = "合同解约成功";
        this.callbackBody = bodyObj.toJSONString();
        JSONObject data = call("/v3/sign-flow/" + flowId + "/file-download-url");
        List<Callable<String>> convertUrls = data.getJSONArray("files").stream().map(f -> (Callable<String>) () ->
                getBean(OssComponent.class).urlConvert(
                        ((JSONObject) f).getString("downloadUrl"),
                        "contract/" + now().format(ofPattern("yyyyMMdd")) + "/" + UUID.randomUUID() + "/" + ((JSONObject) f).getString("fileName")
                )
        ).toList();
        rescissionCompleteFiles = invokeAll(convertUrls);
        ((ESignTarget) target).signResult(true, "解约成功[%s]".formatted(callbackDesc), completeFiles);

        this.status = INVALID;
    }


    private JSONObject call(String api) {
        return getBean(ESignComponent.class).call(api);
    }

    private JSONObject call(String api, Object body) {
        return getBean(ESignComponent.class).call(api, body);
    }

    public String getNotifyUrl() {
        return getProperty("esign.notifyUrl");
    }

    public void cancel(StatementDTO.Role role, String reason) {
        this.status = switch (status) {
            case REJECT -> REJECT;
            case SIGN_ING -> {
                JSONObject cancelResponse = call("/v3/sign-flow/" + flowId + "/revoke", Map.of("revokeReason", reason));
                if (cancelResponse.getIntValue("code") == 0) {
                    yield CANCEL;
                } else {
                    remark = cancelResponse.getString("message");
                    log.error("电子签{}撤销失败:\n{}", id, remark);
                    yield SIGN_ING;
                }
            }
            case COMPLE -> {
                // 暂不分角色进行处理，统一按运营人员身份进行操作
                this.rescissionFlowParam = template.getRescissionFlowParam().parse(this);
                JSONObject data = call("/v3/sign-flow/" + flowId + "/initiate-rescission", rescissionFlowParam);
                this.rescissionFlowId = data.getString("signFlowId");
                JSONObject queryFlowUrl = call(
                        "/v3/sign-flow/" + rescissionFlowId + "/sign-url",
                        Map.of(
                                "clientType", "ALL",
                                "needLogin", false,
                                "urlType", 2,
                                "operator", Map.of(
                                        "psnAccount", user.getContactMobile()
                                )
                        )
                );
                this.rescissionFlowShortUrl = queryFlowUrl.getString("shortUrl");
                this.rescissionFlowUrl = queryFlowUrl.getString("url");
                yield INVALID_ING;
            }
            default -> throw new CodingException("电子签[%s]当前状态[%s]不能取消签约!", id, status);
        };
    }
}
