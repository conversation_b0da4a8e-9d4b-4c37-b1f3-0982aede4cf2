package com.ejuetc.agent.domain.crm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.ejuetc.agent.api.statement.StatementPartnerAmountRO;
import com.ejuetc.agent.domain.clearing.ClearingRpt;
import com.ejuetc.agent.domain.statement.Statement;
import com.ejuetc.agent.domain.statement.StatementAccount;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.utils.StringUtils;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@Getter
@Entity
@Slf4j
@DiscriminatorValue("CHARGE_OFF")
@SubtypeCode(parent = CrmInstruct.class, code = "CHARGE_OFF", name = "oa财务核销")
@NoArgsConstructor
public class CrmInstruct4ChargeOff extends CrmInstruct{

    public CrmInstruct4ChargeOff(OaFinance oaFinance) {
        super(oaFinance);
    }

    @Override
    protected void doExec() {
        Map<String, Object> body = new HashMap<>();
        OaFinance4ChargeOff oaFinance4ChargeOff = (OaFinance4ChargeOff) this.getOaFinance();
        List<Statement> statements = oaFinance4ChargeOff.statements;
        body.put("interfaceType", 2);
        body.put("qkType", 1);
        body.put("qkNo", oaFinance4ChargeOff.getId());
        body.put("operatorUserCode", "36716");
        body.put("operatorUserName", "王方");
        body.put("cgfl1", "乐居专用");
        body.put("cgfl2", "乐居专用");
        body.put("cgfl3", "乐居专用");
        body.put("companyName", oaFinance4ChargeOff.getCompanyName());
        body.put("businessLicenseNo", oaFinance4ChargeOff.getCompanyNo());
        body.put("htdjNo", oaFinance4ChargeOff.getContractNo());
        body.put("kjhtFlag", "1");
        body.put("bankAccountCardCode", oaFinance4ChargeOff.getBankNo());
        body.put("recordDate", oaFinance4ChargeOff.chargeOffTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        body.put("fileList",
                List.of(
                        Map.of("fileName", "cash_out_" + this.getId(), "fileUrl", oaFinance4ChargeOff.getInvoiceUrl()),
                        Map.of("fileName", "cash_out_" + this.getId() + "_confirm", "fileUrl", oaFinance4ChargeOff.getInvoiceConfirmUrl()),
                        Map.of("fileName", "cash_out_" + this.getId() + "_statement", "fileUrl", oaFinance4ChargeOff.getStatementUrl())
                )
        );
        List<Map<String, Object>> qkDetailList = getQkDetailList(this.getType(), statements.stream().map(Statement::getId).collect(Collectors.toList()), null, null);
        body.put("qkDetailList", qkDetailList);
//        body.put("hxDetailList", List.of(Map.of("prePayNo", oaFinance4ChargeOff.getPrePayNo(), "prePayHtNo", oaFinance4ChargeOff.getPrePayContractNo(), "feeType", oaFinance4ChargeOff.getFeeType(), "payAmount", oaFinance4ChargeOff.getPayAmount(), "unCancelAmount", oaFinance4ChargeOff.getUnCancelAmount(), "cancelAmount", oaFinance4ChargeOff.getCancelAmount())));
        send("/ljuApi/etcFlowDlr/qk", body);
    }
}
