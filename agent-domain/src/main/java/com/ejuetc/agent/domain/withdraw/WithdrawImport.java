package com.ejuetc.agent.domain.withdraw;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ejuetc.agent.api.statement.WithdrawImportDetailPO;
import com.ejuetc.agent.api.statement.WithdrawImportPO;
import com.ejuetc.agent.dto.PersonalPayDTO;
import com.ejuetc.agent.dto.WithdrawImportDTO;
import com.ejuetc.commons.base.component.OssComponent;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.Table;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static java.time.LocalDateTime.now;
import static java.time.format.DateTimeFormatter.ofPattern;

@Slf4j
@Getter
@Entity
@NoArgsConstructor
@Comment("出款单导入")
@Table(name = "tb_withdraw_import")
@Where(clause = "logic_delete = 0")
public class WithdrawImport extends BaseEntity<WithdrawImport> {

    @Transient
    private static final DateFormat SDF_YYYY_MM_DD_HH_MM_SS = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
    @Transient
    private static final DecimalFormat decimalFormat = new DecimalFormat("0.00%");
    @Enumerated(EnumType.STRING)
    @Column(name = "processStatus")
    protected WithdrawImportDTO.ProcessStatus processStatus;
    @Column(name = "import_url", columnDefinition = "varchar(511) COMMENT '导入文件URL'")
    protected String importUrl;
    @Column(name = "file_name", columnDefinition = "varchar(511) COMMENT '导入文件名'")
    protected String fileName;

    @Column(name = "import_total_count", columnDefinition = "int(11) COMMENT '导入总条数'")
    protected Integer importTotalCount;

    @Column(name = "import_success_count", columnDefinition = "int(11) COMMENT '成功条数'")
    protected Integer importSuccessCount;

    @Column(name = "import_fail_count", columnDefinition = "int(11) COMMENT '失败条数'")
    protected Integer importFailCount;

    @Column(name = "file_content", columnDefinition = "json COMMENT '导入文件内容'")
    protected String fileContent;

    @Column(name = "export_url", columnDefinition = "varchar(511) COMMENT '导出文件URL'")
    protected String exportUrl;
    @Id
    @GeneratedValue(generator = "withdraw_import_id")
    @SequenceGenerator(name = "withdraw_import_id", sequenceName = "seq_withdraw_import_id")
    private Long id;
    @Column(name = "import_time", columnDefinition = "datetime COMMENT '导入时间'")
    private LocalDateTime importTime;
    @Transient
    private List<WithdrawImportDetailPO> detailList = new ArrayList<>();


    public WithdrawImport(WithdrawImportPO po) {
        this.importUrl = po.getFileUrl();
        this.fileName = po.getFileName();
        this.importTime = LocalDateTime.now();
        // 解析
        detailList = ParseFileContent(importUrl);
        updateFileContent();
        this.importTotalCount = detailList.size();
        if (importTotalCount == 0) {
            this.processStatus = WithdrawImportDTO.ProcessStatus.UNDO;
        } else {
            this.processStatus = WithdrawImportDTO.ProcessStatus.DOING;
        }
    }

    private static List<List<Object>> transToExcelData(List<WithdrawImportDetailPO> etcPersonalPayImportDetailList){
        List<List<Object>> excelDataList = etcPersonalPayImportDetailList.stream().filter(e->e.getStatus()== PersonalPayDTO.Status.FAIL).map(e -> {
            List<Object> list  = Arrays.asList(e.getBatchNo()
                    ,e.getPlatformOrderNo()
                    ,e.getPlatformEnterpriseOrderNo()
                    ,e.getCreateOrderTime() ==null?null:SDF_YYYY_MM_DD_HH_MM_SS.format(e.getCreateOrderTime())
                    ,e.getOrderCompleteTime()==null?null:SDF_YYYY_MM_DD_HH_MM_SS.format(e.getOrderCompleteTime())
                    ,e.getCollectionAccountNo()
                    ,e.getCertificateNo()
                    ,e.getCollectionAccountName()
                    ,e.getBankReservedMobileNo()
                    ,e.getIntegratedServiceSubject()
                    ,e.getPlatformEnterpriseName()
                    ,e.getPaymentBasicServiceFeeAmount()
                    ,e.getUserReceivedAmount()
                    ,e.getBonusServiceFeeReceivedAmount()
                    ,e.getUserBonusServiceFee()
                    ,e.getOrderStatus()
                    ,e.getStatusDescription()
                    ,e.getRemark()
                    ,e.getPaymentPath()
                    ,e.getUserBonusServiceRate()==null?null:decimalFormat.format(e.getUserBonusServiceRate())
                    ,e.getPlatformEnterpriseBonusServiceRate()==null?null:decimalFormat.format(e.getPlatformEnterpriseBonusServiceRate())
                    ,e.getDeductedBonusServiceFeeAmount()
                    ,e.getRefundTime()
                    ,e.getRefundStatus()
                    ,e.getRefundedUserReceivedAmount()
                    ,e.getRefundedBonusServiceFeeAmount()
                    ,e.getRefundedUserBonusServiceFee()
                    ,e.getCollectionBankName()
                    ,e.getError()
            );
            return list;
        }).collect(Collectors.toList());
        return excelDataList;
    }

    public void updateFileContent() {
        try {
            ObjectMapper mapper = new ObjectMapper();
            this.fileContent = mapper.writeValueAsString(detailList);
        } catch (Exception e) {
            log.error("WithdrawImport Exception url=" + importUrl, e.getMessage());
        }
    }

    public void updateImportCntAndStatus(Integer failCnt) {
        this.importFailCount = failCnt;
        this.importSuccessCount = detailList.size() - failCnt;
        updateFileContent();
        this.processStatus = WithdrawImportDTO.ProcessStatus.FINISH;
        try {
            if (failCnt > 0) {
                this.exportUrl = exportExcel();
            }
        } catch (Exception e) {
            log.error("WithdrawImport exportPersonalPay Exception" , e.getMessage());
        }
    }

    private String exportExcel() throws Exception {
        // 1.转换成excel数据
        List<List<Object>> excelDataList = transToExcelData(JSONArray.parseArray(this.fileContent,WithdrawImportDetailPO.class));
        if(excelDataList.isEmpty()){
            return null;
        }
        // 2.获取模板，解析Excel
        URL excelUrl = new URL("https://etc-agent.oss-cn-shanghai.aliyuncs.com/personalPayFail");
        ByteArrayInputStream inputStream = new ByteArrayInputStream(excelUrl.openStream().readAllBytes());
        Workbook workbook = new XSSFWorkbook(inputStream);
        Sheet sheet = workbook.getSheetAt(0);

        // 3.填充数据
        for (int i = 0; i < excelDataList.size(); i++) {
            List<Object> data = excelDataList.get(i);
            Row row = sheet.createRow(i+1);
            for (int j = 0; j < data.size(); j++) {
                Cell cell = row.createCell(j);
                if (data.get(j) != null) {
                    cell.setCellValue(data.get(j).toString());
                }
            }
        }
        // 4.写入ByteArrayOutputStream，创建InputStream
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        workbook.write(byteArrayOutputStream);
        InputStream inputStreamNew = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());

        return getBean(OssComponent.class).putOSS("personalPay/" + now().format(ofPattern("yyyyMMdd")) + "/" + UUID.randomUUID() + "/" + fileName,inputStreamNew);
    }

    public static List<WithdrawImportDetailPO> ParseFileContent(String url) {

        List<WithdrawImportDetailPO> detailList = new ArrayList<>();
        try {
            URL excelUrl = new URL(url);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(excelUrl.openStream().readAllBytes());

            // 解析Excel
            Workbook workbook = new XSSFWorkbook(inputStream);
            Sheet sheet = workbook.getSheetAt(0);

            sheet.forEach((Row row) -> {
                if (row == null || row.getRowNum() < 1 || row.getRowNum() > sheet.getLastRowNum()) {
                    return;
                }
                WithdrawImportDetailPO data = new WithdrawImportDetailPO();
//                data.setBatchId(etcPersonalPayImport.getId());
//                data.setStatus(EtcPersonalPayImportDetailStatus.FAIL);
                data.setBatchNo(getCellValueAsString(row.getCell(0)).replaceAll("\t","").replaceAll("`","").trim());
                data.setPlatformOrderNo(getCellValueAsString(row.getCell(1)).replaceAll("\t","").replaceAll("`","").trim());
                data.setPlatformEnterpriseOrderNo(getCellValueAsString(row.getCell(2)).replaceAll("\t","").replaceAll("`","").trim());
                data.setCreateOrderTime(row.getCell(3) != null ? row.getCell(3).getDateCellValue() : null);
                data.setOrderCompleteTime(row.getCell(4) != null ? row.getCell(4).getDateCellValue() : null);
                data.setCollectionAccountNo(getCellValueAsString(row.getCell(5)));
                data.setCertificateNo(getCellValueAsString(row.getCell(6)));
                data.setCollectionAccountName(getCellValueAsString(row.getCell(7)));
                data.setBankReservedMobileNo(getCellValueAsString(row.getCell(8)));
                data.setIntegratedServiceSubject(getCellValueAsString(row.getCell(9)));
                data.setPlatformEnterpriseName(getCellValueAsString(row.getCell(10)));
                data.setPaymentBasicServiceFeeAmount(getCellValueAsString(row.getCell(11)));
                data.setUserReceivedAmount(getCellValueAsString(row.getCell(12)));
                data.setBonusServiceFeeReceivedAmount(getCellValueAsString(row.getCell(13)));
                data.setUserBonusServiceFee(getCellValueAsString(row.getCell(14)));
                data.setOrderStatus(getCellValueAsString(row.getCell(15)));
                data.setStatusDescription(getCellValueAsString(row.getCell(16)));
                data.setRemark(getCellValueAsString(row.getCell(17)));
                data.setPaymentPath(getCellValueAsString(row.getCell(18)));
                data.setUserBonusServiceRate(row.getCell(19) != null ? new BigDecimal(row.getCell(19).getNumericCellValue()) : null);
                data.setPlatformEnterpriseBonusServiceRate(row.getCell(20) != null ? new BigDecimal(row.getCell(20).getNumericCellValue()) : null);
                data.setDeductedBonusServiceFeeAmount(getCellValueAsString(row.getCell(21)));
                data.setRefundTime(row.getCell(22) == null ? null : ("-/-".equals(row.getCell(22).getStringCellValue())?null:row.getCell(22).getDateCellValue()));
                data.setRefundStatus(getCellValueAsString(row.getCell(23)));
                data.setRefundedUserReceivedAmount(getCellValueAsString(row.getCell(24)));
                data.setRefundedBonusServiceFeeAmount(getCellValueAsString(row.getCell(25)));
                data.setRefundedUserBonusServiceFee(getCellValueAsString(row.getCell(26)));
                data.setCollectionBankName(getCellValueAsString(row.getCell(27)));
                detailList.add(data);
            });
            workbook.close();

        } catch (IOException e) {
            log.error("ParseFileContent Exception url=" + url, e.getMessage());
        }
        return detailList;
    }

    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }
        String cellValue = "";
        switch (cell.getCellType()) {
            case STRING:
                cellValue = cell.getStringCellValue();
                break;
            case NUMERIC:
                // 设置数字格式，避免显示为科学计数法
                DataFormatter dataFormatter = new DataFormatter();
                cellValue = dataFormatter.formatCellValue(cell);
                break;
            case BOOLEAN:
                cellValue = String.valueOf(cell.getBooleanCellValue());
                break;
            case FORMULA:
                cellValue = String.valueOf(cell.getCellFormula());
                break;
            default:
                cellValue = "";
                break;
        }
        return cellValue;
    }

}
