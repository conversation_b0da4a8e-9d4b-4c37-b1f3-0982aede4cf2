package com.ejuetc.agent.domain.statement;

import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import static jakarta.persistence.LockModeType.PESSIMISTIC_WRITE;

public interface StatementInvoiceRpt extends JpaRepositoryImplementation<StatementInvoice, Long> {


}
