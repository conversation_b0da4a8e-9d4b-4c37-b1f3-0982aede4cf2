package com.ejuetc.agent.domain.statement;

import com.ejuetc.agent.domain.contract.Contract;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Entity
@Getter
@DiscriminatorValue("YZH_ACCOUNT")
@SubtypeCode(parent = Contract.class, code = "YZH_ACCOUNT", name = "云账户")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class StatementAccount4Yzh extends StatementAccount {

    @Override
    public void launchPay() {

    }
}