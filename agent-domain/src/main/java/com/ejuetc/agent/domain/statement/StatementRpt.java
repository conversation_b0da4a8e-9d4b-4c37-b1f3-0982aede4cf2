package com.ejuetc.agent.domain.statement;

import com.ejuetc.agent.api.statement.SelPersonalPayDonePO;
import com.ejuetc.agent.dto.StatementDTO;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.List;

import static jakarta.persistence.LockModeType.PESSIMISTIC_WRITE;

public interface StatementRpt extends JpaRepositoryImplementation<Statement, Long> {


    @Modifying
    @Query(value = """
            insert into tb_statement(id,statement_no, statement_month,user_id,tax_rate,clearing_item_count,comm,comm_pre_tax,disburse_fee,excess_comm,normal_comm,report_after_tax,report_pre_tax,statement_amount,status,statement_batch)
            select NEXTVAL(seq_statement_id),
                   statement_no,
                   SUBSTRING_INDEX(SUBSTRING_INDEX(statement_no, '_', 1), '_', -1) AS month,
                   SUBSTRING_INDEX(SUBSTRING_INDEX(statement_no, '_', 2), '_', -1) AS user_id,
                   SUBSTRING_INDEX(SUBSTRING_INDEX(statement_no, '_', 3), '_', -1) AS tax_rate,
                   count(ci.id),
                   sum(ci.comm),
                   sum(ci.comm_pre_tax),
                   sum(ci.disburse_fee),
                   sum(ci.excess_comm),
                   sum(ci.normal_comm),
                   sum(ci.report_after_tax),
                   sum(ci.report_pre_tax),
                   sum(ci.statement_amount),
                   'CHECK_WAIT',
                   :statementBatch
            from tb_clearing ci
            where statement_batch = :statementBatch
            group by statement_no
            """, nativeQuery = true)
    int insertStatement(String statementBatch);

    @Lock(PESSIMISTIC_WRITE)
    Statement findAndLockById(Long id);

    @Query("""
            FROM Statement s 
            LEFT JOIN User u on s.user.id = u.id
            WHERE s.status = 'PAY_DONE'
            AND u.type = 'PERSONAL'
            AND (s.oaFinance.id is null or s.oaFinance.id = -1)
            and (:#{#po.statementNo} is null or s.statementNO = :#{#po.statementNo})
            and (:#{#po.batchNo} is null or s.invoiceBatchNO = :#{#po.batchNo})
            and (:#{#po.userName} is null or u.personalName = :#{#po.userName})
            order by s.payTime
            """)
    List<Statement> selPersonalPayDone(SelPersonalPayDonePO po);

    List<Statement> findByStatementBatch(String statementBatch);

    List<Statement> findByStatus(StatementDTO.Status status);
}
