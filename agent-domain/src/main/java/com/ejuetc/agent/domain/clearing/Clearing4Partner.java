package com.ejuetc.agent.domain.clearing;

import com.ejuetc.agent.domain.contract.Contract;
import com.ejuetc.agent.domain.deal.Deal;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

import static java.math.BigDecimal.ONE;

@Slf4j
@Getter
@Entity
@DiscriminatorValue("PARTNER")
@SubtypeCode(parent = Contract.class, code = "PARTNER", name = "合伙佣金清分")
@NoArgsConstructor
public class Clearing4Partner extends Clearing {

    public Clearing4Partner(Deal deal) {
        super(deal, deal.getParentPartnerClearing());
        this.user = this.deal.getPartnerUser();
        this.taxRate = user.getTaxRate();
        this.excessCommRatio = this.deal.getAgentExcessCommRatio() != null ? ONE.subtract(this.deal.getAgentExcessCommRatio()) : null;
        this.normalCommRate = this.deal.getPartnerCommRate();
        this.discountPrice = this.deal.getAgentDiscountPrice();
    }


    protected BigDecimal calcExcessComm() {
        return excessAmount.multiply(excessCommRatio).multiply(normalCommRate);
    }

    protected BigDecimal calcNormalComm() {
        return deal.getStandardPrice().subtract(deal.getTmServiceFee())
                .multiply(ONE.subtract(deal.getAgentNormalCommRate()))
                .multiply(normalCommRate)
                .add(discountPrice.multiply(normalCommRate));
    }


}
