package com.ejuetc.agent.domain.contract;

import com.ejuetc.agent.api.contract.CreateAgentContractPO;
import com.ejuetc.agent.domain.deal.Deal;
import com.ejuetc.agent.domain.esign.ESign;
import com.ejuetc.agent.domain.price.Price;
import com.ejuetc.agent.dto.ContractDTO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.exception.CodingException;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;

import java.util.ArrayList;
import java.util.List;

import static com.alibaba.fastjson.JSON.parseObject;
import static com.ejuetc.agent.dto.ContractDTO.Status.*;

@Slf4j
@Entity
@Getter
@DiscriminatorValue("AGENT")
@SubtypeCode(parent = Contract.class, code = "AGENT", name = "代理协议")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class AgentContract extends Contract {

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "esign_id", columnDefinition = "bigint(20) COMMENT '关联电子签Id'")
    protected ESign eSign;

    @ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinTable(name = "tb_contract_price",
            joinColumns = @JoinColumn(name = "contract_id", columnDefinition = "bigint(20) COMMENT '合同外键'"),
            inverseJoinColumns = @JoinColumn(name = "price_id", columnDefinition = "bigint(20) COMMENT '价格外键'")
    )
    @Comment("邀请码邀请创建的合同")
    private List<Price> prices = new ArrayList<>();

    public AgentContract(CreateAgentContractPO po) {
        super(po);

        if (po.getPricePOS() != null && !po.getPricePOS().isEmpty()) {
            po.getPricePOS().forEach(p -> addPrice(new Price(p)));
        } else {
            this.template.getPrices().forEach(this::addPrice);

        }

        makeOperatorInvitation();
    }


    private void addPrice(Price price) {
        prices.add(price);
    }

    public boolean hasCityPrice(String cityCode) {
        return prices.stream().anyMatch(p -> p.getCityCode().equals(cityCode));
    }

    public Price findPrice(Deal deal) {
        return prices.stream().filter(p -> p.matchDeal(deal))
                .findFirst()
                .orElseThrow(() -> new CodingException("代理商合同[%s]无匹配交易[%s]的代理价配置!", this, deal));
    }

    @Override
    public void exec() {
        if (status != INIT)
            throw new CodingException("合同状态异常");

        if (getSubType().equals(ContractDTO.SubType.AGENT_NORMAL)) {
            this.eSign = new ESign(getUser(), getTemplate(), this);
            eSign.exec();
            status = SIGN_ING;
        } else {
            status = EFFECTIVE;
        }
    }

    @Override
    public boolean serviceOverlap(Contract po) {
        return ((AgentContract) po).prices.stream().anyMatch(p -> hasCityPrice(p.getCityCode()));
    }

    @Override
    public boolean isApplyCity(String cityCode) {
        return prices.stream().anyMatch(p -> p.getCityCode().equals(cityCode));
    }
}
