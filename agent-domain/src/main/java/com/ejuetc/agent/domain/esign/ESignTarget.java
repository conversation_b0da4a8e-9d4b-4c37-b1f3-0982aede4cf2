package com.ejuetc.agent.domain.esign;

import com.ejuetc.agent.domain.user.User;

import java.util.List;

import static com.ejuetc.agent.dto.ESignDTO.Status.COMPLE;

public interface ESignTarget {
    default void signResult(ESign eSign) {
        signResult(eSign.getStatus() == COMPLE, eSign.getCallbackDesc(), eSign.getCompleteFiles());
    }

    void signResult(Boolean signResult, String description, List<String> completeFiles);

}
