package com.ejuetc.agent.domain.crm;

import com.ejuetc.agent.api.statement.StatementPartnerAmountRO;
import com.ejuetc.agent.domain.clearing.ClearingRpt;
import com.ejuetc.agent.domain.contract.Contract;
import com.ejuetc.agent.domain.statement.Statement;
import com.ejuetc.agent.domain.statement.StatementAccount;
import com.ejuetc.agent.domain.statement.StatementRpt;
import com.ejuetc.agent.dto.ContractDTO;
import com.ejuetc.agent.dto.CrmInstructDTO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.utils.StringUtils;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@Getter
@Entity
@Slf4j
@DiscriminatorValue("CASH_OUT")
@SubtypeCode(parent = CrmInstruct.class, code = "CASH_OUT", name = "oa财务请款")
@NoArgsConstructor
public class CrmInstruct4CashOut extends CrmInstruct{

    public CrmInstruct4CashOut(OaFinance oaFinance) {
        super(oaFinance);
    }

    @Override
    protected void doExec() {
        Map<String, Object> body = new HashMap<>();
        OaFinance4CashOut oaFinance4CashOut = (OaFinance4CashOut) this.getOaFinance();
        List<Statement> statements = oaFinance4CashOut.getStatements();
        List<StatementAccount> statementAccounts = statements.stream().flatMap(statement -> statement.getStatementAccounts().stream()).collect(Collectors.toList());
        StatementAccount statementAccount = statementAccounts.get(0);
        Statement statement = statementAccount.getStatement();
        body.put("interfaceType", 1);
        body.put("qkType", 1);
        body.put("qkNo", oaFinance4CashOut.getId());
        body.put("operatorUserCode", "112559");
        body.put("operatorUserName", "陈芸");
        body.put("cgfl1", "服务");
        body.put("cgfl2", "业务资源");
        body.put("cgfl3", "房友门店");
        body.put("companyName", statementAccount.getUser().getCertName());
        body.put("businessLicenseNo", statementAccount.getUser().getCompanyNum());
        body.put("htdjNo", statementAccount.getUser().getContracts().stream().map(Contract::getOaNum).filter(oaNum -> !StringUtils.isBlank(oaNum)).findFirst().orElse(null));
        body.put("kjhtFlag", "1");
        body.put("bankAccountCardCode", statementAccount.getAccount().getAccountNO());
        body.put("recordDate", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        String agentFile = statement.getUser().getContracts().stream().filter(t -> t.getType() == ContractDTO.Type.AGENT && t.getStatus() == ContractDTO.Status.EFFECTIVE)
                .map(Contract::getCompleteFiles)
                .flatMap(List::stream)
                .findFirst().orElse("");
        List<Object> fileList = new ArrayList<>();
        fileList.add(Map.of("fileName", "cash_out_" + this.getId(), "fileUrl", statement.getInvoiceUrl()));
        fileList.add(Map.of("fileName", "cash_out_" + this.getId() + "_confirm", "fileUrl", statement.getInvoiceConfirmUrl()));
        fileList.add(Map.of("fileName", "cash_out_" + this.getId() + "_statement", "fileUrl", statement.getESign().getCompleteFiles().get(0)));
        fileList.add(Map.of("fileName", "cash_out_" + this.getId() + "_agent", "fileUrl", agentFile));
        String supplFile = statement.getUser().getContracts().stream()
                .filter(t -> t.getType() == ContractDTO.Type.AGENT && t.getStatus() == ContractDTO.Status.EFFECTIVE)
                .map(t -> Optional.ofNullable(t.getSupplESign())
                        .map(s -> s.getCompleteFiles().stream().findFirst().orElse(""))
                        .orElseGet(() -> Optional.ofNullable(t.getSupplEsignUrl()).orElse(null)))
                .filter(Objects::nonNull)
                .findFirst()
                .orElse("");
        if (StringUtils.notBlank(supplFile)) {
            fileList.add(Map.of("fileName", "cash_out_" + this.getId() + "_suppl", "fileUrl", supplFile));
        }
        body.put("fileList", fileList);
        List<Map<String, Object>> qkDetailList = getQkDetailList(this.getType(),List.of(statement.getId()), statement.getInvoicePreTax(), statement.getInvoiceAfterTax());
        body.put("qkDetailList", qkDetailList);
        send("/ljuApi/etcFlowDlr/qk", body);
    }

}
