package com.ejuetc.agent.domain.account;

import jakarta.persistence.LockModeType;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import jakarta.persistence.QueryHint;

import java.util.List;
import java.util.Optional;

public interface AccountRpt extends JpaRepositoryImplementation<Account, Long> {


}
