package com.ejuetc.agent.domain.esign;

import java.util.Optional;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

public interface ESignRpt extends JpaRepositoryImplementation<ESign, Long> {
	Optional<ESign> findByFlowId(String signFlowId);
	
	@Query("""
            select t from ESign t
            where (t.flowId = ?1 or t.rescissionFlowId = ?1)
            """)
    Optional<ESign> findByFlowIdOrRescissionFlowId(String signFlowId);
}
