package com.ejuetc.agent.domain.clearing;

import com.ejuetc.agent.domain.user.User;
import com.ejuetc.commons.base.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

import static java.math.BigDecimal.ONE;
import static java.math.BigDecimal.ZERO;
import static java.math.RoundingMode.DOWN;
import static lombok.AccessLevel.PROTECTED;

@Getter
@MappedSuperclass
public abstract class ClearingStatement<T extends ClearingStatement<?>> extends BaseEntity<T> {
    static final BigDecimal PT10 = new BigDecimal("0.1");
    static final BigDecimal PT6 = new BigDecimal("0.06");
    static final BigDecimal PT12 = new BigDecimal("0.12");
    static final BigDecimal PT7_5 = new BigDecimal("0.075");


    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", columnDefinition = "bigint(20) COMMENT '所属主体外键'")
    protected User user;

    @Column(name = "excess_comm", columnDefinition = "decimal(20,2) default 0 COMMENT '超售佣金'")
    protected BigDecimal excessComm = ZERO;

    @Column(name = "normal_comm", columnDefinition = "decimal(20,2) default 0 COMMENT '普通佣金'")
    protected BigDecimal normalComm = ZERO;

    @Column(name = "comm", columnDefinition = "decimal(20,2) default 0 COMMENT '佣金总额'")
    protected BigDecimal comm = ZERO;

    @Column(name = "tax_rate", columnDefinition = "decimal(20,2) default 0.06 COMMENT '税率'")
    protected BigDecimal taxRate = ZERO;

    @Setter(PROTECTED)
    @Column(name = "report_pre_tax", columnDefinition = "decimal(20,2) default 0 COMMENT '报表税前金额'")
    protected BigDecimal reportPreTax = ZERO;

    @Setter(PROTECTED)
    @Column(name = "report_after_tax", columnDefinition = "decimal(20,2) default 0 COMMENT '报表税后金额'")
    protected BigDecimal reportAfterTax = ZERO;

    @Setter(PROTECTED)
    @Column(name = "comm_pre_tax", columnDefinition = "decimal(20,2) default 0 COMMENT '税前返佣金额'")
    protected BigDecimal commPreTax = ZERO;

    @Setter(PROTECTED)
    @Column(name = "disburse_fee", columnDefinition = "decimal(20,2) default 0 COMMENT '出款服务费'")
    protected BigDecimal disburseFee = ZERO;

    public void calcTaxValue() {
        this.commPreTax = switch (user.getType()) {
            case COMPANY -> reportPreTax;
            case PERSONAL -> reportPreTax.divide(ONE.add(PT6),DOWN);
        };
        this.disburseFee = comm.subtract(commPreTax);
    }

    public <PB extends ClearingStatement<?>, CB extends ClearingStatement<?>> void calcTaxValue4Last(PB parent, List<CB> children) {
        BigDecimal otherCommPreTax = children.stream().filter(cb -> !cb.equals(this)).map(ClearingStatement::getCommPreTax).reduce(ZERO, BigDecimal::add);
        BigDecimal otherDisburseFee = children.stream().filter(cb -> !cb.equals(this)).map(ClearingStatement::getDisburseFee).reduce(ZERO, BigDecimal::add);

        this.commPreTax = parent.getCommPreTax().subtract(otherCommPreTax);
        this.disburseFee = parent.getDisburseFee().subtract(otherDisburseFee);
    }


}
