package com.ejuetc.agent.domain.deal;

import com.ejuetc.agent.api.deal.CreateClueConsumeDealPO;
import com.ejuetc.agent.domain.clearing.Clearing;
import com.ejuetc.agent.domain.contract.AgentContract;
import com.ejuetc.agent.domain.contract.BrokerContract;
import com.ejuetc.agent.domain.contract.Contract;
import com.ejuetc.agent.domain.contract.PartnerContract;
import com.ejuetc.agent.domain.invitation.Invitation;
import com.ejuetc.agent.domain.price.Price;
import com.ejuetc.agent.domain.user.User;
import com.ejuetc.agent.dto.ContractDTO;
import com.ejuetc.agent.dto.DealDTO;
import com.ejuetc.agent.dto.UserDTO;
import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.valueobj.Memo;

import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Getter
@Entity
@DiscriminatorValue("CLUE_CONSUME")
@SubtypeCode(parent = Contract.class, code = "CLUE_CONSUME", name = "线索消耗")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class ClueConsumeDeal extends Deal {

    @Column(name = "goods_name", columnDefinition = "varchar(255) COMMENT '商品名称'")
    private String goodsName;

    @Column(name = "order_num", columnDefinition = "varchar(255) COMMENT 'MALL订单号'")
    private String orderNum;

    public ClueConsumeDeal(CreateClueConsumeDealPO po) {
        super(po, getBean(DealRpt.class).findByTypeAndSrcId(DealDTO.Type.CLUE_BUY, po.getParentSrcId()).orElseThrow(() -> new CodingException("线索购买订单[" + po.getParentSrcId() + "]不存在")));
        this.goodsName = po.getGoodsName();
        this.orderNum = po.getOrderNum();
    }

}
