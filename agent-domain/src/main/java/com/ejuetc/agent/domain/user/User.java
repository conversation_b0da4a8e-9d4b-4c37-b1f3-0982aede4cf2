package com.ejuetc.agent.domain.user;

import com.alibaba.fastjson.JSONObject;
import com.ejuetc.agent.api.account.CreateAccountPO;
import com.ejuetc.agent.api.account.CreateBankAccountPO;
import com.ejuetc.agent.api.user.EditUserPO;
import com.ejuetc.agent.api.user.UserBindNotifyPo;
import com.ejuetc.agent.domain.account.Account;
import com.ejuetc.agent.domain.contract.Contract;
import com.ejuetc.agent.domain.invitation.Invitation;
import com.ejuetc.agent.dto.ContractDTO;
import com.ejuetc.agent.dto.UserDTO;
import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.commons.base.clazz.ClassUtils;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.integration.user.MerchantApi;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.OptimisticLock;
import org.hibernate.annotations.Where;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ejuetc.agent.dto.ContractDTO.Type.BROKER;
import static com.ejuetc.agent.dto.UserDTO.Status.*;
import static com.ejuetc.commons.base.clazz.ClassUtils.caseToSubType;
import static com.ejuetc.commons.base.spring.SpringUtil.*;
import static com.ejuetc.commons.base.utils.ThreadUtils.asyncExec;
import static java.time.LocalDateTime.now;
import static org.hibernate.internal.util.collections.CollectionHelper.toMap;

@Slf4j
@Getter
@Entity
@NoArgsConstructor
@Comment("用户")
@Table(name = "tb_user")
@Where(clause = "logic_delete = 0")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", columnDefinition = "enum('COMPANY','PERSONAL') COMMENT '用户类型'")
public abstract class User extends BaseEntity<User> {
    @Id
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", insertable = false, updatable = false)
    private UserDTO.Type type;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", columnDefinition = "enum('INIT','AUTH_ING','AUTH_SUCC','AUTH_FAIL') default 'INIT' COMMENT '认证状态'", nullable = false)
    private UserDTO.Status status = INIT;

    @Column(name = "bind_xy_rent", nullable = false, columnDefinition = "bit(1) DEFAULT b'0' COMMENT '是否绑定闲鱼租房账号'")
    private Boolean bindXYRent = false;

    @Column(name = "bind_xy_sale", nullable = false, columnDefinition = "bit(1) DEFAULT b'0' COMMENT '是否绑定闲鱼二手房账号'")
    private Boolean bindXYSale = false;

    @Column(name = "employee_flag", nullable = false, columnDefinition = "bit(1) DEFAULT b'0' COMMENT '是否是员工'")
    private Boolean employeeFlag = false;

    @Column(name = "test_flag", nullable = false, columnDefinition = "bit(1) DEFAULT b'0' COMMENT '是否是测试数据'")
    private Boolean testFlag = false;

    @Column(name = "contact_name", columnDefinition = "varchar(63) COMMENT '联系人姓名'")
    private String contactName;

    @Column(name = "contact_mobile", columnDefinition = "varchar(63) COMMENT '联系人手机号'")
    private String contactMobile;

    @Column(name = "contact_address", columnDefinition = "varchar(255) COMMENT '联系地址'")
    private String contactAddress;

    @Column(name = "personal_name", columnDefinition = "varchar(63) COMMENT '身份证姓名'")
    private String personalName;

    @Column(name = "personal_num", columnDefinition = "varchar(63) COMMENT '身份证号'")
    private String personalNum;

    @Column(name = "company_name", columnDefinition = "varchar(63) COMMENT '营业执照名'")
    private String companyName;

    @Column(name = "store_name", columnDefinition = "varchar(63) COMMENT '企业认证时门店名称'")
    private String storeName;

    @Column(name = "company_legal", columnDefinition = "varchar(63) COMMENT '公司法人(姓名)'")
    private String companyLegal;

    @Column(name = "company_address", columnDefinition = "varchar(63) COMMENT '公司地址'")
    private String companyAddress;

    @Column(name = "company_num", columnDefinition = "varchar(63) COMMENT '营业执照号'")
    private String companyNum;

    @Column(name = "cert_url", columnDefinition = "varchar(512) COMMENT '证件照URL'")
    private String certUrl;

    @Column(name = "auth_url", columnDefinition = "varchar(512) COMMENT '认证URL'")
    private String authUrl;

    @Column(name = "auth_short_url", columnDefinition = "varchar(512) COMMENT '认证短链接'")
    private String authShortUrl;

    @Column(name = "auth_flow_id", columnDefinition = "varchar(63) COMMENT '认证流程ID'")
    private String authFlowId;

    @OptimisticLock(excluded = true)
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "user")
    List<Contract> contracts = new ArrayList<>();

    @OptimisticLock(excluded = true)
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "user")
    @OrderColumn(name = "sort")
    List<Account> accounts = new ArrayList<>();

    @OptimisticLock(excluded = true)
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "user")
    List<Invitation> userInvitations = new ArrayList<>();

    @OptimisticLock(excluded = true)
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "operator")
    List<Invitation> operatorInvitations = new ArrayList<>();

    @Column(name = "auth_succ_time", columnDefinition = "datetime COMMENT '认证成功时间'")
    private LocalDateTime authSuccTime;

    @Setter
    @Column(name = "tax_rate", columnDefinition = "decimal(20,2) default 0 COMMENT '税率'", nullable = false)
    private BigDecimal taxRate;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", columnDefinition = "bigint(20) COMMENT '所属主体用户ID'")
    private User parent;

    @Column(name = "license_type", columnDefinition = "varchar(63) COMMENT 'SaaS许可类型'")
    private String licenseType;

    @Column(name = "authed", columnDefinition = "bit(1) DEFAULT b'0' COMMENT 'SaaS认证状态'")
    private Boolean authed;

    public User(Long id) {
        this.id = id;
        this.taxRate = getDefaultTaxRate();
    }

    protected abstract BigDecimal getDefaultTaxRate();

    public User(EditUserPO po) {
        this(po.getId());
        edit(po);
    }

    private static MerchantApi getMerchantApi() {
        return getAPI(MerchantApi.class, getProperty("saas.api.url"));
    }

    public static User of(EditUserPO po) {
        return switch (po.getType()) {
            case COMPANY -> new CompanyUser(po);
            case PERSONAL -> new PersonalUser(po);
        };
    }

    public Invitation newInvitation(User operator) {
        Invitation invitation = new Invitation(this, operator);
        addInvitation(invitation);
        operator.addInvitation(invitation);
        return invitation;
    }

    public void addInvitation(Invitation invitation) {
        if (!operatorInvitations.contains(invitation)) {
            operatorInvitations.add(invitation);
        }
    }

    public boolean withoutInvitation(User user) {
        return operatorInvitations.stream().noneMatch(invitation -> invitation.getUser().equals(user));
    }

    public void removeInvitation(Invitation invitation) {
        operatorInvitations.remove(invitation);
    }

    public void launchAuth() {
        if (status == AUTH_SUCC)
            throw new CodingException("用户已认证通过,无需重新认证!");

        JSONObject authData = doLaunchAuth();
        authUrl = authData.getString("authUrl");
        authShortUrl = authData.getString("authShortUrl");
        authFlowId = authData.getString("authFlowId");
        status = AUTH_ING;
    }

    protected abstract JSONObject doLaunchAuth();

    public void edit(EditUserPO po) {
        if (po.getPersonalName() != null) {
            this.personalName = po.getPersonalName();
        }
        if (po.getPersonalNum() != null) {
            this.personalNum = po.getPersonalNum();
        }
        if (po.getCompanyName() != null) {
            this.companyName = po.getCompanyName();
        }
        if (po.getCompanyNum() != null) {
            this.companyNum = po.getCompanyNum();
        }
        if (po.getType() != null) {
            this.type = po.getType();
        }
        if (po.getContactName() != null) {
            this.contactName = po.getContactName();
        }
        if (po.getContactMobile() != null) {
            this.contactMobile = po.getContactMobile();
        }
        if (po.getContactAddress() != null) {
            this.contactAddress = po.getContactAddress();
        }
        if (po.getCertUrl() != null) {
            this.certUrl = po.getCertUrl();
        }
        if (po.getCompanyLegal() != null) {
            this.companyLegal = po.getCompanyLegal();
        }
        if (po.getCompanyAddress() != null) {
            this.companyAddress = po.getCompanyAddress();
        }
        if (po.getContactAddress() != null) {
            this.contactAddress = po.getContactAddress();
        }
        if (po.getStoreName() != null) {
            this.storeName = po.getStoreName();
        }
        this.parent = po.getParentId() != null
                ? getBean(UserRpt.class).getReferenceById(po.getParentId())
                : null;

        if (po.getAuthed() != null) {
            this.authed = po.getAuthed();
        }
        if (po.getLicenseType() != null) {
            this.licenseType = po.getLicenseType();
        }

        if (getType() == UserDTO.Type.COMPANY && po.getBankAccount() != null) {
            addAccount(new CreateBankAccountPO(po));
        }
    }

    public abstract String getCertName();

    public abstract String getCertNum();

    public void authPass() {
        this.status = AUTH_SUCC;
        this.authSuccTime = now();
        asyncExec(() -> {
            try {
                log.info("用户认证成功,回调商户API");
                getMerchantApi().userAuthCallback(id);
            } catch (Throwable e) {
                log.error("用户认证回调失败", e);
            }
        });
    }

    public <T extends Contract> T findContract(Class<T> clazz, LocalDateTime time, String cityCode, List<ContractDTO.Status> statuses) {
        ContractDTO.Type contractType = ContractDTO.Type.valueOf(clazz.getAnnotation(DiscriminatorValue.class).value());
        Contract contract = findContract(contractType, time, cityCode, statuses);
        return caseToSubType(contract, clazz);
    }

    public Contract findContract(ContractDTO.Type contractType, LocalDateTime time, String cityCode, List<ContractDTO.Status> statuses) {
        return contracts.stream().filter(c -> c.isMatch(contractType, time, cityCode, statuses))
                .findFirst().orElse(null);
    }

    public UserDTO.Type getType() {
        if (type == null)
            type = UserDTO.Type.valueOf(ClassUtils.getClassAnnotation(this.getClass(), DiscriminatorValue.class).value());
        return type;
    }

    public Account addAccount(CreateAccountPO po) {
        Account account = Account.of(po, this);
        accounts.add(account);
        account.prepare();
        return account;
    }

    public void sortAccount(List<Long> accountIds) {
        List<Account> newSort = new ArrayList<>();
        Map<Long, Account> accountMap = accounts.stream().collect(Collectors.toMap(Account::getId, a -> a));
        accountIds.forEach(id -> {
            Account account = accountMap.get(id);
            if (account == null)
                throw new CodingException("用户[%s]不存在账户[%s]", this.getId(), id);
            else
                accountMap.remove(id);

            addNewSort(account, newSort);
        });

        accountMap.values().forEach(acc -> addNewSort(acc, newSort));
        this.accounts = newSort;
    }

    private static void addNewSort(Account account, List<Account> newSort) {
        if (account.getSelf()) {
            newSort.add(0, account);
        } else {
            newSort.add(account);
        }
    }


    public Set<ContractDTO.SubType> getContractSubTypes(String cityCode) {
        return contracts.stream().filter(c ->
                !c.getType().equals(BROKER) && c.getStatus().equals(ContractDTO.Status.EFFECTIVE) && c.isApplyCity(cityCode)
        ).map(Contract::getSubType).collect(Collectors.toSet());
    }

    public Account allotAccount(BigDecimal amount) {
        if (accounts.stream().noneMatch(Account::getSelf))
            throw new BusinessException("bc.ejuetc.agent.1005");

        for (Account account : accounts.stream().filter(Account::isSuccess).toList()) {
            if (account.addSum(amount)) {
                return account;
            }
        }
        throw new BusinessException("bc.ejuetc.agent.1011");
    }

    public int getAccountIndex(Account account) {
        return accounts.indexOf(account);
    }

    public void bindNotify(UserBindNotifyPo po) {
        if (po.getBusinessCode() == BusinessOpenDTO.Code.SALE) {
            this.bindXYSale = po.getBind();
        }
        if (po.getBusinessCode() == BusinessOpenDTO.Code.RENT) {
            this.bindXYRent = po.getBind();
        }
    }
}
