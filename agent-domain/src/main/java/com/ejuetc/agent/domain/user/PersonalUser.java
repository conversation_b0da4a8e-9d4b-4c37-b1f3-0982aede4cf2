package com.ejuetc.agent.domain.user;

import com.alibaba.fastjson.JSONObject;
import com.ejuetc.agent.api.account.CreateAccountPO;
import com.ejuetc.agent.api.user.EditUserPO;
import com.ejuetc.agent.domain.account.Account;
import com.ejuetc.agent.domain.contract.Contract;
import com.ejuetc.agent.integration.esign.ESignComponent;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.integration.user.EmployeeCompanyRO;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

import static com.ejuetc.agent.dto.AccountDTO.Status.BIND_ING;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.spring.SpringUtil.getProperty;
import static java.math.BigDecimal.ONE;
import static java.math.BigDecimal.ZERO;
import static java.math.RoundingMode.HALF_UP;

@Slf4j
@Entity
@DiscriminatorValue("PERSONAL")
@SubtypeCode(parent = Contract.class, code = "PERSONAL", name = "个人用户")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class PersonalUser extends User {

    @Override
    protected BigDecimal getDefaultTaxRate() {
        return ZERO;
    }

    public PersonalUser(EditUserPO po) {
        super(po);
    }

    @Override
    protected JSONObject doLaunchAuth() {
        if (getPersonalName() == null || getPersonalNum() == null || getContactName() == null || getContactMobile() == null) {
            throw new CodingException("用户信息不全,不能进行实名认证");
        }


        return getBean(ESignComponent.class).call(
                "/v3/psn-auth-url",
                Map.of(
                        "psnAuthConfig", Map.of(
                                "psnAccount", getContactMobile(),
                                "psnInfo", Map.of(
                                        "psnName", getPersonalName(),
                                        "psnIDCardNum", getPersonalNum(),
                                        "psnIDCardType", "CRED_PSN_CH_IDCARD",
                                        "psnMobile", getContactMobile()
                                )
                        ),
                        "authorizeConfig", Map.of(
                                "authorizedScopes", List.of(
                                        "psn_initiate_sign"
                                )
                        ),
                        "redirectConfig", Map.of(
                                "redirectUrl", getProperty("esign.redirectUrl")
                        ),
                        "notifyUrl", getProperty("esign.notifyUrl")
                )
        );
    }

    @Override
    public Account addAccount(CreateAccountPO po) {
        if (accounts.stream().anyMatch(acc -> acc.getStatus() == BIND_ING)) {
            throw new CodingException("用户[%s]已有绑定中账号，不能新增绑定!", getId());
        }
        return super.addAccount(po);
    }

    public String getCertName() {
        return getPersonalName();
    }

    public String getCertNum() {
        return getPersonalNum();
    }


}
