package com.ejuetc.agent.domain.statement;


import com.ejuetc.agent.api.statement.FlowActionPO;
import com.ejuetc.agent.dto.StatementDTO;
import com.ejuetc.commons.base.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

@Slf4j
@Getter
@Entity
@NoArgsConstructor
@Comment("结算单操作日志")
@Table(name = "tb_statement_log")
@Where(clause = "logic_delete = 0")
public class StatementLog extends BaseEntity<StatementLog> {

    @Id
    @GeneratedValue(generator = "statement_log_id")
    @SequenceGenerator(name = "statement_log_id", sequenceName = "seq_statement_log_id")
    private Long id;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "statement_id", columnDefinition = "bigint(20) COMMENT '结算单Id'")
    protected Statement statement;

    @Column(name = "operator_id", columnDefinition = "bigint(20) COMMENT '操作人Id'")
    private Long operatorId;

    @Column(name = "operator_name", columnDefinition = "varchar(127) COMMENT '操作人姓名'")
    private String operatorName;

    @Column(name = "remark", columnDefinition = "varchar(511) COMMENT '操作描述'")
    private String remark;

    @Embedded
    private StatementFlow flow;

    @Enumerated(EnumType.STRING)
    @Column(name = "to_status")
    protected StatementDTO.Status toStatus;

    @Column(name = "param", columnDefinition = "text COMMENT '操作参数'")
    private String param;

    public StatementLog(Statement statement, StatementFlow flow, FlowActionPO po, StatementDTO.Status toStatus) {
        this.statement = statement;
        this.flow = flow;
        this.toStatus = toStatus;
        this.param = po.getParam();
        this.operatorId = po.getOperatorId();
        this.operatorName = po.getOperatorName();
        this.remark = po.getRemark();
    }
}
