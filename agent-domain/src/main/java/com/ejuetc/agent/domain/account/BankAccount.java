package com.ejuetc.agent.domain.account;

import com.ejuetc.agent.api.account.CreateBankAccountPO;
import com.ejuetc.agent.domain.contract.Contract;
import com.ejuetc.agent.domain.user.User;
import com.ejuetc.agent.dto.AccountDTO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.spring.SpringUtil.getProperty;

@Slf4j
@Entity
@Getter
@DiscriminatorValue("BANK_ACCOUNT")
@SubtypeCode(parent = Contract.class, code = "BANK_ACCOUNT", name = "银行账户")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class BankAccount extends Account {
    @Column(name = "bank_province", columnDefinition = "varchar(63) COMMENT '开户行所属省份'")
    private String bankProvince;

    @Column(name = "bank_city", columnDefinition = "varchar(63) COMMENT '开户行所属城市'")
    private String bankCity;

    @Column(name = "bank_address", columnDefinition = "varchar(127) COMMENT '开户行'")
    private String bankAddress;


    public BankAccount(User user, CreateBankAccountPO po) {
        super(user);
        this.accountName = user.getCertName();
        this.cardId = user.getCertNum();
        this.accountNO = po.getAccountNO();
        this.bankProvince = po.getBankProvince();
        this.bankCity = po.getBankCity();
        this.bankAddress = po.getBankAddress();
        refreshSelf();
    }

    @Override
    public void prepare() {
        this.status = AccountDTO.Status.BIND_SUCC;
        this.self = true;
    }

    @Override
    protected BigDecimal getMonthlyLimit() {
        return null;
    }
}