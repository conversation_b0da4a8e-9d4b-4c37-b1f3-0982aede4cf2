package com.ejuetc.agent.domain.esign;

import com.ejuetc.agent.dto.TemplateDTO;
import com.ejuetc.agent.dto.UserDTO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.Optional;

public interface TemplateRpt extends JpaRepositoryImplementation<Template, Long> {
    Template findByUserType(UserDTO.Type userType);

    @Query("""
            select t from Template t
            where (t.userType is null or t.userType = :userType)
            and t.type = :templateType
            """)
    Optional<Template> findByUserTypeAndType(UserDTO.Type userType, TemplateDTO.Type templateType);
}
