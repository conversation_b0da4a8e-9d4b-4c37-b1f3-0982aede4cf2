package com.ejuetc.agent.domain.contract;

import com.ejuetc.agent.dto.ContractDTO;
import com.ejuetc.agent.api.contract.CreatePartnerContractPO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

import static com.ejuetc.commons.base.utils.StringUtils.isBlank;
import static com.ejuetc.commons.base.utils.StringUtils.notBlank;

@Slf4j
@Entity
@Getter
@DiscriminatorValue("PARTNER")
@SubtypeCode(parent = Contract.class, code = "PARTNER", name = "合伙协议")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class PartnerContract extends Contract {

    @Column(name = "commission_rate", columnDefinition = "decimal(20,2) default 0 COMMENT '返佣比例'", nullable = false)
    private BigDecimal commissionRate = new BigDecimal("0.2");

    @Column(name = "kh_code", columnDefinition = "varchar(64) COMMENT '考核主体编码'")
    private String khCode;
    @Column(name = "kh_name", columnDefinition = "varchar(64) COMMENT '考核主体名称'")
    private String khName;
    @Column(name = "cost_code", columnDefinition = "varchar(64) COMMENT '成本中心编码'")
    private String costCode;
    @Column(name = "cost_name", columnDefinition = "varchar(64) COMMENT '成本中心名称'")
    private String costName;

    public PartnerContract(CreatePartnerContractPO po) {
        super(po);
        if (po.getCommissionRate() != null)
            this.commissionRate = po.getCommissionRate();
        makeOperatorInvitation();
    }

    @Override
    public boolean isApplyCity(String cityCode) {
        return true;
    }
}
