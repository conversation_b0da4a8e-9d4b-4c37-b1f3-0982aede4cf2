package com.ejuetc.agent.domain.crm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ejuetc.agent.api.contract.CrmResultPo;
import com.ejuetc.agent.api.statement.ChargeOffPO;
import com.ejuetc.agent.domain.statement.Statement;
import com.ejuetc.agent.domain.statement.StatementAccount;
import com.ejuetc.agent.dto.ContractDTO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.exception.CodingException;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.alibaba.fastjson.JSON.parseObject;

@Slf4j
@Entity
@Getter
@DiscriminatorValue("CHARGE_OFF")
@SubtypeCode(parent = OaFinance.class, code = "CHARGE_OFF", name = "核销")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class OaFinance4ChargeOff extends OaFinance{

    @Column(name = "company_name", columnDefinition = "varchar(255) COMMENT '公司名称'")
    private String companyName;

    @Column(name = "company_no", columnDefinition = "varchar(255) COMMENT '公司营业执照号'")
    private String companyNo;

    @Column(name = "contract_no", columnDefinition = "varchar(255) COMMENT '合同单据号'")
    private String contractNo;

    @Column(name = "bank_no", columnDefinition = "varchar(255) COMMENT '银行账号'")
    private String bankNo;

    @Column(name = "invoice_url", columnDefinition = "varchar(511) COMMENT '发票URL'")
    private String invoiceUrl;

    @Column(name = "invoice_confirm_url", columnDefinition = "varchar(511) COMMENT '发票确认URL'")
    private String invoiceConfirmUrl;

    @Column(name = "setatement_url", columnDefinition = "varchar(511) COMMENT '结算单URL'")
    private String statementUrl;

    @Column(name = "pre_pay_no", columnDefinition = "varchar(255) COMMENT '预付款单号'")
    private String prePayNo;

    @Column(name = "pre_pay_contract_no", columnDefinition = "varchar(255) COMMENT '预付款合同号'")
    private String prePayContractNo;

    @Column(name = "fee_type", columnDefinition = "varchar(255) COMMENT '费用类型'")
    private String feeType;

    @Column(name = "pay_amount", columnDefinition = "decimal(20,2) default 0 COMMENT '已付金额'")
    private BigDecimal payAmount;

    @Column(name = "uncancel_amount", columnDefinition = "decimal(20,2) default 0 COMMENT '未核销总金额'")
    private BigDecimal unCancelAmount;

    @Column(name = "cancel_amount", columnDefinition = "decimal(20,2) default 0 COMMENT '本次核销金额'")
    private BigDecimal cancelAmount;

    @Column(name = "charge_off_time", columnDefinition = "datetime COMMENT '入账日期'")
    protected LocalDate chargeOffTime;

    public OaFinance4ChargeOff(List<Statement> statements, ChargeOffPO po) {
        this.statements.addAll(statements);
        this.statements.forEach(t -> t.setOaFinance(this));
        this.companyName = po.getCompanyName();
        this.companyNo = po.getCompanyNo();
        this.contractNo = po.getContractNo();
        this.bankNo = po.getBankNo();
        this.invoiceUrl = po.getInvoiceUrl();
        this.invoiceConfirmUrl = po.getInvoiceConfirmUrl();
        this.statementUrl = po.getStatementUrl();
        this.prePayNo = po.getPrePayNo();
        this.prePayContractNo = po.getPrePayContractNo();
        this.feeType = po.getFeeType();
        this.payAmount = po.getPayAmount();
        this.unCancelAmount = po.getUnCancelAmount();
        this.cancelAmount = po.getCancelAmount();
        this.remark = po.getRemark();
        this.invoiceAfterTax = po.getInvoiceAfterTax();
        this.invoicePreTax = po.getInvoicePreTax();
        this.chargeOffTime = po.getChargeOffTime();
    }

    @Override
    public void sendCrm(String applyName) {
        if (this.oaStatus == ContractDTO.OAStatus.IN_HAND || this.oaStatus == ContractDTO.OAStatus.SUCCESS)
            return;
        this.applyTime = LocalDateTime.now();
        this.applyName = applyName;
        this.lastCrmInstruct = new CrmInstruct4ChargeOff(this);
        this.crmInstruct.add(lastCrmInstruct);
        this.lastCrmInstruct.exec();
        this.oaStatus = switch (lastCrmInstruct.getStatus()) {
            case SUCC -> ContractDTO.OAStatus.IN_HAND;
            default -> ContractDTO.OAStatus.FAIL;
        };
        this.oaMemo = lastCrmInstruct.getMemo();
        if (oaStatus == ContractDTO.OAStatus.IN_HAND) {
            JSONObject jsonObject = parseObject(this.lastCrmInstruct.getResultData());
            this.oaNum = jsonObject.getString("oaNo");
        }
    }

    @Override
    public void contractCrmResult(CrmResultPo po) {
        super.contractCrmResult(po);
        List<StatementAccount> statementAccounts = getStatements().stream().flatMap(statement -> statement.getStatementAccounts().stream()).toList();
        statementAccounts.forEach(statementAccount -> {
            statementAccount.updateStatusAndPayTime("已支付", LocalDateTime.now());
        });
//        this.getStatements().forEach(Statement::refreshInvoice);
    }
}
