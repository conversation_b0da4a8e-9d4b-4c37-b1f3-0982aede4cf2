package com.ejuetc.agent.domain.contract;

import com.ejuetc.agent.domain.invitation.Invitation;
import com.ejuetc.agent.dto.ContractDTO;
import com.ejuetc.agent.api.contract.CreateBrokerContractPO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.exception.BusinessException;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.Hibernate;

import static com.ejuetc.commons.base.clazz.ClassUtils.caseToSubType;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static org.hibernate.Hibernate.unproxy;

@Getter
@Slf4j
@Entity
@DiscriminatorValue("BROKER")
@SubtypeCode(parent = Contract.class, code = "BROKER", name = "经纪协议")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class BrokerContract extends Contract {


    public BrokerContract(CreateBrokerContractPO po) {
        super(po);
    }

    @Override
    void doCheckInvitedCode(Invitation invited, Contract invitedContract) {
        if (!caseToSubType(invitedContract, AgentContract.class).hasCityPrice(cityCode))
            throw new BusinessException("bc.ejuetc.agent.1002", cityCode);

    }

    @Override
    public boolean isApplyCity(String cityCode) {
        return true;//cityCode.equals(getCityCode()); (250422,林冬成,去掉代理合同城市判断)
    }

}
