package com.ejuetc.agent.domain.crm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ejuetc.agent.api.statement.StatementPartnerAmountRO;
import com.ejuetc.agent.domain.clearing.ClearingRpt;
import com.ejuetc.agent.domain.contract.Contract;
import com.ejuetc.agent.domain.statement.Statement;
import com.ejuetc.agent.domain.statement.StatementAccount;
import com.ejuetc.agent.dto.CrmInstructDTO;
import com.ejuetc.commons.base.clazz.ClassUtils;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.usertype.ApiResponseUT;
import com.ejuetc.commons.base.usertype.MemoUT;
import com.ejuetc.commons.base.valueobj.Command;
import com.ejuetc.commons.base.valueobj.Memo;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Table;
import jakarta.persistence.*;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.*;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.security.MessageDigest;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ejuetc.agent.dto.CrmInstructDTO.Status.*;
import static com.ejuetc.commons.base.exception.ThrowableUtils.throwStackTrace;
import static com.ejuetc.commons.base.response.ApiResponse.apiResponse;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.spring.SpringUtil.getProperty;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.time.LocalDateTime.now;

/**
 * 订单
 */
@Entity
@Table(name = "tb_crm_instruct")
@Comment("crm交互指令")
@Where(clause = "logic_delete = 0")
@BatchSize(size = 100)
@NoArgsConstructor
@Getter
@Slf4j
@Accessors(chain = true)
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", columnDefinition = "enum('CONTRACT,'CASH_OUT','CHARGE_OFF') COMMENT '订单类型'")
public abstract class CrmInstruct extends BaseEntity<CrmInstruct> implements Command {

    @Id
    @GeneratedValue(generator = "crm_instruct_id")
    @SequenceGenerator(name = "crm_instruct_id", sequenceName = "seq_crm_instruct_id")
    protected Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, insertable = false, updatable = false)
    private CrmInstructDTO.Type type;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    protected CrmInstructDTO.Status status;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "contract_id", columnDefinition = "bigint(20) COMMENT '代理合同id'")
    protected Contract contract;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "oa_finance_id", columnDefinition = "bigint(20) COMMENT 'oa财务请款/核销id'")
    protected OaFinance oaFinance;

    @Column(name = "request", columnDefinition = "json COMMENT '请求参数'")
    private String request;

    @Column(name = "response", columnDefinition = "json COMMENT '应答结果'")
    private String response;

    @Column(name = "result_data", columnDefinition = "json COMMENT '应答数据'")
    private String resultData;

    @Type(MemoUT.class)
    @Column(name = "memo", columnDefinition = "text COMMENT '备注信息'")
    protected Memo memo = new Memo();

    private static final RestTemplate restTemplate = new RestTemplate();

    @Accessors(fluent = true)
    @Getter(lazy = true)
    private static final String appId = getProperty("crm.api.appId");

    @Accessors(fluent = true)
    @Getter(lazy = true)
    private static final String appSecret = getProperty("crm.api.appSecret");

    @Accessors(fluent = true)
    @Getter(lazy = true)
    private static final String baseUrl = getProperty("crm.api.baseUrl");

    private static Token TOKEN = null;

    public CrmInstruct(Contract contract) {
        this.contract = contract;
        this.status = WAIT;
    }

    public CrmInstruct(OaFinance oaFinance) {
        this.oaFinance = oaFinance;
        this.status = WAIT;
    }

    @Override
    public void exec() {
        getBean(EntityManager.class).persist(this);
        if (status == WAIT || status == UNKNOW) {
            doExec();
        }
    }

    protected abstract void doExec();

    protected void send(String url, Object body) {
        try {
            this.request = JSON.toJSONString(body, true);
            long timeStamp = Instant.now().toEpochMilli();
            String reqMethod = "POST";
            String accessToken = getAccessToken();
            String sign = generateSignature(request, reqMethod, timeStamp);

            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json;charset=UTF-8");
            headers.set("appId", appId());
            headers.set("timeStamp", String.valueOf(timeStamp));
            headers.set("sign", sign);
            headers.set("accessToken", accessToken);
            HttpEntity<String> entity = new HttpEntity<>(request, headers);
            log.info("CRM推送请求, url={}, entity={}", url, entity);
            ResponseEntity<String> responseEntity = restTemplate.exchange(baseUrl() + url, HttpMethod.POST, entity, String.class);
            this.response = responseEntity.getBody();
            log.info("CRM推送响应，response={}", response);
            JSONObject responseObj = JSONObject.parseObject(response);
            this.status = switch (responseObj.getString("returnCode")) {
                case "200" -> SUCC;
                case "400", "4001", "4002", "4003", "4004", "4005", "4006", "-1" -> FAIL;
                default -> UNKNOW;
            };
            this.memo.addMemo(responseObj.getString("returnMsg"));
            this.resultData = responseObj.getString("returnData");
        } catch (Throwable e) {
            log.error("crm指令执行失败", e);
            this.status = UNKNOW;
            this.memo.addMemo(e.getMessage());
            this.response = throwStackTrace(e);
        }
    }

    private String generateSignature(String bodyParam, String reqMethod, long timeStamp) {
        String param = bodyParam + reqMethod + timeStamp;
        String strMd5 = md5(param);
        String signatureMd5 = strMd5 + appSecret();
        return md5(signatureMd5);
    }

    @SneakyThrows
    private String md5(String value) {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        md5.update((value).getBytes(UTF_8));
        byte[] b = md5.digest();
        StringBuilder buf = new StringBuilder();
        for (byte item : b) {
            int i = item;
            if (i < 0) {
                i += 256;
            }
            if (i < 16) {
                buf.append("0");
            }
            buf.append(Integer.toHexString(i));
        }

        return buf.toString();
    }


    private String getAccessToken() {
        String token = getCache();
        if (StringUtils.isNotBlank(token)) {
            return token;
        }
        String resp = restTemplate.getForObject(baseUrl() + "/crm/api/auth/token?appId={appId}&appSecret={appSecret}", String.class, appId(), appSecret());
        JSONObject responseObj = JSONObject.parseObject(resp);
        if (!"200".equals(responseObj.getString("returnCode"))) {
            throw new CodingException("获取CRM TOKEN失败:" + responseObj.getString("returnMsg"));
        }

        Token tokenObj = responseObj.getObject("returnData", Token.class);
        setCache(tokenObj);
        return tokenObj.accessToken;
    }


    private String getCache() {
        return TOKEN != null && TOKEN.expires.isAfter(now())
                ? TOKEN.accessToken
                : null;
    }

    private void setCache(Token token) {
        TOKEN = token;
    }

    public boolean isSucc() {
        return status == SUCC;
    }

    @Data
    public static class Token {
        private String accessToken;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        public LocalDateTime expires;
    }

    public String getLastMemo() {
        return memo.getLast();
    }

    public CrmInstructDTO.Type getType() {
        if (type == null)
            type = CrmInstructDTO.Type.valueOf(ClassUtils.getClassAnnotation(this.getClass(), DiscriminatorValue.class).value());
        return type;
    }

    protected List<Map<String, Object>> getQkDetailList(CrmInstructDTO.Type type, List<Long> statementIds, BigDecimal invoicePreTax, BigDecimal invoiceAfterTax) {
        List<Object[]> resultList = getBean(ClearingRpt.class).selPartnerAmount(statementIds);
        List<StatementPartnerAmountRO> partnerAmountROList = resultList.stream().map(t -> {
            return new StatementPartnerAmountRO((String) t[0], (String) t[1], (String) t[2], (String) t[3], (BigDecimal) t[4], (BigDecimal) t[5], (Long) t[6], (BigDecimal) t[7], (BigDecimal) t[8]);
        }).collect(Collectors.toList());
        if (partnerAmountROList.stream().anyMatch(t -> com.ejuetc.commons.base.utils.StringUtils.isBlank(t.getKhCode()) || com.ejuetc.commons.base.utils.StringUtils.isBlank(t.getKhCode()) || com.ejuetc.commons.base.utils.StringUtils.isBlank(t.getCostCode()) || com.ejuetc.commons.base.utils.StringUtils.isBlank(t.getCostName())))
            throw new CodingException("根据名称未找到对应的考核主体，请款单id：" + JSON.toJSONString(statementIds));
        //针对 "CHARGE_OFF" 或 "CASH_OUT" 且只有1条明细
        if (type == CrmInstructDTO.Type.CHARGE_OFF || (type == CrmInstructDTO.Type.CASH_OUT && partnerAmountROList.size() == 1)) {
            return partnerAmountROList.stream()
                    .map(t -> createPayDetailMap(t, type, invoicePreTax, invoiceAfterTax))
                    .collect(Collectors.toList());
        }
        //找添玑网络
        StatementPartnerAmountRO dif = partnerAmountROList.stream().filter(t -> t.getUserId().equals(9095376821982095616L)).findFirst().orElse(null);
        if (partnerAmountROList.size() > 1 && dif == null) {
            throw new CodingException("发起公司请款多个合伙但未找到上海添玑网络服务有限公司，请款单id：" + JSON.toJSONString(statementIds));
        }
        //计算总金额
        BigDecimal preAmount = partnerAmountROList.stream().map(StatementPartnerAmountRO::getReportPreTax).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal afterAmount = partnerAmountROList.stream().map(StatementPartnerAmountRO::getReportAfterTax).reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal preAmountNoFact = partnerAmountROList.stream().map(StatementPartnerAmountRO::getReportPreTaxNoFact).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal afterAmountNoFact = partnerAmountROList.stream().map(StatementPartnerAmountRO::getReportAfterTaxNoFact).reduce(BigDecimal.ZERO, BigDecimal::add);
        //计算差额
        BigDecimal preDifAmount = preAmount.subtract(invoicePreTax);
        BigDecimal afterDifAmount = afterAmount.subtract(invoiceAfterTax);

        if (preAmount.compareTo(preAmountNoFact) != 0 || afterAmount.compareTo(afterAmountNoFact) != 0) {
            throw new CodingException("发起公司请款 调差前后金额不一致，请款单id：" + JSON.toJSONString(statementIds));
        }

        // 校验金额差值
        if (preDifAmount.compareTo(new BigDecimal("10")) > 0 || preAmount.subtract(invoicePreTax).compareTo(new BigDecimal("-10")) < 0)
            throw new CodingException("发起公司请款税前金额差值大于10，请款单id：" + JSON.toJSONString(statementIds));
        // 校验调差后金额
        assert dif != null;
        if (dif.getReportPreTax().subtract(preDifAmount).compareTo(BigDecimal.ZERO) < 0 || dif.getReportAfterTax().subtract(afterDifAmount).compareTo(BigDecimal.ZERO) < 0)
            throw new CodingException("发起公司请款调差后添玑税前或税后小于0，请款单id：" + JSON.toJSONString(statementIds));
        // 返回最终的明细
        return partnerAmountROList.stream()
                .map(t -> createPayDetailMap(t, preDifAmount, afterDifAmount))
                .collect(Collectors.toList());
    }

    public static void main(String[] args) {
        BigDecimal a1 = new BigDecimal("1335.36").subtract(new BigDecimal("1335.56"));
        System.out.println();
    }

    private Map<String, Object> createPayDetailMap(StatementPartnerAmountRO t, CrmInstructDTO.Type type, BigDecimal invoicePreTax, BigDecimal invoiceAfterTax) {
        Map<String, Object> temp = new HashMap<>();
        temp.put("payCause", type == CrmInstructDTO.Type.CHARGE_OFF ? "闲鱼代理返佣" : "合同请款");
        temp.put("khCode", t.getKhCode());
        temp.put("khName", t.getKhName());
        temp.put("costCode", t.getCostCode());
        temp.put("costName", t.getCostName());
        temp.put("payAmount", type == CrmInstructDTO.Type.CHARGE_OFF ? t.getReportPreTax() : invoicePreTax);
        temp.put("payTaxAmount", type == CrmInstructDTO.Type.CHARGE_OFF ? t.getReportPreTax().subtract(t.getReportAfterTax()) : invoicePreTax.subtract(invoiceAfterTax));
        return temp;
    }

    // 如果是 "CASH_OUT" 且调差情况时的处理
    private Map<String, Object> createPayDetailMap(StatementPartnerAmountRO t, BigDecimal preDifAmount, BigDecimal afterDifAmount) {
        Map<String, Object> temp = new HashMap<>();
        temp.put("payCause", "合同请款");
        temp.put("khCode", t.getKhCode());
        temp.put("khName", t.getKhName());
        temp.put("costCode", t.getCostCode());
        temp.put("costName", t.getCostName());

        // 处理 preDifAmount 和 afterDifAmount 对应的计算
        if (!t.getUserId().equals(9095376821982095616L)) {
            temp.put("payAmount", t.getReportPreTax());
            temp.put("payTaxAmount", t.getReportPreTax().subtract(t.getReportAfterTax()));
        } else {
            temp.put("payAmount", t.getReportPreTax().subtract(preDifAmount));
            temp.put("payTaxAmount", t.getReportPreTax().subtract(t.getReportAfterTax().subtract(afterDifAmount)));
        }
        return temp;
    }
}
