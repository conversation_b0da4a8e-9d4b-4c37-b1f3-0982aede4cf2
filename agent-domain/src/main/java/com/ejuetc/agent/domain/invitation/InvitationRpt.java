package com.ejuetc.agent.domain.invitation;

import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.Optional;

import static jakarta.persistence.LockModeType.PESSIMISTIC_WRITE;

public interface InvitationRpt extends JpaRepositoryImplementation<Invitation, Long> {
    @Lock(PESSIMISTIC_WRITE)
    Optional<Invitation> findByCode(String code);

    boolean existsByCode(String code);
}
