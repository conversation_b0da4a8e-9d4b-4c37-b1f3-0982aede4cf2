package com.ejuetc.agent.domain.withdraw;

import com.ejuetc.agent.domain.statement.StatementAccount;
import com.ejuetc.agent.dto.WithdrawDTO;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.usertype.MemoUT;
import com.ejuetc.commons.base.valueobj.Memo;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

import static com.ejuetc.agent.dto.WithdrawDTO.Status.INIT;
import static com.ejuetc.commons.base.utils.DateTimeUtils.formatDate;
import static java.math.BigDecimal.ZERO;

@Slf4j
@Getter
@Entity
@NoArgsConstructor
@Comment("出款单")
@Table(name = "tb_withdraw")
@Where(clause = "logic_delete = 0")
public class Withdraw extends BaseEntity<Withdraw> {

    @Id
    @GeneratedValue(generator = "withdraw_id")
    @SequenceGenerator(name = "withdraw_id", sequenceName = "seq_withdraw_id")
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    protected WithdrawDTO.Status status = INIT;

    @Column(name = "statement_account_count", columnDefinition = "int(11) default 0 COMMENT '结算账户数量'", nullable = false)
    private Integer statementAccountCount;

    @Column(name = "withdraw_no", columnDefinition = "varchar(64) COMMENT '出款单号'", nullable = false, unique = true)
    private String withdrawNO;
    @Column(name = "total_amount", columnDefinition = "decimal(20,2) default 0 COMMENT '总金额'")
    protected BigDecimal totalAmount = ZERO;

    @Column(name = "export_url", columnDefinition = "varchar(511) COMMENT '导出文件URL'")
    private String exportUrl;

    @ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinTable(name = "tb_withdraw_statement_account",
            joinColumns = @JoinColumn(name = "withdraw_id", columnDefinition = "bigint(20) COMMENT '出款单号'"),
            inverseJoinColumns = @JoinColumn(name = "statement_account_id", columnDefinition = "bigint(20) COMMENT '结算账户号'")
    )
    @Comment("出款单包含的结算账户")
    private List<StatementAccount> statementAccounts = new ArrayList<>();

    @Enumerated(EnumType.STRING)
    @Column(name = "type", insertable = false, updatable = false)
    private WithdrawDTO.Type type;

    @Type(MemoUT.class)
    @Column(name = "memo", columnDefinition = "text COMMENT '备注信息'")
    private Memo memo = new Memo();

    public List<StatementAccount> getStatementAccounts() {
        // 返回集合的副本，避免外部修改原集合
        return new ArrayList<>(statementAccounts);
    }

    public void setStatementAccounts(List<StatementAccount> statementAccounts) {
        // 创建集合副本，避免外部引用原集合
        this.statementAccounts = new ArrayList<>(statementAccounts);
    }

    public Withdraw(List<StatementAccount> statementAccounts, WithdrawDTO.Type type) {
        setStatementAccounts(statementAccounts);
        String invoiceBatchNO = "%s_%s".formatted(formatDate(new Date(), "yyyyMMddHHmmss"), new Random().nextLong(100000, 999999));
        this.withdrawNO = invoiceBatchNO;
        this.type = type;
        getStatementAccounts().forEach(sa -> sa.setWithdraw(this, invoiceBatchNO));
        this.statementAccountCount = getStatementAccounts().size();
        this.totalAmount = getStatementAccounts().stream().map(StatementAccount::getCommPreTax).reduce(ZERO, BigDecimal::add);
    }

//    public void exec() {
//        if (status != WithdrawDTO.Status.INIT && status != WithdrawDTO.Status.FAIL)
//            throw new CodingException("结算打款中");
//        status = PAYING;
//    }

    public void exportFile() {
        //TODO:生成
        this.exportUrl = "";
    }

    public List<StatementAccount> findSuccStatementAccounts() {
        return statementAccounts.stream().filter(sa -> sa.getWithdraw().equals(this)).collect(Collectors.toList());
    }
}
