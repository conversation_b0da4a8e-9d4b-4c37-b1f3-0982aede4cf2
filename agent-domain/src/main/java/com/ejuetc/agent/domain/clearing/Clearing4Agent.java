package com.ejuetc.agent.domain.clearing;

import com.ejuetc.agent.domain.contract.Contract;
import com.ejuetc.agent.domain.deal.Deal;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

import static java.math.RoundingMode.HALF_UP;

@Slf4j
@Getter
@Entity
@DiscriminatorValue("AGENT")
@SubtypeCode(parent = Contract.class, code = "AGENT", name = "代理佣金清分")
@NoArgsConstructor
public class Clearing4Agent extends Clearing {

    public Clearing4Agent(Deal deal) {
        super(deal, deal.getParentAgentClearing());
        this.user = this.deal.getAgentUser();
        this.taxRate = user.getTaxRate();
        this.excessCommRatio = this.deal.getAgentExcessCommRatio();
        this.normalCommRate = this.deal.getAgentNormalCommRate();
        this.discountPrice = this.deal.getAgentDiscountPrice();
    }

    protected BigDecimal calcExcessComm() {
        return excessAmount.multiply(excessCommRatio);
    }

    protected BigDecimal calcNormalComm() {
        return deal.getStandardPrice().subtract(deal.getTmServiceFee()).subtract(discountPrice).multiply(normalCommRate);
    }


}
