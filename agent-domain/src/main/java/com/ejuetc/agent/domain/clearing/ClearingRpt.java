package com.ejuetc.agent.domain.clearing;

import com.ejuetc.agent.domain.user.User;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.math.BigDecimal;
import java.util.List;

public interface ClearingRpt extends JpaRepositoryImplementation<Clearing, Long> {

    @Modifying
    @Query(value = """
            update Clearing
            set statementBatch = :statementBatch,updateTime=current_timestamp,version=version+1
            where statementTime < current_timestamp - 10 minute
              and statementNO is not null
              and statement is null
            """)
    int updateStatementBatch(String statementBatch);

    @Modifying
    @Query(value = """
            update tb_clearing ci
            left join tb_statement s on ci.statement_no = s.statement_no and ci.statement_batch = s.statement_batch
            set ci.statement_id = s.id,ci.update_time=current_timestamp,ci.version=ci.version+1
            where ci.statement_batch = :statementBatch
            """, nativeQuery = true)
    int updateStatementId(String statementBatch);

    @Modifying
    @Query(value = """
            update tb_clearing c
            left join tb_statement_account sc on c.statement_id=sc.statement_id and c.account_id=sc.account_id
            set c.statement_account_id = sc.id,c.update_time=current_timestamp,c.version=c.version+1
            where c.account_batch_no=:batchNO and sc.account_batch_no=:batchNO
            """, nativeQuery = true)
    int updateStatementAccountId(String batchNO);

    @Modifying
    @Query(value = """
            insert into tb_statement_account_clearing(statement_account_id,clearing_id)
            select statement_account_id,id
            from tb_clearing
            where account_batch_no=:batchNO
            """, nativeQuery = true)
    int insertStatementAccountClearing(String batchNO);


    @Query(value = """
              SELECT cc.kh_name, cc.kh_code, cc.cost_code, cc.cost_name, sum(c.report_pre_tax_fact), sum(c.report_after_tax_fact),cc.user_id, sum(c.report_pre_tax), sum(report_after_tax)
            from tb_clearing c
            LEFT JOIN tb_deal d on c.deal_id = d.id
            LEFT JOIN tb_contract cc on d.partner_contract_id = cc.id
            where c.statement_id in :ids and c.logic_delete = 0 and d.logic_delete = 0 and cc.logic_delete = 0 and c.flag_fact = 1 GROUP BY cc.id
            """, nativeQuery = true)
    List<Object[]> selPartnerAmount(List<Long> ids);

    @Query(value = """
            select c
            from Clearing c
            where c.user = :user
            and c.statementNO is not null
            and c.statement is null
            and c.taxRate != :newTaxRate
            """)
    List<Clearing> findUserTaxDiff(User user, BigDecimal newTaxRate);
}
