package com.ejuetc.agent.domain.invitation;

import com.ejuetc.agent.domain.contract.Contract;
import com.ejuetc.agent.domain.user.User;
import com.ejuetc.agent.domain.user.UserRpt;
import com.ejuetc.commons.base.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.io.Serializable;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@Getter
@Entity
@NoArgsConstructor
@Comment("邀请(码)")
@Table(name = "tb_invitation")
@Where(clause = "logic_delete = 0")
public class Invitation extends BaseEntity<Invitation> {

    @Id
    @GeneratedValue(generator = "invitation_id")
    @SequenceGenerator(name = "invitation_id", sequenceName = "seq_invitation_id")
    private Long id;

    @Column(name = "code", columnDefinition = "varchar(63) COMMENT '推荐码'", nullable = false, unique = true)
    private String code;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "operator_id", columnDefinition = "bigint(20) COMMENT '操作员外键'", nullable = false)
    private User operator;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", columnDefinition = "bigint(20) COMMENT '所属用户外键'", nullable = false)
    private User user;

    @ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinTable(name = "tb_invited_contract",
            joinColumns = @JoinColumn(name = "invitation_id", columnDefinition = "bigint(20) COMMENT '邀请外键'"),
            inverseJoinColumns = @JoinColumn(name = "contract_id", columnDefinition = "bigint(20) COMMENT '合同外键'")
    )
    @Comment("邀请码邀请创建的合同")
    private List<Contract> contracts = new ArrayList<>();

    private static final String CHARACTERS = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789";
    private static final int CODE_LENGTH = 6;
    private static final SecureRandom RANDOM = new SecureRandom();

    public Invitation(User user, User operator) {
        this.user = user;
        this.operator = operator;
        this.code = generateCode();
    }

    private static String generateCode() {
        String code;
        do {
            StringBuilder builder = new StringBuilder(CODE_LENGTH);
            for (int i = 0; i < CODE_LENGTH; i++) {
                builder.append(CHARACTERS.charAt(RANDOM.nextInt(CHARACTERS.length())));
            }
            code = builder.toString();
        } while (getBean(InvitationRpt.class).existsByCode(code));
        return code;
    }


    public void changeOwner(User newOperator) {
        this.operator.removeInvitation(this);
        this.operator = newOperator;
        this.operator.addInvitation(this);
    }

    public void addContract(Contract contract) {
        if (!contracts.contains(contract)) {
            contracts.add(contract);
        }
        if (!contract.getInviteds().contains(this)) {
            contract.addInvited(this);
        }
    }
}
