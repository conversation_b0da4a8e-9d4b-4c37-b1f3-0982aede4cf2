package com.ejuetc.agent.domain.price;

import com.ejuetc.agent.domain.deal.Deal;
import com.ejuetc.agent.dto.DealDTO;
import com.ejuetc.agent.api.price.CreatePricePO;
import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.valueobj.time.TimeInterval;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Entity
@NoArgsConstructor
@Comment("代理价")
@Table(name = "tb_price")
@Where(clause = "logic_delete = 0")
public class Price extends BaseEntity<Price> implements Serializable {

    @Id
    @GeneratedValue(generator = "price_id")
    @SequenceGenerator(name = "price_id", sequenceName = "seq_price_id")
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "channel_code", nullable = false)
    private ChannelDTO.Code channelCode;

    @Enumerated(EnumType.STRING)
    @Column(name = "business_code", nullable = false)
    private BusinessOpenDTO.Code businessCode;

    @Column(name = "city_name", columnDefinition = "varchar(64) COMMENT '城市名称'", nullable = false)
    private String cityName;

    @Column(name = "city_code", columnDefinition = "varchar(64) COMMENT '城市编码'", nullable = false)
    private String cityCode;

    @Enumerated(EnumType.STRING)
    @Column(name = "deal_type", nullable = false)
    private DealDTO.Type dealType;

    @Column(name = "standard_price", columnDefinition = "decimal(20,2) default 0 COMMENT '标准售价'", nullable = false)
    private BigDecimal standardPrice;

    @Column(name = "agent_price", columnDefinition = "decimal(20,2) default 0 COMMENT '代理价'", nullable = false)
    private BigDecimal agentPrice;

    @Column(name = "agent_discount", columnDefinition = "decimal(20,2) default 1 COMMENT '代理折扣'", nullable = false)
    private BigDecimal agentDiscount;

    @Column(name = "agent_discount_price", columnDefinition = "decimal(20,2) default 0 COMMENT '代理折扣价(代理售价*代理折扣)'", nullable = false)
    private BigDecimal agentDiscountPrice;

    @Column(name = "normal_comm_rate", columnDefinition = "decimal(20,2) default 0 COMMENT '正常售价返佣比例'", nullable = false)
    private BigDecimal normalCommRate;

    @Column(name = "excess_comm_ratio", columnDefinition = "decimal(20,2) default 0 COMMENT '超额售价返佣比例'", nullable = false)
    private BigDecimal excessCommRatio;

    @Embedded
    private TimeInterval indate;

    public Price(CreatePricePO po) {
        this.channelCode = po.getChannelCode();
        this.businessCode = po.getBusinessCode();
        this.cityName = po.getCityName();
        this.cityCode = po.getCityCode();
        this.dealType = po.getDealType();
        this.agentPrice = po.getAgentPrice();
        this.standardPrice = po.getStandardPrice();
        this.agentDiscount = po.getAgentDiscount();
        this.agentDiscountPrice = po.getAgentDiscountPrice();
        this.normalCommRate = po.getNormalCommRate();
        this.excessCommRatio = po.getExcessCommRatio();
    }

    public boolean matchDeal(Deal deal) {
        return channelCode == deal.getChannelCode()
               && businessCode == deal.getBusinessCode()
               && dealType == deal.getType()
               && cityCode.equals(deal.getCityCode())
               && (indate == null || indate.isInclude(deal.getFactPayTime()));
    }
}
