package com.ejuetc.agent.domain.clearing;

import static com.ejuetc.agent.dto.DealDTO.Type.CHANNEL_OPEN;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static java.math.BigDecimal.ONE;
import static java.math.BigDecimal.ZERO;
import static java.math.RoundingMode.DOWN;
import static java.math.RoundingMode.HALF_UP;
import static java.time.format.DateTimeFormatter.ofPattern;
import static lombok.AccessLevel.PROTECTED;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import com.ejuetc.commons.base.exception.CodingException;
import jakarta.persistence.*;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.OptimisticLock;
import org.hibernate.annotations.Where;

import com.ejuetc.agent.domain.account.Account;
import com.ejuetc.agent.domain.deal.Deal;
import com.ejuetc.agent.domain.statement.Statement;
import com.ejuetc.agent.domain.statement.StatementAccount;
import com.ejuetc.agent.dto.AccountDTO;
import com.ejuetc.agent.dto.ClearingDTO;
import com.ejuetc.agent.dto.DealDTO;
import com.ejuetc.commons.base.clazz.ClassUtils;
import com.ejuetc.commons.base.valueobj.Command;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@Entity
@NoArgsConstructor
@Comment("佣金清分")
@Table(name = "tb_clearing")
@Where(clause = "logic_delete = 0")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", columnDefinition = "enum('AGENT','PARTNER') COMMENT '类型'")
public abstract class Clearing extends ClearingStatement<Clearing> implements Command {

    @Setter(PROTECTED)
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", columnDefinition = "bigint(20) COMMENT '父级清分外键'")
    private Clearing parent;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, insertable = false, updatable = false)
    protected ClearingDTO.Type type;

    @OptimisticLock(excluded = true)
    @OrderColumn(name = "sort")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "parent")
    private List<Clearing> children = new ArrayList<>();

    @Id
    @GeneratedValue(generator = "clearing_id")
    @SequenceGenerator(name = "clearing_id", sequenceName = "seq_clearing_id")
    protected Long id;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "deal_id", columnDefinition = "bigint(20) COMMENT '关联交易'", nullable = false)
    protected Deal deal;

    @Column(name = "account_batch_no", columnDefinition = "varchar(63) COMMENT '账号批次号'")
    private String accountBatchNO;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "account_id", columnDefinition = "bigint(20) COMMENT '账号外键'")
    protected Account account;

    @Enumerated(EnumType.STRING)
    @Column(name = "account_type")
    private AccountDTO.Type accountType;

    @Column(name = "discount_price", columnDefinition = "decimal(20,2) default 0 COMMENT '折扣价(售价*折扣)'")
    protected BigDecimal discountPrice = ZERO;

    @Column(name = "excess_comm_ratio", columnDefinition = "decimal(20,2) default 0 COMMENT '超额售价返佣比例'")
    protected BigDecimal excessCommRatio = ZERO;

    @Column(name = "normal_comm_rate", columnDefinition = "decimal(20,2) default 0 COMMENT '正常售价返佣比例'")
    protected BigDecimal normalCommRate = ZERO;

    @Setter(PROTECTED)
    @Column(name = "report_pre_tax_fact", columnDefinition = "decimal(20,2) default 0 COMMENT '报表税前金额'")
    protected BigDecimal reportPreTaxFact = ZERO;

    @Setter(PROTECTED)
    @Column(name = "report_after_tax_fact", columnDefinition = "decimal(20,2) default 0 COMMENT '报表税后金额'")
    protected BigDecimal reportAfterTaxFact = ZERO;

    @Setter(PROTECTED)
    @Column(name = "fact_pre_tax", columnDefinition = "decimal(20,2) default 0 COMMENT '实际税前返佣'")
    protected BigDecimal factPreTax = ZERO;

    @Setter(PROTECTED)
    @Column(name = "fact_after_tax", columnDefinition = "decimal(20,2) default 0 COMMENT '实际税后返佣'")
    protected BigDecimal factAfterTax = ZERO;

    @Setter(PROTECTED)
    @Column(name = "fact_time", columnDefinition = "datetime COMMENT '实际返佣时间'")
    protected LocalDateTime factTime;

    @Setter(PROTECTED)
    @Column(name = "effect_time", columnDefinition = "datetime COMMENT '成销时间'")
    protected LocalDateTime effectTime;

    @Setter(PROTECTED)
    @Column(name = "statement_time", columnDefinition = "datetime COMMENT '结算时间'")
    protected LocalDateTime statementTime;

    @Setter(PROTECTED)
    @Column(name = "statement_no", columnDefinition = "varchar(255) COMMENT '结算单号'")
    protected String statementNO;


    @Setter(PROTECTED)
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "statement_id", columnDefinition = "bigint(20) COMMENT '所属结算单'")
    protected Statement statement;

    @Setter(PROTECTED)
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "statement_account_id", columnDefinition = "bigint(20) COMMENT '所属结算账号'")
    protected StatementAccount statementAccount;

    @Column(name = "statement_batch", columnDefinition = "varchar(255) COMMENT '结算批次'")
    private String statementBatch;

    @Setter(PROTECTED)
    @Column(name = "flag_orig", columnDefinition = "bit(1) default b'1' COMMENT '是否为原始清分项'", nullable = false)
    private Boolean flagOrig = true;

    @Setter(PROTECTED)
    @Column(name = "flag_fact", columnDefinition = "bit(1) default b'1' COMMENT '是否为实际清分项'", nullable = false)
    private Boolean flagFact = true;

    @Setter(PROTECTED)
    @Column(name = "flag_diff", columnDefinition = "bit(1) default b'0' COMMENT '是否为差额清分项'", nullable = false)
    private Boolean flagDiff = false;

    @Setter(PROTECTED)
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "orig_id", columnDefinition = "bigint(20) COMMENT '关联原始清分外键'")
    protected Clearing orig;


    @Setter(PROTECTED)
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "fact_id", columnDefinition = "bigint(20) COMMENT '关联实际清分外键'")
    protected Clearing fact;

    @Setter(PROTECTED)
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "diff_id", columnDefinition = "bigint(20) COMMENT '关联差额清分外键'")
    protected Clearing diff;

    @Setter(PROTECTED)
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "invoice_diff_id", columnDefinition = "bigint(20) COMMENT '发票差额清分外键'")
    protected Clearing invoiceDiff;

    @Column(name = "excess_amount", columnDefinition = "decimal(20,2) default 0 COMMENT '超售金额'")
    protected BigDecimal excessAmount;

//    @Setter(AccessLevel.PROTECTED)
//    @Column(name = "last", columnDefinition = "bit(1) DEFAULT b'0'  COMMENT '是否最后一笔'")
//    private Boolean last = false;

    @Comment("交易类型")
    @Enumerated(EnumType.STRING)
    @Column(name = "deal_type")
    private DealDTO.Type dealType;

    @Comment("交易类别")
    @Enumerated(EnumType.STRING)
    @Column(name = "deal_category")
    private DealDTO.Category dealCategory;

    @Setter(PROTECTED)
    @Column(name = "crm_split", columnDefinition = "bit(1) DEFAULT b'0'  COMMENT '是否CRM拆分'")
    private Boolean crmSplit = false;

    public Clearing(Deal deal, Clearing parent) {
        this.deal = deal;
        this.dealCategory = deal.getCategory();
        this.dealType = deal.getType();
        this.crmSplit = dealType == CHANNEL_OPEN;
        this.statementTime = deal.getStatementTime();
//        this.last = deal.getLast();
        if (parent != null) {
            this.parent = parent;
            parent.addChild(this);
        }
    }

    private void addChild(Clearing child) {
        children.add(child);
    }

    public ClearingDTO.Type getType() {
        if (type == null)
            type = ClearingDTO.Type.valueOf(ClassUtils.getClassAnnotation(this.getClass(), DiscriminatorValue.class).value());
        return type;
    }

    public void exec(BigDecimal newTaxRate) {
        this.taxRate = newTaxRate;
        exec();
    }

    @Override
    public void exec() {
        if (dealType == DealDTO.Type.CLUE_BUY || dealCategory == DealDTO.Category.EXPIRE) return;

        this.excessAmount = deal.getPayAmount().subtract(deal.getStandardPrice());
        this.excessComm = calcExcessComm();
        this.normalComm = calcNormalComm();
        this.comm = excessComm.add(normalComm);
        calcTaxValue();
        if (!user.getEmployeeFlag()) {
            this.statementNO = "%s_%s_%s".formatted(statementTime.plusMonths(-1).format(ofPattern("yyyyMM")), user.getId(), taxRate);
        }
    }

    public void calcTaxValue() {
        this.commPreTax = calcCommPreTax(comm);
        this.disburseFee = comm.subtract(commPreTax);
        this.reportPreTax = calcReportPreTax(comm);
        this.reportPreTaxFact = reportPreTax;
        this.reportAfterTax = calcReportAfterTax(reportPreTax);
        this.reportAfterTaxFact = reportAfterTax;
    }

    protected BigDecimal calcCommPreTax(BigDecimal amount) {
        return switch (user.getType()) {
            case COMPANY -> amount.subtract(amount
                    .divide(ONE.add(PT6), 6, RoundingMode.HALF_UP)
                    .multiply(PT6.subtract(taxRate).multiply(ONE.add(PT12)))
            ).setScale(2, RoundingMode.HALF_UP);
            case PERSONAL -> amount.multiply(ONE.subtract(PT7_5))
                    .setScale(2, DOWN);
        };
    }

    protected BigDecimal calcReportPreTax(BigDecimal amount) {
        return switch (user.getType()) {
            case COMPANY -> amount.subtract(amount
                    .divide(ONE.add(PT6), 6, RoundingMode.HALF_UP)
                    .multiply(PT6.subtract(taxRate).multiply(ONE.add(PT12)))
            ).setScale(2, RoundingMode.HALF_UP);
            case PERSONAL -> amount.multiply(ONE.subtract(PT7_5)).multiply(ONE.add(PT6)).setScale(2, HALF_UP);
        };
    }

    protected BigDecimal calcReportAfterTax(BigDecimal preTax) {
        return switch (user.getType()) {
            case COMPANY -> preTax.divide(ONE.add(taxRate), 2, RoundingMode.HALF_UP);
            case PERSONAL -> preTax.divide(ONE.add(PT6), 2, RoundingMode.HALF_UP);
        };
    }

    public void adjustedTaxRate(BigDecimal newTaxRate) {
        if (fact != null && fact.getTaxRate().compareTo(newTaxRate) == 0) {
            return;//如果目标税率与原始税率一致，则无需更新
        }

        //无论后续新增清分记录还是使用原清分记录,已创建的fact和diff都需要逻辑删除
        if (this.diff != null) {
            this.diff.logicDelete();
            this.diff = null;
        }
        if (this.fact != null) {
            this.fact.logicDelete();
            this.fact = null;
        }

        //如果目标费率与原始清分的费率相同,则回复标记位(差异数据已删除)
        if (taxRate.compareTo(newTaxRate) == 0) {
            this.setFlag(true, true, false);
            return;
        } else {
            this.setFlag(true, false, false);
        }

        //生成fact清分记录,并调整费率后重新技术
        fact = newClearingForThis(newTaxRate);
        fact.calcTaxValue();
        fact.setFlag(false, true, false);

        //生成差异清分记录,计算原始清分与实际清分之间的差额
        diff = newClearingForThis(newTaxRate);
        diff.setCommPreTax(fact.getCommPreTax().subtract(commPreTax));
        diff.setReportPreTax(fact.getReportPreTax().subtract(reportPreTax));
        diff.setReportAfterTax(fact.getReportAfterTax().subtract(reportAfterTax));
        diff.setReportPreTaxFact(diff.getReportPreTax());
        diff.setReportAfterTaxFact(diff.getReportAfterTax());
        diff.setDisburseFee(fact.getDisburseFee().subtract(disburseFee));
        diff.setFlag(false, false, true);
    }

    public void new4Invoice(BigDecimal diffPreAmount, BigDecimal diffAfterAmount, BigDecimal taxRate) {
        if (fact == null) {
            if (diff != null) diff.logicDelete();
            diff = newClearingForThis(taxRate);
            diff.setReportPreTax(diffPreAmount);
            diff.setReportAfterTax(diffAfterAmount);
            diff.setReportPreTaxFact(diffPreAmount);
            diff.setReportAfterTaxFact(diffAfterAmount);
            diff.setCommPreTax(ZERO);
            diff.setDisburseFee(ZERO);
            diff.setCrmSplit(false);
            diff.setFlag(false, false, true);

            setReportPreTaxFact(reportPreTax.add(diffPreAmount));
            setReportAfterTaxFact(reportAfterTax.add(diffAfterAmount));
        } else {
            fact.setReportPreTaxFact(fact.getReportPreTax().add(diffPreAmount));
            fact.setReportAfterTaxFact(fact.getReportAfterTax().add(diffAfterAmount));

            diff.setReportPreTaxFact(diff.getReportPreTax().add(diffPreAmount));
            diff.setReportAfterTaxFact(diff.getReportAfterTax().add(diffAfterAmount));
        }
        invoiceDiff = diff;
    }

    @SneakyThrows
    private Clearing newClearingForThis(BigDecimal newTaxRate) {
        Clearing clearing = getClass().getConstructor().newInstance();
        clearing.orig = this;
        clearing.deal = getDeal();
        clearing.user = getUser();
        clearing.excessCommRatio = getExcessCommRatio();
        clearing.normalCommRate = getNormalCommRate();
        clearing.discountPrice = getDiscountPrice();
        clearing.taxRate = newTaxRate;
        clearing.excessComm = getExcessComm();
        clearing.normalComm = getNormalComm();
        clearing.comm = getComm();
        clearing.statementNO = getStatementNO();
        clearing.statementTime = getStatementTime();
        clearing.statement = getStatement();
        clearing.crmSplit = getCrmSplit();
        statement.addChild(clearing);
        return clearing;
    }

    private void setFlag(boolean orig, boolean fact, boolean diff) {
        this.flagOrig = orig;
        this.flagFact = fact;
        this.flagDiff = diff;
    }

    protected abstract BigDecimal calcNormalComm();

    protected abstract BigDecimal calcExcessComm();

    public void allotAccount(String batchNO) {
        if (statementAccount != null) {
            switch (statementAccount.getStatus()) {
                case SUCCESS:
                    account.addSum(commPreTax);
                    return;
                case PAYING:
                    throw new CodingException("结算单[%s]当前状态[%s]不支持所属结算单重新分配账号", statementAccount.getId(), statementAccount.getStatus());
                case INIT:
                    statementAccount.logicDelete();
                case FAIL:
                    statementAccount = null;
            }
        }

        this.accountBatchNO = batchNO;
        this.account = user.allotAccount(commPreTax);
        this.accountType = account.getType();
    }

    public BigDecimal getStatementAmount() {
        return deal.getStatementAmount();
    }

    public int statementAccountSuccFirst() {
        return statementAccount != null && statementAccount.isSucc() ? 0 : 1;
    }

    public void updateFactFields() {
        Clearing target = getFlagOrig()
                ? getDiff() != null ? getDiff() : this
                : getOrig().getDiff();
        log.info("更新清分记录[id:{}][factTime:{}][factPreTax:{}][factAfterTax:{}]", target.getId(), target.getFactTime(), target.getFactPreTax(), target.getFactAfterTax());
        target.setFactTime(getStatementAccount().getPayTime());
        target.setFactPreTax(getReportPreTaxFact());
        target.setFactAfterTax(getReportAfterTaxFact());
        log.info("更新清分记录[id:{}][factTime:{}][factPreTax:{}][factAfterTax:{}]", target.getId(), target.getFactTime(), target.getFactPreTax(), target.getFactAfterTax());
    }

    public void setFactTime(LocalDateTime factTime) {
        this.factTime = factTime;
        this.effectTime = factTime;//成销时间默认与实际返佣时间一致(特殊情况下由人工调整成销时间)
    }
}
