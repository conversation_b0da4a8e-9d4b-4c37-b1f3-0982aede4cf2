package com.ejuetc.agent.domain.deal;

import com.ejuetc.agent.api.deal.BaseDealPO;
import com.ejuetc.agent.api.deal.EnabledOrderPO;
import com.ejuetc.agent.dto.DealDTO;
import jakarta.persistence.QueryHint;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.List;
import java.util.Optional;

import static jakarta.persistence.LockModeType.PESSIMISTIC_WRITE;

public interface DealRpt extends JpaRepositoryImplementation<Deal, Long> {
    @Query("""
            from Deal d
            where d.srcId = :#{#po.srcId}
            and d.type = :#{#po.type}
            """)
    @Lock(PESSIMISTIC_WRITE)
    Deal findExist(BaseDealPO po);

    @Lock(PESSIMISTIC_WRITE)
    Optional<Deal> findByTypeAndSrcId(DealDTO.Type type, Long srcId);

    @Query("""
            from ChannelOpenDeal d
            where d.orderId = :orderId
            """)
    @Lock(PESSIMISTIC_WRITE)
    Optional<ChannelOpenDeal> findAndLockByTypeAndOrderId(Long orderId);

    @Query("""
            from Deal d
            where d.status='INIT'
            and d.createTime<current_timestamp - 2 minute
            order by d.createTime
            limit :batchSize
            """)
    @Lock(PESSIMISTIC_WRITE)
    @QueryHints({@QueryHint(name = "javax.persistence.lock.timeout", value = "60000")})
    List<Deal> findWaitConfig(int batchSize);

    @Query("""
            from Deal d
            where d.status in ('CONFIGED','WAIT_NOTIFY','WAIT_CLEAR')
            and d.payAmount>0
            order by case when d.parent is null then 0 else 1 end, d.parent.id, d.createTime
            """)
    @Lock(PESSIMISTIC_WRITE)
    @QueryHints({@QueryHint(name = "javax.persistence.lock.timeout", value = "60000")})
    List<Deal> findWaitClearing();

    @Query("""
            from Deal d
            where d.id=:dealId
            """)
    @Lock(PESSIMISTIC_WRITE)
    Deal findAndLockById(Long dealId);

    @Query("""
            SELECT COUNT(d)
            from ChannelOpenDeal d
            where d.type = 'CHANNEL_OPEN'
            AND d.category != 'EXPIRE'
            AND (:#{#po.channelCode} IS NULL OR d.channelCode = :#{#po.channelCode} OR d.channelCode is null)
            AND (:#{#po.businessCode} IS NULL OR d.businessCode = :#{#po.businessCode} OR d.businessCode is null)
            AND (:#{#po.upDelegate} IS NULL OR d.upDelegateFlag = :#{#po.upDelegate})
            AND (:#{#po.rentBind} IS NULL OR d.brokerUser.bindXYRent = :#{#po.rentBind})
            AND (:#{#po.saleBind} IS NULL OR d.brokerUser.bindXYSale = :#{#po.saleBind})
            AND (:#{#po.partnerUserId} IS NULL OR d.partnerUser.id = :#{#po.partnerUserId})
            AND (:#{#po.partnerInvitationUserId} IS NULL OR d.partnerInvitation.user.id = :#{#po.partnerInvitationUserId})
            AND (:#{#po.partnerInvitationOperatorUserId} IS NULL OR d.partnerInvitation.operator.id = :#{#po.partnerInvitationOperatorUserId})
            AND (:#{#po.agentUserId} IS NULL OR d.agentUser.id = :#{#po.agentUserId})
            AND (:#{#po.agentInvitationUserId} IS NULL OR d.agentInvitation.user.id = :#{#po.agentInvitationUserId})
            AND (:#{#po.agentInvitationOperatorUserId} IS NULL OR d.agentInvitation.operator.id = :#{#po.agentInvitationOperatorUserId})
            AND (:#{#po.keyWord} IS NULL OR d.brokerUserName LIKE CONCAT('%', :#{#po.keyWord}, '%') OR d.brokerOperatorName LIKE CONCAT('%', :#{#po.keyWord}, '%') OR d.parentOrderNum LIKE CONCAT('%', :#{#po.keyWord}, '%'))
            """)
    Long amountOrderByEnabled(EnabledOrderPO po);

    @Query("""
            from ChannelOpenDeal d
            where d.type = 'CHANNEL_OPEN'
            AND d.category != 'EXPIRE'
            AND (:#{#po.channelCode} IS NULL OR d.channelCode = :#{#po.channelCode} OR d.channelCode is null)
            AND (:#{#po.businessCode} IS NULL OR d.businessCode = :#{#po.businessCode} OR d.businessCode is null)
            AND (:#{#po.upDelegate} IS NULL OR d.upDelegateFlag = :#{#po.upDelegate})
            AND (:#{#po.rentBind} IS NULL OR d.brokerUser.bindXYRent = :#{#po.rentBind})
            AND (:#{#po.saleBind} IS NULL OR d.brokerUser.bindXYSale = :#{#po.saleBind})
            AND (:#{#po.partnerUserId} IS NULL OR d.partnerUser.id = :#{#po.partnerUserId})
            AND (:#{#po.partnerInvitationUserId} IS NULL OR d.partnerInvitation.user.id = :#{#po.partnerInvitationUserId})
            AND (:#{#po.partnerInvitationOperatorUserId} IS NULL OR d.partnerInvitation.operator.id = :#{#po.partnerInvitationOperatorUserId})
            AND (:#{#po.agentUserId} IS NULL OR d.agentUser.id = :#{#po.agentUserId})
            AND (:#{#po.agentInvitationUserId} IS NULL OR d.agentInvitation.user.id = :#{#po.agentInvitationUserId})
            AND (:#{#po.agentInvitationOperatorUserId} IS NULL OR d.agentInvitation.operator.id = :#{#po.agentInvitationOperatorUserId})
            AND (:#{#po.keyWord} IS NULL OR d.brokerUserName LIKE CONCAT('%', :#{#po.keyWord}, '%') OR d.brokerOperatorName LIKE CONCAT('%', :#{#po.keyWord}, '%') OR d.parentOrderNum LIKE CONCAT('%', :#{#po.keyWord}, '%'))
            order by d.payTime desc
            """)
    List<Deal> selOrderByEnabled(EnabledOrderPO po, Pageable pageable);
}
