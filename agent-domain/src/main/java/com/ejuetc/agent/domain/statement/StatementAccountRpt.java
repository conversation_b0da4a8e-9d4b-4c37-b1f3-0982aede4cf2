package com.ejuetc.agent.domain.statement;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.List;

public interface StatementAccountRpt extends JpaRepositoryImplementation<StatementAccount, Long> {


    @Modifying
    @Query(value = """
            insert into tb_statement_account(id,statement_id,account_id,type,user_id,account_batch_no,clearing_item_count,comm,comm_pre_tax,disburse_fee,excess_comm,normal_comm,report_after_tax,report_pre_tax,statement_amount)
                                 select NEXTVAL(seq_statement_account_id),
                                        statement_id,
                                        account_id,
                                        account_type,
                                        user_id,
                                        :batchNO,
                                        count(c.id),
                                        sum(c.comm),
                                        sum(c.comm_pre_tax),
                                        sum(c.disburse_fee),
                                        sum(c.excess_comm),
                                        sum(c.normal_comm),
                                        sum(c.report_after_tax),
                                        sum(c.report_pre_tax),
                                        sum(c.statement_amount)
                                 from tb_clearing c
                                 where c.account_batch_no=:batchNO
                                 group by statement_id,account_id,account_type,user_id
            """, nativeQuery = true)
    int batchInsert(String batchNO);

    List<StatementAccount> findByStatement(Statement statement);

    @Query("""
            SELECT DISTINCT c
            FROM StatementAccount c
            JOIN FETCH c.account a
            WHERE
                c.type = 'YZH_ACCOUNT'
                AND c.statement.id in :statementIds
            ORDER BY c.createTime DESC
            """)
    List<StatementAccount> findPersonalListByStatementIds(List<Long> statementIds);
}
