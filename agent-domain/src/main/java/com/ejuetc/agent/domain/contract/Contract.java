package com.ejuetc.agent.domain.contract;

import static com.alibaba.fastjson.JSON.parseObject;
import static com.ejuetc.agent.dto.ContractDTO.Status.EFFECTIVE;
import static com.ejuetc.agent.dto.ContractDTO.Status.INIT;
import static com.ejuetc.agent.dto.ContractDTO.Status.SIGN_FAIL;
import static com.ejuetc.agent.dto.ContractDTO.Status.SIGN_ING;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.DateTimeUtils.formatDate;
import static java.time.LocalDateTime.now;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

import com.ejuetc.agent.domain.esign.ESign;
import com.ejuetc.agent.domain.esign.ESignTarget;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.OptimisticLock;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import com.alibaba.fastjson.JSONObject;
import com.ejuetc.agent.api.contract.CreateAgentContractPO;
import com.ejuetc.agent.api.contract.CreateBrokerContractPO;
import com.ejuetc.agent.api.contract.CreateContractPO;
import com.ejuetc.agent.api.contract.CreatePartnerContractPO;
import com.ejuetc.agent.api.contract.CrmResultPo;
import com.ejuetc.agent.domain.crm.CrmInstruct;
import com.ejuetc.agent.domain.crm.CrmInstruct4Contract;
import com.ejuetc.agent.domain.esign.Template;
import com.ejuetc.agent.domain.esign.TemplateRpt;
import com.ejuetc.agent.domain.invitation.Invitation;
import com.ejuetc.agent.domain.invitation.InvitationRpt;
import com.ejuetc.agent.domain.user.User;
import com.ejuetc.agent.domain.user.UserRpt;
import com.ejuetc.agent.dto.ContractDTO;
import com.ejuetc.agent.dto.UserDTO;
import com.ejuetc.commons.base.clazz.ClassUtils;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.usertype.ListUT;
import com.ejuetc.commons.base.usertype.MemoUT;
import com.ejuetc.commons.base.valueobj.Memo;
import com.ejuetc.commons.base.valueobj.time.TimeInterval;

import jakarta.persistence.AttributeOverride;
import jakarta.persistence.AttributeOverrides;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorColumn;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Inheritance;
import jakarta.persistence.InheritanceType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Getter
@Entity
@Slf4j
@NoArgsConstructor
@Comment("签约合同")
@Table(name = "tb_contract")
@Where(clause = "logic_delete = 0")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", columnDefinition = "enum('PARTNER','AGENT','BROKER') COMMENT '签约类型'")
public abstract class Contract extends BaseEntity<Contract> implements ESignTarget {

    @Id
    @GeneratedValue(generator = "contract_id")
    @SequenceGenerator(name = "contract_id", sequenceName = "seq_contract_id")
    private Long id;

    @Column(name = "contract_no", columnDefinition = "varchar(255) COMMENT '合同编号'")
    private String contractNo;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, insertable = false, updatable = false)
    private ContractDTO.Type type;

    @Enumerated(EnumType.STRING)
    @Column(name = "sub_type")
    private ContractDTO.SubType subType;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    protected ContractDTO.Status status = INIT;

    @Type(value = ListUT.class, parameters = @org.hibernate.annotations.Parameter(name = "splitChar", value = "\n"))
    @Column(name = "complete_files", columnDefinition = "text COMMENT '下载完成的文件'")
    protected List<String> completeFiles = new ArrayList<>();

    @Type(MemoUT.class)
    @Column(name = "memo", columnDefinition = "text COMMENT '备注信息'")
    protected Memo memo = new Memo();

    @Enumerated(EnumType.STRING)
    @Column(name = "user_type", nullable = false)
    private UserDTO.Type userType;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", columnDefinition = "bigint(20) COMMENT '用户外键'")
    protected User user;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "operator_id", columnDefinition = "bigint(20) COMMENT '操作人外键'")
    protected User operator;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "invited_id", columnDefinition = "bigint(20) COMMENT '(默认)被邀请外键'")
    private Invitation invited;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "invited_user_id", columnDefinition = "bigint(20) COMMENT '(默认)被邀请所属主体外键'")
    private User invitedUser;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "invited_contract_id", columnDefinition = "bigint(20) COMMENT '(默认)被邀请所属合同外键'")
    protected Contract invitedContract;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "template_id", columnDefinition = "bigint(20) COMMENT '使用合同模板外键'")
    protected Template template;

    @Column(name = "invited_code", columnDefinition = "varchar(63) COMMENT '(默认)被邀请推荐码'")
    private String invitedCode;

    @ManyToMany(mappedBy = "contracts", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Invitation> inviteds = new ArrayList<>();

    @Embedded
    @AttributeOverrides({
            @AttributeOverride(name = "beginTime", column = @Column(name = "contract_begin_time", columnDefinition = "datetime COMMENT '合同开始时间'")),
            @AttributeOverride(name = "endTime", column = @Column(name = "contract_end_time", columnDefinition = "datetime COMMENT '合同结束时间'"))
    })
    private TimeInterval contractIndate;

    @Column(name = "city_code", columnDefinition = "varchar(64) COMMENT '主营城市编码'")
    protected String cityCode;

    @Column(name = "city_name", columnDefinition = "varchar(64) COMMENT '主营城市名称'")
    protected String cityName;

    @OptimisticLock(excluded = true)
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "contract")
    protected List<CrmInstruct> crmInstruct = new ArrayList<>();

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "crm_instruct_id", columnDefinition = "bigint(20) COMMENT 'CRM交互号'")
    protected CrmInstruct4Contract lastCrmInstruct;

    @Column(name = "oa_num", columnDefinition = "varchar(255) COMMENT 'OA编号'")
    protected String oaNum;

    @Enumerated(EnumType.STRING)
    @Column(name = "oa_status", nullable = false)
    protected ContractDTO.OAStatus oaStatus = ContractDTO.OAStatus.INIT;

    @Type(MemoUT.class)
    @Column(name = "oa_memo", columnDefinition = "text COMMENT 'oa备注信息'")
    protected Memo oaMemo = new Memo();

    @Column(name = "oa_pass_time", columnDefinition = "datetime COMMENT 'OA审核通过时间'")
    protected LocalDateTime oaPassTime;

    @Setter
    @Column(name = "supp_flag", columnDefinition = "bit(1) DEFAULT b'0'  COMMENT '是否未补录'")
    protected Boolean suppFlag = false;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "suppl_esign_id", columnDefinition = "bigint(20) COMMENT '补充协议电子签Id'")
    private ESign supplESign;

    @Column(name = "suppl_esign_url", columnDefinition = "varchar(255) COMMENT '补充协议线下签文件'")
    protected String supplEsignUrl;

    public void launchSupplESign(Template template) {
        if (supplESign != null) throw new CodingException("补充协议已存在");
        supplESign = new ESign(getUser(), template, this);
        supplESign.exec();
    }

    public static Contract newContract(CreateContractPO po) {
        if (po instanceof CreatePartnerContractPO partnerContractPO)
            return new PartnerContract(partnerContractPO);
        if (po instanceof CreateAgentContractPO agentContractPO)
            return new AgentContract(agentContractPO);
        if (po instanceof CreateBrokerContractPO brokerContractPO)
            return new BrokerContract(brokerContractPO);
        throw new CodingException("未知的签约类型");
    }

    public ContractDTO.Type getType() {
        if (type == null)
            type = ContractDTO.Type.valueOf(ClassUtils.getClassAnnotation(this.getClass(), DiscriminatorValue.class).value());
        return type;
    }

    public Contract(CreateContractPO po) {
        this.user = getBean(UserRpt.class).findById(po.getUserId()).orElseThrow(() -> new CodingException("用户不存在[%s]", po.getUserId()));
        this.userType = user.getType();
        this.operator = getBean(UserRpt.class).findById(po.getOperatorId()).orElseThrow(() -> new CodingException("操作人不存在[%s]", po.getOperatorId()));
        this.template = getBean(TemplateRpt.class).findByUserTypeAndType(user.getType(), getType().getTemplateType()).orElseThrow(() -> new CodingException("合同模板不存在[" + user.getType() + "," + getType() + "]"));
        this.contractIndate = template.makeContractIndate();
        if (po.getBeginTime() != null)
            this.contractIndate = new TimeInterval(po.getBeginTime(), contractIndate.getEndTime());
        if (po.getEndTime() != null)
            this.contractIndate = new TimeInterval(contractIndate.getBeginTime(), po.getEndTime());
        this.cityCode = po.getCityCode();
        this.cityName = po.getCityName();
        this.subType = po.getSubType() != null ? po.getSubType() : getType().getDefSubType();
        this.contractNo = "%s_%s_%s_%s".formatted(this.subType.name(), formatDate(new Date(), "yyMMdd"), po.getUserId(), new Random().nextLong(100000, 999999));
        this.suppFlag = po.getSuppFlag();
        checkInvitedCode(po.getInvitationCode(), true, false);
    }

    public void makeOperatorInvitation() {
        if (user.withoutInvitation(user)) {
            user.newInvitation(user);
        }
        if (operator.withoutInvitation(user)) {
            user.newInvitation(operator);
        }
    }

    public void checkInvitedCode(String invitedCode, boolean update, boolean relation) {
        ContractDTO.Type invitedType = getType().getInvitedType();
        if (invitedType == null && invitedCode == null)
            return;

        Invitation invited = getBean(InvitationRpt.class).findByCode(invitedCode).orElseThrow(() -> new BusinessException("bc.ejuetc.agent.1003", invitedCode));
        Contract invitedContract = invited.getUser().findContract(getType().getInvitedType(), now(), cityCode, List.of(ContractDTO.Status.EFFECTIVE));
        if (invitedContract == null) throw new CodingException("未找到用户[%s],类型[%s],时间[%s],城市[%s]的合同", invited.getUser(), getType().getInvitedType(), now(), cityCode);
        if (update) {
            this.invitedCode = invitedCode;
            this.invited = invited;
            this.invitedUser = invited.getUser();
            this.invitedContract = invitedContract;
        }
        if (relation) {
            addInvited(invited);
        }
    }

    public void addInvited(Invitation invitation) {
        if (!inviteds.contains(invitation)) {
            inviteds.add(invitation);
        }
        if (!invitation.getContracts().contains(this)) {
            invitation.addContract(this);
        }
    }

    void doCheckInvitedCode(Invitation invited, Contract invitedContract) {
    }


    public void exec() {
        if (status != INIT)
            throw new CodingException("合同已生效");
        status = EFFECTIVE;
    }

    public boolean isMatch(ContractDTO.Type contractType, LocalDateTime time, String cityCode, List<ContractDTO.Status> statuses) {
        return getType() == contractType && statuses.contains(getStatus()) && contractIndate.isInclude(time) && isApplyCity(cityCode);
    }

    public boolean serviceOverlap(Contract po) {
        return true;
    }

    public abstract boolean isApplyCity(String cityCode);

    public void signResult(Boolean signResult, String description, List<String> completeFiles) {
        if (!status.equals(SIGN_ING)) {
            log.error("合同状态异常:{}", status);
            return;
        }
        if (signResult) {
            this.status = EFFECTIVE;
            this.completeFiles = completeFiles;
            this.getUser().authPass();
            sendCrmContract();
        } else {
            status = SIGN_FAIL;
            memo.addMemo(description);
        }
    }

    public void sendCrmContract() {
        if (this.getUser().getEmployeeFlag())
            return;
        this.lastCrmInstruct = new CrmInstruct4Contract(this);
        this.crmInstruct.add(lastCrmInstruct);
        this.lastCrmInstruct.exec();
        this.oaStatus = switch (lastCrmInstruct.getStatus()) {
            case SUCC -> !this.suppFlag ? ContractDTO.OAStatus.IN_HAND : ContractDTO.OAStatus.SUCCESS;
            default -> ContractDTO.OAStatus.FAIL;
        };
        if (oaStatus != ContractDTO.OAStatus.FAIL) {
            JSONObject jsonObject = parseObject(this.lastCrmInstruct.getResultData());
            if (!jsonObject.getString("dlrContractNo").equals(this.getContractNo())) {
                throw new CodingException("代理合同编号[" + this.getId() + "]异常");
            }
            this.oaNum = jsonObject.getString("oaNo");
        }
    }

    public void contractCrmResult(CrmResultPo po) {
        if (StringUtils.isNotBlank(po.getRejectReason())) {
            this.oaMemo.addMemo(po.getRejectReason());
        }
        this.oaNum = po.getOaNum();
        this.oaStatus = po.getSuccess() ? ContractDTO.OAStatus.SUCCESS : ContractDTO.OAStatus.FAIL;
        if (po.getSuccess())
            this.oaPassTime = po.getAuditTime();
    }

    public static void main(String[] args) {
        System.out.println(formatContractDateStr(now()));
    }

    public String getContractBeginFormat() {
        return formatContractDateStr(contractIndate.getBeginTime());
    }

    public String getContractEndFormat() {
        return formatContractDateStr(contractIndate.getEndTime());
    }

    private static String formatContractDateStr(LocalDateTime dateTime) {
        return dateTime.format(DateTimeFormatter.ofPattern("【 yyyy 】年【 MM 】月【 dd 】日"));
    }
}
