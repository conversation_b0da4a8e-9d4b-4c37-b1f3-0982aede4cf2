package com.ejuetc.agent.domain.esign;

import com.ejuetc.agent.domain.price.Price;
import com.ejuetc.agent.dto.TemplateDTO;
import com.ejuetc.agent.dto.UserDTO;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.usertype.FMTemplateUT;
import com.ejuetc.commons.base.usertype.PeriodStringConverter;
import com.ejuetc.commons.base.valueobj.FMTemplate;
import com.ejuetc.commons.base.valueobj.time.TimeInterval;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.Period;
import java.util.ArrayList;
import java.util.List;

@Getter
@Entity
@NoArgsConstructor
@Comment("(合同)模板")
@Table(name = "tb_template")
@Where(clause = "logic_delete = 0")
public class Template extends BaseEntity<Template> implements Serializable {

    @Id
    @GeneratedValue(generator = "template_id")
    @SequenceGenerator(name = "template_id", sequenceName = "seq_template_id")
    private Long id;

    @Column(name = "name", columnDefinition = "varchar(64) COMMENT '模板名称'", nullable = false)
    private String name;

    @Type(FMTemplateUT.class)
    @Column(name = "doc_param", columnDefinition = "json COMMENT '生成合同文件参数模板'")
    private FMTemplate docParam;

    @Type(FMTemplateUT.class)
    @Column(name = "flow_param", columnDefinition = "json COMMENT '发起签名参数模板'")
    private FMTemplate flowParam;

    @Type(FMTemplateUT.class)
    @Column(name = "rescission_flow_param", columnDefinition = "json COMMENT '合同解约参数模板'")
    private FMTemplate rescissionFlowParam;

    @Enumerated(EnumType.STRING)
    @Column(name = "user_type")
    private UserDTO.Type userType;

    @Convert(converter = PeriodStringConverter.class)
    @Column(name = "contract_period", columnDefinition = "varchar(64) COMMENT '合同周期'")
    private Period contractPeriod;

    @Column(name = "contract_end_time", columnDefinition = "datetime(6) COMMENT '合同结束时间(优先于合同周期)'")
    private LocalDateTime contractEndTime;

    @ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinTable(name = "tb_template_price",
            joinColumns = @JoinColumn(name = "template_id", columnDefinition = "bigint(20) COMMENT '模板外键'"),
            inverseJoinColumns = @JoinColumn(name = "price_id", columnDefinition = "bigint(20) COMMENT '价格外键'")
    )
    @Comment("模板关联代理价")
    private List<Price> prices = new ArrayList<>();

    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private TemplateDTO.Type type;

    public TimeInterval makeContractIndate() {
        if (contractEndTime != null) {
            return new TimeInterval(LocalDateTime.now().minusMinutes(5), contractEndTime);
        } else if (contractPeriod != null) {
            return new TimeInterval(LocalDateTime.now().minusMinutes(5), contractPeriod);
        } else {
            throw new CodingException("合同周期未设置");
        }
    }

}
