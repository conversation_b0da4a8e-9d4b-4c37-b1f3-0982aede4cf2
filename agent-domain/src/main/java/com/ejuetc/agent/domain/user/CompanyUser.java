package com.ejuetc.agent.domain.user;

import com.alibaba.fastjson.JSONObject;
import com.ejuetc.agent.api.account.CreateAccountPO;
import com.ejuetc.agent.api.account.CreateBankAccountPO;
import com.ejuetc.agent.api.user.EditUserPO;
import com.ejuetc.agent.domain.account.Account;
import com.ejuetc.agent.domain.contract.Contract;
import com.ejuetc.agent.integration.esign.ESignComponent;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.integration.user.EmployeeCompanyRO;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.spring.SpringUtil.getProperty;
import static java.math.BigDecimal.ONE;

@Slf4j
@Entity
@DiscriminatorValue("COMPANY")
@SubtypeCode(parent = Contract.class, code = "COMPANY", name = "公司用户")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class CompanyUser extends User {

    @Override
    protected BigDecimal getDefaultTaxRate() {
        return new BigDecimal("0.06");
    }

    public CompanyUser(EditUserPO po) {
        super(po);
    }

    @Override
    protected JSONObject doLaunchAuth() {
        if (getCompanyNum() == null || getCompanyName() == null || getContactName() == null || getContactMobile() == null) {
            throw new CodingException("用户信息不全,不能进行实名认证");
        }

        return getBean(ESignComponent.class).call(
                "/v3/org-auth-url",
                Map.of(
                        "orgAuthConfig", Map.of(
                                "orgName", getCompanyName(),
                                "orgInfo", Map.of(
                                        "orgIDCardNum", getCompanyNum(),
                                        "orgIDCardType", "CRED_ORG_USCC"
                                ),
                                "transactorInfo", Map.of(
                                        "psnAccount", getContactMobile(),
                                        "psnInfo", Map.of(
                                                "psnName", getContactName()
                                        )
                                )
                        ),
                        "authorizeConfig", Map.of(
                                "authorizedScopes", List.of(
                                        "org_initiate_sign"
                                )
                        ),
                        "redirectConfig", Map.of(
                                "redirectUrl", getProperty("esign.redirectUrl")
                        ),
                        "notifyUrl", getProperty("esign.notifyUrl")
                )
        );

    }

    public String getCertName() {
        return getCompanyName();
    }

    public String getCertNum() {
        return getCompanyNum();
    }

    @Override
    public Account addAccount(CreateAccountPO po) {
        if (getAccounts().isEmpty()) {
            return super.addAccount(po);
        }
        Account account = getAccounts().get(0);
        CreateBankAccountPO bankPO = (CreateBankAccountPO) po;
        if (!account.getAccountNO().equals(bankPO.getAccountNO())) {
            throw new CodingException("公司[%s]已有银行账号[%s],不能新增银行账号[%s]", getId(), account.getAccountNO(), bankPO.getAccountNO());
        }

        return account;
    }
}


