package com.ejuetc.agent.domain.statement;

import com.ejuetc.agent.api.statement.FlowActionPO;
import com.ejuetc.agent.dto.StatementDTO;
import com.ejuetc.commons.base.exception.CodingException;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Getter
@Embeddable
@NoArgsConstructor
public class StatementFlow {
    @Enumerated(EnumType.STRING)
    @Column(name = "from_status")
    private StatementDTO.Status fromStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "role")
    private StatementDTO.Role role;

    @Enumerated(EnumType.STRING)
    @Column(name = "action")
    private StatementDTO.Action action;

    @Transient
    private StatementDTO.Status toStatus;

    @Transient
    private Callback callback;

    private static final List<StatementFlow> registry = new ArrayList<>();

    public static void register(StatementDTO.Status from, StatementDTO.Role role, StatementDTO.Action action, StatementDTO.Status to) {
        register(from, role, action, to, (statement, po) -> null);
    }

    public static void register(StatementDTO.Status from, StatementDTO.Role role, StatementDTO.Action action, StatementDTO.Status to, CallbackWithoutReturn callback) {
        registry.add(new StatementFlow(from, role, action, to, (statement, po) -> {
            callback.exec(statement, po);
            return null;
        }));
    }

    public static void register(StatementDTO.Status from, StatementDTO.Role role, StatementDTO.Action action, StatementDTO.Status to, CallbackWithoutParamReturn callback) {
        registry.add(new StatementFlow(from, role, action, to, (statement, po) -> {
            callback.exec(statement);
            return null;
        }));
    }

    public static void register(StatementDTO.Status from, StatementDTO.Role role, StatementDTO.Action action, StatementDTO.Status to, CallbackWithoutParam callback) {
        registry.add(new StatementFlow(from, role, action, to, (statement, po) -> callback.exec(statement)));
    }

    public static void register(StatementDTO.Status from, StatementDTO.Role role, StatementDTO.Action action, StatementDTO.Status to, Callback callback) {
        registry.add(new StatementFlow(from, role, action, to, callback));
    }

    public static StatementFlow of(StatementDTO.Status from, StatementDTO.Role role, StatementDTO.Action action) {
        return registry.stream().filter(f -> f.fromStatus == from && f.role == role && f.action == action).findFirst().orElseThrow(() -> new CodingException("未找到对应流程[%s,%s,%s]", from, role, action));
    }

    private StatementFlow(StatementDTO.Status fromStatus, StatementDTO.Role role, StatementDTO.Action action, StatementDTO.Status toStatus, Callback callback) {
        this.fromStatus = fromStatus;
        this.role = role;
        this.action = action;
        this.toStatus = toStatus;
        this.callback = callback;
    }

    public interface Callback {
        StatementDTO.Status exec(Statement statement, FlowActionPO po);
    }

    public interface CallbackWithoutParam {
        StatementDTO.Status exec(Statement statement);
    }

    public interface CallbackWithoutReturn {
        void exec(Statement statement, FlowActionPO po);
    }

    public interface CallbackWithoutParamReturn {
        void exec(Statement statement);
    }
}
