package com.ejuetc.agent.domain.account;

import com.ejuetc.agent.api.account.CreateAccountPO;
import com.ejuetc.agent.api.account.CreateBankAccountPO;
import com.ejuetc.agent.api.account.CreateYzhAccountPO;
import com.ejuetc.agent.domain.user.User;
import com.ejuetc.agent.dto.AccountDTO;
import com.ejuetc.commons.base.clazz.ClassUtils;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.usertype.MemoUT;
import com.ejuetc.commons.base.valueobj.Memo;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import java.math.BigDecimal;

import static com.ejuetc.agent.dto.AccountDTO.Status.BIND_SUCC;
import static com.ejuetc.agent.dto.AccountDTO.Status.INIT;
import static com.ejuetc.commons.base.spring.SpringUtil.getProperty;
import static java.time.LocalDateTime.now;

@Getter
@Entity
@NoArgsConstructor
@Comment("(佣金)账户")
@Table(name = "tb_account")
@Where(clause = "logic_delete = 0")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", columnDefinition = "enum('BANK_ACCOUNT','YZH_ACCOUNT') COMMENT '账户类型'")
public abstract class Account extends BaseEntity<Account> {

    @Id
    @GeneratedValue(generator = "account_id")
    @SequenceGenerator(name = "account_id", sequenceName = "seq_account_id")
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", insertable = false, updatable = false)
    private AccountDTO.Type type;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", columnDefinition = "bigint(20) COMMENT '用户外键'")
    protected User user;

    @Column(name = "self", nullable = false, columnDefinition = "bit(1) DEFAULT b'0' COMMENT '是否是自己的'")
    protected Boolean self = false;

    @Column(name = "external_no", columnDefinition = "varchar(63) COMMENT '外部编号'")
    protected String externalNO;

    @Column(name = "account_no", columnDefinition = "varchar(63) COMMENT '账户号(银行卡号或悠客账号)'")
    protected String accountNO;

    @Column(name = "account_name", columnDefinition = "varchar(63) COMMENT '账户名'")
    protected String accountName;

    @Column(name = "card_id", columnDefinition = "varchar(63) COMMENT '身份证号'")
    protected String cardId;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    protected AccountDTO.Status status = INIT;

    @Type(MemoUT.class)
    @Column(name = "memo", columnDefinition = "text COMMENT '备注信息'")
    protected Memo memo = new Memo();

    @Transient
    private BigDecimal monthSum = BigDecimal.ZERO;

    @Column(name = "sort", columnDefinition = "int(11) COMMENT '排序'", insertable = false, updatable = false)
    private Integer sort;

    public Account(User user) {
        this.user = user;
    }

    public static Account of(CreateAccountPO po, User user) {
        if (po instanceof CreateBankAccountPO bankAccountPO) {
            return new BankAccount(user, bankAccountPO);
        }

        if (po instanceof CreateYzhAccountPO yzhAccountPO) {
            return new YzhAccount(user, yzhAccountPO);
        }

        throw new CodingException("未知的账户类型[" + po.getClass() + "]");
    }

    public abstract void prepare();

    public AccountDTO.Type getType() {
        if (type == null)
            type = AccountDTO.Type.valueOf(ClassUtils.getClassAnnotation(this.getClass(), DiscriminatorValue.class).value());
        return type;
    }

    public boolean addSum(BigDecimal amount) {
        if (status != BIND_SUCC) return false;

        BigDecimal monthlyLimit = getMonthlyLimit();
        if (monthlyLimit == null) return true;

        BigDecimal addMonthSum = monthSum.add(amount);
        if (monthlyLimit.compareTo(addMonthSum) < 0)
            return false;

        monthSum = addMonthSum;
        return true;
    }

    protected void refreshSelf() {
        this.self = accountName != null && accountName.equals(user.getCertName())
                    && cardId != null && cardId.equals(user.getCertNum());
    }


    protected abstract BigDecimal getMonthlyLimit();

    public boolean isSuccess() {
        return status == BIND_SUCC;
    }
}
