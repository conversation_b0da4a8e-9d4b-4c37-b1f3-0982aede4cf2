package com.ejuetc.agent.domain.deal;

import com.ejuetc.agent.api.deal.CreateClueBuyDealPO;
import com.ejuetc.agent.domain.contract.Contract;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.spring.SpringUtil.getProperty;

@Slf4j
@Entity
@DiscriminatorValue("CLUE_BUY")
@SubtypeCode(parent = Contract.class, code = "CLUE_BUY", name = "线索购买")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class ClueBuyDeal extends Deal {


    public ClueBuyDeal(CreateClueBuyDealPO po) {
        super(po);
    }
}
