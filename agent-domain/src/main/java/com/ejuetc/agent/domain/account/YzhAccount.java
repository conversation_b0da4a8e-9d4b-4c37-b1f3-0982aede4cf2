package com.ejuetc.agent.domain.account;

import com.ejuetc.agent.api.account.CreateYzhAccountPO;
import com.ejuetc.agent.domain.contract.Contract;
import com.ejuetc.agent.domain.user.User;
import com.ejuetc.agent.integration.yzh.YzhApiResponse;
import com.ejuetc.agent.integration.yzh.YzhComponent;
import com.ejuetc.agent.integration.yzh.YzhResponseCode;
import com.ejuetc.agent.integration.yzh.ro.YzhSignRO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.spring.SpringUtil;
import com.ejuetc.commons.base.valueobj.Command;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

import static com.ejuetc.agent.dto.AccountDTO.Status.BIND_ING;
import static com.ejuetc.agent.dto.AccountDTO.Status.BIND_SUCC;
import static com.ejuetc.commons.base.spring.SpringUtil.getProperty;

@Getter
@Slf4j
@Entity
@DiscriminatorValue("YZH_ACCOUNT")
@SubtypeCode(parent = Contract.class, code = "YZH_ACCOUNT", name = "云账户账户")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class YzhAccount extends Account implements Command {

    @Column(name = "bank_name", columnDefinition = "varchar(63) COMMENT '银行名称'")
    private String bankName;

    @Column(name = "sign_url", columnDefinition = "varchar(255) COMMENT '签约路径'")
    private String signUrl;

    @Column(name = "notify_body", columnDefinition = "json COMMENT '通知内容'")
    private String notifyBody;

    @Column(name = "query_result", columnDefinition = "json COMMENT '查询结果'")
    private String queryResult;

    public YzhAccount(User user, CreateYzhAccountPO po) {
        super(user);
        preCheck(po);
        this.accountName = po.getAccountName();
        this.cardId = po.getCardId();
        this.bankName = po.getBankName();
        this.accountNO = po.getAccountNO();
        refreshSelf();
        selfAccountVerify();
        bankVerify();
        userSign();
    }

    private void preCheck(CreateYzhAccountPO po) {
        if (StringUtils.isBlank(po.getAccountName())
                || StringUtils.isBlank(po.getCardId())
                || StringUtils.isBlank(po.getBankName())
                || StringUtils.isBlank(po.getAccountNO())) {
            throw new CodingException("银行信息和身份证号不能为空");
        }
    }

    private void userSign() {
        YzhApiResponse<YzhSignRO> signResponse =
                SpringUtil.getBean(YzhComponent.class).userSign(this.accountName, this.cardId);
        if (!signResponse.isSucc()) {
            throw new CodingException("云账户签约失败:" + signResponse.getMessage());
        }
        this.status = BIND_SUCC;
    }

    private void bankVerify() {
        YzhComponent yzhComponent = SpringUtil.getBean(YzhComponent.class);
        YzhApiResponse<Void> validAccount = yzhComponent.bankCardVerify(this.cardId, this.accountName, this.accountNO);
        if (!validAccount.isSucc()) {
            throw new BusinessException("bc.ejuetc.agent.1006");
        }
    }

    private void selfAccountVerify() {
        //绑定的本人账户
        if (this.getSelf()) {
            return;
        }

        //本人账户已经绑定
        boolean selfSuccessBind = this.user.getAccounts().stream()
                .anyMatch(a -> a.getSelf() && a.getStatus() == BIND_SUCC);
        if (selfSuccessBind) {
            return;
        }

        //白名单
        String whiteList = getProperty("com.ejuetc.agent.account.accountWhiteList");
        if (whiteList.contains(this.user.getId().toString())) {
            return;
        }

        throw new BusinessException("bc.ejuetc.agent.1007");
    }

    @Override
    public void prepare() {
        if (getStatus() == BIND_SUCC) {
            return;
        }
        this.status = BIND_SUCC;
    }

    @Override
    protected BigDecimal getMonthlyLimit() {
        return getProperty("yzh.monthlySum", BigDecimal.class, new BigDecimal("98000"));
    }

    public void receiveNotify(String notifyBody) {

    }

    public void querySignResult() {

    }

    @Override
    public void exec() {
        if (getStatus() == BIND_ING) {
            try {
                querySignResult();
            } catch (Exception e) {
                log.error("querySignResult error", e);
                memo.addMemo(e.getMessage());
            }
        }
    }
}