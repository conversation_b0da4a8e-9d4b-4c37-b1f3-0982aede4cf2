package com.ejuetc.agent.domain.deal;

import com.ejuetc.agent.api.deal.*;
import com.ejuetc.agent.domain.clearing.Clearing;
import com.ejuetc.agent.domain.clearing.Clearing4Agent;
import com.ejuetc.agent.domain.clearing.Clearing4Partner;
import com.ejuetc.agent.domain.contract.*;
import com.ejuetc.agent.domain.invitation.Invitation;
import com.ejuetc.agent.domain.invitation.InvitationRpt;
import com.ejuetc.agent.domain.price.Price;
import com.ejuetc.agent.domain.user.User;
import com.ejuetc.agent.domain.user.UserRpt;
import com.ejuetc.agent.dto.ContractDTO;
import com.ejuetc.agent.dto.DealDTO;
import com.ejuetc.agent.dto.UserDTO;
import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.clazz.ClassUtils;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.usertype.ListUT;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.OptimisticLock;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.ejuetc.agent.dto.ContractDTO.Status.EFFECTIVE;
import static com.ejuetc.agent.dto.ContractDTO.Status.FREEZE;
import static com.ejuetc.agent.dto.DealDTO.Category.*;
import static com.ejuetc.agent.dto.DealDTO.Status.*;
import static com.ejuetc.agent.dto.DealDTO.Type.*;
import static com.ejuetc.channel.dto.ChannelDTO.Code.ALIPAY;
import static com.ejuetc.commons.base.clazz.ClassUtils.caseToSubType;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.DateTimeUtils.max;
import static com.ejuetc.commons.base.utils.StringUtils.splitString;
import static java.math.BigDecimal.ONE;
import static java.math.BigDecimal.ZERO;
import static java.math.RoundingMode.HALF_UP;
import static java.time.LocalDateTime.now;

@Slf4j
@Getter
@Setter(AccessLevel.PROTECTED)
@Entity
@NoArgsConstructor
@Comment("交易")
@Table(name = "tb_deal", uniqueConstraints = @UniqueConstraint(columnNames = {"src_id", "type"}))
@Where(clause = "logic_delete = 0")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", columnDefinition = "enum('CHANNEL_OPEN','CLUE_BUY','CLUE_CONSUME') COMMENT '交易类型'")
public class Deal extends BaseEntity<Deal> {
    private static final BigDecimal PT10 = new BigDecimal("0.1");
    private static final List<String> COST_ZERO_CITYS = splitString("110100,120100,130100,130600,130800,140100,210100,210200,230100,310100,320100,320200,320300,320500,330100,330200,330300,340100,350100,350200,350500,360100,370100,370200,370300,370600,370700,371300,410100,420100,430100,440100,440300,440600,440700,441200,441300,441900,442000,450100,450200,450300,460100,500100,510100,510700,520100,530100,610100,650100");
    public static final LocalDateTime COST_IMPORT_LIMIT = LocalDateTime.of(2025, 3, 18, 0, 0);//250318后的交易不再需要倒入成本

    @Id
    @GeneratedValue(generator = "deal_id")
    @SequenceGenerator(name = "deal_id", sequenceName = "seq_deal_id")
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, insertable = false, updatable = false)
    private DealDTO.Type type;

    @Comment("交易分类")
    @Enumerated(EnumType.STRING)
    @Column(name = "category")
    private DealDTO.Category category;

    @Column(name = "indate_begin", columnDefinition = "datetime COMMENT '交易有效开始时间'")
    private LocalDateTime indateBegin;

    @Column(name = "indate_end", columnDefinition = "datetime COMMENT '交易有效结束时间'")
    private LocalDateTime indateEnd;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    protected DealDTO.Status status = INIT;

    @Column(name = "src_id", columnDefinition = "bigint(20) COMMENT '来源端交易ID'", nullable = false)
    private Long srcId;

    @Column(name = "parent_src_id", columnDefinition = "bigint(20) COMMENT '来源端父交易ID'")
    private Long parentSrcId;

    @Column(name = "crm_order_no", columnDefinition = "varchar(255) COMMENT 'CRM订单号'")
    private String crmOrderNO;

    @Comment("渠道编码")
    @Enumerated(EnumType.STRING)
    @Column(name = "channel_code")
    private ChannelDTO.Code channelCode;

    @Comment("业务编码")
    @Enumerated(EnumType.STRING)
    @Column(name = "business_code")
    private BusinessOpenDTO.Code businessCode;

    @Column(name = "pay_amount", columnDefinition = "decimal(20,2) default 0 COMMENT '支付金额'", nullable = false)
    private BigDecimal payAmount;

    @Column(name = "pay_time", columnDefinition = "datetime COMMENT '支付时间'", nullable = false)
    private LocalDateTime payTime;

    @Column(name = "fact_pay_time", columnDefinition = "datetime COMMENT '实际支付时间'")
    private LocalDateTime factPayTime;

    @Column(name = "pay_audit_time", columnDefinition = "datetime COMMENT '支付审核时间'")
    private LocalDateTime payAuditTime;

    @Column(name = "cost_zero_flag", nullable = false, columnDefinition = "bit(1) DEFAULT b'0' COMMENT '成本为0城市标记'")
    private boolean costZeroFlag = false;

    @Column(name = "cost_import_time", columnDefinition = "datetime COMMENT '成本导入时间'")
    private LocalDateTime costImportTime;

    @Column(name = "cost_price", columnDefinition = "decimal(20,2) COMMENT '成本价'")
    private BigDecimal costPrice;

    @Column(name = "city_code", columnDefinition = "varchar(64) COMMENT '城市编码'")
    private String cityCode;

    @Column(name = "city_name", columnDefinition = "varchar(64) COMMENT '城市名称'")
    private String cityName;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "broker_operator_id", columnDefinition = "bigint(20) COMMENT '经纪操作员外键'")
    private User brokerOperator;

    @Column(name = "broker_operator_name", columnDefinition = "varchar(255) COMMENT '经纪机构操作员名称'")
    private String brokerOperatorName;

    @Enumerated(EnumType.STRING)
    @Column(name = "broker_user_type")
    private UserDTO.Type brokerUserType;

    @Column(name = "agent_code", columnDefinition = "varchar(63) COMMENT '被邀请代理商的推荐码'", nullable = false)
    private String agentCode;

    @Column(name = "partner_code", columnDefinition = "varchar(63) COMMENT '合伙商的推荐码'")
    private String partnerCode;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "broker_user_id", columnDefinition = "bigint(20) COMMENT '(默认)被邀请所属合伙主体外键'", nullable = false)
    private User brokerUser;

    @Column(name = "broker_user_name", columnDefinition = "varchar(255) COMMENT '经纪机构名称'")
    private String brokerUserName;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "broker_contract_id", columnDefinition = "bigint(20) COMMENT '经纪合同外键'")
    private BrokerContract brokerContract;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_invited_id", columnDefinition = "bigint(20) COMMENT '被邀请代理商邀请外键'")
    private Invitation agentInvitation;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "partner_invited_id", columnDefinition = "bigint(20) COMMENT '合伙邀请外键'")
    private Invitation partnerInvitation;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_user_id", columnDefinition = "bigint(20) COMMENT '(默认)被邀请所属代理主体外键'")
    private User agentUser;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_original_operator_id", columnDefinition = "bigint(20) COMMENT '代理邀请码原始操作员外键'")
    private User agentOriginalOperator;

    @JoinColumn(name = "agent_original_operator_name", columnDefinition = "varchar(255) COMMENT '代理邀请码原始操作员名称'")
    private String agentOriginalOperatorName;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_current_operator_id", columnDefinition = "bigint(20) COMMENT '代理邀请码当前操作员外键'")
    private User agentCurrentOperator;

    @JoinColumn(name = "agent_current_operator_name", columnDefinition = "varchar(255) COMMENT '代理邀请码当前操作员名称'")
    private String agentCurrentOperatorName;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "partner_current_operator_id", columnDefinition = "bigint(20) COMMENT '合伙当前操作员外键'")
    private User partnerCurrentOperator;

    @JoinColumn(name = "partner_current_operator_name", columnDefinition = "varchar(255) COMMENT '合伙当前操作员名称'")
    private String partnerCurrentOperatorName;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "partner_original_operator_id", columnDefinition = "bigint(20) COMMENT '合伙原始操作员外键'")
    private User partnerOriginalOperator;

    @JoinColumn(name = "partner_original_operator_name", columnDefinition = "varchar(255) COMMENT '合伙原始操作员名称'")
    private String partnerOriginalOperatorName;

    @Column(name = "agent_user_name", columnDefinition = "varchar(255) COMMENT '代理商名称'")
    private String agentUserName;

    @Column(name = "agent_sub_type_name", columnDefinition = "varchar(255) COMMENT '代理商子类型名'")
    private String agentSubTypeName;

    @Column(name = "employee_name", columnDefinition = "varchar(255) COMMENT '员工姓名'")
    private String employeeName;

    @Column(name = "employee_no", columnDefinition = "varchar(255) COMMENT '员工工号'")
    private String employeeNO;

    @Column(name = "agent_user_type_name", columnDefinition = "varchar(255) COMMENT '代理商用户类型名'")
    private String agentUserTypeName;

    @Column(name = "agent_employee_flag", nullable = false, columnDefinition = "bit(1) DEFAULT b'0' COMMENT '代理商是否是员工'")
    private Boolean agentEmployeeFlag = false;

    @Column(name = "partner_employee_flag", nullable = false, columnDefinition = "bit(1) DEFAULT b'0' COMMENT '合伙商是否是员工'")
    private Boolean partnerEmployeeFlag = false;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_contract_id", columnDefinition = "bigint(20) COMMENT '(默认)被邀请所属代理合同外键'")
    private AgentContract agentContract;

    @Enumerated(EnumType.STRING)
    @Column(name = "agent_sub_type")
    private ContractDTO.SubType agentSubType;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "partner_contract_id", columnDefinition = "bigint(20) COMMENT '(默认)被邀请所属合伙合同外键'")
    private PartnerContract partnerContract;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "partner_user_id", columnDefinition = "bigint(20) COMMENT '(默认)被邀请所属合伙主体外键'")
    private User partnerUser;

    @Column(name = "partner_user_name", columnDefinition = "varchar(255) COMMENT '合伙商名称'")
    private String partnerUserName;

    @Column(name = "partner_join_comm", columnDefinition = "bit(1) DEFAULT b'1' COMMENT '是否参与合伙分佣'")
    private Boolean partnerJoinComm;

    @Column(name = "up_delegate_time", columnDefinition = "datetime COMMENT '首次上委托时间'")
    private LocalDateTime upDelegateTime;

    @Column(name = "up_delegate_flag", columnDefinition = "bit(1) DEFAULT b'0'  COMMENT '是否上架委托'")
    private Boolean upDelegateFlag = false;

    //    @Type(MemoUT.class)
    @Column(name = "memo", columnDefinition = "text COMMENT '备注信息'")
    private String memo;

    @Type(value = ListUT.class, parameters = {@org.hibernate.annotations.Parameter(name = "elementType", value = "com.ejuetc.agent.dto.ContractDTO$SubType")})
    @Column(name = "broker_contract_sub_types", columnDefinition = "text COMMENT '合同子类型列表'")
    private List<ContractDTO.SubType> brokerContractSubTypes;

    @Column(name = "broker_contract_sub_types_name", columnDefinition = "text COMMENT '合同子类型列表'")
    private String brokerContractSubTypesName;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "price_id", columnDefinition = "bigint(20) COMMENT '关联代理价'")
    private Price price;

    @Column(name = "standard_price", columnDefinition = "decimal(20,2) default 0 COMMENT '标准售价'")
    private BigDecimal standardPrice;

    @Column(name = "agent_price", columnDefinition = "decimal(20,2) default 0 COMMENT '代理价(结算价)'")
    private BigDecimal agentPrice;

    @Column(name = "agent_discount", columnDefinition = "decimal(20,2) default 1 COMMENT '代理折扣'")
    private BigDecimal agentDiscount;

    @Column(name = "agent_discount_price", columnDefinition = "decimal(20,2) default 0 COMMENT '代理折扣价(代理售价*代理折扣)'")
    private BigDecimal agentDiscountPrice;

    @Column(name = "agent_normal_comm_rate", columnDefinition = "decimal(20,2) default 0 COMMENT '代理正常售价返佣比例'")
    private BigDecimal agentNormalCommRate = ZERO;

    @Column(name = "agent_excess_comm_ratio", columnDefinition = "decimal(20,2) default 0 COMMENT '代理超额售价返佣比例'")
    private BigDecimal agentExcessCommRatio = ZERO;

    @Column(name = "partner_comm_rate", columnDefinition = "decimal(20,2) default 0 COMMENT '合伙返佣比例'")
    private BigDecimal partnerCommRate;

    @Column(name = "tm_forward", columnDefinition = "bit(1) DEFAULT b'0'  COMMENT '是否天猫结转'")
    private Boolean tmForward = false;

    @Column(name = "tm_service_fee", columnDefinition = "decimal(20,2) default 0 COMMENT '天猫服务费转'")
    private BigDecimal tmServiceFee = ZERO;

    @Column(name = "tm_bail", columnDefinition = "decimal(20,2) default 0 COMMENT '天猫保证金转'")
    private BigDecimal tmBail = ZERO;

    @Column(name = "tm_new_trade", columnDefinition = "decimal(20,2) default 0 COMMENT '新交'")
    private BigDecimal tmNewTrade = ZERO;

    @Column(name = "tm_income_contract_no", columnDefinition = "varchar(255) COMMENT '天猫结转收入合同编号'")
    private String tmIncomeContractNO;

    @Enumerated(EnumType.STRING)
    @Column(name = "agent_user_type")
    private UserDTO.Type agentUserType;

    @Enumerated(EnumType.STRING)
    @Column(name = "partner_user_type")
    private UserDTO.Type partnerUserType;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", columnDefinition = "bigint(20) COMMENT '父交易外键'")
    protected Deal parent;

    //    @OrderColumn(name = "sort")
    @OptimisticLock(excluded = true)
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "parent")
    private List<Deal> children = new ArrayList<>();

    @Column(name = "children_quantity", columnDefinition = "bigint(20) default 0 COMMENT '子交易数量'")
    protected Integer childQuantity = 0;

    @Column(name = "children_count", columnDefinition = "bigint(20) default 0 COMMENT '已扣减(消耗)数量'")
    private Integer childrenCount = 0;

    @Column(name = "children_amount", columnDefinition = "decimal(20,2) default 0 COMMENT '已扣减(消耗)金额'")
    private BigDecimal childrenAmount = BigDecimal.ZERO;

//    @Setter(AccessLevel.PROTECTED)
//    @Column(name = "last", columnDefinition = "bit(1) DEFAULT b'0'  COMMENT '是否最后一笔'")
//    private Boolean last = false;

    @Column(name = "statement_time", columnDefinition = "datetime COMMENT '结算时间'")
    protected LocalDateTime statementTime;

//    @Column(name = "sort", columnDefinition = "int(11) COMMENT '排序'", insertable = false, updatable = false)
//    private Integer sort;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_clearing_id", columnDefinition = "bigint(20) COMMENT '代理佣金清分外键'")
    private Clearing agentClearing;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "partner_clearing_id", columnDefinition = "bigint(20) COMMENT '合伙佣金清分外键'")
    private Clearing partnerClearing;

    @Column(name = "test_flag", nullable = false, columnDefinition = "bit(1) DEFAULT b'0' COMMENT '是否是测试数据'")
    private Boolean testFlag = false;

    @Schema(description = "是否是畅享包")
    @Column(name = "enjoy", columnDefinition = "bit(1) default b'0' COMMENT '是否是畅享包'")
    protected boolean enjoy = false;

    @Column(name = "statement_amount", columnDefinition = "decimal(20,2) default 0 COMMENT '结算金额'")
    protected BigDecimal statementAmount = ZERO;

    @Column(name = "invoice_batch_no", columnDefinition = "varchar(255) COMMENT '购买人发票批次号'")
    private String invoiceBatchNO;

    public Deal(BaseDealPO po) {
        this.category = po.getCategory();
        this.indateBegin = po.getIndateBegin();
        this.indateEnd = po.getIndateEnd();
        this.srcId = po.getSrcId();
        this.payAmount = po.getPayAmount();
        this.payTime = po.getPayTime();
        this.factPayTime = po.getFactPayTime() != null ? po.getFactPayTime() : po.getPayTime();
        this.payAuditTime = po.getPayAuditTime();
        this.crmOrderNO = po.getCrmOrderNO();
        this.tmForward = po.getTmForward();
        this.tmServiceFee = po.getTmServiceFee();
        this.tmBail = po.getTmBail();
        this.tmNewTrade = po.getTmNewTrade();
        this.tmIncomeContractNO = po.getTmIncomeContractNO();
        this.invoiceBatchNO = po.getInvoiceBatchNO();
    }

    public Deal(RootDealPO po) {
        this((BaseDealPO) po);
        this.channelCode = po.getChannelCode();
        this.businessCode = po.getBusinessCode();
        this.agentCode = po.getInvitedCode();
        this.cityCode = po.getCityCode();
        this.cityName = po.getCityName();
        if (po.getOperatorId() != null) this.brokerOperator = getBean(UserRpt.class).findById(po.getOperatorId()).orElseThrow(() -> new CodingException("找不到操作人[%s]", po.getOperatorId()));
        this.brokerUser = getBean(UserRpt.class).findById(po.getUserId()).orElseThrow(() -> new CodingException("找不到用户[%s]", po.getUserId()));
        this.childQuantity = po.getChildQuantity();
    }

    public Deal(BaseDealPO po, Deal parent) {
        this(po);
        this.channelCode = po.getChannelCode();
        this.businessCode = po.getBusinessCode();
        parent.addChild(this);
        this.parent = parent;
        this.parentSrcId = parent.srcId;
        this.brokerOperator = parent.getBrokerOperator();
        this.brokerUser = parent.getBrokerUser();
        this.agentCode = parent.getAgentCode();
        this.cityCode = parent.getCityCode();
        this.cityName = parent.getCityName();
    }

    public void addChild(Deal child) {
        children.removeIf(Objects::isNull);
        children.add(child);
        childrenCount = children.size();
        childrenAmount = children.stream().map(Deal::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//        if (childrenCount.equals(childQuantity))
//            child.setLast(true);
    }

    public static Deal of(BaseDealPO po) {
        if (po instanceof CreateChannelOpenDealPO createChannelOpenDealPO)
            return new ChannelOpenDeal(createChannelOpenDealPO);
        if (po instanceof CreateClueBuyDealPO createClueBuyDealPO)
            return new ClueBuyDeal(createClueBuyDealPO);
        if (po instanceof CreateClueConsumeDealPO createClueConsumeDealPO)
            return new ClueConsumeDeal(createClueConsumeDealPO);
        throw new CodingException("未知交易类型[" + po.getClass().getName() + "],无法创建交易");
    }

    public void config() {
        if (getStatus() != INIT) return;

        this.brokerUserName = brokerUser.getCertName();
        this.brokerContract = brokerUser.findContract(BrokerContract.class, payTime, cityCode, List.of(EFFECTIVE, FREEZE));
        if (brokerContract == null) {
            throw new CodingException("[%s]经纪合同不存在或已失效", brokerUser);
        }
        this.brokerUserType = brokerUser.getType();
        this.brokerContractSubTypes = new ArrayList<>(brokerUser.getContractSubTypes(cityCode));
        this.brokerContractSubTypesName = String.join(",", brokerContractSubTypes.stream().map(ContractDTO.SubType::getTitle).toArray(String[]::new));
        if (brokerOperator != null) this.brokerOperatorName = brokerOperator.getCertName();

        this.agentInvitation = getBean(InvitationRpt.class).findByCode(agentCode).orElseThrow(() -> new CodingException("无效的邀请码[" + agentCode + "]"));
        this.agentInvitation.addContract(brokerContract);
        this.agentCurrentOperator = agentInvitation.getOperator();
        this.agentCurrentOperatorName = agentCurrentOperator.getCertName();
        this.agentOriginalOperator = agentCurrentOperator;
        this.agentOriginalOperatorName = agentCurrentOperatorName;
        this.agentUser = agentInvitation.getUser();
        this.agentUserName = agentUser.getCertName();
        this.agentEmployeeFlag = agentUser.getEmployeeFlag();
        this.agentContract = agentUser.findContract(AgentContract.class, factPayTime, cityCode, List.of(EFFECTIVE, FREEZE));
        this.agentUserType = agentUser.getType();
        this.agentUserTypeName = agentUserType.getTitle();
        this.agentSubType = agentContract.getSubType();
        this.agentSubTypeName = agentSubType.getTitle();

        this.partnerCode = agentContract.getInvitedCode();
        this.partnerInvitation = agentContract.getInvited();
        this.partnerCurrentOperator = partnerInvitation.getOperator();
        this.partnerCurrentOperatorName = partnerCurrentOperator.getCertName();
        this.partnerOriginalOperator = partnerCurrentOperator;
        this.partnerOriginalOperatorName = partnerCurrentOperatorName;
        this.partnerContract = caseToSubType(agentContract.getInvitedContract(), PartnerContract.class);
        this.partnerUser = partnerContract.getUser();
        this.partnerUserType = partnerUser.getType();
        this.partnerCommRate = partnerContract.getCommissionRate();
        this.partnerUserName = partnerUser.getCertName();
        this.partnerEmployeeFlag = partnerUser.getEmployeeFlag();

        this.employeeName = agentUser.getEmployeeFlag()
                ? agentInvitation.getOperator().getCertName()
                : partnerUser.getEmployeeFlag()
                ? partnerInvitation.getOperator().getCertName()
                : null;
        this.employeeNO = agentUser.getEmployeeFlag()
                ? agentInvitation.getCode()
                : partnerUser.getEmployeeFlag()
                ? partnerInvitation.getCode()
                : null;
        this.testFlag = brokerUser.getTestFlag() || agentUser.getTestFlag() || partnerUser.getTestFlag();

        this.costZeroFlag = channelCode == ALIPAY || COST_ZERO_CITYS.contains(cityCode);
        this.costImportTime = costImportTime != null
                ? costImportTime
                : costZeroFlag || payTime.isAfter(COST_IMPORT_LIMIT)
                ? payTime
                : null;


        if (getType() != CLUE_BUY) {
            switch (getCategory()) {
                case FORWARD_ENJOY -> {
                    this.agentPrice = payAmount.subtract(tmServiceFee).multiply(new BigDecimal("0.5"));
                    this.agentDiscountPrice = agentPrice;
                    this.statementAmount = agentPrice;
                    this.standardPrice = payAmount;
                    this.agentDiscount = ONE;
                    this.agentNormalCommRate = ONE;
                    this.agentExcessCommRatio = ZERO;
                }
                case NORMAL -> {
                    this.price = agentContract.findPrice(this);
                    this.agentPrice = price.getAgentPrice();
                    this.agentDiscount = price.getAgentDiscount();
                    this.agentDiscountPrice = price.getAgentDiscountPrice();
                    this.standardPrice = price.getStandardPrice();
                    this.agentNormalCommRate = price.getNormalCommRate();
                    this.agentExcessCommRatio = price.getExcessCommRatio();
                    this.statementAmount = payAmount.subtract(standardPrice).multiply(ONE.subtract(agentExcessCommRatio)).add(agentDiscountPrice.multiply(agentNormalCommRate)).setScale(2, HALF_UP);
                }
            }
        }

        setStatus(CONFIGED);
        refreshStatus();
    }

    private void refreshStatus() {
        if (getStatus() != CONFIGED && getStatus() != WAIT_NOTIFY) return;

        if (isNormalChannelOpenExpire()) setCategory(EXPIRE);//已分配过期未上架订单归入过期订单

        this.setStatus(switch (getCategory()) {
            case WITHOUT_PAY, COMPANY_ENJOY -> FINAL;
            case EXPIRE -> WAIT_CLEAR;
            case NORMAL, FORWARD_ENJOY -> getPayAuditTime() != null && switch (getType()) {
                case CHANNEL_OPEN -> upDelegateFlag;
                case CLUE_BUY, CLUE_CONSUME -> true;
            } ? WAIT_CLEAR : WAIT_NOTIFY;
        });
        if (getStatus() == WAIT_CLEAR && getStatementTime() == null) {
            LocalDateTime finishTime = max(getPayTime(), getPayAuditTime(), getUpDelegateTime());
            this.setStatementTime(finishTime.plusMonths(1).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0));
        }
    }

    private boolean isNormalChannelOpenExpire() {
        return getType() == CHANNEL_OPEN
               && (getCategory() == NORMAL || getCategory() == FORWARD_ENJOY)
               && getIndateEnd() != null
               && getIndateEnd().isBefore(now());
    }

    public void clearing() {
        refreshStatus();
        if (this.getStatus() != WAIT_CLEAR) return;

        if (this.getAgentClearing() != null) this.getAgentClearing().logicDelete();
        this.setAgentClearing(new Clearing4Agent(this));
        this.getAgentClearing().exec();

        if (this.getPartnerClearing() != null) this.getPartnerClearing().logicDelete();
        this.setPartnerClearing(new Clearing4Partner(this));
        this.getPartnerClearing().exec();

        this.setStatus(CLEARED);
    }

    public void makeUpDelegate(LocalDateTime upTime) {
        if (upDelegateFlag && this.upDelegateTime != null) {
            log.error("交易[{}]已标记上架[{}]或已存在上架委托时间[{}]", getId(), upDelegateFlag, upDelegateTime);
            return;
        }
        this.upDelegateTime = upTime;
        this.upDelegateFlag = true;
        if (getStatus() == WAIT_NOTIFY) refreshStatus();
    }

    public LocalDateTime getPayAuditTime() {
        return getType() == CLUE_CONSUME ? getParent().getPayAuditTime() : payAuditTime;
    }

    public DealDTO.Type getType() {
        if (type == null)
            type = DealDTO.Type.valueOf(ClassUtils.getClassAnnotation(this.getClass(), DiscriminatorValue.class).value());
        return type;
    }

    public void addMemo(String content) {
        memo = content;
    }

    public Clearing getParentAgentClearing() {
        return parent != null ? parent.getAgentClearing() : null;
    }

    public Clearing getParentPartnerClearing() {
        return parent != null ? parent.getPartnerClearing() : null;
    }

    public void update(BaseDealPO po) {
        this.payAuditTime = po.getPayAuditTime();
        if (getStatus() == WAIT_NOTIFY) refreshStatus();
    }

    public String getBusinessCodeName() {
        return businessCode != null ? businessCode.getTitle() : "全业务";
    }

    public String getChannelCodeName() {
        return channelCode != null ? channelCode.getTitle() : "全渠道";
    }

    public Long getBrokerOperatorId() {
        return brokerOperator != null ? brokerOperator.getId() : null;
    }
}
