#datasource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=**********************************************************
spring.datasource.username=prod_consumer_app
spring.datasource.password=A1Co0rC0GZLah7cyAD8E
spring.datasource.hikari.maximum-pool-size=100
spring.datasource.hikari.connection-timeout=30000

#jpa
spring.jpa.database-platform=org.hibernate.dialect.TiDBDialect
#spring.jpa.hibernate.ddl-auto=none
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.open-in-view=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.hibernate.naming-strategy=org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy

#jackson
spring.jackson.serialization.indent_output=true

#eureka-client
#eureka.client.service-url.defaultZone=http://127.0.0.1:8081/eureka/
eureka.client.service-url.defaultZone=http://ejuetc-eureka-0.ejuetc-eureka-statefulset.ejuetc-release:8081/eureka,http://ejuetc-eureka-1.ejuetc-eureka-statefulset.ejuetc-release:8081/eureka,http://ejuetc-eureka-2.ejuetc-eureka-statefulset.ejuetc-release:8081/eureka
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
eureka.instance.prefer-ip-address=true
eureka.instance.lease-renewal-interval-in-seconds=1
eureka.instance.lease-expiration-duration-in-seconds=3
spring.mvc.pathmatch.matching-strategy=ant_path_matcher

# redis
spring.data.redis.cluster.nodes=************:7379,*************:7379,*************:7379,************:7380,*************:7380,*************:7380
spring.data.redis.password=7b97a518e03d663d

saas.api.url=https://om-api.fangyou.com

#rocketmq
rocketmq.name-server=************:9876;*************:9876

redis.saas.cluster.nodes=**************:7000,**************:7000
ejuetc.consumer.Delegation.saasVerificationAPI.url=https://om-api.fangyou.com