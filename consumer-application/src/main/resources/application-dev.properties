#datasource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=**************************************************************
spring.datasource.username=test_consumer_app
spring.datasource.password=vfTQDu6IFZaFAtJ14EeM
spring.datasource.initial-size=10
spring.datasource.max-active=20
spring.datasource.min-idle=8
spring.datasource.max-idle=8
spring.datasource.max-wait=10000

#jpa
spring.jpa.database-platform=org.hibernate.dialect.TiDBDialect
spring.jpa.hibernate.ddl-auto=none
#spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.open-in-view=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.hibernate.naming-strategy=org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy

#jackson
spring.jackson.serialization.indent_output=true

#eureka-client
#eureka.client.service-url.defaultZone=http://127.0.0.1:8081/eureka/
eureka.client.service-url.defaultZone=http://*************:8081/eureka/
#eureka.client.service-url.defaultZone=http://ejuetc-eureka.tst.ejucloud.cn/eureka/
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
eureka.instance.prefer-ip-address=true
eureka.instance.lease-renewal-interval-in-seconds=1
eureka.instance.lease-expiration-duration-in-seconds=3
spring.mvc.pathmatch.matching-strategy=ant_path_matcher

# redis
spring.data.redis.host=**************
spring.data.redis.port=6379
spring.data.redis.password=s95KgPdifH0Cqwed

com.ejuetc.exception.alarm=false

ejuetc.consumer.implicit.startListener=false
ejuetc.consumer.implicit.poolKey=FC100000182138172
ejuetc.consumer.implicit.queueName=Alicom-Queue-1596004024457014-SecretReport-22540001

com.ejuetc.consumer.schedule.implicitCluePush=0 0 1 31 2 *