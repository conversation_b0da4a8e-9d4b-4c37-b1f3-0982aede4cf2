<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.ehcache.org/v3"
        xsi:schemaLocation="http://www.ehcache.org/v3 http://www.ehcache.org/schema/ehcache-core-3.0.xsd">
    <!-- 缓存模板: 未填写缓存名时使用的默认缓存，同时也可被继承 -->
    <cache-template name="defaultCache">
        <key-type>java.lang.String</key-type>
        <value-type>java.lang.Object</value-type>
        <resources>
            <heap unit="MB">1024</heap>
        </resources>
    </cache-template>

    <cache alias="com.ejuetc.consumer.domain.community.Community" uses-template="defaultCache">
        <expiry>
            <ttl unit="minutes">5</ttl>
        </expiry>
    </cache>

    <cache alias="com.ejuetc.consumer.domain.region.Region" uses-template="defaultCache">
        <expiry>
            <ttl unit="minutes">5</ttl>
        </expiry>
    </cache>

    <cache alias="com.ejuetc.consumer.domain.delegation.DelegationDict" uses-template="defaultCache">
        <expiry>
            <ttl unit="minutes">5</ttl>
        </expiry>
    </cache>

    <cache alias="DictRpt.findByCategory" uses-template="defaultCache">
        <expiry>
            <ttl unit="minutes">5</ttl>
        </expiry>
    </cache>

    <cache alias="DictRpt.findNameByCategory" uses-template="defaultCache">
        <expiry>
            <ttl unit="minutes">5</ttl>
        </expiry>
    </cache>

    <cache alias="DictRpt.findNameByCategories" uses-template="defaultCache">
        <expiry>
            <ttl unit="minutes">5</ttl>
        </expiry>
    </cache>

    <cache alias="RegionRpt.findByAmapAdcode" uses-template="defaultCache">
        <expiry>
            <ttl unit="minutes">5</ttl>
        </expiry>
    </cache>

    <cache alias="RegionRpt.findByAmapCitycode" uses-template="defaultCache">
        <expiry>
            <ttl unit="minutes">5</ttl>
        </expiry>
    </cache>

    <cache alias="RegionRpt.findByBusinessOpen" uses-template="defaultCache">
        <expiry>
            <ttl unit="minutes">5</ttl>
        </expiry>
    </cache>

    <cache alias="RegionRpt.findByCode" uses-template="defaultCache">
        <expiry>
            <ttl unit="minutes">5</ttl>
        </expiry>
    </cache>

    <cache alias="RegionRpt.findByType" uses-template="defaultCache">
        <expiry>
            <ttl unit="minutes">5</ttl>
        </expiry>
    </cache>
</config>