spring.profiles.active=local
#spring.profiles.active=dev
#spring.profiles.active=test
#spring.profiles.active=uat
#tomcat
spring.application.name=ejuetc.consumer
server.port=8097
server.servlet.context-path=/consumer
server.servlet.session.timeout=1800s
server.tomcat.uri-encoding=UTF-8
server.tomcat.threads.max=200
server.tomcat.basedir=/tmp
#JPA
spring.jpa.properties.hibernate.default_batch_fetch_size=100
#????
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=1000MB
#feign
feign.hystrix.enabled=false
feign.httpclient.enabled=true
#feign.compression.request.enabled=false
#feign.compression.response.enabled=false
#logback file
logging.config=classpath:logback-spring.xml
# ??hiddenMethod???
spring.mvc.hiddenmethod.filter.enabled=true
#swagger
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
com.ejuetc.exception.alarm=true
eureka.instance.status-page-url-path=/doc.html
knife4j.enable=true
knife4j.setting.language=zh_cn
com.ejuetc.commons.base.LoggerFilter.ignoreRequestURI=.*/doc.html,.*/webjars/.*
#OSS??
ejuetc.commons.oss.access-key-id=LTAI5tR7VRT3oCrM2GLe35ck
ejuetc.commons.oss.access-key-secret=******************************
ejuetc.commons.oss.oss-endpoint=oss-cn-shanghai.aliyuncs.com
ejuetc.commons.oss.url-prefix=https://etc-consumer.oss-cn-shanghai.aliyuncs.com/
ejuetc.commons.oss.bucket-name=etc-consumer
ejuetc.commons.oss.upload-functions.delegation-media.date-format=yyyyMMddHHmm
ejuetc.commons.oss.upload-functions.delegation-media.max-count=10
ejuetc.commons.oss.upload-functions.delegation-media.valid-seconds=900
#??SaaS??
saas.api.url=https://test-om-api.fangyou.com
#rocketmq
rocketmq.name-server=*************:9876;**************:9876
rocketmq.producer.group=order-producer
rocketmq.producer.send-message-timeout=30000
rocketmq.producer.retry-times-when-send-failed=5
rocketmq.producer.retry-times-when-send-async-failed=5
# ??????????
spring.main.banner-mode=log
# alipay
alipay.app-id=2021004197602565
alipay.private-key=MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCQ5Fsck7087cEq8xARbEfT1NCWz+fhybtoK93WGC3Xzw2tuXMspKn8XD8GlQjjjS+ASGCXbVcsU0zcmuPjZLma6s0oLw0nE20/F2iTa+pN1EUUz22j9mPc1ryUYdlSDhUfP7URQ0bmaIfktP9cHFmRiBpTvh0RthIzYwCWXSPvgQ7E4h0gZb5S7FinGOPIXjUfFQfos1MMNpErjTZz78+h/Xas+I3jh+ayACgS2c/+4mYHRI6Je/bAhHnYwoBsJGlEYGth25WuZtCQ7HKu7zhGLIv1EQwaAj5s5uYUp9JrNb8KamkcB11jjrq5Hv64U7S9zBs+9rv5grVa8NuMheqvAgMBAAECggEANO8olkUnYsYdx1orqaHcj/h1CFQGjEfOLt2v2xLUVL6xaaGSAWJbM/ZjlD0IlVG0Y/KNT2VD7s60CviPfzkCUiWsNB0Ueg0v4Vfk/c70N+DwKA0sKjXPABR3quIgqki40MTOmvLwSScxVo4FqRo2YuPNcoDHT/wrpBhsU4LNqSb5fP9q+vczfhLw3mPrG++fmtCjRscgww/EQTarYMQemRFiw49HWcLD1D3wFZO/Jrv7l8V5AmCw7XG3KSjeewZhbWtjldJppYGZOLmKqV/TsbRiIT77IeMxNxfxHdAUyfhLQ9jSe//HbIIq5Z0OZ9Z51jeozGxpCf6oh1lWk7U5eQKBgQDGMGwZRta3TShpPfnjKLRBrgjKxLyEJly+JQo+X4tDu4Z09aZ3fsJpO9gG32v4a3mG0df3zFGASC07r5ImKhqAASQpdIJCmBM5My2VUD/+IBJBYAKflkuRPH1mOjC1IgcTTGLCl/Cz4D8vpvO1uNN0wYQZA7naf6JX29NkKNYPZQKBgQC7KAXrckr3sqa/GbErEp34VXhrjN7JvgRtxZcYi/pnCu+Hhx7R69fPWhpwu87gHRcCjywxYTphRueOyoKnOHXKC0Dzh9wjDHSD2YezQ/L4p39HlO1GQn0RDMYXJg5Y30bM4tz0GXboVh6nob4Y/ei8efYNzKBP3gWA+8D91LRCgwKBgC2AFHFBKC5xEmQHDIWwZKHnmLEktbsJK6Wjz1Q+jzR/qwDTHOK2QV/zk0cQqDMiSYZASvri95sh9vCLAFyuseLNYEX8L6OmUlOBgmeTQv3O0yIbLr8AvFydrJR5q5EKPOGbWdkfFO3W/+qqw6zmv/VAMFml11dokhblqwSFs49ZAoGBAJ8HABiy8h6M8AD7jL5jwFUOExeYCIp16n934M6bynaaJTQMzVRkSOlIz6SxALUQIsgq7cs7qYaU4BdF3kTGxdebPmJwM86YO7z5LsRVXXSJpq4lToRf5LODHiL3VEOR3TAlKMhFhvMekkewOL41qffUzg2lrSaQKi8SPEey4ESPAoGABZeP3d+EV1+qwNHK+d2qH/LsVLjLaqW3jPhA/mlc9MGumU3ocd2Z9tRyGJC9d4IU+sk+/YI0PsILzBk5T4DHzbdqSlZw9FU8Y+kr3JP1TnyRb1cWgQvCwuLj/Y1z0BMgwuuMCmfkC3DBNPUdHg+7j118+JeRj2aXUS28oIsL6+s=
alipay.alipay-public-key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlZr7NyuZKxfh4DyroMrZJO5QYpatYRJIpYeBFJbnOBgoQnrPZG68bD1zwcRLkrfwBmCV0aEMT6MganhWXPd3I4vmOGXsG+rhEIfx8yCGn+b+21MI0g2BCI63AUg3IsJXr0VDuags+PsPCaNWk+XN5LYoaOPk32/4w7qxBapv63+idT/ZU9tDcNaxa3HP9iymYAbISV2fK5KM0ajwMqqQKOmpydozDWzRX/22wHYsfeqveuu4G6BMknQpBepJdAyygzJLlLKS4NGC9kgJsW0mBAXmuBsVty94hH2Kr+q0mDS2ccjTpqFR0kdOVHFTI46H26rjrVRWixWWKfTkX9jB2QIDAQAB
alipay.aes-key=jmOlCEbcE9Ia6pTpocNUpA==
alipay.server-url=https://openapi.alipay.com/gateway.do
alipay.charset=UTF-8
alipay.sign-type=RSA2
amap.key=65bd4aab1dbd7c434a797be65488ce9b
ejuetc.consumer.implicit.accessKey=LTAI5tMmcVwXTXH9uwM4R2os
ejuetc.consumer.implicit.accessSecret=******************************
ejuetc.consumer.implicit.queueName=Alicom-Queue-1596004024457014-SecretReport-22370002
com.ejuetc.consumer.schedule.implicitCluePush=*/30 * * * * ?
ejuetc.loginFilter.enable=true
ejuetc.login.processor.consumer.enable=true
ejuetc.login.processor.saas.enable=true
redis.saas.cluster.nodes=*************:7000

# 买房租房找房
wechat.appId=wx76951b3b02064b1e
wechat.appSecret=6f30349ad76aadf1df952448f0b38276
ejuetc.consumer.DelegationWebImpl.base_field_required.XIANYU-SALE=roomCount,hallCount,toiletCount,currentFloor,totalFloor,redo,priceTotal,title,medias,description,orient,tagElevator,buildingArea,propertyType,completionTime,propertyYears,houseType,listDate,houseCertVerify
ejuetc.consumer.DelegationWebImpl.base_field_required.XIANYU-NEW=roomCount,hallCount,toiletCount,currentFloor,totalFloor,redo,priceTotal,title,medias,description,orient,tagElevator,buildingArea,propertyType,completionTime,propertyYears,houseType,listDate,houseCertVerify
ejuetc.consumer.DelegationWebImpl.base_field_required.XIANYU-RENT=roomCount,hallCount,toiletCount,currentFloor,totalFloor,redo,priceTotal,title,medias,description,subType,buildName,unitName,roomNum,roomName,buildingArea,payMonths
ejuetc.consumer.DelegationWebImpl.base_field_required.ALIPAY-SALE=roomCount,hallCount,toiletCount,orient,buildingArea,currentFloor,totalFloor,propertyType,completionTime,redo,listDate,priceTotal,title,medias,description
ejuetc.consumer.DelegationWebImpl.base_field_required.ALIPAY-RENT=roomCount,hallCount,toiletCount,currentFloor,totalFloor,redo,buildingArea,priceTotal,medias,description
ejuetc.consumer.DelegationWebImpl.base_field_required.ALIPAY-NEW=roomCount,hallCount,toiletCount,orient,tagElevator,buildingArea,currentFloor,totalFloor,propertyType,completionTime,propertyYears,houseType,redo,listDate,houseCertVerify,priceTotal,title,medias,description
ejuetc.consumer.DelegationWebImpl.base_field_required.PRIVATE-SALE=roomCount,hallCount,toiletCount,orient,tagElevator,buildingArea,currentFloor,totalFloor,propertyType,completionTime,propertyYears,houseType,redo,listDate,houseCertVerify,priceTotal,title,medias,description
ejuetc.consumer.DelegationWebImpl.base_field_required.PRIVATE-RENT=roomCount,hallCount,toiletCount,currentFloor,totalFloor,redo,buildingArea,payMonths,priceTotal,title,medias,description,subType
ejuetc.consumer.DelegationWebImpl.base_field_required.PRIVATE-NEW=roomCount,hallCount,toiletCount,orient,tagElevator,buildingArea,currentFloor,totalFloor,propertyType,completionTime,propertyYears,houseType,redo,listDate,houseCertVerify,priceTotal,title,medias,description
ejuetc.consumer.Delegation.saasVerificationAPI.url=https://test-om-api.fangyou.com
ejuetc.consumer.companyIds4DisabledAddCompanyDelegation=9095858086282796801
#spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration
spring.datasource.hikari.transaction-isolation=TRANSACTION_READ_COMMITTED
ImplicitBind4Yihaotong.callClient.url=https://pre-om-api.fangyou.com