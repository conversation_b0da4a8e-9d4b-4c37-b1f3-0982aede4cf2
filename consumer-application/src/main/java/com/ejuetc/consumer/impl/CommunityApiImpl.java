package com.ejuetc.consumer.impl;

import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.response.ResponseStatus;
import com.ejuetc.consumer.api.community.*;
import com.ejuetc.consumer.domain.community.*;
import com.ejuetc.consumer.domain.region.RegionRpt;
import com.ejuetc.consumer.web.vo.BeikeCommunityVO;
import com.ejuetc.consumer.web.vo.CommunityDetailVO;
import com.ejuetc.consumer.web.vo.CommunityVO;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.apiResponse;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.spring.SpringUtil.getProperty;
import static com.ejuetc.commons.base.utils.ThreadUtils.asyncExec;
import static com.ejuetc.commons.base.utils.ThreadUtils.invokeAll;
import static java.util.Collections.emptyMap;
import static org.springframework.data.domain.Pageable.ofSize;
import static org.springframework.transaction.annotation.Propagation.NOT_SUPPORTED;
import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class CommunityApiImpl implements CommunityAPI {
    private final RegionRpt regionRpt;
    private final CommunityRpt communityRpt;
    private final CommunityBindRpt communityBindRpt;
    private final RedisTemplate<String, String> redisTemplate;
    private final EntityManager entityManager;
    private final CommunityPictureRpt pictureRpt;
    private final CommunityLayoutRpt layoutRpt;
    private final CommunityDetailRpt detailRpt;
    private final CommunityTipsRpt tipsRpt;
    private final CommunityService communityService;
    private final CommunityBeikeRpt communityBeikeRpt;

    @Override
    public ApiResponse<BindCommunityRO> bind(String address, String name) {
        CommunityBind bind = communityService.queryOrBind(address, name);
        if (bind.isSucc()) {
            return succ(bind.makeBindRO());
        } else {
            return apiResponse(ResponseStatus.FAIL_BIZ, bind.getErrorMsg(), null);
        }
    }

    @Override
    @Transactional(propagation = NOT_SUPPORTED)
    public ApiResponse<?> bindCommunityDetail(List<String> cityCodes, int limitCount, int threadCount) {
        CommunityApiImpl communityImpl = getBean(CommunityApiImpl.class);
        List<Long> details = detailRpt.findWaitBind(cityCodes, ofSize(limitCount));
        int sublistSize = (int) Math.ceil((double) details.size() / threadCount);
        for (int i = 0; i < details.size(); i += sublistSize) {
            List<Long> detailIds = details.subList(i, Math.min(i + sublistSize, details.size()));
            asyncExec(() -> detailIds.forEach(detailId -> {
                try {
                    communityImpl.doBindCommunityDetail(detailId);
                } catch (Throwable e) {
                    log.error("bindCommunityDetail error", e);
                }
            }));
        }
        return succ();
    }

    @Transactional(propagation = REQUIRES_NEW)
    public void doBindCommunityDetail(Long detailId) {
        CommunityDetail detail = detailRpt.getReferenceById(detailId);
        CommunityBind bind = communityService.queryOrBind(detail.getFullAddress(), detail.getCommunityName());
        detail.bindCommunity(bind);
    }

    @Override
    public ApiResponse<?> updateOverlapRate(int limitCount) {
        detailRpt.findOverlapRateIsNull(ofSize(limitCount)).forEach(CommunityDetail::updateOverlapRate);
        return succ();
    }


    @Override
    public ApiResponse<?> rebind(int maxRowCount, int maxThreadCount) {
        CommunityApiImpl bean = getBean(CommunityApiImpl.class);
        List<Long> bindIds = communityBindRpt.findWaitRebinds(ofSize(maxRowCount));
        int sublistSize = (int) Math.ceil((double) bindIds.size() / maxThreadCount);
        for (int i = 0; i < bindIds.size(); i += sublistSize) {
            List<Long> subBinds = new ArrayList<>(bindIds.subList(i, Math.min(i + sublistSize, bindIds.size())));
            asyncExec(() -> bean.doRebind(subBinds));
        }
        return succ();
    }

    @Transactional(propagation = REQUIRES_NEW)
    public void doRebind(List<Long> bindIds) {
        communityBindRpt.findAllById(bindIds).forEach(CommunityBind::exec);
    }


    @Override
    public void convertCommunityPictureUrl(int maxRowCount, int batchSize) {
        CommunityApiImpl bean = getBean(CommunityApiImpl.class);
        int total = 0;
        while (true) {
            try {
                int size = bean.doConvertCommunityPictureUrl(batchSize);
                total += size;
                if (total >= maxRowCount || size == 0) break;
            } catch (Throwable e) {
                log.error("convertCommunityPictureUrl error", e);
            }
        }
    }

    @Transactional(propagation = REQUIRES_NEW)
    public int doConvertCommunityPictureUrl(int batchSize) {
        List<CommunityPicture> pictures = pictureRpt.findWaitConvertUrl(ofSize(batchSize));
        invokeAll(pictures);
        return pictures.size();
    }

    @Override
    public void convertCommunityLayoutUrl(int maxRowCount, int batchSize) {
        CommunityApiImpl bean = getBean(CommunityApiImpl.class);
        int total = 0;
        while (true) {
            try {
                int size = bean.doConvertCommunityLayoutUrl(batchSize);
                total += size;
                if (total >= maxRowCount || size == 0) break;
            } catch (Throwable e) {
                log.error("convertCommunityLayoutUrl error", e);
            }
        }
    }

    @Transactional(propagation = REQUIRES_NEW)
    public int doConvertCommunityLayoutUrl(int batchSize) {
        List<CommunityLayout> layouts = layoutRpt.findWaitConvertUrl(ofSize(batchSize));
        invokeAll(layouts);
        return layouts.size();
    }

    @Override
    @Transactional(propagation = NOT_SUPPORTED)
    public ApiResponse<?> bindCommunityBeike(List<String> cities, int limitCount) {
        log.info("开始批量绑定贝壳小区，城市列表：{}，限制数量：{}", cities, limitCount);
        communityService.bindCommunityBeike(cities, limitCount);
        return succ();
    }


}

