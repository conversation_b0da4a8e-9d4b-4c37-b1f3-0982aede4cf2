package com.ejuetc.consumer.impl;

import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.broker.BrokerAPI;
import com.ejuetc.consumer.api.broker.EditBrokerPO;
import com.ejuetc.consumer.domain.broker.Broker;
import com.ejuetc.consumer.domain.broker.BrokerRpt;
import com.ejuetc.consumer.api.dto.BrokerDTO;
import com.ejuetc.consumer.web.broker.BrokerWeb;
import com.ejuetc.consumer.web.vo.BrokerVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class BrokerApiImpl implements BrokerAPI {
    private final BrokerRpt brokerRpt;

    @Override
    public ApiResponse<BrokerDTO> edit(EditBrokerPO po) {
        Broker broker = brokerRpt.findAndLockById(po.getBrokerId());
        if (broker == null) {
            broker = new Broker(po).save();
        } else {
            broker.edit(po);
        }
        return succ(convert2DTO(broker, new BrokerDTO()));
    }


}

