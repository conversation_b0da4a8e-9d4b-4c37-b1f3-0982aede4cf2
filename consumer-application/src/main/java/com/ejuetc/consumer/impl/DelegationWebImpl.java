package com.ejuetc.consumer.impl;

import com.alibaba.fastjson.JSON;
import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.response.ResponseStatus;
import com.ejuetc.commons.base.spring.SpringUtil;
import com.ejuetc.commons.base.utils.StringUtils;
import com.ejuetc.consumer.api.delegation.EditDelegationPO;
import com.ejuetc.consumer.api.dto.DelegationDictDTO;
import com.ejuetc.consumer.domain.broker.BrokerRpt;
import com.ejuetc.consumer.domain.community.CommunityBind;
import com.ejuetc.consumer.domain.community.CommunityBindRpt;
import com.ejuetc.consumer.domain.community.CommunityService;
import com.ejuetc.consumer.domain.consumer.ConsumerRpt;
import com.ejuetc.consumer.domain.consumer.ConsumerTrack;
import com.ejuetc.consumer.domain.consumer.ConsumerTrackRpt;
import com.ejuetc.consumer.domain.delegation.*;
import com.ejuetc.consumer.domain.consumer.ConsumerRelation;
import com.ejuetc.consumer.domain.consumer.ConsumerRelationRpt;
import com.ejuetc.consumer.domain.region.RegionRpt;
import com.ejuetc.consumer.web.consumer.LoginToken;
import com.ejuetc.consumer.web.delegation.DelegationWeb;
import com.ejuetc.consumer.web.delegation.QueryListPO2B;
import com.ejuetc.consumer.web.delegation.QueryListPO2C;
import com.ejuetc.consumer.web.vo.BrokerVO;
import com.ejuetc.consumer.web.vo.CommunityVO;
import com.ejuetc.consumer.web.vo.DelegationVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.apiResponse;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.StringUtils.notBlank;
import static com.ejuetc.commons.base.utils.ThreadUtils.asyncExec;
import static com.ejuetc.consumer.api.dto.DelegationDTO.Level.COMPANY;
import static com.ejuetc.consumer.domain.delegation.Delegation.make;
import static java.util.stream.Collectors.toMap;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class DelegationWebImpl implements DelegationWeb {
    public static final String BASE_FIELD_REQUIRED_KEY = "ejuetc.consumer.DelegationWebImpl.base_field_required.%s-%s";
    private final DelegationRpt delegationRpt;
    private final ConsumerRpt consumerRpt;
    private final ConsumerTrackRpt consumerTrackRpt;
    private final ConsumerRelationRpt relationRpt;
    private final RegionRpt regionRpt;
    private final CommunityBindRpt communityBindRpt;
    private final DelegationDictRpt dictRpt;
    private final BrokerRpt brokerRpt;
    private final CommunityService communityService;

    @Override
    public ApiResponse<DelegationVO> edit(SaasLoginToken saasToken, EditDelegationPO po) {
        po.setSourceId(null).setSourceType(null);//前端基于公司房源创建经纪人房源时,清空该字段避免编辑公司对象
        ApiResponse<Delegation> resp = doEdit(saasToken, po);
        if (resp.isSucc()) {
            return succ(convert2DTO(resp.getData(), new DelegationVO()
                    .setCommunity(new CommunityVO())
                    .setChannelDelegationMap(Map.of())
                    .setBroker(new BrokerVO())
            ));
        } else {
            return apiResponse(resp.getStatus(), resp.getMessage(), null);
        }

    }

    protected ApiResponse<Delegation> doEdit(SaasLoginToken token, EditDelegationPO po) {
        log.info("DelegationWebImpl.doEdit({})", token);
        dictRpt.checkDictFieldsValue(po);
        Delegation delegation = delegationRpt.findExist(po, token.getUserId());
        if (delegation == null) {
            delegation = make(token.getUserId(), po).save();
        } else {
            if (delegation.cantManage(token)) throw new BusinessException("bc.ejuetc.consumer.1015");
            delegation.modify(po);
        }
        delegation.upDown(po.getChannelUp(), "房源编辑", po.getChannelCodes());

        return succ(delegation);
    }

    @Override
    public ApiResponse<DelegationVO> upDown(SaasLoginToken saasToken, Long channelDelegationId, boolean up) {
        log.info("DelegationWebImpl.upDown( {} )", saasToken);
        ChannelDelegation delegation = delegationRpt.findChannelDelegationById(channelDelegationId);
        if (delegation.cantManage(saasToken))
            throw new BusinessException("bc.ejuetc.consumer.1015");

        if (up) {
            delegation.up();
        } else {
            delegation.down("主动下架");
        }
        return succ(convert2DTO(delegation, new DelegationVO()));
    }

    @Override
    public ApiResponse<DelegationVO> batchUp(SaasLoginToken saasToken, List<Long> brokerDelegationIds, List<ChannelDTO.Code> channelCodes) {
        log.info("DelegationWebImpl.batchUp( {} )", saasToken);
        delegationRpt.findAndLockListByIds(brokerDelegationIds).forEach(d -> {
            if (d.cantManage(saasToken))
                throw new BusinessException("bc.ejuetc.consumer.1015");
            d.subType(BrokerDelegation.class).upDown(true, "批量上架", channelCodes);
        });
        return null;
    }

    @Override
    public ApiResponse<DelegationVO> boutique(SaasLoginToken saasToken, Long id, boolean boutique) {
        log.info("DelegationWebImpl.boutique( {} )", saasToken);
        Delegation delegation = delegationRpt.findAndLockById(id);
        if (delegation.cantManage(saasToken))
            throw new BusinessException("bc.ejuetc.consumer.1015");

        delegation.modifyBoutique(boutique);
        return succ(convert2DTO(delegation, new DelegationVO()));
    }

    @Override
    public ApiResponse<DelegationVO> delete(SaasLoginToken saasToken, Long id, String reason) {
        log.info("DelegationWebImpl.delete( {} )", saasToken);
        Delegation delegation = delegationRpt.findAndLockById(id);
        if (delegation.cantManage(saasToken)) throw new BusinessException("bc.ejuetc.consumer.1015");
        delegation.delete(reason);
        return succ(convert2DTO(delegation, new DelegationVO()));
    }

    @Override
    public ApiResponse<List<DelegationVO>> list2b(SaasLoginToken saasToken, QueryListPO2B request, int pageSize, int pageNum) {
        log.info("DelegationWebImpl.list2b( {} , {} , {} , {} )", saasToken, request, pageSize, pageNum);
        pageNum = Math.min(pageNum, 10);
        pageSize = Math.min(pageSize, 20);
        Pageable pageable = Pageable.ofSize(pageSize).withPage(pageNum - 1);
        Page<Delegation> page = delegationRpt.findList2B(
                brokerRpt.getReferenceById(saasToken.getUserId()).getCompanyId(),
                saasToken.getUserId(),
                saasToken.getManagerRole() && !request.isOnlySelf(),
                request,
                pageable
        );
        List<Delegation> delegations = page.getContent();
        return succ(convert2DTO(delegations, new DelegationVO()
                .setCommunity(new CommunityVO())
                .setBroker(new BrokerVO().setPhone(""))
                .setCurrentFloor(0)
                .setChannelDelegationMap(Map.of())
        )).setPage(pageSize, pageNum, page.getTotalElements());
    }

    @Override
    public ApiResponse<List<DelegationVO>> list2c(LoginToken login, QueryListPO2C request, int pageSize, int pageNum) {
        log.info("delegation list, historyId={}, LoginToken={}, request={}, pageSize={}, pageNum={}",
                login.getLoginHistoryId(), JSON.toJSON(login), JSON.toJSONString(request), pageSize, pageNum);
        pageNum = Math.min(pageNum, 10);
        pageSize = Math.min(pageSize, 20);
        Pageable pageable = Pageable.ofSize(pageSize).withPage(pageNum - 1);
        Page<Delegation> page = request.canRecommend() && Objects.equals(request.getCityId(), login.getCityId()) && notBlank(login.getLocation())
                ? delegationRpt.findOrderByLocation(login.getChannelCode(), request.getCityId(), login.getLongitude(), login.getLatitude(), request.getType(), pageable)
                : delegationRpt.findList2C(login.getChannelCode(), request, pageable);
        List<Delegation> delegations = page.getContent();
        return succ(convert2DTO(delegations, new DelegationVO()
                .setCommunity(new CommunityVO())
                .setBroker(new BrokerVO()))
        ).setPage(pageSize, pageNum, page.getTotalElements());
    }

    @Override
    public ApiResponse<DelegationVO> detail2c(LoginToken loginToken, String code) {
        log.info("delegation detail, historyId={}, code={}", loginToken.getLoginHistoryId(), code);
        Delegation delegation = delegationRpt.findChannelUpByCode(code).orElseThrow(() -> new BusinessException("bc.ejuetc.consumer.1010"));
        DelegationVO delegationVO = convert2DTO(delegation, new DelegationVO()
                .setCommunity(new CommunityVO().setAround(new CommunityVO.AroundWrapper()))
                .setBroker(new BrokerVO())
        );
        asyncExec(() -> getBean(DelegationWebImpl.class).saveConsumerTrack(loginToken.getLoginHistoryId(), delegation.getId()));
        return succ(delegationVO);
    }

    @Override
    public ApiResponse<DelegationVO> detail2b(SaasLoginToken loginToken, Long id) {
        log.info("DelegationWebImpl.detail2b( {} )", loginToken);
        Delegation delegation = delegationRpt.findById(id).orElseThrow(() -> new BusinessException("bc.ejuetc.consumer.1010"));
        if (delegation.cantManage(loginToken))
            return apiResponse(new BusinessException("bc.ejuetc.consumer.1015"));

        DelegationVO delegationVO = convert2DTO(delegation, new DelegationVO()
                .setCurrentFloor(0)
                .setCommunity(new CommunityVO().setAround(new CommunityVO.AroundWrapper()))
                .setChannelDelegationMapPlus(Map.of())
                .setBroker(new BrokerVO())
        );

        if (delegation.getLevel() == COMPANY)
            delegationVO.setOwnChildCode(delegation.getOwnChildCode(loginToken.getUserId()));
        return succ(delegationVO);
    }

    public void saveConsumerTrack(Long loginHistoryId, Long delegationId) {
        log.info("saveConsumerTrack, loginHistoryId={}, delegationId={}", loginHistoryId, delegationId);
        new ConsumerTrack(delegationId, loginHistoryId).save();
        log.info("saveConsumerTrack, loginHistoryId={}, delegationId={} success", loginHistoryId, delegationId);
    }

    @Override
    public ApiResponse<List<DelegationVO>> brokerMoreHouses(LoginToken loginToken, String code, int pageSize, int pageNum) {
        log.info("delegation brokerMoreHouses, historyId={}, code={}, pageSize={}, pageNum={}",
                loginToken == null ? null : loginToken.getLoginHistoryId(), code, pageSize, pageNum);
        pageSize = Math.min(pageSize, 20);
        if (StringUtils.isBlank(code)) {
            return succ(List.<DelegationVO>of()).setPage(pageSize, pageNum, 0);
        }
        Page<Delegation> page = delegationRpt.findMoreHouses(code, Pageable.ofSize(pageSize).withPage(pageNum - 1));
        return succ(convert2DTO(page.getContent(), new DelegationVO()
                .setCommunity(new CommunityVO())
                .setBroker(new BrokerVO()))
        ).setPage(pageSize, pageNum, page.getTotalElements());
    }

    @Override
    public ApiResponse<List<DelegationVO>> mostViewedDelegations(SaasLoginToken saasLoginToken, Long relationId) {
        ConsumerRelation relation = relationRpt.findById(relationId)
                .orElseThrow(() -> new BusinessException("bc.ejuetc.consumer.1013"));
        Long consumerId = relation.getConsumerId();
        LocalDateTime startDate = LocalDateTime.now().minusMonths(3);
        Map<Long, Long> delegationMap = consumerTrackRpt.findMostViewedDelegations(saasLoginToken.getUserId(), consumerId, startDate, 3)
                .stream()
                .collect(toMap(r -> (Long) r[0], r -> (Long) r[1]));
        List<DelegationVO> list = delegationRpt.findAllById(delegationMap.keySet()).stream()
                .map(d -> {
                    DelegationVO vo = convert2DTO(d, new DelegationVO()
                            .setCommunity(new CommunityVO())
                    );
                    vo.setViewCount(delegationMap.getOrDefault(vo.getId(), 1L));
                    return vo;
                })
                .sorted((d1, d2) -> d2.getViewCount().compareTo(d1.getViewCount()))
                .toList();
        return succ(list).setPage(1, 3, list.size());
    }

    @Override
    public ApiResponse<List<DelegationVO>> viewedDelegations(SaasLoginToken saasLoginToken, Long relationId, int pageSize, int pageNum) {
        ConsumerRelation relation = relationRpt.findById(relationId)
                .orElseThrow(() -> new BusinessException("bc.ejuetc.consumer.1013"));
        Long consumerId = relation.getConsumerId();
        LocalDateTime startDate = LocalDateTime.now().minusMonths(3);
        Page<ConsumerTrack> delegationViews = consumerTrackRpt.findDelegationViews(saasLoginToken.getUserId(), consumerId, startDate,
                Pageable.ofSize(pageSize).withPage(pageNum - 1));
        List<Long> delegationIds = delegationViews.stream().map(v -> v.getDelegation().getId()).toList();
        Map<Long, Long> viewCountMap = consumerTrackRpt.countDelegationView(delegationIds, consumerId, startDate).stream()
                .collect(toMap(r -> (Long) r[0], r -> (Long) r[1]));
        List<DelegationVO> list = delegationViews.stream()
                .map(v -> {
                    DelegationVO vo = convert2DTO(v.getDelegation(), new DelegationVO()
                            .setCommunity(new CommunityVO())
                    );
                    vo.setViewTime(v.getCreateTime());
                    vo.setViewCount(viewCountMap.getOrDefault(v.getDelegation().getId(), 1L));
                    return vo;
                })
                .toList();
        return succ(list).setPage(pageSize, pageNum, delegationViews.getTotalElements());
    }

    @Override
    public ApiResponse<Map<DelegationDictDTO.Category, List<String>>> list(List<DelegationDictDTO.Category> categories) {
        Map<DelegationDictDTO.Category, List<String>> vos = new HashMap<>();
        for (DelegationDict dict : dictRpt.findByCategory(categories)) {
            vos.computeIfAbsent(dict.getCategory(), k -> new ArrayList<>()).add(dict.getName());
        }
        return succ(vos);
    }

    @Override
    public ApiResponse<Collection<String>> baseFieldRule(List<ChannelDTO.Code> channelCodes, BusinessOpenDTO.Code businessCode) {
        return succ(channelCodes.stream()
                .map(channel -> BASE_FIELD_REQUIRED_KEY.formatted(channel.name(), businessCode.name()))
                .map(SpringUtil::getProperty)
                .map(StringUtils::splitString)
                .flatMap(List::stream)
                .collect(Collectors.toSet()));
    }

    @Override
    public ApiResponse<Collection<String>> govVerifyFieldRule(String communityAddress, String communityName, BusinessOpenDTO.Code businessCode) {
        CommunityBind communityBind = communityService.queryOrBind(communityAddress, communityName);
        if (communityBind.getCommunity() != null) {
            return succ(communityBind.getCommunity().getCity().getGovVerifyFields(businessCode));
        } else {
            return apiResponse(ResponseStatus.FAIL_BIZ, communityBind.getErrorMsg(), null);
        }
    }
}