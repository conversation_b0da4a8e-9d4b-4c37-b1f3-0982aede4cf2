package com.ejuetc.consumer.impl;

import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.dto.LoginAccountDTO;
import com.ejuetc.consumer.domain.consumer.ConsumerLogin;
import com.ejuetc.consumer.domain.consumer.ConsumerLoginRpt;
import com.ejuetc.consumer.domain.delegation.ChannelDelegation;
import com.ejuetc.consumer.domain.delegation.DelegationRpt;
import com.ejuetc.consumer.domain.implicit.bind.ImplicitBind;
import com.ejuetc.consumer.domain.implicit.bind.ImplicitBind4Aliyun;
import com.ejuetc.consumer.domain.implicit.bind.ImplicitBind4Yihaotong;
import com.ejuetc.consumer.domain.implicit.bind.ImplicitBindRpt;
import com.ejuetc.consumer.web.consumer.LoginToken;
import com.ejuetc.consumer.web.implicit.ImplicitWeb;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import static com.alibaba.fastjson.JSON.parseObject;
import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.apiResponse;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class ImplicitWebImpl implements ImplicitWeb {
    private final DelegationRpt delegationRpt;
    private final ConsumerLoginRpt loginRpt;
    private final ImplicitBindRpt bindRpt;

    @Override
    public ApiResponse<String> bind(LoginToken loginToken, String delegationCode, String consumerPhone) {
        log.info("implicit bind, historyId={}, type={}, delegationCode={}, consumerPhone={}",
                loginToken.getLoginHistoryId(), loginToken.getType(), delegationCode, consumerPhone);
        ChannelDelegation delegation = delegationRpt.findByCode(delegationCode).orElseThrow(() -> new BusinessException("bc.ejuetc.consumer.1010")).subType(ChannelDelegation.class);
        if (loginToken.getType() == LoginAccountDTO.Type.WECHAT) {
            //微信直接返回经纪人真实号码
            return succ(delegation.getBrokerPhone());
        }
        ConsumerLogin consumerLogin = loginRpt.findById(loginToken.getLoginHistoryId()).orElseThrow();
        ImplicitBind bind = bindRpt.findExist(consumerPhone, delegation.getBrokerPhone());
        if (bind == null) {
            bind = new ImplicitBind4Yihaotong(consumerLogin, delegation, consumerPhone).save();
            bind.bind();
        } else {
            bind.addDelegation(delegation);
        }
        return apiResponse(bind.getBindResult(), bind.getSecretPhone());
    }


}