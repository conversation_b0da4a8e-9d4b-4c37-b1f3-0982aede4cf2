package com.ejuetc.consumer.impl;

import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.domain.broker.Broker;
import com.ejuetc.consumer.domain.broker.BrokerRpt;
import com.ejuetc.consumer.web.broker.BrokerWeb;
import com.ejuetc.consumer.web.broker.CompanyConfigVO;
import com.ejuetc.consumer.web.vo.BrokerVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class BrokerWebImpl implements BrokerWeb {
    private final BrokerRpt brokerRpt;

    @Value("${ejuetc.consumer.companyIds4DisabledAddCompanyDelegation}")
    private List<Long> disabledCompanyDelegationIds = List.of();

    @Override
    public ApiResponse<BrokerVO> detail(Long brokerId) {
        return succ(convert2DTO(brokerRpt.getReferenceById(brokerId), new BrokerVO()));
    }

    @Override
    public ApiResponse<List<BrokerVO>> brokerList4Company(SaasLoginToken loginToken) {
        if (!loginToken.getManagerRole()) throw new BusinessException("bc.ejuetc.consumer.1018");

        List<Broker> brokers = brokerRpt.findCompanyBrokers(loginToken.getUserId());
        return succ(convert2DTO(brokers, new BrokerVO().setPhone("")));
    }

}

