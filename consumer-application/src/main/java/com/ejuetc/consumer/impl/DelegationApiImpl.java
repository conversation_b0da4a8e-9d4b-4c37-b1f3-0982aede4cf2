package com.ejuetc.consumer.impl;

import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.response.ResponseStatus;
import com.ejuetc.consumer.api.delegation.*;
import com.ejuetc.consumer.api.dto.*;
import com.ejuetc.consumer.domain.broker.Broker;
import com.ejuetc.consumer.domain.broker.BrokerRpt;
import com.ejuetc.consumer.domain.consumer.ConsumerRpt;
import com.ejuetc.consumer.domain.consumer.ConsumerTrackRpt;
import com.ejuetc.consumer.domain.delegation.*;
import com.ejuetc.consumer.domain.consumer.ConsumerRelationRpt;
import com.ejuetc.consumer.domain.region.RegionRpt;
import com.ejuetc.consumer.web.vo.DelegationVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.apiResponse;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.ThreadUtils.asyncExec;
import static java.lang.Math.min;
import static org.hibernate.Hibernate.initialize;
import static org.springframework.transaction.annotation.Propagation.NOT_SUPPORTED;
import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class DelegationApiImpl implements DelegationAPI {
    private final DelegationRpt delegationRpt;
    private final ConsumerRpt consumerRpt;
    private final ConsumerTrackRpt consumerTrackRpt;
    private final ConsumerRelationRpt relationRpt;
    private final RegionRpt regionRpt;
    private final DelegationWebImpl delegationWebImpl;
    private final DelegationDictRpt dictRpt;
    private final BrokerRpt brokerRpt;

    @Override
    public ApiResponse<DelegationDTO> edit(Long userId, EditDelegationPO po) {
        Broker broker = brokerRpt.findById(userId).orElseThrow(() -> new BusinessException("经纪人不存在"));
        ApiResponse<Delegation> resp = delegationWebImpl.doEdit(
                new SaasLoginToken()
                        .setUserId(userId)
                        .setCompanyId(broker.getCompanyId())
                        .setManagerRole(true)
                , po);
        if (resp.isSucc()) {
            return succ(convert2DTO(resp.getData(), new DelegationDTO()
                    .setBroker(new BrokerDTO())
                    .setCommunity(new CommunityDTO())
                    .setCity(new RegionDTO())
                    .setDistrict(new RegionDTO())
            ));
        } else {
            return apiResponse(resp.getStatus(), resp.getMessage(), null);
        }
    }

    @Override
    public ApiResponse<?> deleteBrokerDelegates(Long brokerId, String parentSourceId, String reason) {
        return deleteBrokerDelegates(new DeleteBrokerDelegatePO()
                .setBrokerId(brokerId)
                .setParentSourceId(parentSourceId)
                .setReason(reason)
        );
    }

    public ApiResponse<?> deleteBrokerDelegates(DeleteBrokerDelegatePO po) {
        if (po.isFieldNull()) {
            return apiResponse(ResponseStatus.FAIL_BIZ, "删除时参数不能均为空", null);
        }
        List<BrokerDelegation> delegations = delegationRpt.findBrokerDelegations(po);
        if (delegations.isEmpty())
            return apiResponse(ResponseStatus.FAIL_BIZ, "删除时未找到对应的委托[%s]".formatted(po), null);
        delegations.forEach(d -> d.delete(po.getReason()));
        return succ();
    }

    @Override
    public ApiResponse<DelegationDTO> receiveChannelNotify(Long id, DelegationDTO.Status newStatus, String remark) {
        ChannelDelegation delegation = delegationRpt.findAndLockChannelDelegationById(id);
        initialize(delegation.getDetail().getCommunityBind().getAddress());
        delegation.receiveChannelNotify(newStatus, remark);
        return succ(convert2DTO(delegation, new DelegationDTO()));
    }

    @Override
    public ApiResponse<DelegationVO> upDown(Long brokerDelegationId, List<ChannelDTO.Code> channelCodes, boolean up, String remark) {
        BrokerDelegation delegation = delegationRpt.findBrokerDelegationById(brokerDelegationId);
        delegation.upDown(up, remark, channelCodes);
        return succ(convert2DTO(delegation, new DelegationVO()
                .setChannelDelegationMap(Map.of())
        ));
    }

    @Override
    public ApiResponse<?> refreshDict() {
        dictRpt.refresh();
        return succ();
    }

    @Override
    @Transactional(propagation = NOT_SUPPORTED)
    public ApiResponse<?> manualPush(int threadCount, int batchSize, int maxCount) {
        for (int i = 0; i < threadCount; i++) {
            int threadNum = i;
            asyncExec(() -> {
                DelegationApiImpl api = getBean(DelegationApiImpl.class);
                log.info("thread [{}] manualPush start", threadNum);
                int total = 0;
                while (true) {
                    try {
                        log.info("thread [{}] manualPush start", threadNum);
                        int count = api.doManualPush(maxCount, threadNum, threadCount, batchSize);
                        total += count;
                        log.info("thread [{}] manualPush size:{}", threadNum, total);
                        if (count <= 0 || total >= maxCount) break;
                    } catch (Exception e) {
                        log.error("thread [{}] manualPush error", threadNum, e);
                    }
                }
                log.info("thread [{}] manualPush done", threadNum);
            });

        }
        return succ();
    }

    @Override
    public ApiResponse<List<String>> list(DelegationDictDTO.Category category) {
        return succ(dictRpt.findByCategory(List.of(category)).stream().map(DelegationDict::getName).toList());
    }

    @Transactional(propagation = REQUIRES_NEW)
    public int doManualPush(int maxCount, int threadNum, int threadCount, int batchSize) {
        List<ChannelDelegation> histories = delegationRpt.findHistoryDelegations(threadCount, threadNum, Pageable.ofSize(min(maxCount, batchSize)));
        histories.forEach(ChannelDelegation::manualPush);
        return histories.size();
    }

    @Override
    public void rebindCommunity() {
        delegationRpt.findByCommunityIsNull().forEach(Delegation::rebindCommunity);
    }

    @Override
    public ApiResponse<List<DelegationVO>> query(ApiQueryListPO po) {
        List<Delegation> delegations = delegationRpt.findList2Api(po);
        return succ(convert2DTO(delegations, new DelegationVO()));
    }

}