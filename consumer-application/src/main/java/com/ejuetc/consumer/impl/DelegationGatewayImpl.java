package com.ejuetc.consumer.impl;

import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.response.ResponseStatus;
import com.ejuetc.consumer.api.delegation.*;
import com.ejuetc.consumer.domain.broker.Broker;
import com.ejuetc.consumer.domain.broker.BrokerRpt;
import com.ejuetc.consumer.domain.consumer.ConsumerRelationRpt;
import com.ejuetc.consumer.domain.consumer.ConsumerRpt;
import com.ejuetc.consumer.domain.consumer.ConsumerTrackRpt;
import com.ejuetc.consumer.domain.delegation.*;
import com.ejuetc.consumer.domain.region.RegionRpt;
import com.ejuetc.consumer.gateway.DelegationGateway;
import com.ejuetc.saasapi.pro.GetawayResponse;
import com.ejuetc.saasapi.sdk.SaaSApiSDK;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.apiResponse;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.ThreadUtils.asyncExec;
import static com.ejuetc.consumer.api.dto.DelegationDTO.SOURCE_GATEWAY;
import static java.lang.Math.min;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class DelegationGatewayImpl implements DelegationGateway {
    private final DelegationWebImpl delegationWebImpl;
    private final DelegationApiImpl delegationApiImpl;
    private final BrokerRpt brokerRpt;

    @Override
    public GetawayResponse edit(Map<String, String> headers, EditDelegationPO4Gateway po) {
        Broker broker = brokerRpt.findById(po.getBrokerId()).orElseThrow(() -> new BusinessException("经纪人不存在"));
        String companyUserId = headers.get(SaaSApiSDK.HEADER_USER_ID);
        if (!broker.getCompanyId().toString().equals(companyUserId))
            throw new BusinessException("bc.ejuetc.consumer.1028", po.getBrokerId(), companyUserId);

        ApiResponse<Delegation> resp = delegationWebImpl.doEdit(
                new SaasLoginToken()
                        .setUserId(broker.getId())
                        .setCompanyId(broker.getCompanyId())
                        .setManagerRole(true)
                , po.setSourceType(SOURCE_GATEWAY + "-" + companyUserId)
        );
        if (resp.isSucc()) {
            DelegationVO4Gateway vo = convert2DTO(resp.getData(), new DelegationVO4Gateway().setChannelDelegationMap(Map.of()));
            return new GetawayResponse(
                    ResponseStatus.SUCC_DONE,
                    "请求成功",
                    vo
            );
        } else {
            return new GetawayResponse(
                    resp.getStatus(),
                    resp.getMessage(),
                    resp.getError()
            );
        }
    }

    @Override
    public GetawayResponse deleteBrokerDelegates(Map<String, String> headers,DeleteBrokerDelegatePO po) {
        Broker broker = brokerRpt.findById(po.getBrokerId()).orElseThrow(() -> new BusinessException("经纪人不存在"));
        String companyUserId = headers.get(SaaSApiSDK.HEADER_USER_ID);
        if (!broker.getCompanyId().toString().equals(companyUserId))
            throw new BusinessException("bc.ejuetc.consumer.1028", po.getBrokerId(), companyUserId);

        po.setSourceType(SOURCE_GATEWAY + "-" + companyUserId);
        ApiResponse<?> response = delegationApiImpl.deleteBrokerDelegates(po);
        return new GetawayResponse(
                response.getStatus(),
                response.getMessage(),
                null
        );
    }

}