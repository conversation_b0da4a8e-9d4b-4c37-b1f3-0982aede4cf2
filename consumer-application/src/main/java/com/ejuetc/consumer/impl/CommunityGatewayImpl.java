package com.ejuetc.consumer.impl;

import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.community.*;
import com.ejuetc.consumer.domain.community.*;
import com.ejuetc.consumer.domain.region.RegionRpt;
import com.ejuetc.consumer.gateway.CommunityGateway;
import com.ejuetc.consumer.web.vo.BeikeCommunityVO;
import com.ejuetc.consumer.web.vo.CommunityDetailVO;
import com.ejuetc.consumer.web.vo.CommunityVO;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.apiResponse;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.ThreadUtils.asyncExec;
import static org.springframework.data.domain.Pageable.ofSize;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class CommunityGatewayImpl implements CommunityGateway {
    private final CommunityBeikeRpt communityBeikeRpt;

    @Override
    public ApiResponse<List<BeikeCommunityVO>> queryBeikeCommunity(QueryCommunityPO queryCommunityPO) {
        Page<CommunityBeike> page = communityBeikeRpt.findHasDetailByName(queryCommunityPO.getCityId(), queryCommunityPO.getKeyword(), ofSize(queryCommunityPO.getPageSize()).withPage(queryCommunityPO.getPageNum() - 1));
        return succ(convert2DTO(page.getContent(), new BeikeCommunityVO())).setPage(queryCommunityPO.getPageSize(), queryCommunityPO.getPageNum(), page.getTotalElements());
    }


    @Override
    public ApiResponse<Map<Integer, Set<String>>> queryLayoutGroup(QueryLayoutGroupPO po) {
        CommunityBeike beike = communityBeikeRpt.findById(po.getBeikeId()).orElseThrow(() -> new CodingException("贝壳小区不存在，ID: %s", po));
        Community community = beike.getCommunity();
        return succ(community != null ? community.getLayoutNames() : null);
    }

    @Override
    public ApiResponse<CommunityVO> queryDetail(QueryCommunityDetailPO po) {
        CommunityBeike beike = communityBeikeRpt.findById(po.getBeikeId()).orElseThrow(() -> new CodingException("贝壳小区不存在，ID: %s", po));

        CommunityVO communityVO = convert2DTO(beike.getCommunity(), new CommunityVO());
        CommunityDetail detail = beike.getDetail();
        communityVO.setDetail(convert2DTO(detail, new CommunityDetailVO()));
        communityVO.setPicturesMap(detail.getPicturesMap());
        communityVO.setLayoutsSummaryMap(detail.getLayoutsSummaryMap());
        return succ(communityVO.setBeikeId(beike.getId()).setBeikeName(beike.getName()));
    }

}

