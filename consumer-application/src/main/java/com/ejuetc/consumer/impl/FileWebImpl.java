package com.ejuetc.consumer.impl;

import com.ejuetc.commons.base.component.OssComponent;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.spring.SpringUtil;
import com.ejuetc.consumer.web.consumer.LoginToken;
import com.ejuetc.consumer.web.file.FileWeb;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.UUID;

import static java.time.LocalDateTime.now;
import static java.time.format.DateTimeFormatter.ofPattern;

@RequiredArgsConstructor
@Slf4j
@RestController
public class FileWebImpl implements FileWeb {

    @SneakyThrows
    @Override
    public ApiResponse<String> upload(LoginToken loginToken, MultipartFile file) {
        log.info("FileWebImpl.upload accountId={}, fileName={}, fileSize={}, contentType={}",
                loginToken == null ? null : loginToken.getLoginAccountId(), file.getOriginalFilename(), file.getSize(), file.getContentType());
        if (!file.getContentType().startsWith("image/")) {
            throw new BusinessException("bc.ejuetc.consumer.1027");
        }
        String filePath = "consumer/%s/%s-%s".formatted(now().format(ofPattern("yyyyMM")), UUID.randomUUID(), file.getOriginalFilename());
        String url = SpringUtil.getBean(OssComponent.class).putOSS(filePath, file.getInputStream());
        return ApiResponse.succ(url);
    }
}
