package com.ejuetc.consumer.impl;

import com.alicom.mns.tools.DefaultAlicomMessagePuller;
import com.aliyun.mns.model.Message;
import com.aliyuncs.exceptions.ClientException;
import com.ejuetc.commons.base.component.OssComponent;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.implicit.ImplicitAPI;
import com.ejuetc.consumer.domain.community.CommunityRpt;
import com.ejuetc.consumer.domain.consumer.ConsumerRelationRpt;
import com.ejuetc.consumer.domain.consumer.ConsumerRpt;
import com.ejuetc.consumer.domain.delegation.DelegationRpt;
import com.ejuetc.consumer.domain.implicit.*;
import com.ejuetc.consumer.domain.consumer.ConsumerLoginRpt;
import com.ejuetc.consumer.domain.implicit.bind.ImplicitBind;
import com.ejuetc.consumer.domain.implicit.bind.ImplicitBindRpt;
import com.ejuetc.consumer.web.vo.ImplicitCallPO;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.alibaba.fastjson.JSON.parseObject;
import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.apiResponse;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.ThreadUtils.invokeAll;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class ImplicitApiImpl implements ImplicitAPI {
    private final DelegationRpt delegationRpt;
    private final CommunityRpt communityRpt;
    private final ConsumerRpt consumerRpt;
    private final ConsumerLoginRpt loginRpt;
    private final ImplicitBindRpt bindRpt;
    private final ImplicitCallRpt callRpt;
    private final OssComponent ossComponent;
    private final ConsumerRelationRpt relationRpt;

    @Value("${ejuetc.consumer.implicit.accessKey}")
    private String accessKey;

    @Value("${ejuetc.consumer.implicit.accessSecret}")
    private String accessSecret;

    @Value("${ejuetc.consumer.implicit.queueName}")
    private String queueName;

    @Value("${ejuetc.consumer.implicit.startListener:true}")
    private boolean startListener;

    @PostConstruct
    public void startListener() throws ClientException {
        if (startListener) {
            log.info("启动隐号监听服务");
        } else {
            log.info("不启动隐号监听服务");
            return;
        }

        DefaultAlicomMessagePuller puller = new DefaultAlicomMessagePuller();
        puller.setConsumeMinThreadSize(6);
        puller.setConsumeMaxThreadSize(16);
        puller.setThreadQueueSize(200);
        puller.setPullMsgThreadSize(1);
        puller.openDebugLog(false);
        puller.startReceiveMsg(accessKey, accessSecret, "SecretReport", queueName, (message) -> getBean(ImplicitApiImpl.class).receiveCall(message));
    }

    @Override
    public ApiResponse<?> receiveCall(ImplicitCallPO po) {
        try {
            receiveCall(new ImplicitCall(po));
            return succ();
        } catch (Throwable e) {
            log.error("处理隐号回执失败", e);
            return apiResponse(e);
        }
    }

    public boolean receiveCall(Message message) {
        try {
            String msgBody = message.getMessageBodyAsString();
            log.info("收到隐号回执:{}", msgBody);
            ApiResponse<?> response = receiveCall(new ImplicitCallPO(parseObject(msgBody)));
            return response.isSucc();
        } catch (Throwable e) {
            log.error("处理隐号回执失败", e);
            return false;
        }
    }

    public void receiveCall(ImplicitCall call) {
        if (callRpt.findByCallId(call.getCallId()) == null) {
            call.save();
            call.process();
        }
    }


    @Override
    @Scheduled(cron = "${ejuetc.consumer.implicit.convertRecordUrl.cron:13 */3 * * * ?}")
    public void convertRecordUrl() {
        log.info("转换录音文件URL");
        List<ImplicitCall> calls = callRpt.findWaitConvert(ossComponent.getUrlPrefix());
        log.info("待转换录音文件URL数量:{}", calls.size());
        invokeAll(calls);
        log.info("转换录音文件URL完成");
    }

    @Scheduled(cron = "${ejuetc.consumer.implicit.unbind.cron:37 */5 * * * ?}")
    public void unbind() {
        log.info("解绑无效绑定");
        List<ImplicitBind> waitUnbind = bindRpt.findWaitUnbind();
        log.info("待解绑数量:{}", waitUnbind.size());
        invokeAll(waitUnbind);
        log.info("解绑无效绑定完成");
    }

    @Override
    public void unbind(Long bindId) {
        if (bindId != null)
            bindRpt.getReferenceById(bindId).unbind();
        else
            unbind();
    }
}