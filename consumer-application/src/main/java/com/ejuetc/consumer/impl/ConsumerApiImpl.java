package com.ejuetc.consumer.impl;

import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.filter.LoginTokenFilter;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.spring.SpringUtil;
import com.ejuetc.consumer.api.dto.ConsumerShareDTO;
import com.ejuetc.consumer.api.dto.LoginAccountDTO;
import com.ejuetc.consumer.domain.delegation.Delegation;
import com.ejuetc.consumer.domain.delegation.DelegationRpt;
import com.ejuetc.consumer.domain.region.RegionRpt;
import com.ejuetc.consumer.domain.consumer.*;
import com.ejuetc.consumer.integration.alipay.AliPayService;
import com.ejuetc.consumer.integration.wechat.WechatService;
import com.ejuetc.consumer.web.consumer.*;
import com.ejuetc.consumer.web.vo.*;
import com.fangyou.repo.property.api.VerificationAPI;
import jakarta.persistence.EntityManager;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.ejuetc.commons.base.filter.LoginTokenFilter.cacheLoginInfo;
import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.*;
import static com.ejuetc.commons.base.utils.StringUtils.notBlank;
import static java.util.stream.Collectors.toMap;
import static org.springframework.data.domain.PageRequest.ofSize;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class ConsumerApiImpl implements ConsumerApi, ConsumerWeb {

    private final AliPayService aliPayService;
    private final WechatService wechatService;
    private final ConsumerAccountRpt accountRpt;
    private final ConsumerLoginRpt consumerLoginRpt;
    private final EntityManager entityManager;
    private final RegionRpt regionRpt;
    private final ConsumerRpt consumerRpt;
    private final ConsumerTrackRpt trackRpt;
    private final ConsumerShareRpt consumerShareRpt;
    private final ConsumerRelationRpt relationRpt;
    private final DelegationRpt delegationRpt;
    private final ConsumerShareRpt shareRpt;

    @Value("${ejuetc.consumer.Delegation.saasVerificationAPI.url}")
    private String verificationApiUrl;

    @Getter(lazy = true)
    @Accessors(fluent = true)
    private final VerificationAPI verificationAPI = getAPI(VerificationAPI.class, verificationApiUrl);


    @Override
    public ApiResponse<LoginToken> login(LoginPO po) {
        String accountIdent = switch (po.getType()) {
            case ALIPAY -> aliPayService.getOpenId(po.getLoginIdent());
            case WECHAT -> wechatService.getOpenId(po.getLoginIdent());
            default -> throw new CodingException("不支持的登录方式[%s]", po.getType());
        };

        ConsumerAccount account = accountRpt.findByTypeAndAccountIdent(po.getType(), accountIdent).orElseGet(() -> {
            ConsumerAccount newAccount = ConsumerAccount.of(po.getType(), accountIdent);
            newAccount.save();
            return newAccount;
        });
        if (!account.checkAuth(po.getLoginAuth()))
            throw new BusinessException("bc.ejuetc.consumer.1001");
        ConsumerLogin login = new ConsumerLogin(account, po.getChannelCode()).save();
        if (notBlank(po.getShareCode())) {
            unionShareCode(login, po.getShareCode());
        }

        Consumer consumer = account.getConsumer();

        LoginToken ro = new LoginToken()
                .setLoginAccountId(account.getId())
                .setAccountIdent(account.getAccountIdent())
                .setType(account.getType())
                .setChannelCode(po.getChannelCode())
                .setLoginHistoryId(login.getId())
                .setLocation(login.getLocation())
                .setConsumerId(consumer != null ? consumer.getId() : null)
                .setPhone(consumer != null ? consumer.getPhone() : null)
                .setNickName(account.getNiceName())
                .setIconUrl(account.getIconUrl());
        cacheLoginInfo(ro, Duration.ofDays(14));
        return succ(ro);
    }

    private void unionShareCode(ConsumerLogin login, String shareCode) {
        Optional<ConsumerShare> shareOptional = consumerShareRpt.findByCode(shareCode);
        if (shareOptional.isEmpty()) throw new CodingException("分享码不存在[%s]", shareCode);

        ConsumerShare share = shareOptional.get();
        share.setAimLogin(login);
        login.setShare(share);
    }

    @Override
    public ApiResponse<Void> changeShareCode(LoginToken loginToken, String shareCode) {
        ConsumerAccount account = accountRpt.getReferenceById(loginToken.getLoginAccountId());
        ConsumerLogin login = new ConsumerLogin(account, loginToken.getChannelCode()).save();
        unionShareCode(login, shareCode);

        loginToken.setLoginHistoryId(login.getId());
        cacheLoginInfo(loginToken);
        return succ();
    }

    @Override
    public ApiResponse<LoginToken> edit(LoginToken loginToken, EditConsumerPO po) {
        log.info("edit consumer, accountId={}, nickName={}, iconUrl={}",
                loginToken.getLoginAccountId(), po.getNickName(), po.getIconUrl());
        ConsumerAccount consumerAccount = accountRpt.findById(loginToken.getLoginAccountId())
                .orElseThrow(() -> new CodingException("LoginAccount not found, accountIdent=%s,loginAccountId=%s",
                        loginToken.getAccountIdent(), loginToken.getLoginAccountId()));

        consumerAccount.edit(po.getNickName(), po.getIconUrl());

        loginToken.setNickName(consumerAccount.getNiceName());
        loginToken.setIconUrl(consumerAccount.getIconUrl());
        cacheLoginInfo(loginToken, Duration.ofDays(14));
        return succ(loginToken);
    }

    @Override
    public ApiResponse<?> location(LoginToken loginToken, String location) {
        log.info("location, historyId={}, location={}", loginToken.getLoginHistoryId(), location);
        ConsumerLogin consumerLogin = consumerLoginRpt.findById(loginToken.getLoginHistoryId())
                .orElseThrow(() -> new CodingException("LoginHistory not found, accountIdent=%s,loginHistoryId=%s",
                        loginToken.getAccountIdent(), loginToken.getLoginHistoryId()));
        consumerLogin.setLocation(location);
        if (consumerLogin.getRegion() != null) {
            loginToken.setCityId(consumerLogin.getRegion().getCityId());
            loginToken.setCityName(consumerLogin.getRegion().getCityName());
        }
        loginToken.setLocation(location);
        cacheLoginInfo(loginToken, Duration.ofDays(14));
        return succ();
    }

    @Override
    public ApiResponse<String> bindPhoneByAuth(LoginToken loginToken, String authCode) {
        log.info("bindPhoneByAuth, historyId={}, authCode={}", loginToken.getLoginHistoryId(), authCode);
        ConsumerAccount loginAccount = entityManager.find(ConsumerAccount.class, loginToken.getLoginAccountId());
        String phone = loginAccount.bindPhoneByCode(authCode);
        return succ(phone);
    }

    @Override
    public ApiResponse<?> shareByConsumer(LoginToken token, String shareCode, ConsumerShareDTO.TargetType targetType, Long targetId, Long brokerId) {
        ConsumerShare share = new ConsumerShare(token.getLoginHistoryId(), shareCode, targetType, targetId, brokerId);
        share.save();
        return succ();
    }

    @Override
    public ApiResponse<ConsumerVO> consumerDetail(SaasLoginToken loginToken, Long consumerId) {
        Consumer consumer = consumerRpt.findById(consumerId).orElseThrow(() -> new BusinessException("bc.ejuetc.consumer.1013"));
        LocalDateTime lastViewTime = trackRpt.findLastViewTime(loginToken.getUserId(), consumer.getId());
        return succ(new ConsumerVO(consumer.getId(), consumer.getConsumerPhone4Mask(), lastViewTime));
    }

    @Override
    public ApiResponse<List<RelationVO>> relationList(SaasLoginToken loginToken, LoginAccountDTO.Type accountType, String keyword, Boolean expirationFlag, int pageSize, int pageNum) {
        log.info("relationList, userId={}, accountType={}, keyword={}, expirationFlag={}, pageSize={}, pageNum={}", loginToken.getUserId(), accountType, keyword, expirationFlag, pageSize, pageNum);
        Pageable pageable = Pageable.ofSize(pageSize).withPage(pageNum - 1);
        Page<ConsumerRelation> page = relationRpt.findList(loginToken.getUserId(), accountType, keyword, expirationFlag, pageable);
        List<ConsumerRelation> relations = page.getContent();
        return succ(convert2DTO(relations, new RelationVO()))
                .setPage(pageSize, pageNum, page.getTotalElements());
    }

    @Override
    public ApiResponse<RelationVO> relationDetail(SaasLoginToken loginToken, Long relationId) {
        ConsumerRelation relation = relationRpt.findById(relationId).orElseThrow();
        if (!relation.equalsBrokerId(loginToken.getUserId()))
            throw new BusinessException("bc.ejuetc.consumer.1012");
        return succ(convert2DTO(relation, new RelationVO()
                .setCalls(List.of(new CallVO().setDelegation(new DelegationVO())))
                .setSuccBinds(List.of(new BindVO()))
                .setTrackCountVOS(List.of(new TrackCountVO()))
        ));
    }

    @Override
    public ApiResponse<List<TrackCountVO>> relationViewDelegations(SaasLoginToken loginToken, Long relationId, int pageSize, int pageNum) {
        ConsumerRelation relation = relationRpt.findById(relationId).orElseThrow();
        if (!relation.equalsBrokerId(loginToken.getUserId()))
            throw new BusinessException("bc.ejuetc.consumer.1012");
        Page<TrackCountVO> voPage = trackRpt.dayGroupAndCountByRelationId(relationId, ofSize(pageSize).withPage(pageNum - 1));
        Map<Long, DelegationVO> id2DelegationMap = delegationRpt.findListByIds(voPage.map(TrackCountVO::getDelegationId).toList()).stream().collect(toMap(Delegation::getId, d -> convert2DTO(d, new DelegationVO())));
        List<TrackCountVO> vos = voPage.map(vo -> vo.setDelegation(id2DelegationMap.get(vo.getDelegationId()))).toList();
        ApiResponse<List<TrackCountVO>> delegationsResp = succ(vos).setPage(pageSize, pageNum, voPage.getTotalElements());
        return succ(delegationsResp.getData()).setPage(delegationsResp.getPage());
    }

    @Override
    public ApiResponse<RelationVO> relationEdit(SaasLoginToken token, RelationPO<?> ro) {
        return relationEdit(token.getUserId(), ro);
    }

    @Override
    public ApiResponse<ShareStatisticsVO> shareStatistics(SaasLoginToken token) {
        ShareStatisticsVO vo = shareRpt.shareStatistics(token.getUserId());
        vo.setConsumerRelationCount(relationRpt.countByBrokerId(token.getUserId()));

        log.info("saasAgentStore, userId={}, merchantId={}", token.getUserId(), token.getMerchantId());
        Map<String, Object> map = verificationAPI().saasAgentStore(token.getUserId(), token.getMerchantId());
        log.info("saasAgentStore, userId={}, merchantId={}, map={}", token.getUserId(), token.getMerchantId(), map);
        if (map != null && "success".equals(map.get("status"))) {
            vo.addBrokerSharerCount((Integer) map.get("shareCount"));
            vo.addConsumerSharerCount((Integer) map.get("forwardCount"));
            vo.addDistinctDelegationCount((Integer) map.get("shareHouseCount"));
            vo.addConsumerRelationCount((Integer) map.get("visitorCount"));
        } else {
            log.error("saasAgentStore error, userId={}, merchantId={}", token.getUserId(), token.getMerchantId());
        }
        return succ(vo);
    }


    @Override
    public ApiResponse<RelationVO> relationEdit(Long brokerId, RelationPO<?> ro) {
        List<ConsumerRelation> phoneRelations = relationRpt.findByRemarkPhones(brokerId, ro.getRemarkPhones());
        ConsumerRelation relation = ro.getId() != null
                ? relationRpt.findById(ro.getId()).orElseThrow()
                : ro.getSourceId() != null && ro.getSourceType() != null
                ? relationRpt.findBySource(ro.getSourceId(), ro.getSourceType())
                : null;
        if (relation == null) {
            if (!phoneRelations.isEmpty())
                throw new BusinessException("bc.ejuetc.consumer.1026");
            relation = new ConsumerRelation(brokerId, ro).save();
        } else {
            Long relationId = relation.getId();
            if (phoneRelations.stream().anyMatch(r -> !r.getId().equals(relationId)))
                throw new BusinessException("bc.ejuetc.consumer.1026");
            relation.edit(brokerId, ro);
        }


        return succ(convert2DTO(relation, new RelationVO().setCalls(List.of(new CallVO()))));
    }

    @Override
    public void clearToken(String token) {
        String tokenKey = LoginTokenFilter.getKey(token);
        RedisTemplate redisTemplate = getBean(RedisTemplate.class, "redisTemplate");
        redisTemplate.delete(tokenKey);
    }

}

