package com.ejuetc.consumer.impl;

import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.response.ResponseStatus;
import com.ejuetc.consumer.api.community.*;
import com.ejuetc.consumer.domain.region.RegionRpt;
import com.ejuetc.consumer.domain.community.*;
import com.ejuetc.consumer.web.community.CommunityWeb;
import com.ejuetc.consumer.web.consumer.LoginToken;
import com.ejuetc.consumer.web.vo.BeikeCommunityVO;
import com.ejuetc.consumer.web.vo.CommunityLayoutVO;
import com.ejuetc.consumer.web.vo.CommunityVO;
import com.ejuetc.consumer.web.vo.CommunityTipsVO;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.apiResponse;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.spring.SpringUtil.getProperty;
import static com.ejuetc.commons.base.utils.StringUtils.notBlank;
import static com.ejuetc.commons.base.utils.ThreadUtils.asyncExec;
import static org.springframework.data.domain.Pageable.ofSize;
import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class CommunityWebImpl implements CommunityWeb {
    private final RegionRpt regionRpt;
    private final CommunityRpt communityRpt;
    private final CommunityBindRpt communityBindRpt;
    private final RedisTemplate<String, String> redisTemplate;
    private final EntityManager entityManager;
    private final CommunityPictureRpt pictureRpt;
    private final CommunityDetailRpt detailRpt;
    private final CommunityTipsRpt tipsRpt;
    private int batchSize;
    private final CommunityService communityService;
    private final CommunityGatewayImpl gateway;
    private final CommunityApiImpl communityApi;

    @Override
    public ApiResponse<List<CommunityVO>> queryByKeyword(LoginToken loginToken, Long cityId, String keyword, int pageSize, int pageNum) {
        log.info("community list, historyId={}, cityId={}, keyword={}",
                loginToken == null ? null : loginToken.getLoginHistoryId(), cityId, keyword);
        if (loginToken == null || StringUtils.isBlank(keyword)) {
            return succ(List.<CommunityVO>of()).setPage(pageSize, pageNum, 0);
        }
        pageSize = Math.min(pageSize, 10);
        Page<Community> page = communityRpt.findByName(cityId, keyword,
                ofSize(pageSize).withPage(pageNum - 1));
        return succ(convert2DTO(page.getContent(), new CommunityVO())).setPage(pageSize, pageNum, page.getTotalElements());
    }

    @Override
    public ApiResponse<List<CommunityTipsVO>> queryByTips(SaasLoginToken loginToken, String cityName, String keyword, int pageNum, int pageSize) {
        List<CommunityTipsVO> vos = communityService.queryTips(cityName, keyword);
        return succ(vos).setPage(pageSize, pageNum, vos.size());
    }


    @Override
    public ApiResponse<Map<String, List<String>>> queryCommunityPictures(SaasLoginToken loginToken, String communityAddress, String communityName) {
        CommunityBind communityBind = communityService.queryOrBind(communityAddress, communityName);
        if (communityBind.getCommunity() != null) {
            return succ(communityBind.getCommunity().getPicturesMap());
        } else {
            return apiResponse(ResponseStatus.FAIL_BIZ, communityBind.getErrorMsg(), null);
        }
    }

    @Override
    public ApiResponse<BindCommunityRO> bind(SaasLoginToken loginToken, String address, String name) {
        log.info("bind community, loginToken={}, address={}, name={}", loginToken, address, name);
        return communityApi.bind(address, name);
    }

    @Override
    public ApiResponse<List<CommunityLayoutVO>> queryCommunityLayouts(SaasLoginToken loginToken, String communityAddress, String communityName) {
        CommunityBind communityBind = communityService.queryOrBind(communityAddress, communityName);
        if (communityBind.getCommunity() != null) {
            List<CommunityLayout> layouts = communityBind.getCommunity().getLayouts().stream().filter(l -> notBlank(l.getPicUrl())).toList();
            return succ(convert2DTO(layouts, new CommunityLayoutVO()));
        } else {
            return apiResponse(ResponseStatus.FAIL_BIZ, communityBind.getErrorMsg(), null);
        }
    }

    @Override
    public ApiResponse<List<BeikeCommunityVO>> queryBeikeCommunity(SaasLoginToken loginToken, QueryCommunityPO po) {
        log.info("queryBeikeCommunity, loginToken={}, po={}", loginToken, po);
        return gateway.queryBeikeCommunity(po);
    }

    @Override
    public ApiResponse<Map<Integer, Set<String>>> queryLayoutGroup(SaasLoginToken loginToken, QueryLayoutGroupPO po) {
        log.info("queryLayoutGroup, loginToken={}, po={}", loginToken, po);
        return gateway.queryLayoutGroup(po);
    }

    @Transactional(propagation = REQUIRES_NEW)
    public void doBindCommunityDetail(Long detailId) {
        CommunityDetail detail = detailRpt.getReferenceById(detailId);
        CommunityBind bind = communityService.queryOrBind(detail.getFullAddress(), detail.getCommunityName());
        detail.bindCommunity(bind);
    }

    @Transactional(propagation = REQUIRES_NEW)
    public void doRebind(List<Long> bindIds) {
        communityBindRpt.findAllById(bindIds).forEach(CommunityBind::exec);
    }

}

