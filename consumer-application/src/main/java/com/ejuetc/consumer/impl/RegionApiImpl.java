package com.ejuetc.consumer.impl;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;

import com.ejuetc.consumer.api.dto.RegionDTO;
import com.ejuetc.consumer.api.region.RegionAPI;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import com.ejuetc.consumer.domain.region.Region;
import com.ejuetc.consumer.domain.region.RegionRpt;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class RegionApiImpl implements RegionAPI {
    private final RegionRpt regionRpt;

//    @Override
//    public void initTown(String cityCode) {
//        if (cityCode == null) {
//            regionRpt.findByParentCodeAndType(null, RegionDTO.Type.CITY).forEach(Region::insertTown);
//        } else {
//            regionRpt.findByCode(cityCode).insertTown();
//        }
//    }
}
