package com.ejuetc.consumer.impl;

import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.consumer.api.implicit.ImplicitClueAPI;
import com.ejuetc.consumer.domain.implicit.ImplicitClue;
import com.ejuetc.consumer.domain.implicit.ImplicitClueRpt;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.util.List;
import java.util.Objects;


@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class ImplicitClueApiImpl implements ImplicitClueAPI {

    private final ImplicitClueRpt implicitClueRpt;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Scheduled(cron = "${com.ejuetc.consumer.schedule.implicitCluePush}")
    @Override
    public void pushClue() {
        String lockKey = "consumer:lock:pushClue";
        Boolean set = redisTemplate.opsForValue().setIfAbsent(lockKey, "1", Duration.ofMinutes(1));
        if (!Objects.equals(true, set)) {
            return;
        }
        List<ImplicitClue> clues = implicitClueRpt.findNotPushClue();
        for (ImplicitClue clue : clues) {
            clue.pushClue();
        }
        redisTemplate.delete(lockKey);
    }

    @Override
    public void pushOneClue(Long id) {
        ImplicitClue implicitClue = implicitClueRpt.findByIdAndLock(id)
                .orElseThrow(() -> new CodingException("ImplicitClue Not Found, %s", id));
        implicitClue.pushClue();
    }
}
