package com.ejuetc.consumer.integration.alipay;

import com.ejuetc.consumer.integration.alipay.pro.AlipayUserInfoRO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.ejuetc.consumer.integration.alipay.pro.AlipayGenericResponse;
import com.ejuetc.consumer.integration.alipay.pro.AlipayOauthTokenRO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(properties = {"spring.jpa.hibernate.ddl-auto=none"})
public class AlipayServiceTest {
    @Autowired
    private AliPayService aliPayService;
    @Autowired
    private AlipayConfig alipayConfig;

    @Test
    public void getOauthToken() {
        AlipayGenericResponse<AlipayOauthTokenRO> response = aliPayService.getOauthToken("4b203fe6c11548bcabd8da5bb087a83b");
        log.info("{}", response);
    }

    @Test
    public void getUserInfo() {
        AlipayGenericResponse<AlipayUserInfoRO> response = aliPayService.getUserInfo("accessToken");
        log.info("{}", response);
    }

    @Test
    public void decryptPhone() {
        String encryptData = "A9elIDzs/MvlqYa0Mk5SCBpj2ctSQHxwf/kphm1Er6jomhc5u2FyTSbcdF8Quu2YFjRMX8J9iaTCvAk3RInqEsq6isaPh98U4a7XCJBo03GGIGuvRPKYfaanx4Pe4cmckI0S0x8zDFrLYRtEPtXcki1LdFFAPRQbB3i9kxaL29UlT27AwImSsD4fy/G5+0tU6HzVJXI5rQIqf26hM3sDbg5S8AZpL3wISHktd8zEYGuIjztUDx5HfAJllAY9kkKFTZltnMETUic1NT+PR6DGJ4UQl08b5hpaBojfbmbaP3C1vWrCwBD7yUwfnsA4zqJuwMwDMJ0mMMYdPoUdmNsWbt+jxp/moOVq+LqdlIdqKBJD6ibKIuxFM93MdQulG/m/wBJ77/tyH9quonbxzPFODRSgz60CY8bEUd7qagOC5QKuV5SHqwRx/jtV2JW3q3u6ukVNSs4xmq/FwBFxdGMnIkIA05NuzF2OV6w6iNJQhMY=";
        System.out.println(AlipayUtil.decrypt(encryptData, alipayConfig.getAesKey()));
    }
}
