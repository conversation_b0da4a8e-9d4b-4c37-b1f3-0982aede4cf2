package com.ejuetc.consumer.integration.wechat;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(properties = {"spring.jpa.hibernate.ddl-auto=none"})
public class WechatServiceTest {
    @Autowired
    private WechatService wechatService;

    @Test
    public void getOpenId() {
        String openId = wechatService.getOpenId("123456");
        log.info("openId={}", openId);
    }

    @Test
    public void getAccessToken() {
        String accessToken = wechatService.getAccessToken();
        log.info("accessToken={}", accessToken);
    }

    @Test
    public void getPhone() {
        String phone = wechatService.getPhone("abd");
        System.out.println(phone);
    }
}
