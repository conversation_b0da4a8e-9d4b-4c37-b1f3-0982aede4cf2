package com.ejuetc.consumer.impl;

import com.alibaba.fastjson.JSON;
import com.ejuetc.commons.base.component.OssComponent;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.dto.ImplicitCallDTO;
import com.ejuetc.consumer.api.implicit.ImplicitAPI;
import com.ejuetc.consumer.domain.implicit.bind.ImplicitBindRpt;
import com.ejuetc.consumer.web.consumer.LoginToken;
import com.ejuetc.consumer.web.vo.ImplicitCallPO;
import com.fangyou.service.hiddencall.client.HiddenCallClient;
import com.fangyou.service.hiddencall.model.CallBindByConsumerPO;
import com.fangyou.service.hiddencall.model.CallBindDTO;
import com.fangyou.service.hiddencall.model.request.CallBindRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.UUID;

import static com.ejuetc.commons.base.spring.SpringUtil.getAPI;
import static java.time.LocalDateTime.now;


//@RunWith(SpringRunner.class)
//@SpringBootTest
public class ImplicitTest {

//    @Autowired
//    private ImplicitApiImpl implicitAPI;
//    private ImplicitAPI implicitAPI = getAPI(ImplicitAPI.class, "http://ejuetc-consumer.tst.ejucloud.cn");
    private ImplicitAPI implicitAPI = getAPI(ImplicitAPI.class, "http://ejuetc-consumer.uat.ejucloud.cn");
//    private ImplicitAPI implicitAPI = getAPI(ImplicitAPI.class, "http://localhost:8097");

    //        @Autowired
    private ImplicitWebImpl implicitWeb;
//    private ImplicitWeb implicitWeb = getAPI(ImplicitWeb.class, "http://localhost:8097");

    //    @Autowired
    private OssComponent ossComponent;

    @Test
    public void testBind() {
        LoginToken loginToken = new LoginToken(2L);
        ApiResponse<String> response = implicitWeb.bind(
                loginToken,
                "1a5d88dee00f47fda7dc17e69177cc6a",//二手
//                "c65126def5954a6eb641e2cdcc7d5e99",//租赁
                "18616583894"
        );
        System.out.println(JSON.toJSONString(response, true));
    }

    @Test
    public void receiveCall() {
        ApiResponse<?> response = implicitAPI.receiveCall(new ImplicitCallPO()
                .setCallId(System.currentTimeMillis() + "")
                .setSubId("a44c82d5-a9f6-4ddb-bc8e-40001a05b2dc")
                .setConsumerPhone("18502196708")
                .setBrokerPhone("18922222226")
                .setSecretPhone("13123456789")
                .setCityName("上海")
                .setStarter(ImplicitCallDTO.Starter.CONSUMER)
                .setCloser(ImplicitCallDTO.Closer.CONSUMER)

                .setControlType(ImplicitCallDTO.ControlType.CONTINUE)
                .setControlMsg("接通")
                .setCallTime(now().minusSeconds(100))
                .setRingTime(now().minusSeconds(90))
                .setStartTime(now().minusSeconds(80))
                .setReleaseTime(now().minusSeconds(10))
                .setRecordUrl("https://secret-axb-record-files.oss-cn-shanghai.aliyuncs.com/1000221435481266_41674ec2b9564d51_0.mp3?Expires=1735806926&OSSAccessKeyId=LTAI4G1kg5pSvJMv2rKNc7Pz&Signature=BfAQUMBD8pKt6%2BCX2woco%2BjKWu0%3D")
        );
        System.out.println(JSON.toJSONString(response, true));
    }

    @Test
    public void testConvertRecordUrl() {
        implicitAPI.convertRecordUrl();
    }

    @Test
    public void testUnbind() {
        implicitAPI.unbind(912354L);
    }

    @Test
    public void testOss() {
        String ossUrl = ossComponent.urlConvert("https://secret-axb-record-files.oss-cn-shanghai.aliyuncs.com/1000221435481266_41674ec2b9564d51_0.mp3?Expires=1735806926&OSSAccessKeyId=LTAI4G1kg5pSvJMv2rKNc7Pz&Signature=BfAQUMBD8pKt6%2BCX2woco%2BjKWu0%3D", "call_record/202412/1.mp3");
        System.out.println(ossUrl);
    }

    public static void main(String[] args) {
        HiddenCallClient callClient = getAPI(HiddenCallClient.class, "https://pre-om-api.fangyou.com");
        ApiResponse<CallBindDTO> bind = callClient.editBindByConsumer(new CallBindByConsumerPO()
                .setUserPhone("18616583894")
                .setBrokerPhone("18916694185")
                .setExpireTime(now().plusDays(1))
        );
        System.out.println(JSON.toJSONString(bind, true));
        if (!bind.isSucc()) return;

        ApiResponse<?> unbind = callClient.unbindByConsumer(new CallBindByConsumerPO().setBindId(bind.getData().getId()));
        System.out.println(JSON.toJSONString(unbind, true));
    }

}