package com.ejuetc.consumer.impl;

import com.alibaba.fastjson.JSON;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.community.*;
import com.ejuetc.consumer.web.vo.BeikeCommunityVO;
import com.ejuetc.consumer.web.vo.CommunityLayoutVO;
import com.ejuetc.consumer.web.vo.CommunityTipsVO;
import com.ejuetc.consumer.web.vo.CommunityVO;
import com.ejuetc.saasapi.sdk.SaaSApiSDK;
import lombok.SneakyThrows;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.ejuetc.commons.base.spring.SpringUtil.getAPI;
import static java.lang.Thread.sleep;


//@RunWith(SpringRunner.class)
//@SpringBootTest
public class CommunityTest {


//    @Autowired
    private CommunityWebImpl communityWeb;
    //    private CommunityWeb communityWeb = getAPI(CommunityWeb.class, "http://ejuetc-consumer.tst.ejucloud.cn/consumer");
    SaaSApiSDK sdk = new SaaSApiSDK(
            "e200a3b268394d6db4ac9b97f2ea5bc9",
            "mY24t2ARVPx3l11+Sjw/WNNCxiHX5zB64aWE7IoRIbk=",
            "http://localhost:8095"
//                "http://saasapi-test.ebaas.com/gateway/invoke"
    );

//        @Autowired
//    private CommunityApiImpl communityAPI;
//    private CommunityAPI communityAPI = sdk.feignClient(CommunityAPI.class, "http://localhost:8097/consumer");
//    private CommunityAPI communityAPI = sdk.feignClient(CommunityAPI.class, "http://ejuetc-consumer.tst.ejucloud.cn/consumer");
//    private CommunityAPI communityAPI = getAPI(CommunityAPI.class, "http://ejuetc-consumer.tst.ejucloud.cn/consumer");
//    private CommunityAPI communityAPI = getAPI(CommunityAPI.class, "http://localhost:8097/consumer");
    //    private CommunityAPI communityAPI = getAPI(CommunityAPI.class, "http://ejuetc-consumer.uat.ejucloud.cn");
    private CommunityAPI communityAPI = getAPI(CommunityAPI.class, "http://ejuetc-consumer.ejucloud.cn");

    //    @Autowired
    private CommunityWebImpl communityWebImpl;

    //    @Autowired
//    private CommunityApiImpl communityImpl;
    private CommunityAPI communityImpl = getAPI(CommunityAPI.class, "http://ejuetc-consumer.uat.ejucloud.cn/consumer");
//    private CommunityAPI communityImpl = getAPI(CommunityAPI.class, "http://ejuetc-consumer.ejucloud.cn/consumer");
//    private CommunityAPI communityImpl = getAPI(CommunityAPI.class, "http://localhost:8097");

    @Test
    public void bindCommunityBeike() throws InterruptedException {
        List<String> cities = List.of(
                "上海",
                "北京",
                "成都",
                "重庆",
                "广州",
                "武汉",
                "深圳",
                "天津",
                "杭州",
                "郑州",
                "苏州",
                "西安",
                "南京",
                "佛山",
                "青岛",
                "济南",
                "昆明",
                "长沙",
                "宁波",
                "南昌"
        );
        ApiResponse<?> response = communityImpl.bindCommunityBeike(cities, 100);
        System.out.println(response);
//        sleep(5 * 60 * 1000);
    }


//    @Test
//    public void testList() {
//        ApiResponse<List<CommunityVO>> communities =
//                communityImpl.list(new LoginToken(), 310000L, "慧", 5, 1);
//        System.out.println(JSON.toJSONString(communities));
//    }

    @Test
    public void testBindCommunityDetail() throws InterruptedException {
        List<String> cityCodes = List.of(
                "3101",
                "1101",
                "5101",
                "4401",
                "3302",
                "3201",
                "4406",
                "4201",
                "3701",
                "5301",
                "3205",
                "4403",
                "6101",
                "1201",
                "3702",
                "4301",
                "3601",
                "4101",
                "5001",
                "3301"
        );
//        List<String> cityCodes = List.of(
//                "3101"
//        );
        ApiResponse<?> response = communityImpl.bindCommunityDetail(cityCodes, 10000, 10);
        System.out.println(response);
//        sleep(3 * 60 * 1000);
    }

    @SneakyThrows
    @Test
    public void testRebind() {
        ApiResponse<?> response = communityAPI.rebind(1000, 100);
        System.out.println(response);
//        sleep(3 * 60 * 1000);
    }

    @SneakyThrows
    @Test
    public void testUpdateOverlapRate() {
        ApiResponse<?> response = communityAPI.updateOverlapRate(2000);
        System.out.println(response);
//        sleep(3 * 60 * 1000);
    }

    @Test
    public void testSearch() {
        ApiResponse<List<CommunityTipsVO>> response = communityWeb.queryByTips(new SaasLoginToken(), "海口", "金色假日", 1, 10);
        System.out.println(JSON.toJSONString(response, true));
    }

    @Test
    public void testSearchPictures() {
        ApiResponse<Map<String, List<String>>> response = communityWeb.queryCommunityPictures(new SaasLoginToken(), "上海静安", "慧芝湖");
        System.out.println(JSON.toJSONString(response, true));
    }

    @Test
    public void testSearchLayout() {
        ApiResponse<List<CommunityLayoutVO>> response = communityWeb.queryCommunityLayouts(new SaasLoginToken(), "上海静安", "慧芝湖");
        System.out.println(JSON.toJSONString(response, true));
    }

    @Test
    public void testWebQueryHasDetailByName() {
        ApiResponse<List<BeikeCommunityVO>> response = communityWeb.queryBeikeCommunity(
                new SaasLoginToken(),
                new QueryCommunityPO(310100L, "慧芝湖", 5, 1)
        );
        System.out.println("CommunityWeb queryHasDetailByName: " + JSON.toJSONString(response, true));
    }

    @Test
    public void testWebQueryLayoutGroup() {
        ApiResponse<Map<Integer, Set<String>>> response = communityWeb.queryLayoutGroup(
                new SaasLoginToken(),
                new QueryLayoutGroupPO().setBeikeId(5020069179760703L)
        );
        System.out.println("CommunityWeb queryLayoutGroup: " + JSON.toJSONString(response, true));
    }
}