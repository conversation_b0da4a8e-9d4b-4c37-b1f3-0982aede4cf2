package com.ejuetc.consumer.impl;

import com.ejuetc.commons.base.querydomain.impl.QueryDomainApiImpl;
import com.ejuetc.consumer.ConsumerApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ConsumerApplication.class})
public class QueryDomainTest {

        @Autowired
    private QueryDomainApiImpl queryDomain;
//    private QueryDomainAPI queryDomain = getAPI(QueryDomainAPI.class, "http://ejuetc-agent.release.ejucloud.cn");
//    private QueryDomainAPI queryDomain = getAPI(QueryDomainAPI.class, "http://ejuetc-agent.tst.ejucloud.cn");
//    private QueryDomainAPI queryDomain = getAPI(QueryDomainAPI.class, "http://localhost:8097");

    @Test //查询下游经纪人或代理机构
    public void queryDownstreamBrokers() {

    }

}
