package com.ejuetc.consumer.impl;

import com.alibaba.fastjson.JSON;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.dto.ConsumerShareDTO;
import com.ejuetc.consumer.api.dto.LoginAccountDTO;
import com.ejuetc.consumer.integration.alipay.AliPayService;
import com.ejuetc.consumer.integration.alipay.AlipayConfig;
import com.ejuetc.consumer.integration.alipay.AlipayUtil;
import com.ejuetc.consumer.integration.wechat.WechatService;
import com.ejuetc.consumer.web.consumer.LoginToken;
import com.ejuetc.consumer.web.consumer.LoginPO;
import com.ejuetc.consumer.web.vo.*;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.UUID;

import org.junit.runner.RunWith;

import static com.alibaba.fastjson.JSON.toJSONString;
import static com.ejuetc.consumer.api.dto.LoginAccountDTO.Type.ALIPAY;
import static com.ejuetc.consumer.api.dto.LoginAccountDTO.Type.WECHAT;
import static java.time.LocalDateTime.now;
import static org.mockito.Mockito.*;


@SuppressWarnings("ALL")
@RunWith(SpringRunner.class)
@SpringBootTest
public class ConsumerTest {

    @MockBean
    private AliPayService aliPayService;

    @MockBean
    private WechatService wechatService;

    @Autowired
    private ConsumerApiImpl consumerApi;
//    private ConsumerApi consumerApi = getAPI(ConsumerApi.class, "http://ejuetc-consumer.tst.ejucloud.cn");

    //    @Autowired
    private AlipayConfig alipayConfig;

    @Test
    public void testAlipayLogin() {
        String token = UUID.randomUUID().toString();
        when(aliPayService.getOpenId(token)).thenReturn(token);

        LoginPO po = new LoginPO()
                .setType(ALIPAY)
                .setLoginIdent(token)
                .setShareCode("6fd7e775-2e63-44e3-879a-83b7bca41e51");
        ApiResponse<LoginToken> login = consumerApi.login(po);
        System.out.println(toJSONString(login.getData()));
    }

    @Test
    public void testWechatLogin() {
        String token = UUID.randomUUID().toString();
        when(wechatService.getOpenId(token)).thenReturn(token);

        LoginPO po = new LoginPO()
                .setType(WECHAT)
                .setLoginIdent(token)
                .setShareCode("6fd7e775-2e63-44e3-879a-83b7bca41e51");
        ApiResponse<LoginToken> login = consumerApi.login(po);
        System.out.println(toJSONString(login.getData()));
    }

    @Test
    public void testBindPhone() {
        String encrypt = AlipayUtil.encrypt(alipayConfig.getAesKey(), """
                {"code":"10000","msg":"Success","mobile":"***********"}
                """);
        when(aliPayService.getPhone(encrypt)).thenReturn("***********");

        LoginToken loginToken = JSON.parseObject("""
                {"accountIdent":"806a8349-6ef3-453a-ac27-936776c9f172","loginAccountId":252,"loginHistoryId":402,"token":"consumer_1e73d6b34877426396cc3612edd93978","type":"ALIPAY"}
                """, LoginToken.class);
        ApiResponse<?> apiResponse = consumerApi.bindPhoneByAuth(loginToken, encrypt);
        System.out.println(apiResponse);
    }

    @Test
    public void location() {
        consumerApi.location(new LoginToken().setLoginHistoryId(100402L), "120.107822,30.267023");
    }

    @Test
    public void changeShareCode() {
        consumerApi.changeShareCode(
                new LoginToken()
                        .setLoginHistoryId(271L)
                        .setLoginAccountId(50002L)
                        .setChannelCode(ChannelDTO.Code.PRIVATE)
                , "120.107822,30.267023");
    }


    @Test
    public void shareByConsumer() {
        consumerApi.shareByConsumer(
                new LoginToken().setLoginHistoryId(100003L),
                UUID.randomUUID().toString(),
                ConsumerShareDTO.TargetType.DELEGATION,
                112L,
                9094662523348608768L
        );
    }


    @Test
    public void testRelationList() {
        ApiResponse<List<RelationVO>> relation = consumerApi.relationList(
                new SaasLoginToken()
                        .setToken(UUID.randomUUID().toString())
                        .setUserId(9094662523348608768L),
                ALIPAY,
                null,
                true,
                10, 1
        );
        System.out.println(toJSONString(relation, true));
    }

    @Test
    public void relationEdit() {
        RelationPO ro = new RelationPO()
                .setSourceId(UUID.randomUUID().toString())
                .setSourceType("SaaS")
                .setId(152L)
                .setName("林冬成")
                .setMister(true)
                .setIconUrl("https://oss-consumer.ebaas.com/community_picture/100007/u%3D3598104138%2C3632108415%26fm%3D253%26fmt%3Dauto%26app%3D120%26f%3DJPEG.jpeg")
                .setRemarkPhones(List.of("***********", "***********", "***********"))
                .setRemark("备注123")
                .setAccountType(WECHAT)
                .setActivityTime(now().minusDays(3))
                .setActivityRemark("手工录入");
        System.out.println(toJSONString(ro, true));
        ApiResponse<RelationVO> relation = consumerApi.relationEdit(
//                new SaasLoginToken()
//                        .setToken(UUID.randomUUID().toString())
//                        .setUserId(1741958112698L),
                240110135381364480L,
                ro
        );
        System.out.println(toJSONString(relation, true));
    }

    @Test
    public void testRelationDetail() {
        ApiResponse<RelationVO> relation = consumerApi.relationDetail(
                new SaasLoginToken()
                        .setToken(UUID.randomUUID().toString())
                        .setUserId(1741958112698L),
                99952L
        );
        System.out.println(toJSONString(relation, true));
    }

    @Test
    public void shareStatistics() {
        ApiResponse<ShareStatisticsVO> relation = consumerApi.shareStatistics(
                new SaasLoginToken()
                        .setToken(UUID.randomUUID().toString())
                        .setUserId(9094957789532445443L)
                        .setMerchantId(9094559399707452416L)
        );
        System.out.println(toJSONString(relation, true));
    }

    @Test
    public void relationViewDelegations() {
        ApiResponse<List<TrackCountVO>> relation = consumerApi.relationViewDelegations(
                new SaasLoginToken()
                        .setToken(UUID.randomUUID().toString())
                        .setUserId(1741958112698L),
                99952L,
                10, 1
        );
        System.out.println(toJSONString(relation, true));
    }
}