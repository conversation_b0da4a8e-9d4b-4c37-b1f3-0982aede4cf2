package com.ejuetc.consumer.impl;

import com.ejuetc.saasapi.sdk.SaaSApiSDK;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.UUID;

import static java.lang.Thread.sleep;
import static java.time.LocalDateTime.now;
import static java.util.UUID.randomUUID;

@Slf4j
@SpringBootTest
public class DelegationGatewayTest {
    //生成环境戴阳商户秘钥
    SaaSApiSDK sdk = new SaaSApiSDK(
            "314f985724474b0a9d9d1089e7da85c5",
            "8B4VVNal4tJK7o0k8qA501WAVJkju9lnEjgl28eQDtA=",
            "http://saasapi.ebaas.com/gateway/invoke"
    );
    //测试环境U租商户秘钥
//    SaaSApiSDK sdk = new SaaSApiSDK(
//            "c90982b1474641c5b9ad10724f4f8cd4",
//            "gcuTerMoadFj8nQkFP7U3i84BZu5shoDBO0xkxB3ehI=",
//            "http://saasapi-test.ebaas.com/gateway/invoke"
////            "http://localhost:8095/gateway/invoke"
//    );

    //测试环境测试商户秘钥
//        SaaSApiSDK sdk = new SaaSApiSDK(
//                "8f0372d5cb11442bb2952f86703dbea9",
//                "TN1WU0ggSqBBUPp7ZlhO6rVH8MoUQW/WFCp7LUyp0Gs=",
//                "https://saasapi-test.ebaas.com/gateway/invoke"
////                "http://localhost:8095/gateway/invoke"
//        );

    @Test
    public void edit() {
        String sourceId = UUID.randomUUID().toString();
        String json = BROKER_DELEGATION_JSON.formatted("9090203767480038912", sourceId);
        ResponseEntity<String> resp = sdk.businessRequest(
                "editDelegation",
                sourceId,
                json
        );
        System.out.println(resp);
    }

    @Test
    public void delete() {
        ResponseEntity<String> resp = sdk.businessRequest(
                "deleteBrokerDelegates",
                randomUUID().toString(),
                """
                            {
                                "brokerId":9090203767480038912,
                                "reason":"测试"
                            }
                        """
        );
        System.out.println(resp);
    }


    public static final String BROKER_DELEGATION_JSON = """
                {
                    "brokerId":%s,
                    "sourceId":"%s",
                    "buildingArea":222,
                    "type":"RENT",
                    "useArea":222,
                    "roomNum":"010A",
                    "freshCompanyDelegation":false,
                    "communityAddress":"上海",
                    "communityName":"花园",
                    "roomCount":1,
                    "unitName":"无单元",
                    "orient":"朝东",
                    "level":"BROKER",
                    "medias":[
                        {
                            "subtype":"实景图",
                            "type":"IMAGE",
                            "url":"https://fyoss-test.fangyou.com/250423163ec6487e0a3477bfb5f3209f010587e8b9c1e29e.jpg"
                        }
                    ],
                    "sourceType":"SAAS",
                	"completionTime":"2024",
                    "toiletCount":0,
                    "totalFloor":8,
                    "hallCount":0,
                    "subType":"RENT_FULL",
                    "currentFloor":1,
                    "description":"52525252525",
                    "redo":"精装",
                    "title":"别墅测试啊 1室 3000.00",
                    "around":[
                        "餐饮",
                        "公交",
                        "超市",
                        "菜场"
                    ],
                    "propertyType":"别墅",
                    "priceTotal":3000,
                    "channelUp":true,
                    "buildName":"1幢",
                    "equipments":[
                        "衣柜",
                        "热水器",
                        "燃气",
                        "冰箱",
                        "空调"
                    ],
                    "payMonths":2,
                    "depositMonths":1,
                    "channelCodes":[
                        "XIANYU","ALIPAY"
                    ]
                }
            """;

}