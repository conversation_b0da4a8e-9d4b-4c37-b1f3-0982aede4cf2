package com.ejuetc.consumer.impl;

import com.alibaba.fastjson.JSON;
import com.ejuetc.commons.base.querydomain.impl.QueryDomainApiImpl;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.dto.RegionDTO;
import com.ejuetc.consumer.web.region.RegionWeb;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static com.ejuetc.consumer.api.dto.RegionDTO.Type.*;

@SpringBootTest
public class RegionTest {

    //    @Autowired
    private QueryDomainApiImpl queryDomainApi;

    @Autowired
    private RegionApiImpl regionAPI;

    @Autowired
    private RegionWebImpl regionWeb;
//    private RegionWeb regionWeb = getAPI(RegionWeb.class, "http://localhost:8097");
//    private CommunityAPI communityImpl = getAPI(CommunityAPI.class, "http://localhost:8097");

    @Test
    public void testRegion() {
        ApiResponse<?> response = regionWeb.businessOpenCities();
        System.out.println(JSON.toJSONString(response, true));
    }

//    @Test
//    public void testInitTown() {
//        List.of(
//                "441900"
//        ).forEach(cityCode -> {
//            try {
//                regionAPI.initTown(cityCode);
//            } catch (Exception e) {
//                System.out.println("error cityCode:" + cityCode);
//                e.printStackTrace();
//            }
//        });
//    }

    @Test
    public void testWebList() {
        // 先测试查询所有城市
        ApiResponse<Object> allCitiesResponse = regionWeb.list(null, null, CITY, RegionDTO.FeatureCode.HOUSE_VALUE_ASSESSMENT, null, null);
        System.out.println(JSON.toJSONString(allCitiesResponse, true));

    }

    @Test
    public void letterGroup() {
        // 先测试查询所有城市
        ApiResponse<Object> allCitiesResponse = regionWeb.list(null, null, CITY, null, "州", RegionWeb.GroupType.LETTER);
        System.out.println(JSON.toJSONString(allCitiesResponse, true));

    }

    @Test
    public void typeGroup() {
        // 先测试查询所有城市
        ApiResponse<Object> allCitiesResponse = regionWeb.list(null, 310100L, null, null, "浦", RegionWeb.GroupType.TYPE);
        System.out.println(JSON.toJSONString(allCitiesResponse, true));

    }
}
