package com.ejuetc.consumer.impl;

import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.broker.EditBrokerPO;
import com.ejuetc.consumer.api.dto.BrokerDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static java.time.LocalDateTime.now;


@RunWith(SpringRunner.class)
@SpringBootTest
public class BrokerTest {

        @Autowired
    private BrokerApiImpl brokerAPI;
//    private BrokerAPI brokerAPI = getAPI(BrokerAPI.class, "http://10.123.159.50:8097");

    @Test
    public void testEditBroker() {
        ApiResponse<BrokerDTO> response = brokerAPI.edit(new EditBrokerPO()
                .setBrokerId(System.currentTimeMillis())
                .setName("张三_" + now())
                .setIdNum("310123456789012345")
                .setStoreName("上海大学店")
                .setCityCode("310100")
                .setCompanyName("上海添玑好房网络服务有限公司")
                .setCompanyLicenseNum("91310000676211967N")
                .setCompanyLicenseUrl("https://gimg2.baidu.com/image_search/src=https%3A%2F%2Fwww.generalwatertech.com%2Fuploadfiles%2F2020%2F01%2F20200109175314541.jpg%3FMS5qcGc%3D&refer=http%3A%2F%2Fwww.generalwatertech.com&app=2002&size=f10000,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=**********&t=0e2cf6f9df768dfc36a32ebbc2d2bccd")
                .setCompanyLegal("聂万海")
                .setPhone("13123456789")
                .setIcon("https://www.baidu.com")
                .setIntroduce("经纪人介绍")
                .setProfessionInformationCardUrl("https://imgwcs3.soufunimg.com/yun/2020_02/17/pic/ca66a828-7fbf-4d67-92af-8577aefb0100.jpg")
                .setProfessionInformationCardNumber("123456789012345678")
                .setCompanyId(System.currentTimeMillis())
        );
        System.out.println(response);
    }
}