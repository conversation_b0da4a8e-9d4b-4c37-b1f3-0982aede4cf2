package com.ejuetc.consumer.impl;

import com.alibaba.fastjson.JSON;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.component.OssComponent;
import com.ejuetc.commons.base.filter.LoginTokenFilter;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.querydomain.impl.QueryDomainApiImpl;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.delegation.MediaPO;
import com.ejuetc.consumer.api.dto.DelegationDTO;
import com.ejuetc.consumer.api.dto.DelegationDictDTO;
import com.ejuetc.consumer.web.consumer.LoginToken;
import com.ejuetc.consumer.web.delegation.QueryListPO2B;
import com.ejuetc.consumer.web.delegation.QueryListPO2C;
import com.ejuetc.consumer.web.vo.DelegationVO;
import lombok.SneakyThrows;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.ejuetc.channel.dto.BusinessOpenDTO.Code.*;
import static com.ejuetc.channel.dto.ChannelDTO.Code.PRIVATE;
import static com.ejuetc.channel.dto.ChannelDTO.Code.XIANYU;
import static com.ejuetc.consumer.api.dto.DelegationDTO.Level.*;
import static com.ejuetc.consumer.api.dto.DelegationDTO.Status.UP_SUCC;
import static com.ejuetc.consumer.api.dto.DelegationDictDTO.Category.*;
import static com.ejuetc.consumer.api.dto.LoginAccountDTO.Type.ALIPAY;
import static java.lang.Thread.sleep;
import static java.time.LocalDateTime.now;

@RunWith(SpringRunner.class)
@SpringBootTest
public class DelegationWebTest {

    @Autowired
    private DelegationWebImpl delegationWeb;

//    private DelegationWeb delegationWeb = getAPI(DelegationWeb.class, "http://localhost:8097");

    //    @Autowired
    private QueryDomainApiImpl queryDomainApi;

    //    @Autowired
    private OssComponent ossComponent;

    private static @NotNull List<MediaPO> getMedias() {
        return List.of(
                new MediaPO()
                        .setUrl("https://fyoss-test.fangyou.com/jpg_2307031720a9fe142488e77bfdd48d9a00dc6d5d4e2ef555?x-oss-process=image/resize,m_mfit,w_1080,h_1080,limit_0/quality,q_85/format,jpg")
                        .setType(MediaPO.Type.IMAGE)
                        .setSubtype("实景图")
                        .setName("厨房")
                ,
                new MediaPO()
                        .setUrl("https://fyoss-test.fangyou.com/jpg_23070317bd4b9554c222de2e69efb29aa8cae879789166ca?x-oss-process=image/resize,m_mfit,w_1080,h_1080,limit_0/quality,q_85/format,jpg")
                        .setType(MediaPO.Type.IMAGE)
                        .setSubtype("户型图")
                        .setName("卫生间")
                ,
                new MediaPO()
                        .setUrl("https://fyoss-test.fangyou.com/jpg_21122717e1772984d9e7ab7bd26344da40db72a70a195a4d?x-oss-process=image/resize,m_mfit,w_1080,h_1080/quality,q_70/format,jpg")
                        .setType(MediaPO.Type.IMAGE)
                        .setSubtype("小区图")
                        .setName("卧室")
        );
    }

    @Test
    public void testQuery() {
        queryDomainApi.query(new DelegationDTO());
    }

    @Test
    public void test() {
        long cityId = 310000L;
        QueryListPO2C request = new QueryListPO2C()
                .setCityId(cityId)
                .setBoutique(true);
        ApiResponse<?> response = delegationWeb.list2c(
                new LoginToken()
                        .setCityId(cityId)
                        .setLocation("上海")
                        .setType(ALIPAY)
                ,
                request,
                10,
                1
        );
        System.out.println(response);
    }

    @SneakyThrows
    @Test
    public void detail2c() {
        List.of(
                "33b85b3fc0a3474ebc4f21419f683f71",
                "03034122cb2b4a8fa0943c4f2838c6ee",
                "c4229f8d6a5e441ebd06b4b876b96d3f",
                "33b85b3fc0a3474ebc4f21419f683f71",
                "03034122cb2b4a8fa0943c4f2838c6ee",
                "c4229f8d6a5e441ebd06b4b876b96d3f",
                "33b85b3fc0a3474ebc4f21419f683f71",
                "03034122cb2b4a8fa0943c4f2838c6ee",
                "c4229f8d6a5e441ebd06b4b876b96d3f",
                "33b85b3fc0a3474ebc4f21419f683f71",
                "03034122cb2b4a8fa0943c4f2838c6ee",
                "c4229f8d6a5e441ebd06b4b876b96d3f",
                "33b85b3fc0a3474ebc4f21419f683f71",
                "03034122cb2b4a8fa0943c4f2838c6ee",
                "c4229f8d6a5e441ebd06b4b876b96d3f",
                "33b85b3fc0a3474ebc4f21419f683f71",
                "03034122cb2b4a8fa0943c4f2838c6ee",
                "c4229f8d6a5e441ebd06b4b876b96d3f"
        ).forEach(code -> {
            Map.of(
                    49952L, 49952L,
                    2L, 2L,
                    1L, 1L
            ).forEach((loginId, accountId) -> {
                ApiResponse<DelegationVO> response = delegationWeb.detail2c(
                        new LoginToken()
                                .setLoginHistoryId(loginId)
                                .setLoginAccountId(accountId)
                        ,
                        code);
                System.out.println(JSON.toJSONString(response));
            });
        });
        sleep(10000);
    }

    @Test
    public void detail2b() {
        ApiResponse<DelegationVO> response = delegationWeb.detail2b(
                new SaasLoginToken()
                        .setUserId(1741958112698L)
//                        .setCompanyId(9095991053772670464L)
//                        .setManagerRole(true),
                ,
                1L
        );
        System.out.println(JSON.toJSONString(response, true));
    }

    @SneakyThrows
    @Test
    public void list() {
        LoginToken login = new LoginToken().setType(ALIPAY);
        QueryListPO2C request = new QueryListPO2C()
                .setType(RENT)
                .setCityId(310000L);
        ApiResponse<List<DelegationVO>> list = delegationWeb.list2c(login, request, 10, 1);
        System.out.println(JSON.toJSONString(list));
    }

    @Test
    public void testList2B() {
        //9095074090737248256,9094662523348608768,SALE,CHANNEL,13
        SaasLoginToken saasToken = new SaasLoginToken()
                .setUserId(9094662523348608768L)
                .setCompanyId(9095074090737248256L)
                .setManagerRole(true);
        LoginTokenFilter.setLoginToken(saasToken);
        ApiResponse<List<DelegationVO>> response = delegationWeb.list2b(
                saasToken
                ,
                new QueryListPO2B()
//                        .setLevel(BROKER)
                        .setLevel(CHANNEL)
                        .setType(SALE)
//                        .setKeyword("123")
//                        .setChannelCode(PRIVATE)
//                        .setChildStatus(UP_SUCC)
//                        .setDistrictId(310101L)
//                        .setBrokerId(1738158521239L)
//                        .setOrient("朝南")
//                        .setFloorCategory("中楼层")
//                        .setRedo("普装")
//                        .setKeyword("老西门新苑")
//                        .setRoomCount(2)
//                        .setHallCount(2)
//                        .setToiletCount(2)
//                        .setMinPrice(new BigDecimal("100.00"))
//                        .setMaxPrice(new BigDecimal("100000000.00"))
//                        .setTownName("老西门街道")
//                        .setSort(QueryListPO2B.Sort.PRICE_DESC)
//                        .setBoutique(false)
//                        .setChildChannelCode(PRIVATE)
//                        .setChildStatus(WITHOUT)
//                        .setChannelCode(PRIVATE)
                ,
                10,
                1
        );
        System.out.println(JSON.toJSONString(response, true));
    }

    @Test
    public void moreHouses() {
        ApiResponse<List<DelegationVO>> listApiResponse = delegationWeb.brokerMoreHouses(new LoginToken(), "d248130bf4db4f009a84b4c4600421e3", 5, 1);
        System.out.println(JSON.toJSONString(listApiResponse));
    }

    @Test
    public void mostViewedDelegations() {
        SaasLoginToken saasLoginToken = new SaasLoginToken().setUserId(9094662523348608768L);
        ApiResponse<List<DelegationVO>> list = delegationWeb.mostViewedDelegations(saasLoginToken, 102L);
        System.out.println(JSON.toJSONString(list));
    }

    @SneakyThrows
    @Test
    public void upDown() {
        SaasLoginToken saasLoginToken = new SaasLoginToken().setUserId(9095985378040515840L);
        ApiResponse<DelegationVO> response = delegationWeb.upDown(saasLoginToken, 201003L, false);
        System.out.println(JSON.toJSONString(response));
//        sleep(60000);
    }

    @Test
    public void batchUp() {
        SaasLoginToken saasLoginToken = new SaasLoginToken().setUserId(9095985378040515840L);
        ApiResponse<DelegationVO> response = delegationWeb.batchUp(
                saasLoginToken,
                List.of(201002L, 254852L),
                List.of(XIANYU, ChannelDTO.Code.ALIPAY, PRIVATE)
        );
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void viewedDelegations() {
        SaasLoginToken saasLoginToken = new SaasLoginToken().setUserId(9094662523348608768L);
        ApiResponse<List<DelegationVO>> list = delegationWeb.viewedDelegations(saasLoginToken, 102L, 10, 1);
        System.out.println(JSON.toJSONString(list));
    }

    @Test
    public void boutique() {
        SaasLoginToken saasLoginToken = new SaasLoginToken().setUserId(9094662523348608768L);
        ApiResponse<DelegationVO> response = delegationWeb.boutique(saasLoginToken, 251364L, false);
        System.out.println(JSON.toJSONString(response));
    }

    @SneakyThrows
    @Test
    public void delete() {
        SaasLoginToken saasLoginToken = new SaasLoginToken().setUserId(9094992955919342336L);
        ApiResponse<DelegationVO> response = delegationWeb.delete(saasLoginToken, 254060L, "手工删除");
        System.out.println(JSON.toJSONString(response));
        sleep(60000 * 3);
    }

    @Test
    public void testList() {
        ApiResponse<Map<DelegationDictDTO.Category, List<String>>> response = delegationWeb.list(List.of(COMPLETION_TIME, ROOM_EQUIPMENTS));
        System.out.println(JSON.toJSONString(response, true));
    }

    @Test
    public void baseFieldRule() {
        ApiResponse<Collection<String>> response = delegationWeb.baseFieldRule(List.of(XIANYU, ChannelDTO.Code.ALIPAY), SALE);
        System.out.println(JSON.toJSONString(response, true));
    }

    @Test
    public void govVerifyFieldRule() {
        String communityAddress = "徐州市铜山区";
        String communityName = "立德小区";
        ApiResponse<Collection<String>> response = delegationWeb.govVerifyFieldRule(communityAddress, communityName, SALE);
        System.out.println(JSON.toJSONString(response, true));
    }

}