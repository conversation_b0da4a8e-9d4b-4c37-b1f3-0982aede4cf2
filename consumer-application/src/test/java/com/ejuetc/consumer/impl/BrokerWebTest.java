package com.ejuetc.consumer.impl;

import com.alibaba.fastjson.JSON;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.broker.EditBrokerPO;
import com.ejuetc.consumer.api.dto.BrokerDTO;
import com.ejuetc.consumer.web.broker.CompanyConfigVO;
import com.ejuetc.consumer.web.vo.BrokerVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static java.time.LocalDateTime.now;


@RunWith(SpringRunner.class)
@SpringBootTest
public class BrokerWebTest {

    @Autowired
    private BrokerWebImpl brokerAPI;
//    private BrokerAPI brokerAPI = getAPI(BrokerAPI.class, "http://10.123.159.50:8097");

    @Test
    public void brokerList4Company() {
        SaasLoginToken token = new SaasLoginToken().setUserId(9091787685781110272L).setManagerRole(true);
        System.out.println(token);
        ApiResponse<List<BrokerVO>> response = brokerAPI.brokerList4Company(token);
        System.out.println(JSON.toJSONString(response, true));
    }
}