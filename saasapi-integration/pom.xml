<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ejuetc.saasapi</groupId>
        <artifactId>saasapi</artifactId>
        <version>0.0.3</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>saasapi-integration</artifactId>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ejuetc.saasapi</groupId>
            <artifactId>saasapi-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.taobao.api</groupId>
            <artifactId>taobao-sdk-java-auto</artifactId>
            <version>20230825</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ejuetc.commons</groupId>
            <artifactId>commons-base</artifactId>
        </dependency>
    </dependencies>

</project>