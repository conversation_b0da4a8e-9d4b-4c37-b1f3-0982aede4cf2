package com.dcai.aixg.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.deepseek.DeepSeekChatModel;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * AI生成配置类
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class AiGenerateConfig {

    /**
     * 配置ChatClient用于AI生成
     */
    @Bean
    public ChatClient generateChatClient(DeepSeekChatModel chatModel) {
        log.info("🔧 配置AI生成ChatClient...");
        
        ChatClient client = ChatClient.builder(chatModel)
                .build();
        
        log.info("✅ AI生成ChatClient配置完成");
        return client;
    }
}
