package com.dcai.aixg.converter;

import com.dcai.aixg.domain.generate.*;
import com.dcai.aixg.dto.generate.GenerateConfigDTO;
import com.dcai.aixg.dto.generate.GenerateDTO;
import com.dcai.aixg.dto.generate.GenerateStepDTO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 生成相关DTO转换器
 * 
 * <AUTHOR>
 */
@Component
public class GenerateConverter {

    /**
     * 转换GenerateConfig到DTO
     */
    public GenerateConfigDTO toConfigDTO(GenerateConfig config) {
        if (config == null) {
            return null;
        }
        
        return GenerateConfigDTO.builder()
                .id(config.getId())
                .code(config.getCode())
                .title(config.getTitle())
                .description(config.getDescription())
                .demo(config.getDemo())
                .systemTemplate(config.getSystemTemplate())
                .userTemplate(config.getUserTemplate())
                .taskType(config.getTaskType())
                .taskTypeDescription(config.getTaskType() != null ? config.getTaskType().getDescription() : null)
                .enabled(config.getEnabled())
                .sortOrder(config.getSortOrder())
                .createTime(config.getCreateTime())
                .updateTime(config.getUpdateTime())
                .build();
    }

    /**
     * 转换Generate到DTO
     */
    public GenerateDTO toGenerateDTO(Generate generate) {
        if (generate == null) {
            return null;
        }
        
        GenerateDTO.GenerateDTOBuilder builder = GenerateDTO.builder()
                .id(generate.getId())
                .config(toConfigDTO(generate.getConfig()))
                .request(generate.getRequest())
                .systemPrompt(generate.getSystemPrompt())
                .userPrompt(generate.getUserPrompt())
                .response(generate.getResponse())
                .duration(generate.getDuration())
                .status(generate.getStatus())
                .statusDescription(generate.getStatus() != null ? generate.getStatus().getDescription() : null)
                .memo(generate.getMemo())
                .errorMessage(generate.getErrorMessage())
                .startedAt(generate.getStartedAt())
                .completedAt(generate.getCompletedAt())
                .createTime(generate.getCreateTime())
                .updateTime(generate.getUpdateTime());
        
        // 转换步骤
        if (generate.getSteps() != null && !generate.getSteps().isEmpty()) {
            List<GenerateStepDTO> stepDTOs = generate.getSteps().stream()
                    .map(this::toStepDTO)
                    .collect(Collectors.toList());
            builder.steps(stepDTOs);
        }
        
        // 计算进度信息
        GenerateDTO.ProgressInfo progressInfo = calculateProgressInfo(generate);
        builder.progressInfo(progressInfo);
        
        return builder.build();
    }

    /**
     * 转换GenerateStep到DTO
     */
    public GenerateStepDTO toStepDTO(GenerateStep step) {
        if (step == null) {
            return null;
        }
        
        GenerateStepDTO.GenerateStepDTOBuilder builder = GenerateStepDTO.builder()
                .id(step.getId())
                .generateId(step.getGenerate() != null ? step.getGenerate().getId() : null)
                .title(step.getTitle())
                .request(step.getRequest())
                .response(step.getResponse())
                .duration(step.getDuration())
                .status(step.getStatus())
                .statusDescription(step.getStatus() != null ? step.getStatus().getDescription() : null)
                .memo(step.getMemo())
                .errorMessage(step.getErrorMessage())
                .startedAt(step.getStartedAt())
                .completedAt(step.getCompletedAt())
                .stepOrder(step.getStepOrder())
                .createTime(step.getCreateTime());
        
        // 判断步骤类型
        if (step instanceof FunctionCalling) {
            FunctionCalling functionCalling = (FunctionCalling) step;
            builder.stepType("FUNCTION_CALLING");
            
            // 转换函数调用信息
            GenerateStepDTO.FunctionCallingInfo functionInfo = GenerateStepDTO.FunctionCallingInfo.builder()
                    .functionName(functionCalling.getFunctionName())
                    .functionArgs(functionCalling.getFunctionArgs())
                    .functionResult(functionCalling.getFunctionResult())
                    .functionStatus(functionCalling.getFunctionStatus())
                    .functionStatusDescription(functionCalling.getFunctionStatus() != null ? 
                            functionCalling.getFunctionStatus().getDescription() : null)
                    .retryCount(functionCalling.getRetryCount())
                    .maxRetry(functionCalling.getMaxRetry())
                    .canRetry(functionCalling.canRetry())
                    .build();
            builder.functionCallingInfo(functionInfo);
        } else {
            builder.stepType("DEFAULT");
        }
        
        return builder.build();
    }

    /**
     * 计算进度信息
     */
    private GenerateDTO.ProgressInfo calculateProgressInfo(Generate generate) {
        if (generate.getSteps() == null || generate.getSteps().isEmpty()) {
            return GenerateDTO.ProgressInfo.builder()
                    .totalSteps(0)
                    .completedSteps(0)
                    .progressPercentage(0.0)
                    .build();
        }
        
        int totalSteps = generate.getSteps().size();
        int completedSteps = (int) generate.getSteps().stream()
                .mapToLong(step -> step.isCompleted() ? 1 : 0)
                .sum();
        
        double progressPercentage = totalSteps > 0 ? (double) completedSteps / totalSteps * 100 : 0.0;
        
        // 获取当前步骤
        String currentStep = null;
        for (GenerateStep step : generate.getSteps()) {
            if (!step.isCompleted()) {
                currentStep = step.getTitle();
                break;
            }
        }
        
        return GenerateDTO.ProgressInfo.builder()
                .totalSteps(totalSteps)
                .completedSteps(completedSteps)
                .currentStep(currentStep)
                .progressPercentage(progressPercentage)
                .build();
    }

    /**
     * 转换配置列表到DTO列表
     */
    public List<GenerateConfigDTO> toConfigDTOList(List<GenerateConfig> configs) {
        if (configs == null) {
            return null;
        }
        return configs.stream()
                .map(this::toConfigDTO)
                .collect(Collectors.toList());
    }

    /**
     * 转换生成任务列表到DTO列表
     */
    public List<GenerateDTO> toGenerateDTOList(List<Generate> generates) {
        if (generates == null) {
            return null;
        }
        return generates.stream()
                .map(this::toGenerateDTO)
                .collect(Collectors.toList());
    }
}
