package com.dcai.aixg.impl;

import com.dcai.aixg.api.GenerateAPI;
import com.dcai.aixg.converter.GenerateConverter;
import com.dcai.aixg.domain.generate.Generate;
import com.dcai.aixg.domain.generate.GenerateConfig;
import com.dcai.aixg.domain.generate.GenerateRpt;
import com.dcai.aixg.dto.generate.GenerateConfigDTO;
import com.dcai.aixg.dto.generate.GenerateDTO;
import com.dcai.aixg.pro.generate.*;
import com.dcai.aixg.service.GenerateConfigService;
import com.dcai.aixg.service.GenerateService;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 生成API实现
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class GenerateImpl implements GenerateAPI {

    private final GenerateConfigService generateConfigService;
    private final GenerateService generateService;
    private final GenerateConverter generateConverter;
    private final GenerateRpt generateRpt;

    // ==================== 配置管理 ====================

    @Override
    public ApiResponse<GenerateConfigDTO> createConfig(SaasLoginToken saasLoginToken, CreateGenerateConfigPO po) {
        log.info("创建生成配置，用户: {}, 代码: {}", saasLoginToken.getUserId(), po.getCode());
        
        GenerateConfig config = generateConfigService.createConfig(
                po.getCode(), po.getTitle(), po.getDescription(), po.getDemo(),
                po.getSystemTemplate(), po.getUserTemplate(), po.getTaskType(),
                po.getEnabled(), po.getSortOrder());
        
        GenerateConfigDTO dto = generateConverter.toConfigDTO(config);
        return ApiResponse.success(dto);
    }

    @Override
    public ApiResponse<GenerateConfigDTO> updateConfig(SaasLoginToken saasLoginToken, UpdateGenerateConfigPO po) {
        log.info("更新生成配置，用户: {}, ID: {}", saasLoginToken.getUserId(), po.getId());
        
        GenerateConfig config = generateConfigService.updateConfig(
                po.getId(), po.getTitle(), po.getDescription(), po.getDemo(),
                po.getSystemTemplate(), po.getUserTemplate(), po.getTaskType(),
                po.getEnabled(), po.getSortOrder());
        
        GenerateConfigDTO dto = generateConverter.toConfigDTO(config);
        return ApiResponse.success(dto);
    }

    @Override
    public ApiResponse<Void> deleteConfig(SaasLoginToken saasLoginToken, Long id) {
        log.info("删除生成配置，用户: {}, ID: {}", saasLoginToken.getUserId(), id);
        
        generateConfigService.deleteConfig(id);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<GenerateConfigDTO> getConfig(SaasLoginToken saasLoginToken, Long id) {
        GenerateConfig config = generateConfigService.getConfig(id)
                .orElseThrow(() -> new RuntimeException("配置不存在: " + id));
        
        GenerateConfigDTO dto = generateConverter.toConfigDTO(config);
        return ApiResponse.success(dto);
    }

    @Override
    public ApiResponse<GenerateConfigDTO> getConfigByCode(SaasLoginToken saasLoginToken, String code) {
        GenerateConfig config = generateConfigService.getConfigByCode(code)
                .orElseThrow(() -> new RuntimeException("配置不存在: " + code));
        
        GenerateConfigDTO dto = generateConverter.toConfigDTO(config);
        return ApiResponse.success(dto);
    }

    @Override
    public ApiResponse<List<GenerateConfigDTO>> getEnabledConfigs(SaasLoginToken saasLoginToken) {
        List<GenerateConfig> configs = generateConfigService.getEnabledConfigs();
        List<GenerateConfigDTO> dtos = generateConverter.toConfigDTOList(configs);
        return ApiResponse.success(dtos);
    }

    @Override
    public ApiResponse<List<GenerateConfigDTO>> getConfigsByTaskType(SaasLoginToken saasLoginToken, String taskType) {
        GenerateConfig.TaskType type = GenerateConfig.TaskType.valueOf(taskType.toUpperCase());
        List<GenerateConfig> configs = generateConfigService.getConfigsByTaskType(type);
        List<GenerateConfigDTO> dtos = generateConverter.toConfigDTOList(configs);
        return ApiResponse.success(dtos);
    }

    @Override
    public ApiResponse<Void> toggleConfig(SaasLoginToken saasLoginToken, Long id) {
        log.info("切换配置状态，用户: {}, ID: {}", saasLoginToken.getUserId(), id);
        
        generateConfigService.toggleConfig(id);
        return ApiResponse.success();
    }

    // ==================== 任务管理 ====================

    @Override
    public ApiResponse<GenerateDTO> createGenerate(SaasLoginToken saasLoginToken, CreateGeneratePO po) {
        log.info("创建生成任务，用户: {}, 配置ID: {}", saasLoginToken.getUserId(), po.getConfigId());
        
        Generate generate = generateService.createGenerate(po.getConfigId(), po.getRequest(), po.getMemo());
        
        // 异步执行生成任务
        executeGenerateAsync(generate.getId());
        
        GenerateDTO dto = generateConverter.toGenerateDTO(generate);
        return ApiResponse.success(dto);
    }

    @Override
    public ApiResponse<GenerateDTO> createGenerateByCode(SaasLoginToken saasLoginToken, CreateGenerateByCodePO po) {
        log.info("根据配置代码创建生成任务，用户: {}, 代码: {}", saasLoginToken.getUserId(), po.getConfigCode());
        
        Generate generate = generateService.createGenerateByCode(po.getConfigCode(), po.getRequest(), po.getMemo());
        
        // 异步执行生成任务
        executeGenerateAsync(generate.getId());
        
        GenerateDTO dto = generateConverter.toGenerateDTO(generate);
        return ApiResponse.success(dto);
    }

    @Override
    public ApiResponse<GenerateDTO> getGenerate(SaasLoginToken saasLoginToken, Long id) {
        Generate generate = generateService.getGenerate(id)
                .orElseThrow(() -> new RuntimeException("生成任务不存在: " + id));
        
        GenerateDTO dto = generateConverter.toGenerateDTO(generate);
        return ApiResponse.success(dto);
    }

    @Override
    public ApiResponse<Page<GenerateDTO>> queryGenerates(SaasLoginToken saasLoginToken, QueryGeneratePO po) {
        // 构建分页参数
        Sort sort = Sort.by(Sort.Direction.fromString(po.getSortDirection()), po.getSortBy());
        Pageable pageable = PageRequest.of(po.getPage() - 1, po.getSize(), sort);
        
        // 这里简化处理，实际应该根据查询条件构建复杂查询
        Page<Generate> generatePage = generateRpt.findByOrderByCreateTimeDesc(pageable);
        
        Page<GenerateDTO> dtoPage = generatePage.map(generateConverter::toGenerateDTO);
        return ApiResponse.success(dtoPage);
    }

    @Override
    public ApiResponse<Void> cancelGenerate(SaasLoginToken saasLoginToken, Long id) {
        log.info("取消生成任务，用户: {}, ID: {}", saasLoginToken.getUserId(), id);
        
        generateService.cancelGenerate(id);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<GenerateDTO> retryGenerate(SaasLoginToken saasLoginToken, Long id) {
        log.info("重新执行生成任务，用户: {}, ID: {}", saasLoginToken.getUserId(), id);
        
        Generate generate = generateService.retryGenerate(id);
        GenerateDTO dto = generateConverter.toGenerateDTO(generate);
        return ApiResponse.success(dto);
    }

    @Override
    public ApiResponse<GenerateDTO.ProgressInfo> getGenerateProgress(SaasLoginToken saasLoginToken, Long id) {
        Generate generate = generateService.getGenerate(id)
                .orElseThrow(() -> new RuntimeException("生成任务不存在: " + id));
        
        GenerateDTO dto = generateConverter.toGenerateDTO(generate);
        return ApiResponse.success(dto.getProgressInfo());
    }

    // ==================== 统计分析 ====================

    @Override
    public ApiResponse<Object> getGenerateStats(SaasLoginToken saasLoginToken) {
        // 这里可以实现统计功能，暂时返回空
        return ApiResponse.success(null);
    }

    @Override
    public ApiResponse<GenerateConfigDTO.GenerateConfigStatsDTO> getConfigStats(SaasLoginToken saasLoginToken, Long id) {
        Object stats = generateConfigService.getConfigStats(id);
        return ApiResponse.success((GenerateConfigDTO.GenerateConfigStatsDTO) stats);
    }

    /**
     * 异步执行生成任务
     */
    private void executeGenerateAsync(Long generateId) {
        // 这里应该使用异步执行，比如@Async或者线程池
        // 为了简化，这里直接调用，实际项目中应该异步执行
        try {
            generateService.executeGenerate(generateId);
        } catch (Exception e) {
            log.error("异步执行生成任务失败: {}", generateId, e);
        }
    }
}
