package com.dcai.aixg.service;

import com.dcai.aixg.domain.generate.GenerateConfig;
import com.dcai.aixg.domain.generate.GenerateConfigRpt;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 生成配置服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GenerateConfigService {

    private final GenerateConfigRpt generateConfigRpt;

    /**
     * 创建生成配置
     */
    @Transactional
    public GenerateConfig createConfig(String code, String title, String description,
                                       String demo, String systemTemplate, String userTemplate,
                                       GenerateConfig.GenerateType generateType, Boolean enabled, Integer sortOrder) {
        log.info("创建生成配置，代码: {}, 标题: {}", code, title);
        
        // 检查代码是否已存在
        if (generateConfigRpt.existsByCode(code)) {
            throw new RuntimeException("配置代码已存在: " + code);
        }
        
        GenerateConfig config = new GenerateConfig(code, title, description, systemTemplate, userTemplate, generateType);
        config.setDemo(demo);
        
        if (enabled != null) {
            if (enabled) {
                config.enable();
            } else {
                config.disable();
            }
        }
        
        if (sortOrder != null) {
            config.setSortOrder(sortOrder);
        } else {
            // 设置为最大排序值+1
            Integer maxSortOrder = generateConfigRpt.findMaxSortOrder();
            config.setSortOrder(maxSortOrder + 1);
        }
        
        return generateConfigRpt.save(config);
    }

    /**
     * 更新生成配置
     */
    @Transactional
    public GenerateConfig updateConfig(Long id, String title, String description, String demo,
                                       String systemTemplate, String userTemplate,
                                       GenerateConfig.GenerateType generateType, Boolean enabled, Integer sortOrder) {
        log.info("更新生成配置，ID: {}", id);
        
        GenerateConfig config = generateConfigRpt.findById(id)
                .orElseThrow(() -> new RuntimeException("配置不存在: " + id));
        
        config.updateConfig(title, description, demo, systemTemplate, userTemplate, generateType);
        
        if (enabled != null) {
            if (enabled) {
                config.enable();
            } else {
                config.disable();
            }
        }
        
        if (sortOrder != null) {
            config.setSortOrder(sortOrder);
        }
        
        return generateConfigRpt.save(config);
    }

    /**
     * 删除生成配置
     */
    @Transactional
    public void deleteConfig(Long id) {
        log.info("删除生成配置，ID: {}", id);
        
        GenerateConfig config = generateConfigRpt.findById(id)
                .orElseThrow(() -> new RuntimeException("配置不存在: " + id));
        
        // 这里可以添加删除前的检查，比如是否有关联的生成任务
        
        generateConfigRpt.delete(config);
    }

    /**
     * 获取生成配置
     */
    public Optional<GenerateConfig> getConfig(Long id) {
        return generateConfigRpt.findById(id);
    }

    /**
     * 根据代码获取生成配置
     */
    public Optional<GenerateConfig> getConfigByCode(String code) {
        return generateConfigRpt.findByCode(code);
    }

    /**
     * 获取所有启用的配置
     */
    public List<GenerateConfig> getEnabledConfigs() {
        return generateConfigRpt.findByEnabledOrderBySortOrderAsc(true);
    }

    /**
     * 根据任务类型获取配置
     */
    public List<GenerateConfig> getConfigsByTaskType(GenerateConfig.GenerateType generateType) {
        return generateConfigRpt.findByTaskTypeAndEnabledOrderBySortOrderAsc(generateType, true);
    }

    /**
     * 启用/禁用配置
     */
    @Transactional
    public GenerateConfig toggleConfig(Long id) {
        log.info("切换配置状态，ID: {}", id);
        
        GenerateConfig config = generateConfigRpt.findById(id)
                .orElseThrow(() -> new RuntimeException("配置不存在: " + id));
        
        if (config.getEnabled()) {
            config.disable();
        } else {
            config.enable();
        }
        
        return generateConfigRpt.save(config);
    }

    /**
     * 根据标题搜索配置
     */
    public List<GenerateConfig> searchConfigsByTitle(String title) {
        return generateConfigRpt.findByTitleContainingAndEnabled(title, true);
    }

    /**
     * 获取配置统计信息
     */
    public Object getConfigStats(Long configId) {
        // 这里可以实现配置的统计信息，比如使用次数、成功率等
        // 暂时返回null，后续可以扩展
        return null;
    }
}
