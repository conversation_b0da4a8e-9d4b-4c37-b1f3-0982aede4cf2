package com.dcai.aixg.service;

import com.dcai.aixg.domain.generate.GenerateConfig;
import com.dcai.aixg.domain.generate.service.GenerateConfigDomainService;
import com.dcai.aixg.pro.generate.CreateGenerateConfigPO;
import com.dcai.aixg.pro.generate.UpdateGenerateConfigPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 生成配置服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GenerateConfigService {

    private final GenerateConfigDomainService generateConfigDomainService;

    /**
     * 创建生成配置
     */
    @Transactional
    public GenerateConfig createConfig(CreateGenerateConfigPO po) {
        return generateConfigDomainService.createConfig(po);
    }

    /**
     * 更新生成配置
     */
    @Transactional
    public GenerateConfig updateConfig(UpdateGenerateConfigPO po) {
        return generateConfigDomainService.updateConfig(po);
    }

    /**
     * 删除生成配置
     */
    @Transactional
    public void deleteConfig(Long id) {
        generateConfigDomainService.deleteConfig(id);
    }

    /**
     * 获取生成配置
     */
    public Optional<GenerateConfig> getConfig(Long id) {
        return generateConfigDomainService.getConfig(id);
    }

    /**
     * 根据代码获取生成配置
     */
    public Optional<GenerateConfig> getConfigByCode(String code) {
        return generateConfigDomainService.getConfigByCode(code);
    }

    /**
     * 获取所有启用的配置
     */
    public List<GenerateConfig> getEnabledConfigs() {
        return generateConfigDomainService.getEnabledConfigs();
    }

    /**
     * 根据任务类型获取配置
     */
    public List<GenerateConfig> getConfigsByTaskType(GenerateConfig.GenerateType generateType) {
        return generateConfigDomainService.getConfigsByTaskType(generateType);
    }

    /**
     * 启用/禁用配置
     */
    @Transactional
    public GenerateConfig toggleConfig(Long id) {
        return generateConfigDomainService.toggleConfig(id);
    }

    /**
     * 根据标题搜索配置
     */
    public List<GenerateConfig> searchConfigsByTitle(String title) {
        return generateConfigDomainService.searchConfigsByTitle(title);
    }

    /**
     * 获取配置统计信息
     */
    public Object getConfigStats(Long configId) {
        // 这里可以实现配置的统计信息，比如使用次数、成功率等
        // 暂时返回null，后续可以扩展
        return null;
    }
}
