package com.dcai.aixg.service;

import com.dcai.aixg.domain.generate.Generate;
import com.dcai.aixg.domain.generate.GenerateConfig;
import com.dcai.aixg.domain.generate.GenerateConfigRpt;
import com.dcai.aixg.domain.generate.GenerateRpt;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.Optional;

/**
 * 生成服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GenerateService {

    private final ChatClient chatClient;
    private final GenerateConfigRpt generateConfigRpt;
    private final GenerateRpt generateRpt;

    /**
     * 根据配置ID创建生成任务
     */
    @Transactional
    public Generate createGenerate(Long configId, String request, String memo) {
        log.info("创建生成任务，配置ID: {}, 请求: {}", configId, request);
        
        GenerateConfig config = generateConfigRpt.findById(configId)
                .orElseThrow(() -> new RuntimeException("配置不存在: " + configId));
        
        if (!config.getEnabled()) {
            throw new RuntimeException("配置已禁用: " + configId);
        }
        
        Generate generate = config.makeGenerate(request, request);
        if (memo != null) {
            generate.setMemo(memo);
        }
        
        return generateRpt.save(generate);
    }

    /**
     * 根据配置代码创建生成任务
     */
    @Transactional
    public Generate createGenerateByCode(String configCode, String request, String memo) {
        log.info("根据配置代码创建生成任务，代码: {}, 请求: {}", configCode, request);
        
        GenerateConfig config = generateConfigRpt.findByCodeAndEnabled(configCode, true)
                .orElseThrow(() -> new RuntimeException("配置不存在或已禁用: " + configCode));
        
        Generate generate = config.makeGenerate(request, request);
        if (memo != null) {
            generate.setMemo(memo);
        }
        
        return generateRpt.save(generate);
    }

    /**
     * 执行生成任务
     */
    @Transactional
    public Generate executeGenerate(Long generateId) {
        log.info("执行生成任务: {}", generateId);
        
        Generate generate = generateRpt.findById(generateId)
                .orElseThrow(() -> new RuntimeException("生成任务不存在: " + generateId));
        
        if (generate.isCompleted()) {
            log.warn("生成任务已完成，无需重复执行: {}", generateId);
            return generate;
        }
        
        try {
            generate.startProcessing();
            generateRpt.save(generate);
            
            // 执行AI生成
            String response = performAiGeneration(generate);
            
            generate.completeSuccess(response);
            return generateRpt.save(generate);
            
        } catch (Exception e) {
            log.error("生成任务执行失败: {}", generateId, e);
            generate.completeFailed(e.getMessage());
            return generateRpt.save(generate);
        }
    }

    /**
     * 执行AI生成
     */
    private String performAiGeneration(Generate generate) {
        log.info("开始AI生成，任务ID: {}", generate.getId());
        
        try {
            // 构建系统提示和用户提示
            String systemPrompt = generate.buildSystemPrompt();
            String userPrompt = generate.buildUserPrompt();
            
            log.debug("系统提示: {}", systemPrompt);
            log.debug("用户提示: {}", userPrompt);
            
            // 调用AI模型
            String response = chatClient.prompt()
                    .system(systemPrompt)
                    .user(userPrompt)
                    .call()
                    .content();
            
            log.info("AI生成完成，任务ID: {}, 响应长度: {}", generate.getId(), 
                    response != null ? response.length() : 0);
            
            return response;
            
        } catch (Exception e) {
            log.error("AI生成失败，任务ID: {}", generate.getId(), e);
            throw new RuntimeException("AI生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 取消生成任务
     */
    @Transactional
    public Generate cancelGenerate(Long generateId) {
        log.info("取消生成任务: {}", generateId);
        
        Generate generate = generateRpt.findById(generateId)
                .orElseThrow(() -> new RuntimeException("生成任务不存在: " + generateId));
        
        if (generate.isCompleted()) {
            log.warn("生成任务已完成，无法取消: {}", generateId);
            return generate;
        }
        
        generate.cancel();
        return generateRpt.save(generate);
    }

    /**
     * 重新执行生成任务
     */
    @Transactional
    public Generate retryGenerate(Long generateId) {
        log.info("重新执行生成任务: {}", generateId);
        
        Generate generate = generateRpt.findById(generateId)
                .orElseThrow(() -> new RuntimeException("生成任务不存在: " + generateId));
        
        // 重置状态
        generate.setStatus(Generate.GenerateStatus.PENDING);
        generate.setResponse(null);
        generate.setErrorMessage(null);
        generate.setStartedAt(null);
        generate.setCompletedAt(null);
        generate.setDuration(null);
        
        generateRpt.save(generate);
        
        // 重新执行
        return executeGenerate(generateId);
    }

    /**
     * 获取生成任务
     */
    public Optional<Generate> getGenerate(Long generateId) {
        return generateRpt.findById(generateId);
    }
}
