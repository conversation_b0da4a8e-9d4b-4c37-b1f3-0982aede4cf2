package com.dcai.aixg.service;

import com.dcai.aixg.domain.generate.Generate;
import com.dcai.aixg.domain.generate.GenerateConfig;
import com.dcai.aixg.domain.generate.GenerateConfigRpt;
import com.dcai.aixg.domain.generate.GenerateRpt;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ai.chat.client.ChatClient;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 生成服务测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class GenerateServiceTest {

    @Mock
    private ChatClient chatClient;

    @Mock
    private GenerateConfigRpt generateConfigRpt;

    @Mock
    private GenerateRpt generateRpt;

    @InjectMocks
    private GenerateService generateService;

    private GenerateConfig testConfig;
    private Generate testGenerate;

    @BeforeEach
    void setUp() {
        testConfig = new GenerateConfig(
                "test_config",
                "测试配置",
                "这是一个测试配置",
                "你是一个AI助手",
                "请回答：{request}",
                GenerateConfig.GenerateType.DEFAULT
        );
        testConfig.setId(1L);
        testConfig.enable();

        testGenerate = testConfig.makeGenerate("测试请求", "测试请求");
        testGenerate.setId(1L);
    }

    @Test
    void testCreateGenerate() {
        // Given
        when(generateConfigRpt.findById(1L)).thenReturn(Optional.of(testConfig));
        when(generateRpt.save(any(Generate.class))).thenAnswer(invocation -> {
            Generate generate = invocation.getArgument(0);
            generate.setId(1L);
            return generate;
        });

        // When
        Generate result = generateService.createGenerate(1L, "测试请求", "测试备忘录");

        // Then
        assertNotNull(result);
        assertEquals("测试请求", result.getRequest());
        assertEquals("测试备忘录", result.getMemo());
        assertEquals(Generate.GenerateStatus.PENDING, result.getStatus());

        verify(generateConfigRpt).findById(1L);
        verify(generateRpt).save(any(Generate.class));
    }

    @Test
    void testCreateGenerateByCode() {
        // Given
        when(generateConfigRpt.findByCodeAndEnabled("test_config", true))
                .thenReturn(Optional.of(testConfig));
        when(generateRpt.save(any(Generate.class))).thenReturn(testGenerate);

        // When
        Generate result = generateService.createGenerateByCode("test_config", "测试请求", null);

        // Then
        assertNotNull(result);
        assertEquals("测试请求", result.getRequest());
        assertEquals(Generate.GenerateStatus.PENDING, result.getStatus());
        
        verify(generateConfigRpt).findByCodeAndEnabled("test_config", true);
        verify(generateRpt).save(any(Generate.class));
    }

    @Test
    void testCreateGenerateWithDisabledConfig() {
        // Given
        testConfig.disable();
        when(generateConfigRpt.findById(1L)).thenReturn(Optional.of(testConfig));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            generateService.createGenerate(1L, "测试请求", null);
        });
        
        assertTrue(exception.getMessage().contains("配置已禁用"));
        verify(generateConfigRpt).findById(1L);
        verify(generateRpt, never()).save(any(Generate.class));
    }

    @Test
    void testCreateGenerateWithNonExistentConfig() {
        // Given
        when(generateConfigRpt.findById(999L)).thenReturn(Optional.empty());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            generateService.createGenerate(999L, "测试请求", null);
        });
        
        assertTrue(exception.getMessage().contains("配置不存在"));
        verify(generateConfigRpt).findById(999L);
        verify(generateRpt, never()).save(any(Generate.class));
    }

    @Test
    void testCancelGenerate() {
        // Given
        when(generateRpt.findById(1L)).thenReturn(Optional.of(testGenerate));
        when(generateRpt.save(any(Generate.class))).thenReturn(testGenerate);

        // When
        Generate result = generateService.cancelGenerate(1L);

        // Then
        assertNotNull(result);
        assertEquals(Generate.GenerateStatus.CANCELLED, result.getStatus());
        assertNotNull(result.getCompletedAt());
        
        verify(generateRpt).findById(1L);
        verify(generateRpt).save(testGenerate);
    }

    @Test
    void testGetGenerate() {
        // Given
        when(generateRpt.findById(1L)).thenReturn(Optional.of(testGenerate));

        // When
        Optional<Generate> result = generateService.getGenerate(1L);

        // Then
        assertTrue(result.isPresent());
        assertEquals(testGenerate, result.get());
        
        verify(generateRpt).findById(1L);
    }
}
