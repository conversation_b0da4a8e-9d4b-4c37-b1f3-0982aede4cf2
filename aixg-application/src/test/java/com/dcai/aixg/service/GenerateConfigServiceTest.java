package com.dcai.aixg.service;

import com.dcai.aixg.domain.generate.GenerateConfig;
import com.dcai.aixg.domain.generate.GenerateConfigRpt;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 生成配置服务测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class GenerateConfigServiceTest {

    @Mock
    private GenerateConfigRpt generateConfigRpt;

    @InjectMocks
    private GenerateConfigService generateConfigService;

    private GenerateConfig testConfig;

    @BeforeEach
    void setUp() {
        testConfig = new GenerateConfig(
                "test_config",
                "测试配置",
                "这是一个测试配置",
                "你是一个AI助手",
                "请回答：{request}",
                GenerateConfig.TaskType.DEFAULT
        );
        testConfig.setId(1L);
        testConfig.enable();
    }

    @Test
    void testCreateConfig() {
        // Given
        when(generateConfigRpt.existsByCode("new_config")).thenReturn(false);
        when(generateConfigRpt.findMaxSortOrder()).thenReturn(10);
        when(generateConfigRpt.save(any(GenerateConfig.class))).thenReturn(testConfig);

        // When
        GenerateConfig result = generateConfigService.createConfig(
                "new_config", "新配置", "描述", "演示", 
                "系统模板", "用户模板", GenerateConfig.TaskType.DEFAULT, 
                true, null);

        // Then
        assertNotNull(result);
        verify(generateConfigRpt).existsByCode("new_config");
        verify(generateConfigRpt).findMaxSortOrder();
        verify(generateConfigRpt).save(any(GenerateConfig.class));
    }

    @Test
    void testCreateConfigWithExistingCode() {
        // Given
        when(generateConfigRpt.existsByCode("existing_config")).thenReturn(true);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            generateConfigService.createConfig(
                    "existing_config", "配置", "描述", "演示",
                    "系统模板", "用户模板", GenerateConfig.TaskType.DEFAULT,
                    true, null);
        });
        
        assertTrue(exception.getMessage().contains("配置代码已存在"));
        verify(generateConfigRpt).existsByCode("existing_config");
        verify(generateConfigRpt, never()).save(any(GenerateConfig.class));
    }

    @Test
    void testUpdateConfig() {
        // Given
        when(generateConfigRpt.findById(1L)).thenReturn(Optional.of(testConfig));
        when(generateConfigRpt.save(any(GenerateConfig.class))).thenReturn(testConfig);

        // When
        GenerateConfig result = generateConfigService.updateConfig(
                1L, "更新标题", "更新描述", "更新演示",
                "更新系统模板", "更新用户模板", GenerateConfig.TaskType.CUSTOM,
                false, 5);

        // Then
        assertNotNull(result);
        verify(generateConfigRpt).findById(1L);
        verify(generateConfigRpt).save(testConfig);
    }

    @Test
    void testUpdateConfigNotFound() {
        // Given
        when(generateConfigRpt.findById(999L)).thenReturn(Optional.empty());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            generateConfigService.updateConfig(
                    999L, "标题", "描述", "演示",
                    "系统模板", "用户模板", GenerateConfig.TaskType.DEFAULT,
                    true, null);
        });
        
        assertTrue(exception.getMessage().contains("配置不存在"));
        verify(generateConfigRpt).findById(999L);
        verify(generateConfigRpt, never()).save(any(GenerateConfig.class));
    }

    @Test
    void testDeleteConfig() {
        // Given
        when(generateConfigRpt.findById(1L)).thenReturn(Optional.of(testConfig));

        // When
        generateConfigService.deleteConfig(1L);

        // Then
        verify(generateConfigRpt).findById(1L);
        verify(generateConfigRpt).delete(testConfig);
    }

    @Test
    void testGetConfig() {
        // Given
        when(generateConfigRpt.findById(1L)).thenReturn(Optional.of(testConfig));

        // When
        Optional<GenerateConfig> result = generateConfigService.getConfig(1L);

        // Then
        assertTrue(result.isPresent());
        assertEquals(testConfig, result.get());
        verify(generateConfigRpt).findById(1L);
    }

    @Test
    void testGetConfigByCode() {
        // Given
        when(generateConfigRpt.findByCode("test_config")).thenReturn(Optional.of(testConfig));

        // When
        Optional<GenerateConfig> result = generateConfigService.getConfigByCode("test_config");

        // Then
        assertTrue(result.isPresent());
        assertEquals(testConfig, result.get());
        verify(generateConfigRpt).findByCode("test_config");
    }

    @Test
    void testGetEnabledConfigs() {
        // Given
        List<GenerateConfig> configs = Arrays.asList(testConfig);
        when(generateConfigRpt.findByEnabledOrderBySortOrderAsc(true)).thenReturn(configs);

        // When
        List<GenerateConfig> result = generateConfigService.getEnabledConfigs();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testConfig, result.get(0));
        verify(generateConfigRpt).findByEnabledOrderBySortOrderAsc(true);
    }

    @Test
    void testGetConfigsByTaskType() {
        // Given
        List<GenerateConfig> configs = Arrays.asList(testConfig);
        when(generateConfigRpt.findByTaskTypeAndEnabledOrderBySortOrderAsc(
                GenerateConfig.TaskType.DEFAULT, true)).thenReturn(configs);

        // When
        List<GenerateConfig> result = generateConfigService.getConfigsByTaskType(
                GenerateConfig.TaskType.DEFAULT);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testConfig, result.get(0));
        verify(generateConfigRpt).findByTaskTypeAndEnabledOrderBySortOrderAsc(
                GenerateConfig.TaskType.DEFAULT, true);
    }

    @Test
    void testToggleConfig() {
        // Given
        when(generateConfigRpt.findById(1L)).thenReturn(Optional.of(testConfig));
        when(generateConfigRpt.save(any(GenerateConfig.class))).thenReturn(testConfig);

        // When
        GenerateConfig result = generateConfigService.toggleConfig(1L);

        // Then
        assertNotNull(result);
        verify(generateConfigRpt).findById(1L);
        verify(generateConfigRpt).save(testConfig);
    }
}
