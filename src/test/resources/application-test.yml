spring:
  application:
    name: aiagent-test
  ai:
    deepseek:
      # 使用测试API密钥
      api-key: ***********************************
      chat:
        options:
          model: deepseek-chat
          temperature: 0.7
          max-tokens: 2000
          top-p: 0.9

server:
  port: 0  # 随机端口，避免冲突

logging:
  level:
    com.dcai.aiagent: DEBUG
    org.springframework.ai: DEBUG
    freemarker: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 应用配置
app:
  content:
    generation:
      default-word-count: 600
      max-word-count: 800
      min-word-count: 500
    platforms:
      - xiaohongshu
      - xianyu
      - anjuke
      - wechat
    types:
      - property-recommendation
      - market-analysis
      - lifestyle-amenities
      - buying-guide
      - investment-analysis
      - school-district
      - xiaohongshu-post
      - xianyu-promotion
      - anjuke-professional
