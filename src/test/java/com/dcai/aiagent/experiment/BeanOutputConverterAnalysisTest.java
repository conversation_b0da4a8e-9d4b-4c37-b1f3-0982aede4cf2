package com.dcai.aiagent.experiment;

import com.dcai.aiagent.model.StructuredContentResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.core.ParameterizedTypeReference;

/**
 * 分析SpringAI BeanOutputConverter向提示词添加的格式指令
 * 用于验证是否需要显式添加JSON格式要求
 */
@Slf4j
public class BeanOutputConverterAnalysisTest {

    @Test
    void analyzeBeanOutputConverterFormatInstructions() {
        log.info("=== 分析SpringAI BeanOutputConverter格式指令 ===");
        
        // 创建BeanOutputConverter实例
        BeanOutputConverter<StructuredContentResponse> converter = 
            new BeanOutputConverter<>(StructuredContentResponse.class);
        
        // 获取格式指令
        String formatInstructions = converter.getFormat();
        
        log.info("=== BeanOutputConverter生成的格式指令 ===");
        log.info("指令长度: {} 字符", formatInstructions.length());
        log.info("完整指令内容:");
        log.info("{}", formatInstructions);
        
        // 分析指令内容
        analyzeFormatInstructions(formatInstructions);
    }
    
    @Test
    void analyzeParameterizedTypeConverter() {
        log.info("=== 分析ParameterizedTypeReference的BeanOutputConverter ===");
        
        // 创建带泛型的BeanOutputConverter
        BeanOutputConverter<StructuredContentResponse> converter = 
            new BeanOutputConverter<>(new ParameterizedTypeReference<StructuredContentResponse>() {});
        
        String formatInstructions = converter.getFormat();
        
        log.info("=== ParameterizedType BeanOutputConverter格式指令 ===");
        log.info("指令长度: {} 字符", formatInstructions.length());
        log.info("完整指令内容:");
        log.info("{}", formatInstructions);
        
        analyzeFormatInstructions(formatInstructions);
    }
    
    private void analyzeFormatInstructions(String instructions) {
        log.info("=== 格式指令分析 ===");
        
        // 检查是否包含JSON相关指令
        boolean containsJson = instructions.toLowerCase().contains("json");
        boolean containsFormat = instructions.toLowerCase().contains("format");
        boolean containsSchema = instructions.toLowerCase().contains("schema");
        boolean containsRfc8259 = instructions.contains("RFC8259");
        boolean containsExplanation = instructions.toLowerCase().contains("explanation");
        
        log.info("包含JSON关键词: {}", containsJson);
        log.info("包含Format关键词: {}", containsFormat);
        log.info("包含Schema关键词: {}", containsSchema);
        log.info("包含RFC8259标准: {}", containsRfc8259);
        log.info("包含禁止解释说明: {}", containsExplanation);
        
        // 检查是否包含具体的JSON Schema
        boolean containsProperties = instructions.contains("properties");
        boolean containsRequired = instructions.contains("required");
        boolean containsType = instructions.contains("type");
        
        log.info("包含JSON Schema properties: {}", containsProperties);
        log.info("包含JSON Schema required: {}", containsRequired);
        log.info("包含JSON Schema type: {}", containsType);
        
        // 分析指令结构
        String[] lines = instructions.split("\n");
        log.info("指令行数: {}", lines.length);
        
        for (int i = 0; i < Math.min(lines.length, 10); i++) {
            log.info("第{}行: {}", i + 1, lines[i].trim());
        }
        
        if (lines.length > 10) {
            log.info("... (省略剩余{}行)", lines.length - 10);
        }
    }
    
    @Test
    void compareWithManualJsonInstructions() {
        log.info("=== 对比手动JSON指令与BeanOutputConverter指令 ===");
        
        // 手动JSON指令（我们当前在提示词中使用的）
        String manualInstructions = """
            必须以纯JSON格式返回结果（不要用```json包围），包含所有必需字段
            
            JSON输出格式要求：
            {
              "title": "房源标题",
              "content": "房源详细内容",
              "tags": ["标签1", "标签2"],
              "charts": [{"type": "价格对比图", "title": "图表标题", "data": {}, "config": {"colors": ["#FF6384"], "showLegend": true, "showGrid": true}}],
              "platformOptimization": {
                "平台名": {
                  "adaptedTitle": "平台适配标题",
                  "adaptedContent": "平台适配内容",
                  "platformTags": ["平台标签1"],
                  "publishingSuggestion": "发布建议"
                }
              },
              "contentQuality": {
                "score": 90,
                "readability": 95,
                "engagement": 92,
                "seoOptimization": 88,
                "suggestions": ["建议1"]
              }
            }
            """;
        
        // BeanOutputConverter指令
        BeanOutputConverter<StructuredContentResponse> converter = 
            new BeanOutputConverter<>(StructuredContentResponse.class);
        String beanConverterInstructions = converter.getFormat();
        
        log.info("=== 手动JSON指令 ===");
        log.info("长度: {} 字符", manualInstructions.length());
        log.info("内容: {}", manualInstructions);
        
        log.info("=== BeanOutputConverter指令 ===");
        log.info("长度: {} 字符", beanConverterInstructions.length());
        log.info("内容: {}", beanConverterInstructions);
        
        // 分析差异
        log.info("=== 指令对比分析 ===");
        log.info("手动指令更长: {}", manualInstructions.length() > beanConverterInstructions.length());
        log.info("长度差异: {} 字符", Math.abs(manualInstructions.length() - beanConverterInstructions.length()));
        
        // 检查BeanOutputConverter是否包含格式控制指令
        boolean beanHasFormatControl = beanConverterInstructions.toLowerCase().contains("```json") 
                                    || beanConverterInstructions.toLowerCase().contains("markdown")
                                    || beanConverterInstructions.toLowerCase().contains("不要用");
        
        log.info("BeanOutputConverter包含格式控制指令: {}", beanHasFormatControl);
        
        if (!beanHasFormatControl) {
            log.warn("⚠️  BeanOutputConverter可能不包含防止Markdown格式的指令！");
            log.warn("⚠️  这可能解释了为什么需要手动添加格式控制指令");
        }
    }
}
