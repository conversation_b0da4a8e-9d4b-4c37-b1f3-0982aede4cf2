package com.dcai.aiagent.experiment;

import com.dcai.aiagent.model.ContentGenerationRequest;
import com.dcai.aiagent.service.ContentGenerationService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.Arrays;
import java.util.List;

/**
 * 测试优化后的JSON模式：SpringAI BeanOutputConverter + DeepSeek JSON模式
 * 验证移除手动JSON格式要求后的效果
 */
@Slf4j
@SpringBootTest
@TestPropertySource(properties = {
        "spring.ai.deepseek.api-key=***********************************",
        "logging.level.com.dcai.aiagent=DEBUG",
        "logging.level.org.springframework.ai=DEBUG"
})
public class OptimizedJsonModeTest {

    @Autowired
    private ContentGenerationService contentGenerationService;

    @Test
    void testOptimizedJsonMode() {
        log.info("=== 测试优化后的JSON模式 ===");
        log.info("✅ 使用SpringAI BeanOutputConverter自动格式指令");
        log.info("✅ 使用DeepSeek JSON模式 (response_format: json_object)");
        log.info("✅ 移除手动JSON格式要求，避免冗余");
        
        // 构建测试请求
        ContentGenerationRequest request = ContentGenerationRequest.builder()
                .propertyId("PROP_001")
                .contentType(ContentGenerationRequest.ContentType.XIAOHONGSHU_POST)
                .wordCount(600)
                .targetPlatforms(List.of(ContentGenerationRequest.Platform.XIAOHONGSHU))
                .needCharts(true)
                .chartTypes(Arrays.asList(
                        ContentGenerationRequest.ChartType.PRICE_COMPARISON,
                        ContentGenerationRequest.ChartType.MARKET_TREND
                ))
                .specialRequirements("测试优化后的JSON模式，验证SpringAI BeanOutputConverter的效果")
                .build();

        try {
            // 调用服务
            var response = contentGenerationService.generateContent(request);
            
            log.info("=== 优化后JSON模式测试结果 ===");
            log.info("✅ 成功生成内容");
            log.info("标题: {}", response.getContent().getTitle());
            log.info("内容长度: {}", response.getContent().getContent().length());
            log.info("标签数量: {}", response.getContent().getTags().size());
            log.info("图表数量: {}", response.getContent().getCharts().size());

            // 验证结果
            assert response.getContent().getTitle() != null && !response.getContent().getTitle().isEmpty();
            assert response.getContent().getContent() != null && !response.getContent().getContent().isEmpty();
            assert response.getContent().getTags() != null && !response.getContent().getTags().isEmpty();
            assert response.getContent().getCharts() != null && !response.getContent().getCharts().isEmpty();
            
            log.info("=== 优化验证成功 ===");
            log.info("🎉 SpringAI BeanOutputConverter + DeepSeek JSON模式工作正常");
            log.info("🎉 成功移除冗余的手动JSON格式要求");
            
        } catch (Exception e) {
            log.error("❌ 优化后JSON模式测试失败", e);
            throw e;
        }
    }
    
    @Test
    void testPromptOptimization() {
        log.info("=== 验证提示词优化效果 ===");
        log.info("📝 检查提示词是否已移除手动JSON格式要求");
        log.info("📝 验证SpringAI是否自动添加了BeanOutputConverter格式指令");
        
        // 这个测试主要用于观察日志输出，验证：
        // 1. 系统提示词中不再包含手动JSON格式要求
        // 2. SpringAI自动添加的格式指令是否正确
        // 3. DeepSeek JSON模式是否正确启用
        
        ContentGenerationRequest request = ContentGenerationRequest.builder()
                .propertyId("PROP_002")
                .contentType(ContentGenerationRequest.ContentType.ANJUKE_PROFESSIONAL)
                .wordCount(500)
                .targetPlatforms(List.of(ContentGenerationRequest.Platform.ANJUKE))
                .needCharts(false)
                .specialRequirements("简单测试，验证提示词优化")
                .build();

        try {
            var response = contentGenerationService.generateContent(request);
            log.info("✅ 提示词优化验证成功");
            
        } catch (Exception e) {
            log.error("❌ 提示词优化验证失败", e);
            throw e;
        }
    }
}
