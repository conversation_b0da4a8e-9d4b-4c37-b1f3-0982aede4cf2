package com.dcai.aiagent.service;

import com.dcai.aiagent.model.Property;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 房源服务测试
 */
@ExtendWith(MockitoExtension.class)
class PropertyServiceTest {

    private PropertyService propertyService;

    @BeforeEach
    void setUp() {
        propertyService = new PropertyService();
    }

    @Test
    void testGetPropertyById_Success() {
        // Given
        String propertyId = "PROP_001";

        // When
        Property property = propertyService.getPropertyById(propertyId);

        // Then
        assertNotNull(property);
        assertEquals(propertyId, property.getId());
        assertEquals("星河湾", property.getCommunityName());
        assertEquals(Property.PropertyStatus.AVAILABLE, property.getStatus());
    }

    @Test
    void testGetPropertyById_NotFound() {
        // Given
        String propertyId = "PROP_999";

        // When
        Property property = propertyService.getPropertyById(propertyId);

        // Then
        assertNull(property);
    }

    @Test
    void testGetAllProperties() {
        // When
        List<Property> properties = propertyService.getAllProperties();

        // Then
        assertNotNull(properties);
        assertEquals(2, properties.size());
        
        // 验证房源信息
        Property property1 = properties.stream()
                .filter(p -> "PROP_001".equals(p.getId()))
                .findFirst()
                .orElse(null);
        assertNotNull(property1);
        assertEquals("星河湾", property1.getCommunityName());
        
        Property property2 = properties.stream()
                .filter(p -> "PROP_002".equals(p.getId()))
                .findFirst()
                .orElse(null);
        assertNotNull(property2);
        assertEquals("翠湖花园", property2.getCommunityName());
    }

    @Test
    void testSearchProperties_ByCommunityName() {
        // Given
        String communityName = "星河湾";

        // When
        List<Property> properties = propertyService.searchProperties(communityName, null, null);

        // Then
        assertNotNull(properties);
        assertEquals(1, properties.size());
        assertEquals("星河湾", properties.get(0).getCommunityName());
    }

    @Test
    void testSearchProperties_ByPriceRange() {
        // Given
        BigDecimal minPrice = new BigDecimal("500");
        BigDecimal maxPrice = new BigDecimal("600");

        // When
        List<Property> properties = propertyService.searchProperties(null, minPrice, maxPrice);

        // Then
        assertNotNull(properties);
        assertEquals(1, properties.size());
        assertEquals("翠湖花园", properties.get(0).getCommunityName());
        assertTrue(properties.get(0).getListPrice().compareTo(minPrice) >= 0);
        assertTrue(properties.get(0).getListPrice().compareTo(maxPrice) <= 0);
    }

    @Test
    void testSearchProperties_NoResults() {
        // Given
        BigDecimal minPrice = new BigDecimal("1000");
        BigDecimal maxPrice = new BigDecimal("2000");

        // When
        List<Property> properties = propertyService.searchProperties(null, minPrice, maxPrice);

        // Then
        assertNotNull(properties);
        assertTrue(properties.isEmpty());
    }

    @Test
    void testPropertyLayout() {
        // Given
        String propertyId = "PROP_001";

        // When
        Property property = propertyService.getPropertyById(propertyId);

        // Then
        assertNotNull(property);
        assertNotNull(property.getLayout());
        assertEquals("3室2厅2卫", property.getLayout().getLayoutDescription());
        assertEquals(Integer.valueOf(3), property.getLayout().getBedrooms());
        assertEquals(Integer.valueOf(2), property.getLayout().getLivingRooms());
        assertEquals(Integer.valueOf(2), property.getLayout().getBathrooms());
    }

    @Test
    void testFloorInfo() {
        // Given
        String propertyId = "PROP_001";

        // When
        Property property = propertyService.getPropertyById(propertyId);

        // Then
        assertNotNull(property);
        assertNotNull(property.getFloorInfo());
        assertEquals("6/18层", property.getFloorInfo().getFloorDescription());
        assertEquals(Integer.valueOf(6), property.getFloorInfo().getCurrentFloor());
        assertEquals(Integer.valueOf(18), property.getFloorInfo().getTotalFloors());
    }

    @Test
    void testPropertyFeatures() {
        // Given
        String propertyId = "PROP_001";

        // When
        Property property = propertyService.getPropertyById(propertyId);

        // Then
        assertNotNull(property);
        assertNotNull(property.getFeatures());
        assertTrue(property.getFeatures().contains("满五唯一"));
        assertTrue(property.getFeatures().contains("南北通透"));
        assertTrue(property.getFeatures().contains("精装修"));
    }

    @Test
    void testCommunityInfo() {
        // Given
        String propertyId = "PROP_001";

        // When
        Property property = propertyService.getPropertyById(propertyId);

        // Then
        assertNotNull(property);
        assertNotNull(property.getCommunityInfo());
        assertEquals("星河地产", property.getCommunityInfo().getDeveloper());
        assertEquals("万科物业", property.getCommunityInfo().getPropertyCompany());
        assertEquals(new BigDecimal("4.8"), property.getCommunityInfo().getPropertyFee());
    }

    @Test
    void testTransactionRequirement() {
        // Given
        String propertyId = "PROP_001";

        // When
        Property property = propertyService.getPropertyById(propertyId);

        // Then
        assertNotNull(property);
        assertNotNull(property.getTransactionRequirement());
        assertEquals("移民", property.getTransactionRequirement().getSellingReason());
        assertEquals(new BigDecimal("800"), property.getTransactionRequirement().getReservePrice());
        assertEquals("随时看房", property.getTransactionRequirement().getViewingConvenience());
    }
}
