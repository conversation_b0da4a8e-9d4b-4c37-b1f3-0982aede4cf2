package com.dcai.aiagent.controller;

import com.dcai.aiagent.model.Property;
import com.dcai.aiagent.service.PropertyService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 房源控制器测试
 */
@WebMvcTest(PropertyController.class)
class PropertyControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PropertyService propertyService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testGetAllProperties() throws Exception {
        // Given
        List<Property> properties = Arrays.asList(
                Property.builder()
                        .id("PROP_001")
                        .communityName("星河湾")
                        .listPrice(new BigDecimal("830"))
                        .status(Property.PropertyStatus.AVAILABLE)
                        .build(),
                Property.builder()
                        .id("PROP_002")
                        .communityName("翠湖花园")
                        .listPrice(new BigDecimal("560"))
                        .status(Property.PropertyStatus.AVAILABLE)
                        .build()
        );

        when(propertyService.getAllProperties()).thenReturn(properties);

        // When & Then
        mockMvc.perform(get("/api/properties")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].id").value("PROP_001"))
                .andExpect(jsonPath("$[0].communityName").value("星河湾"))
                .andExpect(jsonPath("$[1].id").value("PROP_002"))
                .andExpect(jsonPath("$[1].communityName").value("翠湖花园"));
    }

    @Test
    void testGetPropertyById_Success() throws Exception {
        // Given
        String propertyId = "PROP_001";
        Property property = Property.builder()
                .id(propertyId)
                .communityName("星河湾")
                .address("上海市徐汇区漕河泾开发区桂平路188号")
                .listPrice(new BigDecimal("830"))
                .area(new BigDecimal("120"))
                .status(Property.PropertyStatus.AVAILABLE)
                .build();

        when(propertyService.getPropertyById(propertyId)).thenReturn(property);

        // When & Then
        mockMvc.perform(get("/api/properties/{propertyId}", propertyId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(propertyId))
                .andExpect(jsonPath("$.communityName").value("星河湾"))
                .andExpect(jsonPath("$.address").value("上海市徐汇区漕河泾开发区桂平路188号"))
                .andExpect(jsonPath("$.listPrice").value(830))
                .andExpect(jsonPath("$.area").value(120));
    }

    @Test
    void testGetPropertyById_NotFound() throws Exception {
        // Given
        String propertyId = "PROP_999";
        when(propertyService.getPropertyById(propertyId)).thenReturn(null);

        // When & Then
        mockMvc.perform(get("/api/properties/{propertyId}", propertyId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    void testSearchProperties_ByCommunityName() throws Exception {
        // Given
        String communityName = "星河湾";
        List<Property> properties = Arrays.asList(
                Property.builder()
                        .id("PROP_001")
                        .communityName(communityName)
                        .listPrice(new BigDecimal("830"))
                        .status(Property.PropertyStatus.AVAILABLE)
                        .build()
        );

        when(propertyService.searchProperties(eq(communityName), any(), any()))
                .thenReturn(properties);

        // When & Then
        mockMvc.perform(get("/api/properties/search")
                        .param("communityName", communityName)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].communityName").value(communityName));
    }

    @Test
    void testSearchProperties_ByPriceRange() throws Exception {
        // Given
        BigDecimal minPrice = new BigDecimal("500");
        BigDecimal maxPrice = new BigDecimal("600");
        List<Property> properties = Arrays.asList(
                Property.builder()
                        .id("PROP_002")
                        .communityName("翠湖花园")
                        .listPrice(new BigDecimal("560"))
                        .status(Property.PropertyStatus.AVAILABLE)
                        .build()
        );

        when(propertyService.searchProperties(any(), eq(minPrice), eq(maxPrice)))
                .thenReturn(properties);

        // When & Then
        mockMvc.perform(get("/api/properties/search")
                        .param("minPrice", "500")
                        .param("maxPrice", "600")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].listPrice").value(560));
    }

    @Test
    void testSearchProperties_NoResults() throws Exception {
        // Given
        when(propertyService.searchProperties(any(), any(), any()))
                .thenReturn(Arrays.asList());

        // When & Then
        mockMvc.perform(get("/api/properties/search")
                        .param("communityName", "不存在的小区")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(0));
    }
}
