package com.dcai.aiagent.controller;

import com.dcai.aiagent.model.ContentGenerationRequest;
import com.dcai.aiagent.model.ContentGenerationResponse;
import com.dcai.aiagent.model.StructuredContentResponse;
import com.dcai.aiagent.service.ContentGenerationService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 内容生成控制器测试
 */
@WebMvcTest(ContentGenerationController.class)
class ContentGenerationControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ContentGenerationService contentGenerationService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testGenerateContent_Success() throws Exception {
        // Given
        ContentGenerationRequest request = ContentGenerationRequest.builder()
                .propertyId("PROP_001")
                .contentType(ContentGenerationRequest.ContentType.PROPERTY_RECOMMENDATION)
                .targetPlatforms(Arrays.asList(ContentGenerationRequest.Platform.XIAOHONGSHU))
                .targetAudience(ContentGenerationRequest.TargetAudience.UPGRADING_BUYER)
                .contentStyle(ContentGenerationRequest.ContentStyle.PROFESSIONAL)
                .wordCount(600)
                .needCharts(true)
                .chartTypes(Arrays.asList(ContentGenerationRequest.ChartType.PRICE_COMPARISON))
                .build();

        ContentGenerationResponse mockResponse = ContentGenerationResponse.builder()
                .taskId(UUID.randomUUID().toString())
                .propertyId("PROP_001")
                .content(StructuredContentResponse.builder()
                        .title("内环稀缺次新房！单价回到10年前！满五唯一税费省20万🔥")
                        .content("精品房源推荐内容...")
                        .tags(Arrays.asList("房产推荐", "买房攻略", "投资置业"))
                        .wordCount(580)
                        .build())
                .generatedAt(LocalDateTime.now())
                .processingTime(2500L)
                .status(ContentGenerationResponse.GenerationStatus.SUCCESS)
                .build();

        when(contentGenerationService.generateContent(any(ContentGenerationRequest.class)))
                .thenReturn(mockResponse);

        // When & Then
        mockMvc.perform(post("/api/content/generate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.propertyId").value("PROP_001"))
                .andExpect(jsonPath("$.status").value("SUCCESS"))
                .andExpect(jsonPath("$.content.title").exists())
                .andExpect(jsonPath("$.content.body").exists())
                .andExpect(jsonPath("$.content.tags").isArray())
                .andExpect(jsonPath("$.content.wordCount").isNumber())
                .andExpect(jsonPath("$.processingTime").isNumber());
    }

    @Test
    void testGenerateContent_ValidationError() throws Exception {
        // Given - 缺少必填字段的请求
        ContentGenerationRequest invalidRequest = ContentGenerationRequest.builder()
                .contentType(ContentGenerationRequest.ContentType.PROPERTY_RECOMMENDATION)
                // 缺少 propertyId 和 targetPlatforms
                .build();

        // When & Then
        mockMvc.perform(post("/api/content/generate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testGenerateContent_ServiceException() throws Exception {
        // Given
        ContentGenerationRequest request = ContentGenerationRequest.builder()
                .propertyId("PROP_001")
                .contentType(ContentGenerationRequest.ContentType.PROPERTY_RECOMMENDATION)
                .targetPlatforms(Arrays.asList(ContentGenerationRequest.Platform.XIAOHONGSHU))
                .wordCount(600)
                .build();

        when(contentGenerationService.generateContent(any(ContentGenerationRequest.class)))
                .thenThrow(new RuntimeException("AI服务暂时不可用"));

        // When & Then
        mockMvc.perform(post("/api/content/generate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.status").value("FAILED"))
                .andExpect(jsonPath("$.errorMessage").exists());
    }

    @Test
    void testGetSupportedContentTypes() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/content/types")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(ContentGenerationRequest.ContentType.values().length));
    }

    @Test
    void testGetSupportedPlatforms() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/content/platforms")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(ContentGenerationRequest.Platform.values().length));
    }

    @Test
    void testGetSupportedAudiences() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/content/audiences")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(ContentGenerationRequest.TargetAudience.values().length));
    }

    @Test
    void testGetSupportedStyles() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/content/styles")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(ContentGenerationRequest.ContentStyle.values().length));
    }

    @Test
    void testGetSupportedChartTypes() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/content/chart-types")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(ContentGenerationRequest.ChartType.values().length));
    }
}
