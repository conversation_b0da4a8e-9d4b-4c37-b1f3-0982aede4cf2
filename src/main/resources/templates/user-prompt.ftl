请为房源ID: ${propertyId} 生成${contentType.description}内容。

<#-- 如果有特殊要求，则显示 -->
<#if specialRequirements?has_content>
特殊要求：${specialRequirements}

</#if>
<#-- 根据内容类型提供具体指导 -->
<#if contentType.description == "小红书种草文案">
请特别注意：
- 标题要有吸引力，包含emoji
- 内容要有代入感，让用户想象住在这里的生活
- 多使用数据对比，突出性价比
- 加入互动元素，引导用户评论和私信
<#elseif contentType.description == "闲鱼推广文案">
请特别注意：
- 突出价格优势和急售理由
- 提供详细的房源信息
- 强调看房的便利性
- 展示诚意出售的态度
<#elseif contentType == "安居客专业推荐">
请特别注意：
- 内容要专业详细
- 提供全面的市场分析
- 突出投资价值
- 展示专业顾问的服务优势
</#if>

<#-- 如果需要图表，提供具体要求 -->
<#if needCharts!false && chartTypes?has_content>
图表要求：
<#list chartTypes as chartType>
- ${chartType.description}：<#if chartType.name() == "PRICE_COMPARISON">请提供本房源与同小区、同区域房源的价格对比数据<#elseif chartType.name() == "MARKET_TREND">请提供最近6个月的区域房价走势数据<#elseif chartType.name() == "AMENITIES_MAP">请提供周边配套设施的分布和距离数据<#else>请提供相关的数据分析</#if>
</#list>

</#if>
请先调用相关函数获取房源信息、市场数据、周边配套和价格分析，然后基于这些真实数据生成高质量的推广内容。

**请务必以JSON格式返回结果，确保JSON格式正确且包含所有必需字段。**
