package com.dcai.aiagent.config;

import freemarker.template.Configuration;
import freemarker.template.TemplateExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.core.io.ResourceLoader;

import java.io.IOException;
import java.util.Locale;

/**
 * FreeMarker配置类
 * 用于配置FreeMarker模板引擎，替代SpringAI的PromptTemplate
 */
@org.springframework.context.annotation.Configuration
public class FreeMarkerConfig {

    /**
     * 配置FreeMarker Configuration Bean
     */
    @Bean("customFreeMarkerConfiguration")
    public Configuration customFreeMarkerConfiguration(ResourceLoader resourceLoader) throws IOException {
        Configuration cfg = new Configuration(Configuration.VERSION_2_3_32);
        
        // 设置模板加载器 - 从classpath加载模板
        cfg.setClassLoaderForTemplateLoading(getClass().getClassLoader(), "templates");
        
        // 设置默认编码
        cfg.setDefaultEncoding("UTF-8");
        
        // 设置区域
        cfg.setLocale(Locale.SIMPLIFIED_CHINESE);
        
        // 设置模板异常处理器
        cfg.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
        
        // 不要回退到更高版本的不兼容改进
        cfg.setLogTemplateExceptions(false);
        cfg.setWrapUncheckedExceptions(true);
        cfg.setFallbackOnNullLoopVariable(false);
        
        // 设置数字格式 - 避免科学计数法
        cfg.setNumberFormat("0.######");
        
        return cfg;
    }
}
