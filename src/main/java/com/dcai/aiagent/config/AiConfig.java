package com.dcai.aiagent.config;

import com.dcai.aiagent.function.MarketDataFunction;
import com.dcai.aiagent.function.PriceAnalysisFunction;
import com.dcai.aiagent.function.PropertyDataFunction;
import com.dcai.aiagent.function.SurroundingAmenitiesFunction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.deepseek.DeepSeekChatModel;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * AI配置类
 * 配置ChatClient并显式注册所有Function/Tool实例
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class AiConfig {

    private final PropertyDataFunction propertyDataFunction;
    private final MarketDataFunction marketDataFunction;
    private final SurroundingAmenitiesFunction surroundingAmenitiesFunction;
    private final PriceAnalysisFunction priceAnalysisFunction;

    /**
     * 配置ChatClient并显式注册所有Function实例
     * 根据SpringAI文档，应该传递Function实例而不是名称字符串
     */
    @Bean
    public ChatClient chatClient(DeepSeekChatModel chatModel) {
        log.info("🔧 配置ChatClient，显式注册Function实例...");

        ChatClient client = ChatClient.builder(chatModel)
                // 显式注册所有Function实例
                .defaultTools(
                    propertyDataFunction,           // PropertyDataFunction实例
                    marketDataFunction,            // MarketDataFunction实例
                    surroundingAmenitiesFunction,  // SurroundingAmenitiesFunction实例
                    priceAnalysisFunction          // PriceAnalysisFunction实例
                )
                .build();

        log.info("✅ ChatClient配置完成，已注册4个Function实例");
        return client;
    }
}
