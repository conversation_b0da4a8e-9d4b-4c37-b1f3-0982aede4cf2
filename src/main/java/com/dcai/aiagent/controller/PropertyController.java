package com.dcai.aiagent.controller;

import com.dcai.aiagent.model.Property;
import com.dcai.aiagent.service.PropertyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 房源管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/properties")
@RequiredArgsConstructor
public class PropertyController {

    private final PropertyService propertyService;

    /**
     * 获取所有房源列表
     */
    @GetMapping
    public ResponseEntity<List<Property>> getAllProperties() {
        log.info("获取所有房源列表");
        List<Property> properties = propertyService.getAllProperties();
        return ResponseEntity.ok(properties);
    }

    /**
     * 根据ID获取房源详情
     */
    @GetMapping("/{propertyId}")
    public ResponseEntity<Property> getPropertyById(@PathVariable String propertyId) {
        log.info("获取房源详情，ID: {}", propertyId);
        Property property = propertyService.getPropertyById(propertyId);
        
        if (property == null) {
            return ResponseEntity.notFound().build();
        }
        
        return ResponseEntity.ok(property);
    }

    /**
     * 搜索房源
     */
    @GetMapping("/search")
    public ResponseEntity<List<Property>> searchProperties(
            @RequestParam(required = false) String communityName,
            @RequestParam(required = false) BigDecimal minPrice,
            @RequestParam(required = false) BigDecimal maxPrice) {
        
        log.info("搜索房源，小区: {}, 价格区间: {}-{}", communityName, minPrice, maxPrice);
        
        List<Property> properties = propertyService.searchProperties(communityName, minPrice, maxPrice);
        return ResponseEntity.ok(properties);
    }
}
