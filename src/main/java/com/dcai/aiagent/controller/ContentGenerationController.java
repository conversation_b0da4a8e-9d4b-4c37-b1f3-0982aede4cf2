package com.dcai.aiagent.controller;

import com.dcai.aiagent.model.ContentGenerationRequest;
import com.dcai.aiagent.model.ContentGenerationResponse;
import com.dcai.aiagent.service.ContentGenerationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 内容生成控制器
 */
@Slf4j
@RestController
@RequestMapping("/content")
@RequiredArgsConstructor
@Validated
public class ContentGenerationController {

    private final ContentGenerationService contentGenerationService;

    /**
     * 生成推广内容
     */
    @PostMapping("/generate")
    public ResponseEntity<ContentGenerationResponse> generateContent(
            @Valid @RequestBody ContentGenerationRequest request) {
        
        log.info("收到内容生成请求，房源ID: {}, 类型: {}", 
                request.getPropertyId(), request.getContentType());
        
        try {
            ContentGenerationResponse response = contentGenerationService.generateContent(request);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("内容生成失败", e);
            
            ContentGenerationResponse errorResponse = ContentGenerationResponse.builder()
                    .propertyId(request.getPropertyId())
                    .status(ContentGenerationResponse.GenerationStatus.FAILED)
                    .errorMessage("内容生成失败: " + e.getMessage())
                    .build();
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 获取支持的内容类型
     */
    @GetMapping("/types")
    public ResponseEntity<ContentGenerationRequest.ContentType[]> getSupportedContentTypes() {
        return ResponseEntity.ok(ContentGenerationRequest.ContentType.values());
    }

    /**
     * 获取支持的平台
     */
    @GetMapping("/platforms")
    public ResponseEntity<ContentGenerationRequest.Platform[]> getSupportedPlatforms() {
        return ResponseEntity.ok(ContentGenerationRequest.Platform.values());
    }

    /**
     * 获取支持的目标客户群体
     */
    @GetMapping("/audiences")
    public ResponseEntity<ContentGenerationRequest.TargetAudience[]> getSupportedAudiences() {
        return ResponseEntity.ok(ContentGenerationRequest.TargetAudience.values());
    }

    /**
     * 获取支持的内容风格
     */
    @GetMapping("/styles")
    public ResponseEntity<ContentGenerationRequest.ContentStyle[]> getSupportedStyles() {
        return ResponseEntity.ok(ContentGenerationRequest.ContentStyle.values());
    }

    /**
     * 获取支持的图表类型
     */
    @GetMapping("/chart-types")
    public ResponseEntity<ContentGenerationRequest.ChartType[]> getSupportedChartTypes() {
        return ResponseEntity.ok(ContentGenerationRequest.ChartType.values());
    }
}
