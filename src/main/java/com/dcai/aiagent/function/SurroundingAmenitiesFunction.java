package com.dcai.aiagent.function;

import com.dcai.aiagent.model.SurroundingAmenities;
import com.dcai.aiagent.service.SurroundingAmenitiesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;

/**
 * 周边配套信息查询工具类
 * 用于AI模型通过Tool Calling获取学区、交通、生活配套等信息
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SurroundingAmenitiesFunction {

    private final SurroundingAmenitiesService amenitiesService;

    @Tool(description = "获取房源周边配套信息，包括学区、交通、生活配套、产业环境、规划潜力等")
    public SurroundingAmenities getSurroundingAmenities(
            @ToolParam(description = "房源ID") String propertyId,
            @ToolParam(description = "配套类型：education(学区)、transportation(交通)、life(生活配套)、industry(产业环境)、development(规划潜力)、all(全部配套)", required = false) String amenityType,
            @ToolParam(description = "查询半径，单位：米，默认2000米", required = false) Integer radius) {

        log.info("🔧 [Function Call] getSurroundingAmenities 被调用");
        log.info("📥 入参: propertyId = {}, amenityType = {}, radius = {}米", propertyId, amenityType, radius);

        try {
            SurroundingAmenities amenities = amenitiesService.getSurroundingAmenities(
                propertyId,
                amenityType,
                radius
            );

            if (amenities == null) {
                log.error("❌ 该房源暂无周边配套信息: {}", propertyId);
                throw new RuntimeException("该房源暂无周边配套信息: " + propertyId);
            }

            log.info("✅ 成功获取周边配套信息");
            if (amenities.getEducation() != null) {
                log.info("📤 学区信息: 小学={}, 中学={}, 学校排名={}",
                        amenities.getEducation().getPrimarySchool(),
                        amenities.getEducation().getMiddleSchool(),
                        amenities.getEducation().getSchoolRanking());
            }
            if (amenities.getTransportation() != null) {
                log.info("📤 交通信息: 地铁站数量={}, 机场距离={}",
                        amenities.getTransportation().getSubwayStations().size(),
                        amenities.getTransportation().getAirportDistance());
            }
            if (amenities.getLifeAmenities() != null) {
                log.info("📤 生活配套: 商场数量={}, 医院数量={}",
                        amenities.getLifeAmenities().getShoppingCenters().size(),
                        amenities.getLifeAmenities().getHospitals().size());
            }

            return amenities;
        } catch (Exception e) {
            log.error("❌ 获取周边配套信息失败，房源ID: {}", propertyId, e);
            throw new RuntimeException("获取周边配套信息失败: " + e.getMessage());
        }
    }
}
