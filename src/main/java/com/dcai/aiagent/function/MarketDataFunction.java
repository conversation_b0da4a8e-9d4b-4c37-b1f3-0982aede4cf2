package com.dcai.aiagent.function;

import com.dcai.aiagent.model.MarketData;
import com.dcai.aiagent.service.MarketDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;

/**
 * 市场数据查询工具类
 * 用于AI模型通过Tool Calling获取市场行情、成交数据等信息
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MarketDataFunction {

    private final MarketDataService marketDataService;

    @Tool(description = "获取指定区域的市场数据，包括挂牌信息、成交数据、市场趋势、政策信息等。注意：需要传递区域名称（如徐汇区、浦东新区），而不是房源ID。如果你有房源信息，请从房源地址中提取区域名称。")
    public MarketData getMarketData(
            @ToolParam(description = "区域名称，必须是具体的行政区名称，如：徐汇区、浦东新区、黄浦区等。不要传递房源ID或小区名称。") String regionName,
            @ToolParam(description = "数据类型：listing(挂牌数据)、transaction(成交数据)、trend(市场趋势)、policy(政策信息)、all(全部数据)", required = false) String dataType) {

        log.info("🔧 [Function Call] getMarketData 被调用");
        log.info("📥 入参: regionName = {}, dataType = {}", regionName, dataType);

        try {
            MarketData marketData = marketDataService.getMarketData(regionName, dataType);
            if (marketData == null) {
                log.error("❌ 该区域暂无市场数据: {}", regionName);
                throw new RuntimeException("该区域暂无市场数据: " + regionName);
            }

            log.info("✅ 成功获取市场数据");
            log.info("📤 返回值: 区域={}, 挂牌量={}, 平均价格={}元/㎡, 成交量={}",
                    marketData.getRegionName(),
                    marketData.getListingData().getTotalListings(),
                    marketData.getListingData().getAveragePrice(),
                    marketData.getTransactionData().getTransactionVolume());
            log.info("📤 市场趋势: 价格变化率={}%, 趋势={}, 市场热度={}",
                    marketData.getMarketTrend().getPriceChangeRate(),
                    marketData.getMarketTrend().getTrendDirection(),
                    marketData.getMarketTrend().getMarketHeat());

            return marketData;
        } catch (Exception e) {
            log.error("❌ 获取市场数据失败，区域: {}", regionName, e);
            throw new RuntimeException("获取市场数据失败: " + e.getMessage());
        }
    }
}
