package com.dcai.aiagent.model;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * AI生成内容的结构化响应模型
 * 用于SpringAI的BeanOutputConverter进行结构化输出
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonPropertyOrder({"title", "content", "tags", "wordCount", "charts", "platformContents", "contentQuality"})
public class StructuredContentResponse {

    /**
     * 内容标题
     */
    private String title;

    /**
     * 主要内容
     */
    private String content;

    /**
     * 标签列表
     */
    private List<String> tags;

    /**
     * 字数统计
     */
    private Integer wordCount;

    /**
     * 图表数据列表
     */
    private List<ChartData> charts;

    /**
     * 针对不同平台的适配内容
     */
    private Map<String, PlatformContent> platformContents;

    /**
     * 内容质量评估
     */
    private ContentQuality contentQuality;

    /**
     * 图表数据结构
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonPropertyOrder({"type", "title", "description", "data", "config"})
    public static class ChartData {
        /**
         * 图表类型
         */
        private String type;

        /**
         * 图表标题
         */
        private String title;

        /**
         * 图表描述
         */
        private String description;

        /**
         * 图表数据
         */
        private Object data;

        /**
         * 图表配置
         */
        private ChartConfig config;
    }

    /**
     * 图表配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonPropertyOrder({"xAxis", "yAxis", "colors", "showLegend", "showGrid"})
    public static class ChartConfig {
        /**
         * X轴标签
         */
        private String xAxis;

        /**
         * Y轴标签
         */
        private String yAxis;

        /**
         * 颜色配置
         */
        private List<String> colors;

        /**
         * 是否显示图例
         */
        private Boolean showLegend;

        /**
         * 是否显示网格
         */
        private Boolean showGrid;
    }

    /**
     * 平台适配内容
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonPropertyOrder({"adaptedTitle", "adaptedContent", "platformTags", "publishingSuggestion"})
    public static class PlatformContent {
        /**
         * 适配后的标题
         */
        private String adaptedTitle;

        /**
         * 适配后的内容
         */
        private String adaptedContent;

        /**
         * 平台特定的标签
         */
        private List<String> platformTags;

        /**
         * 发布建议
         */
        private String publishingSuggestion;
    }

    /**
     * 内容质量评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonPropertyOrder({"overallScore", "attractivenessScore", "professionalismScore", "readabilityScore", "completenessScore", "improvementSuggestions"})
    public static class ContentQuality {
        /**
         * 总体评分（1-10）
         */
        private Double overallScore;

        /**
         * 吸引力评分
         */
        private Double attractivenessScore;

        /**
         * 专业性评分
         */
        private Double professionalismScore;

        /**
         * 可读性评分
         */
        private Double readabilityScore;

        /**
         * 完整性评分
         */
        private Double completenessScore;

        /**
         * 改进建议
         */
        private List<String> improvementSuggestions;
    }
}
