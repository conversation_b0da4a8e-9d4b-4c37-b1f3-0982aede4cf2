package com.dcai.aiagent.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 周边配套信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SurroundingAmenities {
    
    /**
     * 学区信息
     */
    private EducationInfo education;
    
    /**
     * 交通配套
     */
    private TransportationInfo transportation;
    
    /**
     * 生活配套
     */
    private LifeAmenities lifeAmenities;
    
    /**
     * 产业环境
     */
    private IndustryEnvironment industry;
    
    /**
     * 规划潜力
     */
    private DevelopmentPotential development;

    /**
     * 学区信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EducationInfo {
        private String primarySchool;      // 对口小学
        private String middleSchool;       // 对口初中
        private String schoolPolicy;       // 学区政策
        private String schoolRanking;      // 学校排名
        private String admissionInfo;      // 入学条件
    }

    /**
     * 交通配套信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransportationInfo {
        private List<SubwayStation> subwayStations;  // 地铁站点
        private List<BusStation> busStations;        // 公交站点
        private List<String> mainRoads;              // 主干道
        private String airportDistance;              // 机场距离
        private String trainStationDistance;         // 火车站距离
        private List<String> plannedTransport;       // 交通规划
    }

    /**
     * 地铁站信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SubwayStation {
        private String name;        // 站点名称
        private String line;        // 线路
        private Integer distance;   // 距离（米）
        private Integer walkTime;   // 步行时间（分钟）
    }

    /**
     * 公交站信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BusStation {
        private String name;           // 站点名称
        private List<String> lines;    // 公交线路
        private Integer distance;      // 距离（米）
    }

    /**
     * 生活配套
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LifeAmenities {
        private List<ShoppingCenter> shoppingCenters;  // 商场
        private List<Hospital> hospitals;              // 医院
        private List<String> parks;                    // 公园
        private List<String> banks;                    // 银行
        private List<String> restaurants;              // 餐饮
    }

    /**
     * 商场信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShoppingCenter {
        private String name;        // 商场名称
        private Integer distance;   // 距离（米）
        private String scale;       // 规模
        private String brands;      // 主要品牌
    }

    /**
     * 医院信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Hospital {
        private String name;        // 医院名称
        private String level;       // 医院等级
        private Integer distance;   // 距离（米）
        private String specialty;   // 专科特色
    }

    /**
     * 产业环境
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IndustryEnvironment {
        private List<String> businessDistricts;  // 商务区
        private List<String> industrialParks;    // 产业园区
        private String employmentOpportunity;    // 就业机会
        private List<String> landscapes;         // 景观环境
    }

    /**
     * 规划潜力
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DevelopmentPotential {
        private List<String> keyDevelopmentAreas;  // 重点发展区域
        private List<String> majorProjects;        // 重大项目
        private List<String> urbanRenewal;         // 旧改计划
        private String governmentPlanning;         // 政府规划
    }
}
