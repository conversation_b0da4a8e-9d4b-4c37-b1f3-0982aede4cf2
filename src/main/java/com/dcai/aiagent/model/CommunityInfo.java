package com.dcai.aiagent.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 小区详情信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommunityInfo {
    
    /**
     * 开发商
     */
    private String developer;
    
    /**
     * 物业公司
     */
    private String propertyCompany;
    
    /**
     * 物业费（元/平方米/月）
     */
    private BigDecimal propertyFee;
    
    /**
     * 容积率
     */
    private BigDecimal plotRatio;
    
    /**
     * 绿化率（%）
     */
    private BigDecimal greeningRate;
    
    /**
     * 总户数
     */
    private Integer totalHouseholds;
    
    /**
     * 车位比
     */
    private BigDecimal parkingRatio;
    
    /**
     * 建筑类型
     */
    private String buildingType;
    
    /**
     * 小区品质评级
     */
    private String qualityRating;
    
    /**
     * 物业服务评分
     */
    private BigDecimal serviceRating;
}
