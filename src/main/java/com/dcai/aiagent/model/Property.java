package com.dcai.aiagent.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 房源信息模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Property {
    
    /**
     * 房源ID
     */
    private String id;
    
    /**
     * 小区名称
     */
    private String communityName;
    
    /**
     * 详细地址
     */
    private String address;
    
    /**
     * 户型信息
     */
    private PropertyLayout layout;
    
    /**
     * 楼层信息
     */
    private FloorInfo floorInfo;
    
    /**
     * 面积（平方米）
     */
    private BigDecimal area;
    
    /**
     * 朝向
     */
    private String orientation;
    
    /**
     * 装修状态
     */
    private String decoration;
    
    /**
     * 建造年代
     */
    private Integer buildYear;
    
    /**
     * 产权信息
     */
    private String propertyRight;
    
    /**
     * 挂牌价格（万元）
     */
    private BigDecimal listPrice;
    
    /**
     * 单价（元/平方米）
     */
    private BigDecimal unitPrice;
    
    /**
     * 挂牌时间
     */
    private LocalDateTime listTime;
    
    /**
     * 房源状态
     */
    private PropertyStatus status;
    
    /**
     * 房源特色标签
     */
    private List<String> features;
    
    /**
     * 核心卖点
     */
    private String sellingPoints;
    
    /**
     * 房源描述
     */
    private String description;
    
    /**
     * 房源图片URL列表
     */
    private List<String> imageUrls;
    
    /**
     * 视频URL
     */
    private String videoUrl;
    
    /**
     * 小区详情
     */
    private CommunityInfo communityInfo;
    
    /**
     * 交易需求信息
     */
    private TransactionRequirement transactionRequirement;
    
    /**
     * 带看数据
     */
    private ViewingData viewingData;

    /**
     * 户型信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PropertyLayout {
        private Integer bedrooms;      // 卧室数
        private Integer livingRooms;   // 客厅数
        private Integer bathrooms;     // 卫生间数
        private Integer kitchens;      // 厨房数
        private Integer balconies;     // 阳台数
        
        public String getLayoutDescription() {
            return String.format("%d室%d厅%d卫", bedrooms, livingRooms, bathrooms);
        }
    }

    /**
     * 楼层信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FloorInfo {
        private Integer currentFloor;  // 当前楼层
        private Integer totalFloors;   // 总楼层
        private String floorType;      // 楼层类型：低层、中层、高层
        
        public String getFloorDescription() {
            return String.format("%d/%d层", currentFloor, totalFloors);
        }
    }

    /**
     * 房源状态枚举
     */
    public enum PropertyStatus {
        AVAILABLE("在售"),
        SOLD("已售"),
        SUSPENDED("暂停"),
        RESERVED("预定");
        
        private final String description;
        
        PropertyStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
