package com.dcai.aiagent.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 带看数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ViewingData {
    
    /**
     * 总带看次数
     */
    private Integer totalViewings;
    
    /**
     * 带看记录列表
     */
    private List<ViewingRecord> viewingRecords;
    
    /**
     * 客户反馈汇总
     */
    private ViewingFeedbackSummary feedbackSummary;

    /**
     * 带看记录
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ViewingRecord {
        private LocalDateTime viewingDate;     // 带看日期
        private String agentName;              // 带看经纪人
        private String agentCompany;           // 经纪人公司
        private String clientProfile;          // 客户画像
        private String viewingFeedback;        // 带看反馈
        private List<String> clientConcerns;   // 客户关注点
        private List<String> clientObjections; // 客户抗性点
        private String followUpPlan;           // 跟进计划
    }

    /**
     * 带看反馈汇总
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ViewingFeedbackSummary {
        private List<String> mostConcernedAspects;  // 最关注的方面
        private List<String> commonObjections;      // 常见抗性
        private String overallFeedback;             // 整体反馈
        private String improvementSuggestions;      // 改进建议
    }
}
