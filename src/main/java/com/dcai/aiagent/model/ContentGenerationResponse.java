package com.dcai.aiagent.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 内容生成响应模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContentGenerationResponse {
    
    /**
     * 生成任务ID
     */
    private String taskId;
    
    /**
     * 房源ID
     */
    private String propertyId;
    
    /**
     * 生成的内容
     */
    private StructuredContentResponse content;
    
    /**
     * 生成时间
     */
    private LocalDateTime generatedAt;
    
    /**
     * 生成耗时（毫秒）
     */
    private Long processingTime;
    
    /**
     * 生成状态
     */
    private GenerationStatus status;
    
    /**
     * 错误信息（如果有）
     */
    private String errorMessage;

    /**
     * 生成状态枚举
     */
    public enum GenerationStatus {
        SUCCESS("成功"),
        FAILED("失败"),
        PARTIAL_SUCCESS("部分成功"),
        PROCESSING("处理中");
        
        private final String description;
        
        GenerationStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
