package com.dcai.aiagent.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 交易需求信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionRequirement {
    
    /**
     * 出售原因
     */
    private String sellingReason;
    
    /**
     * 心理底价（万元）
     */
    private BigDecimal reservePrice;
    
    /**
     * 看房便利性
     */
    private String viewingConvenience;
    
    /**
     * 议价空间评估
     */
    private String negotiationSpace;
    
    /**
     * 特殊要求
     */
    private String specialRequirements;
    
    /**
     * 付款周期要求
     */
    private String paymentCycle;
    
    /**
     * 业主联系方式
     */
    private String ownerContact;
    
    /**
     * 业主配合度
     */
    private String ownerCooperation;
}
