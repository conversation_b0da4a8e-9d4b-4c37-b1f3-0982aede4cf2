package com.dcai.aiagent.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.util.List;

/**
 * 内容生成请求模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContentGenerationRequest {
    
    /**
     * 房源ID
     */
    @NotBlank(message = "房源ID不能为空")
    private String propertyId;
    
    /**
     * 推文类型
     */
    @NotNull(message = "推文类型不能为空")
    private ContentType contentType;
    
    /**
     * 目标平台
     */
    @NotNull(message = "目标平台不能为空")
    private List<Platform> targetPlatforms;
    
    /**
     * 目标客户群体
     */
    private TargetAudience targetAudience;
    
    /**
     * 内容风格
     */
    private ContentStyle contentStyle;
    
    /**
     * 字数要求
     */
    @Min(value = 200, message = "字数不能少于200字")
    @Max(value = 1000, message = "字数不能超过1000字")
    private Integer wordCount;
    
    /**
     * 是否需要图表
     */
    private Boolean needCharts;
    
    /**
     * 图表类型列表
     */
    private List<ChartType> chartTypes;
    
    /**
     * 特殊要求
     */
    private String specialRequirements;
    
    /**
     * 推文类型枚举
     */
    public enum ContentType {
        PROPERTY_RECOMMENDATION("房源推荐"),
        MARKET_ANALYSIS("市场分析"),
        LIFESTYLE_AMENITIES("生活配套"),
        BUYING_GUIDE("买房攻略"),
        INVESTMENT_ANALYSIS("投资分析"),
        SCHOOL_DISTRICT("学区房分析"),
        XIAOHONGSHU_POST("小红书种草文案"),
        XIANYU_PROMOTION("闲鱼推广文案"),
        ANJUKE_PROFESSIONAL("安居客专业推荐");
        
        private final String description;
        
        ContentType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 目标平台枚举
     */
    public enum Platform {
        XIAOHONGSHU("小红书"),
        XIANYU("闲鱼"),
        ANJUKE("安居客"),
        WECHAT("微信朋友圈");
        
        private final String description;
        
        Platform(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 目标客户群体枚举
     */
    public enum TargetAudience {
        FIRST_TIME_BUYER("首次购房"),
        UPGRADING_BUYER("改善型购房"),
        INVESTOR("投资客"),
        YOUNG_PROFESSIONAL("年轻白领"),
        FAMILY_WITH_CHILDREN("有孩家庭");
        
        private final String description;
        
        TargetAudience(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 内容风格枚举
     */
    public enum ContentStyle {
        PROFESSIONAL("专业理性"),
        FRIENDLY("亲民易懂"),
        HUMOROUS("幽默风趣"),
        URGENT("紧迫感"),
        DETAILED("详细分析"),
        TRENDY_CASUAL("时尚轻松");
        
        private final String description;
        
        ContentStyle(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 图表类型枚举
     */
    public enum ChartType {
        PRICE_COMPARISON("价格对比图"),
        MARKET_TREND("市场趋势图"),
        AMENITIES_MAP("配套地图"),
        LAYOUT_DIAGRAM("户型图"),
        INVESTMENT_RETURN("投资回报图"),
        TRANSPORTATION_MAP("交通示意图");
        
        private final String description;
        
        ChartType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
