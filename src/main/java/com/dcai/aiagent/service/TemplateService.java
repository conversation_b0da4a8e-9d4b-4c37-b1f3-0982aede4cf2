package com.dcai.aiagent.service;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.StringWriter;
import java.util.Map;

/**
 * 模板服务类
 * 使用FreeMarker处理模板渲染，支持条件判断和循环等逻辑
 */
@Slf4j
@Service
public class TemplateService {

    private final Configuration customFreeMarkerConfiguration;

    public TemplateService(@Qualifier("customFreeMarkerConfiguration") Configuration customFreeMarkerConfiguration) {
        this.customFreeMarkerConfiguration = customFreeMarkerConfiguration;
    }

    /**
     * 渲染模板
     *
     * @param templateName 模板名称（不包含.ftl后缀）
     * @param model 模板数据模型
     * @return 渲染后的字符串
     */
    public String renderTemplate(String templateName, Map<String, Object> model) {
        try {
            Template template = customFreeMarkerConfiguration.getTemplate(templateName + ".ftl");
            StringWriter writer = new StringWriter();
            template.process(model, writer);
            String result = writer.toString();

            log.debug("模板 {} 渲染完成，数据模型: {}", templateName, model.keySet());
            return result;

        } catch (IOException e) {
            log.error("模板文件 {} 加载失败", templateName, e);
            throw new RuntimeException("模板文件加载失败: " + templateName, e);
        } catch (TemplateException e) {
            log.error("模板 {} 渲染失败，数据模型: {}", templateName, model, e);
            throw new RuntimeException("模板渲染失败: " + templateName, e);
        }
    }

    /**
     * 渲染模板 - 直接传递对象作为根数据模型
     *
     * @param templateName 模板名称（不包含.ftl后缀）
     * @param dataModel 数据模型对象
     * @return 渲染后的字符串
     */
    public String renderTemplate(String templateName, Object dataModel) {
        try {
            Template template = customFreeMarkerConfiguration.getTemplate(templateName + ".ftl");
            StringWriter writer = new StringWriter();
            template.process(dataModel, writer);
            String result = writer.toString();

            log.debug("模板 {} 渲染完成，数据模型类型: {}", templateName, dataModel.getClass().getSimpleName());
            return result;

        } catch (IOException e) {
            log.error("模板文件 {} 加载失败", templateName, e);
            throw new RuntimeException("模板文件加载失败: " + templateName, e);
        } catch (TemplateException e) {
            log.error("模板 {} 渲染失败，数据模型: {}", templateName, dataModel, e);
            throw new RuntimeException("模板渲染失败: " + templateName, e);
        }
    }

    /**
     * 检查模板是否存在
     * 
     * @param templateName 模板名称
     * @return 是否存在
     */
    public boolean templateExists(String templateName) {
        try {
            customFreeMarkerConfiguration.getTemplate(templateName + ".ftl");
            return true;
        } catch (IOException e) {
            return false;
        }
    }
}
