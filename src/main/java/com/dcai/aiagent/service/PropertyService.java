package com.dcai.aiagent.service;

import com.dcai.aiagent.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 房源服务
 * 提供房源数据的查询和管理功能
 */
@Slf4j
@Service
public class PropertyService {

    // 模拟房源数据存储
    private final Map<String, Property> propertyDatabase = new HashMap<>();

    public PropertyService() {
        initializeMockData();
    }

    /**
     * 根据房源ID获取房源信息
     */
    public Property getPropertyById(String propertyId) {
        log.info("查询房源信息，ID: {}", propertyId);
        return propertyDatabase.get(propertyId);
    }

    /**
     * 获取所有房源列表
     */
    public List<Property> getAllProperties() {
        return propertyDatabase.values().stream().toList();
    }

    /**
     * 根据条件搜索房源
     */
    public List<Property> searchProperties(String communityName, BigDecimal minPrice, BigDecimal maxPrice) {
        return propertyDatabase.values().stream()
                .filter(property -> {
                    if (communityName != null && !property.getCommunityName().contains(communityName)) {
                        return false;
                    }
                    if (minPrice != null && property.getListPrice().compareTo(minPrice) < 0) {
                        return false;
                    }
                    if (maxPrice != null && property.getListPrice().compareTo(maxPrice) > 0) {
                        return false;
                    }
                    return true;
                })
                .toList();
    }

    /**
     * 初始化模拟数据
     */
    private void initializeMockData() {
        // 创建示例房源1 - 星河湾
        Property property1 = Property.builder()
                .id("PROP_001")
                .communityName("星河湾")
                .address("上海市徐汇区漕河泾开发区桂平路188号")
                .layout(Property.PropertyLayout.builder()
                        .bedrooms(3)
                        .livingRooms(2)
                        .bathrooms(2)
                        .kitchens(1)
                        .balconies(2)
                        .build())
                .floorInfo(Property.FloorInfo.builder()
                        .currentFloor(6)
                        .totalFloors(18)
                        .floorType("中层")
                        .build())
                .area(new BigDecimal("120"))
                .orientation("南北通透")
                .decoration("精装修")
                .buildYear(2010)
                .propertyRight("商品房")
                .listPrice(new BigDecimal("830"))
                .unitPrice(new BigDecimal("69167"))
                .listTime(LocalDateTime.now().minusDays(15))
                .status(Property.PropertyStatus.AVAILABLE)
                .features(Arrays.asList("满五唯一", "南北通透", "精装修", "业主急售", "双地铁"))
                .sellingPoints("内环稀缺次新房，满五唯一省税30万，双地铁交汇，业主移民急售")
                .description("房源位于星河湾高品质社区，南北通透户型，采光极佳，精装修拎包入住")
                .imageUrls(Arrays.asList(
                        "https://example.com/images/prop1_1.jpg",
                        "https://example.com/images/prop1_2.jpg"
                ))
                .communityInfo(CommunityInfo.builder()
                        .developer("星河地产")
                        .propertyCompany("万科物业")
                        .propertyFee(new BigDecimal("4.8"))
                        .plotRatio(new BigDecimal("2.5"))
                        .greeningRate(new BigDecimal("35"))
                        .totalHouseholds(1200)
                        .parkingRatio(new BigDecimal("1.2"))
                        .buildingType("高层")
                        .qualityRating("高端")
                        .serviceRating(new BigDecimal("4.5"))
                        .build())
                .transactionRequirement(TransactionRequirement.builder()
                        .sellingReason("移民")
                        .reservePrice(new BigDecimal("800"))
                        .viewingConvenience("随时看房")
                        .negotiationSpace("有一定议价空间")
                        .specialRequirements("希望快速成交")
                        .paymentCycle("30天内完成过户")
                        .ownerContact("业主本人")
                        .ownerCooperation("配合度高")
                        .build())
                .viewingData(ViewingData.builder()
                        .totalViewings(12)
                        .feedbackSummary(ViewingData.ViewingFeedbackSummary.builder()
                                .mostConcernedAspects(Arrays.asList("价格", "装修", "学区"))
                                .commonObjections(Arrays.asList("价格偏高", "楼层不够高"))
                                .overallFeedback("房源品质不错，但价格需要商量")
                                .improvementSuggestions("适当降价，突出地段优势")
                                .build())
                        .build())
                .build();

        // 创建示例房源2 - 翠湖花园
        Property property2 = Property.builder()
                .id("PROP_002")
                .communityName("翠湖花园")
                .address("上海市浦东新区张江高科技园区科苑路666号")
                .layout(Property.PropertyLayout.builder()
                        .bedrooms(2)
                        .livingRooms(1)
                        .bathrooms(1)
                        .kitchens(1)
                        .balconies(1)
                        .build())
                .floorInfo(Property.FloorInfo.builder()
                        .currentFloor(8)
                        .totalFloors(20)
                        .floorType("中高层")
                        .build())
                .area(new BigDecimal("85"))
                .orientation("南向")
                .decoration("简装")
                .buildYear(2015)
                .propertyRight("商品房")
                .listPrice(new BigDecimal("560"))
                .unitPrice(new BigDecimal("65882"))
                .listTime(LocalDateTime.now().minusDays(8))
                .status(Property.PropertyStatus.AVAILABLE)
                .features(Arrays.asList("地铁房", "学区房", "投资首选", "配套齐全"))
                .sellingPoints("张江核心区域，地铁直达，对口重点学校，投资自住两相宜")
                .description("位于张江高科技园区核心位置，周边配套完善，交通便利")
                .imageUrls(Arrays.asList(
                        "https://example.com/images/prop2_1.jpg",
                        "https://example.com/images/prop2_2.jpg"
                ))
                .communityInfo(CommunityInfo.builder()
                        .developer("绿地集团")
                        .propertyCompany("绿地物业")
                        .propertyFee(new BigDecimal("3.2"))
                        .plotRatio(new BigDecimal("3.0"))
                        .greeningRate(new BigDecimal("30"))
                        .totalHouseholds(800)
                        .parkingRatio(new BigDecimal("0.8"))
                        .buildingType("高层")
                        .qualityRating("中高端")
                        .serviceRating(new BigDecimal("4.0"))
                        .build())
                .transactionRequirement(TransactionRequirement.builder()
                        .sellingReason("置换")
                        .reservePrice(new BigDecimal("540"))
                        .viewingConvenience("需预约")
                        .negotiationSpace("价格基本到位")
                        .specialRequirements("无特殊要求")
                        .paymentCycle("45天")
                        .ownerContact("委托中介")
                        .ownerCooperation("一般")
                        .build())
                .viewingData(ViewingData.builder()
                        .totalViewings(8)
                        .feedbackSummary(ViewingData.ViewingFeedbackSummary.builder()
                                .mostConcernedAspects(Arrays.asList("地段", "交通", "升值潜力"))
                                .commonObjections(Arrays.asList("面积偏小", "装修需要更新"))
                                .overallFeedback("地段不错，适合投资")
                                .improvementSuggestions("强调地段优势和升值潜力")
                                .build())
                        .build())
                .build();

        propertyDatabase.put("PROP_001", property1);
        propertyDatabase.put("PROP_002", property2);
        
        log.info("初始化房源模拟数据完成，共{}套房源", propertyDatabase.size());
    }
}
