package com.dcai.aiagent.service;

import com.dcai.aiagent.model.SurroundingAmenities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 周边配套服务
 * 提供学区、交通、生活配套等信息
 */
@Slf4j
@Service
public class SurroundingAmenitiesService {

    // 模拟配套数据存储
    private final Map<String, SurroundingAmenities> amenitiesDatabase = new HashMap<>();

    public SurroundingAmenitiesService() {
        initializeMockData();
    }

    /**
     * 获取房源周边配套信息
     */
    public SurroundingAmenities getSurroundingAmenities(String propertyId, String amenityType, Integer radius) {
        log.info("查询周边配套信息，房源ID: {}, 类型: {}, 半径: {}米", propertyId, amenityType, radius);
        return amenitiesDatabase.get(propertyId);
    }

    /**
     * 初始化模拟配套数据
     */
    private void initializeMockData() {
        // 星河湾周边配套
        SurroundingAmenities amenities1 = SurroundingAmenities.builder()
                .education(SurroundingAmenities.EducationInfo.builder()
                        .primarySchool("徐汇区第一中心小学")
                        .middleSchool("南洋模范中学")
                        .schoolPolicy("对口入学，需满足户籍和房产证要求")
                        .schoolRanking("市重点，排名前10")
                        .admissionInfo("2024年入学稳定，无预警")
                        .build())
                .transportation(SurroundingAmenities.TransportationInfo.builder()
                        .subwayStations(Arrays.asList(
                                SurroundingAmenities.SubwayStation.builder()
                                        .name("桂林路站")
                                        .line("12号线")
                                        .distance(500)
                                        .walkTime(5)
                                        .build(),
                                SurroundingAmenities.SubwayStation.builder()
                                        .name("漕河泾开发区站")
                                        .line("9号线")
                                        .distance(800)
                                        .walkTime(8)
                                        .build()
                        ))
                        .busStations(Arrays.asList(
                                SurroundingAmenities.BusStation.builder()
                                        .name("桂平路站")
                                        .lines(Arrays.asList("43路", "166路", "732路"))
                                        .distance(200)
                                        .build()
                        ))
                        .mainRoads(Arrays.asList("中环高架", "桂平路", "漕河泾开发区"))
                        .airportDistance("虹桥机场15公里，30分钟车程")
                        .trainStationDistance("上海南站8公里，20分钟车程")
                        .plannedTransport(Arrays.asList("15号线规划中", "中环高架扩建"))
                        .build())
                .lifeAmenities(SurroundingAmenities.LifeAmenities.builder()
                        .shoppingCenters(Arrays.asList(
                                SurroundingAmenities.ShoppingCenter.builder()
                                        .name("汇金百货")
                                        .distance(600)
                                        .scale("大型")
                                        .brands("国际一线品牌")
                                        .build(),
                                SurroundingAmenities.ShoppingCenter.builder()
                                        .name("美罗城")
                                        .distance(1200)
                                        .scale("超大型")
                                        .brands("综合性商场")
                                        .build()
                        ))
                        .hospitals(Arrays.asList(
                                SurroundingAmenities.Hospital.builder()
                                        .name("上海市第六人民医院")
                                        .level("三甲")
                                        .distance(1500)
                                        .specialty("综合性医院")
                                        .build()
                        ))
                        .parks(Arrays.asList("桂林公园", "漕河泾公园"))
                        .banks(Arrays.asList("工商银行", "建设银行", "招商银行"))
                        .restaurants(Arrays.asList("海底捞", "外婆家", "星巴克"))
                        .build())
                .industry(SurroundingAmenities.IndustryEnvironment.builder()
                        .businessDistricts(Arrays.asList("漕河泾开发区", "徐家汇商圈"))
                        .industrialParks(Arrays.asList("漕河泾高新技术开发区"))
                        .employmentOpportunity("IT、金融、贸易等行业就业机会丰富")
                        .landscapes(Arrays.asList("黄浦江滨江绿地", "桂林公园"))
                        .build())
                .development(SurroundingAmenities.DevelopmentPotential.builder()
                        .keyDevelopmentAreas(Arrays.asList("徐汇滨江", "漕河泾开发区"))
                        .majorProjects(Arrays.asList("西岸文化艺术示范区", "徐汇滨江金融城"))
                        .urbanRenewal(Arrays.asList("漕河泾地区城市更新"))
                        .governmentPlanning("打造科技创新中心重要承载区")
                        .build())
                .build();

        // 翠湖花园周边配套
        SurroundingAmenities amenities2 = SurroundingAmenities.builder()
                .education(SurroundingAmenities.EducationInfo.builder()
                        .primarySchool("张江高科实验小学")
                        .middleSchool("上海中学东校")
                        .schoolPolicy("对口入学，张江地区优质教育资源")
                        .schoolRanking("区重点，科技特色突出")
                        .admissionInfo("2024年学位充足")
                        .build())
                .transportation(SurroundingAmenities.TransportationInfo.builder()
                        .subwayStations(Arrays.asList(
                                SurroundingAmenities.SubwayStation.builder()
                                        .name("张江高科站")
                                        .line("2号线")
                                        .distance(800)
                                        .walkTime(10)
                                        .build()
                        ))
                        .busStations(Arrays.asList(
                                SurroundingAmenities.BusStation.builder()
                                        .name("科苑路站")
                                        .lines(Arrays.asList("张江1路", "张江2路"))
                                        .distance(300)
                                        .build()
                        ))
                        .mainRoads(Arrays.asList("外环高速", "中环路", "科苑路"))
                        .airportDistance("浦东机场25公里，40分钟车程")
                        .trainStationDistance("龙阳路站15公里，25分钟车程")
                        .plannedTransport(Arrays.asList("21号线规划中"))
                        .build())
                .lifeAmenities(SurroundingAmenities.LifeAmenities.builder()
                        .shoppingCenters(Arrays.asList(
                                SurroundingAmenities.ShoppingCenter.builder()
                                        .name("张江汇智国际商业中心")
                                        .distance(1000)
                                        .scale("中型")
                                        .brands("生活配套齐全")
                                        .build()
                        ))
                        .hospitals(Arrays.asList(
                                SurroundingAmenities.Hospital.builder()
                                        .name("上海市东方医院")
                                        .level("三甲")
                                        .distance(2000)
                                        .specialty("综合性医院")
                                        .build()
                        ))
                        .parks(Arrays.asList("张江公园", "世纪公园"))
                        .banks(Arrays.asList("中国银行", "农业银行"))
                        .restaurants(Arrays.asList("必胜客", "肯德基", "瑞幸咖啡"))
                        .build())
                .industry(SurroundingAmenities.IndustryEnvironment.builder()
                        .businessDistricts(Arrays.asList("张江高科技园区", "陆家嘴金融区"))
                        .industrialParks(Arrays.asList("张江高科技园区", "张江药谷"))
                        .employmentOpportunity("高科技、生物医药、金融等高薪行业集中")
                        .landscapes(Arrays.asList("张江公园", "川杨河绿地"))
                        .build())
                .development(SurroundingAmenities.DevelopmentPotential.builder()
                        .keyDevelopmentAreas(Arrays.asList("张江科学城", "自贸区临港新片区"))
                        .majorProjects(Arrays.asList("张江实验室", "上海光源二期"))
                        .urbanRenewal(Arrays.asList("张江南区城市更新"))
                        .governmentPlanning("建设具有全球影响力的科技创新中心核心区")
                        .build())
                .build();

        amenitiesDatabase.put("PROP_001", amenities1);
        amenitiesDatabase.put("PROP_002", amenities2);
        
        log.info("初始化周边配套数据完成，覆盖{}个房源", amenitiesDatabase.size());
    }
}
