package com.dcai.aiagent.service;

import com.dcai.aiagent.function.MarketDataFunction;
import com.dcai.aiagent.function.PriceAnalysisFunction;
import com.dcai.aiagent.function.PropertyDataFunction;
import com.dcai.aiagent.function.SurroundingAmenitiesFunction;
import com.dcai.aiagent.model.ContentGenerationRequest;
import com.dcai.aiagent.model.ContentGenerationResponse;
import com.dcai.aiagent.model.StructuredContentResponse;

import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.ai.deepseek.DeepSeekChatOptions;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 内容生成服务
 * 使用AI模型和Function Calling生成房地产推广内容
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ContentGenerationService {

    private final ChatClient chatClient;
    private final PropertyDataFunction propertyDataFunction;
    private final MarketDataFunction marketDataFunction;
    private final SurroundingAmenitiesFunction surroundingAmenitiesFunction;
    private final PriceAnalysisFunction priceAnalysisFunction;
    private final TemplateService templateService;

    /**
     * 生成推广内容
     */
    public ContentGenerationResponse generateContent(ContentGenerationRequest request) {
        log.info("开始生成内容，房源ID: {}, 类型: {}", request.getPropertyId(), request.getContentType());
        
        long startTime = System.currentTimeMillis();
        String taskId = UUID.randomUUID().toString();
        
        try {
            // 构建系统提示词
            String systemPrompt = buildSystemPrompt(request);

            // 构建用户提示词
            String userPrompt = buildUserPrompt(request);

            // 打印完整提示词
            log.info("=== 系统提示词 ===");
            log.info(systemPrompt);
            log.info("=== 用户提示词 ===");
            log.info(userPrompt);
            log.info("=== 开始调用AI模型 ===");

            // 创建BeanOutputConverter
            BeanOutputConverter<StructuredContentResponse> outputConverter =
                new BeanOutputConverter<>(StructuredContentResponse.class);

            // 使用SpringAI BeanOutputConverter自动处理JSON格式
            // BeanOutputConverter会自动添加JSON格式指令到提示词中
            StructuredContentResponse structuredResponse = chatClient.prompt()
                    .system(systemPrompt)
                    .user(userPrompt)
                    .options(DeepSeekChatOptions.builder()
                            .maxTokens(4000)
                            .build())
                    .call()
                    .entity(outputConverter);

            log.info("=== AI模型结构化响应 ===");

            log.info("标题: {}", structuredResponse.getTitle());
            log.info("内容长度: {}", structuredResponse.getContent() != null ? structuredResponse.getContent().length() : 0);
            log.info("标签数量: {}", structuredResponse.getTags() != null ? structuredResponse.getTags().size() : 0);
            log.info("图表数量: {}", structuredResponse.getCharts() != null ? structuredResponse.getCharts().size() : 0);
            log.info("=== AI调用完成 ===");

            // 直接使用结构化响应，无需转换
            StructuredContentResponse content = structuredResponse;

            // 计算字数（如果AI没有提供）
            if (content.getWordCount() == null && content.getContent() != null) {
                content.setWordCount(countWords(content.getContent()));
            }

            long processingTime = System.currentTimeMillis() - startTime;

            return ContentGenerationResponse.builder()
                    .taskId(taskId)
                    .propertyId(request.getPropertyId())
                    .content(content)
                    .generatedAt(LocalDateTime.now())
                    .processingTime(processingTime)
                    .status(ContentGenerationResponse.GenerationStatus.SUCCESS)
                    .build();
                    
        } catch (Exception e) {
            log.error("内容生成失败，房源ID: {}", request.getPropertyId(), e);
            
            long processingTime = System.currentTimeMillis() - startTime;
            
            return ContentGenerationResponse.builder()
                    .taskId(taskId)
                    .propertyId(request.getPropertyId())
                    .generatedAt(LocalDateTime.now())
                    .processingTime(processingTime)
                    .status(ContentGenerationResponse.GenerationStatus.FAILED)
                    .errorMessage(e.getMessage())
                    .build();
        }
    }

    /**
     * 构建系统提示词 - 直接传递ContentGenerationRequest对象给FreeMarker模板
     */
    private String buildSystemPrompt(ContentGenerationRequest request) {
        // 直接传递request对象，让FreeMarker模板处理所有逻辑
        return templateService.renderTemplate("system-prompt", request);
    }

    /**
     * 构建用户提示词 - 直接传递ContentGenerationRequest对象给FreeMarker模板
     */
    private String buildUserPrompt(ContentGenerationRequest request) {
        // 直接传递request对象，让FreeMarker模板处理所有逻辑
        return templateService.renderTemplate("user-prompt", request);
    }



    /**
     * 解析生成的内容 - 备用方法（已废弃）
     * 注意：现在主要使用SpringAI BeanOutputConverter进行结构化输出
     * 此方法保留用于紧急情况下的文本解析
     */
    @Deprecated
    private StructuredContentResponse parseGeneratedContent(String aiResponse, ContentGenerationRequest request) {
        log.warn("使用备用解析方法，建议检查SpringAI BeanOutputConverter配置");

        // 简化的备用解析逻辑
        return StructuredContentResponse.builder()
                .title("解析失败 - 请检查AI响应格式")
                .content(aiResponse)
                .tags(List.of("解析失败"))
                .wordCount(countWords(aiResponse))
                .build();
    }





    /**
     * 解析文本格式的内容（已废弃）
     */
    @Deprecated
    private StructuredContentResponse parseTextContent(String aiResponse, ContentGenerationRequest request) {
        // 简化的备用实现
        return StructuredContentResponse.builder()
                .title(extractTitle(aiResponse.split("\n")))
                .content(aiResponse)
                .tags(extractTags(aiResponse))
                .wordCount(countWords(aiResponse))
                .build();
    }

    /**
     * 提取标题
     */
    private String extractTitle(String[] lines) {
        for (String line : lines) {
            if (line.startsWith("**") && line.endsWith("**") && line.length() > 4) {
                return line.substring(2, line.length() - 2);
            }
            if (line.startsWith("# ")) {
                return line.substring(2);
            }
        }
        return "精品房源推荐";
    }

    /**
     * 提取标签
     */
    private List<String> extractTags(String content) {
        List<String> tags = new ArrayList<>();
        
        // 查找#标签
        String[] words = content.split("\\s+");
        for (String word : words) {
            if (word.startsWith("#") && word.length() > 1) {
                tags.add(word.substring(1));
            }
        }
        
        // 如果没有找到标签，添加默认标签
        if (tags.isEmpty()) {
            tags.addAll(Arrays.asList("房产推荐", "买房攻略", "投资置业"));
        }
        
        return tags;
    }

    /**
     * 统计字数
     */
    private int countWords(String content) {
        return content.replaceAll("\\s+", "").length();
    }

    /**
     * 生成图表数据
     */
    private List<StructuredContentResponse.ChartData> generateChartData(List<ContentGenerationRequest.ChartType> chartTypes) {
        List<StructuredContentResponse.ChartData> charts = new ArrayList<>();
        
        for (ContentGenerationRequest.ChartType chartType : chartTypes) {
            StructuredContentResponse.ChartData chart = StructuredContentResponse.ChartData.builder()
                    .type(chartType.name())
                    .title(chartType.getDescription())
                    .data(generateMockChartData(chartType))
                    .config(StructuredContentResponse.ChartConfig.builder()
                            .xAxis("X轴")
                            .yAxis("Y轴")
                            .colors(List.of("#FF6B6B", "#4ECDC4", "#45B7D1"))
                            .showLegend(true)
                            .showGrid(true)
                            .build())
                    .description("基于真实市场数据生成的" + chartType.getDescription())
                    .build();
            charts.add(chart);
        }
        
        return charts;
    }

    /**
     * 生成模拟图表数据 - 增强版，提供更丰富的数据结构
     */
    private Object generateMockChartData(ContentGenerationRequest.ChartType chartType) {
        Map<String, Object> data = new HashMap<>();

        switch (chartType) {
            case PRICE_COMPARISON:
                data.put("labels", Arrays.asList("本房源", "同小区均价", "区域均价", "全市均价"));
                data.put("values", Arrays.asList(830, 950, 920, 980));
                data.put("unit", "万元/套");
                data.put("colors", Arrays.asList("#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4"));
                data.put("percentages", Arrays.asList(-12.6, 0, -3.2, -15.3)); // 相对于同小区均价的百分比
                break;
            case MARKET_TREND:
                data.put("months", Arrays.asList("2024-01", "2024-02", "2024-03", "2024-04", "2024-05", "2024-06"));
                data.put("prices", Arrays.asList(85000, 87000, 89000, 91000, 93000, 95000));
                data.put("unit", "元/㎡");
                data.put("trend", "上涨");
                data.put("growthRate", 11.8); // 半年涨幅百分比
                data.put("volumes", Arrays.asList(120, 135, 142, 158, 163, 171)); // 成交量
                break;
            case AMENITIES_MAP:
                List<Map<String, Object>> amenities = new ArrayList<>();
                amenities.add(Map.of("type", "地铁站", "name", "徐家汇站", "distance", 500, "walkTime", 6, "lines", Arrays.asList("1号线", "9号线", "11号线")));
                amenities.add(Map.of("type", "学校", "name", "上海中学", "distance", 800, "walkTime", 10, "level", "市重点"));
                amenities.add(Map.of("type", "医院", "name", "瑞金医院", "distance", 1200, "walkTime", 15, "level", "三甲"));
                amenities.add(Map.of("type", "商场", "name", "港汇恒隆广场", "distance", 600, "walkTime", 8, "level", "高端"));
                data.put("amenities", amenities);
                data.put("center", Map.of("lat", 31.1993, "lng", 121.4336)); // 房源坐标
                break;
            case INVESTMENT_RETURN:
                data.put("currentPrice", 830);
                data.put("estimatedValue3Years", 1050);
                data.put("estimatedValue5Years", 1280);
                data.put("rentalYield", 2.8);
                data.put("monthlyRent", 8500);
                data.put("appreciationRate", Map.of("3years", 26.5, "5years", 54.2));
                data.put("riskLevel", "中低");
                break;
            default:
                data.put("message", "图表数据生成中...");
                data.put("type", chartType.name());
        }

        return data;
    }



    /**
     * 生成平台适配内容
     */
    private Map<String, StructuredContentResponse.PlatformContent> generatePlatformContents(
            String title, String body, List<String> tags, List<ContentGenerationRequest.Platform> platforms) {

        Map<String, StructuredContentResponse.PlatformContent> platformContents = new HashMap<>();

        for (ContentGenerationRequest.Platform platform : platforms) {
            StructuredContentResponse.PlatformContent platformContent = StructuredContentResponse.PlatformContent.builder()
                    .adaptedTitle(adaptTitleForPlatform(title, platform))
                    .adaptedContent(adaptContentForPlatform(body, platform))
                    .platformTags(adaptTagsForPlatform(tags, platform))
                    .publishingSuggestion(generatePublishingSuggestion(platform))
                    .build();
            
            platformContents.put(platform.name(), platformContent);
        }
        
        return platformContents;
    }

    /**
     * 为平台适配标题
     */
    private String adaptTitleForPlatform(String title, ContentGenerationRequest.Platform platform) {
        return switch (platform) {
            case XIAOHONGSHU -> "🏠" + title + "✨";
            case XIANYU -> "【急售】" + title;
            case ANJUKE -> title + " - 专业推荐";
            default -> title;
        };
    }

    /**
     * 为平台适配内容
     */
    private String adaptContentForPlatform(String content, ContentGenerationRequest.Platform platform) {
        // 根据不同平台的特点调整内容格式
        switch (platform) {
            case XIAOHONGSHU:
                return content + "\n\n💬 评论区留言获取更多信息";
            case XIANYU:
                return content + "\n\n🔥 支持看房，诚心出售";
            case ANJUKE:
                return content + "\n\n📞 专业顾问为您服务";
            default:
                return content;
        }
    }

    /**
     * 为平台适配标签
     */
    private List<String> adaptTagsForPlatform(List<String> tags, ContentGenerationRequest.Platform platform) {
        List<String> adaptedTags = new ArrayList<>(tags);
        
        switch (platform) {
            case XIAOHONGSHU:
                adaptedTags.add("小红书房产");
                adaptedTags.add("买房日记");
                break;
            case XIANYU:
                adaptedTags.add("二手房");
                adaptedTags.add("急售");
                break;
            case ANJUKE:
                adaptedTags.add("专业推荐");
                adaptedTags.add("品质房源");
                break;
        }
        
        return adaptedTags;
    }

    /**
     * 生成发布建议
     */
    private String generatePublishingSuggestion(ContentGenerationRequest.Platform platform) {
        switch (platform) {
            case XIAOHONGSHU:
                return "建议在晚上8-10点发布，配图要精美，多使用emoji";
            case XIANYU:
                return "建议在工作日上午发布，价格要有吸引力";
            case ANJUKE:
                return "建议在周末发布，内容要专业详细";
            default:
                return "建议选择合适的时间发布";
        }
    }

    /**
     * 评估内容质量
     */
    private StructuredContentResponse.ContentQuality evaluateContentQuality(String content, ContentGenerationRequest request) {
        // 简单的质量评估逻辑
        double overallScore = 8.5;
        double attractivenessScore = 8.0;
        double professionalismScore = 9.0;
        double readabilityScore = 8.5;
        double completenessScore = 8.8;

        List<String> improvements = Arrays.asList(
                "可以增加更多数据支撑",
                "建议添加更多视觉元素",
                "可以强化行动召唤"
        );

        return StructuredContentResponse.ContentQuality.builder()
                .overallScore(overallScore)
                .attractivenessScore(attractivenessScore)
                .professionalismScore(professionalismScore)
                .readabilityScore(readabilityScore)
                .completenessScore(completenessScore)
                .improvementSuggestions(improvements)
                .build();
    }
}
